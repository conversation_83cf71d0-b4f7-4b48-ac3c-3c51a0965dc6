<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="photovoltaic.AppTest" time="0.017" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="gopherProxySet" value="false"/>
    <property name="awt.toolkit" value="sun.lwawt.macosx.LWCToolkit"/>
    <property name="socksProxyHost" value="127.0.0.1"/>
    <property name="http.proxyHost" value="127.0.0.1"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="sun.cpu.isalist" value=""/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/Workspace/ymxsoft-java/socket-api/target/test-classes:/Users/<USER>/Workspace/ymxsoft-java/socket-api/target/classes:/Users/<USER>/Workspace/ymxsoft-java/photovoltaic-service/target/photovoltaic-service.jar:/Users/<USER>/Workspace/ymxsoft-java/photovoltaic-common/target/photovoltaic-common-1.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/net/sf/json-lib/json-lib/2.4/json-lib-2.4-jdk15.jar:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.8.0/commons-beanutils-1.8.0.jar:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar:/Users/<USER>/.m2/repository/net/sf/ezmorph/ezmorph/1.0.6/ezmorph-1.0.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/4.3.5.RELEASE/spring-core-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/4.3.5.RELEASE/spring-tx-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/4.3.5.RELEASE/spring-jdbc-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/4.3.5.RELEASE/spring-web-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/4.3.5.RELEASE/spring-aop-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/1.5.0.RELEASE/spring-data-commons-1.5.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/slf4j/jcl-over-slf4j/1.7.1/jcl-over-slf4j-1.7.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/4.3.5.RELEASE/spring-context-support-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/4.3.5.RELEASE/spring-context-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/4.3.5.RELEASE/spring-expression-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/4.3.5.RELEASE/spring-webmvc-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/4.3.5.RELEASE/spring-beans-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/1.7.8.RELEASE/spring-data-redis-1.7.8.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/1.1.8.RELEASE/spring-data-keyvalue-1.1.8.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/4.2.9.RELEASE/spring-oxm-4.2.9.RELEASE.jar:/Users/<USER>/.m2/repository/commons-httpclient/commons-httpclient/3.1/commons-httpclient-3.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.6/commons-codec-1.6.jar:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.2.2/commons-fileupload-1.2.2.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.2/commons-io-2.2.jar:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.8/poi-3.8.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.8/poi-ooxml-3.8.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.8/poi-ooxml-schemas-3.8.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.4.0/xmlbeans-2.4.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.16/druid-1.2.16.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.0.1/javax.servlet-api-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.3.5/fluent-hc-4.3.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.3.5/httpclient-4.3.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient-cache/4.3.5/httpclient-cache-4.3.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.3.2/httpcore-4.3.2.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.3.5/httpmime-4.3.5.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.1.41/fastjson-1.1.41.jar:/Users/<USER>/.m2/repository/org/apache/mina/mina-core/2.0.7/mina-core-2.0.7.jar:/Users/<USER>/.m2/repository/org/apache/mina/mina-statemachine/2.0.7/mina-statemachine-2.0.7.jar:/Users/<USER>/.m2/repository/org/apache/mina/mina-integration-spring/1.1.7/mina-integration-spring-1.1.7.jar:/Users/<USER>/.m2/repository/org/apache/mina/mina-filter-ssl/1.1.7/mina-filter-ssl-1.1.7.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/23.0/guava-23.0.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.0.18/error_prone_annotations-2.0.18.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1.jar:/Users/<USER>/.m2/repository/org/codehaus/mojo/animal-sniffer-annotations/1.14/animal-sniffer-annotations-1.14.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.20.0/log4j-api-2.20.0.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.20.0/log4j-core-2.20.0.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-jcl/2.20.0/log4j-jcl-2.20.0.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-slf4j2-impl/2.20.0/log4j-slf4j2-impl-2.20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.7/slf4j-api-2.0.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.19/aspectjweaver-1.9.19.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.0-rc3/jackson-annotations-2.15.0-rc3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.0-rc3/jackson-core-2.15.0-rc3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.0-rc3/jackson-databind-2.15.0-rc3.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.2.8/mybatis-3.2.8.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/1.2.2/mybatis-spring-1.2.2.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/5.1.30/mysql-connector-java-5.1.30.jar:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.5.4/javax.mail-1.5.4.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/.m2/repository/cn/jpush/api/jpush-client/3.2.17/jpush-client-3.2.17.jar:/Users/<USER>/.m2/repository/cn/jpush/api/jiguang-common/1.0.3/jiguang-common-1.0.3.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.3.1/okhttp-3.3.1.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.8.0/okio-1.8.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.6.Final/netty-all-4.1.6.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.3/gson-2.3.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/2.6.2/jedis-2.6.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.0/commons-pool2-2.0.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.54/bcprov-jdk15on-1.54.jar:/Users/<USER>/.m2/repository/com/aliyun/dysmsapi20170525/3.1.0/dysmsapi20170525-3.1.0.jar:/Users/<USER>/.m2/repository/com/aliyun/tea-util/0.2.23/tea-util-0.2.23.jar:/Users/<USER>/.m2/repository/com/aliyun/endpoint-util/0.0.7/endpoint-util-0.0.7.jar:/Users/<USER>/.m2/repository/com/aliyun/tea/1.3.1/tea-1.3.1.jar:/Users/<USER>/.m2/repository/com/aliyun/tea-openapi/0.3.6/tea-openapi-0.3.6.jar:/Users/<USER>/.m2/repository/com/aliyun/credentials-java/0.3.10/credentials-java-0.3.10.jar:/Users/<USER>/.m2/repository/org/ini4j/ini4j/0.5.4/ini4j-0.5.4.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.0/jaxb-api-2.3.0.jar:/Users/<USER>/.m2/repository/com/sun/xml/bind/jaxb-core/2.3.0/jaxb-core-2.3.0.jar:/Users/<USER>/.m2/repository/com/sun/xml/bind/jaxb-impl/2.3.0/jaxb-impl-2.3.0.jar:/Users/<USER>/.m2/repository/com/aliyun/alibabacloud-gateway-spi/0.0.2/alibabacloud-gateway-spi-0.0.2.jar:/Users/<USER>/.m2/repository/com/aliyun/tea-xml/0.1.6/tea-xml-0.1.6.jar:/Users/<USER>/.m2/repository/org/dom4j/dom4j/2.0.3/dom4j-2.0.3.jar:/Users/<USER>/.m2/repository/org/jacoco/org.jacoco.agent/0.8.4/org.jacoco.agent-0.8.4-runtime.jar:/Users/<USER>/.m2/repository/com/aliyun/openapiutil/0.2.1/openapiutil-0.2.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcpkix-jdk15on/1.70/bcpkix-jdk15on-1.70.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcutil-jdk15on/1.70/bcutil-jdk15on-1.70.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.16/lombok-1.18.16.jar:/Users/<USER>/.m2/repository/junit/junit/4.11/junit-4.11.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/eclipse/paho/org.eclipse.paho.client.mqttv3/1.2.5/org.eclipse.paho.client.mqttv3-1.2.5.jar:"/>
    <property name="https.proxyPort" value="7890"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value=""/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/Workspace/ymxsoft-java/socket-api/target/surefire/surefirebooter-20250708163536237_3.jar /Users/<USER>/Workspace/ymxsoft-java/socket-api/target/surefire 2025-07-08T16-35-25_216-jvmRun1 surefire-20250708163536237_1tmp surefire_0-20250708163536237_2tmp"/>
    <property name="http.nonProxyHosts" value="***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|127.0.0.1|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/Workspace/ymxsoft-java/socket-api/target/test-classes:/Users/<USER>/Workspace/ymxsoft-java/socket-api/target/classes:/Users/<USER>/Workspace/ymxsoft-java/photovoltaic-service/target/photovoltaic-service.jar:/Users/<USER>/Workspace/ymxsoft-java/photovoltaic-common/target/photovoltaic-common-1.0-SNAPSHOT.jar:/Users/<USER>/.m2/repository/net/sf/json-lib/json-lib/2.4/json-lib-2.4-jdk15.jar:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.8.0/commons-beanutils-1.8.0.jar:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar:/Users/<USER>/.m2/repository/net/sf/ezmorph/ezmorph/1.0.6/ezmorph-1.0.6.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/4.3.5.RELEASE/spring-core-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/4.3.5.RELEASE/spring-tx-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/4.3.5.RELEASE/spring-jdbc-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/4.3.5.RELEASE/spring-web-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/4.3.5.RELEASE/spring-aop-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/1.5.0.RELEASE/spring-data-commons-1.5.0.RELEASE.jar:/Users/<USER>/.m2/repository/org/slf4j/jcl-over-slf4j/1.7.1/jcl-over-slf4j-1.7.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/4.3.5.RELEASE/spring-context-support-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/4.3.5.RELEASE/spring-context-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/4.3.5.RELEASE/spring-expression-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/4.3.5.RELEASE/spring-webmvc-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/4.3.5.RELEASE/spring-beans-4.3.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/1.7.8.RELEASE/spring-data-redis-1.7.8.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/1.1.8.RELEASE/spring-data-keyvalue-1.1.8.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/4.2.9.RELEASE/spring-oxm-4.2.9.RELEASE.jar:/Users/<USER>/.m2/repository/commons-httpclient/commons-httpclient/3.1/commons-httpclient-3.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.6/commons-codec-1.6.jar:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.2.2/commons-fileupload-1.2.2.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.2/commons-io-2.2.jar:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.8/poi-3.8.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.8/poi-ooxml-3.8.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.8/poi-ooxml-schemas-3.8.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.4.0/xmlbeans-2.4.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.16/druid-1.2.16.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.0.1/javax.servlet-api-3.0.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.3.5/fluent-hc-4.3.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.3.5/httpclient-4.3.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient-cache/4.3.5/httpclient-cache-4.3.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.3.2/httpcore-4.3.2.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.3.5/httpmime-4.3.5.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.1.41/fastjson-1.1.41.jar:/Users/<USER>/.m2/repository/org/apache/mina/mina-core/2.0.7/mina-core-2.0.7.jar:/Users/<USER>/.m2/repository/org/apache/mina/mina-statemachine/2.0.7/mina-statemachine-2.0.7.jar:/Users/<USER>/.m2/repository/org/apache/mina/mina-integration-spring/1.1.7/mina-integration-spring-1.1.7.jar:/Users/<USER>/.m2/repository/org/apache/mina/mina-filter-ssl/1.1.7/mina-filter-ssl-1.1.7.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/23.0/guava-23.0.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.0.18/error_prone_annotations-2.0.18.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1.jar:/Users/<USER>/.m2/repository/org/codehaus/mojo/animal-sniffer-annotations/1.14/animal-sniffer-annotations-1.14.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.20.0/log4j-api-2.20.0.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.20.0/log4j-core-2.20.0.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-jcl/2.20.0/log4j-jcl-2.20.0.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-slf4j2-impl/2.20.0/log4j-slf4j2-impl-2.20.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.7/slf4j-api-2.0.7.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.19/aspectjweaver-1.9.19.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.0-rc3/jackson-annotations-2.15.0-rc3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.0-rc3/jackson-core-2.15.0-rc3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.0-rc3/jackson-databind-2.15.0-rc3.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.2.8/mybatis-3.2.8.jar:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/1.2.2/mybatis-spring-1.2.2.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/5.1.30/mysql-connector-java-5.1.30.jar:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.5.4/javax.mail-1.5.4.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/.m2/repository/cn/jpush/api/jpush-client/3.2.17/jpush-client-3.2.17.jar:/Users/<USER>/.m2/repository/cn/jpush/api/jiguang-common/1.0.3/jiguang-common-1.0.3.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.3.1/okhttp-3.3.1.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.8.0/okio-1.8.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.6.Final/netty-all-4.1.6.Final.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.3/gson-2.3.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/2.6.2/jedis-2.6.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.0/commons-pool2-2.0.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.54/bcprov-jdk15on-1.54.jar:/Users/<USER>/.m2/repository/com/aliyun/dysmsapi20170525/3.1.0/dysmsapi20170525-3.1.0.jar:/Users/<USER>/.m2/repository/com/aliyun/tea-util/0.2.23/tea-util-0.2.23.jar:/Users/<USER>/.m2/repository/com/aliyun/endpoint-util/0.0.7/endpoint-util-0.0.7.jar:/Users/<USER>/.m2/repository/com/aliyun/tea/1.3.1/tea-1.3.1.jar:/Users/<USER>/.m2/repository/com/aliyun/tea-openapi/0.3.6/tea-openapi-0.3.6.jar:/Users/<USER>/.m2/repository/com/aliyun/credentials-java/0.3.10/credentials-java-0.3.10.jar:/Users/<USER>/.m2/repository/org/ini4j/ini4j/0.5.4/ini4j-0.5.4.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.0/jaxb-api-2.3.0.jar:/Users/<USER>/.m2/repository/com/sun/xml/bind/jaxb-core/2.3.0/jaxb-core-2.3.0.jar:/Users/<USER>/.m2/repository/com/sun/xml/bind/jaxb-impl/2.3.0/jaxb-impl-2.3.0.jar:/Users/<USER>/.m2/repository/com/aliyun/alibabacloud-gateway-spi/0.0.2/alibabacloud-gateway-spi-0.0.2.jar:/Users/<USER>/.m2/repository/com/aliyun/tea-xml/0.1.6/tea-xml-0.1.6.jar:/Users/<USER>/.m2/repository/org/dom4j/dom4j/2.0.3/dom4j-2.0.3.jar:/Users/<USER>/.m2/repository/org/jacoco/org.jacoco.agent/0.8.4/org.jacoco.agent-0.8.4-runtime.jar:/Users/<USER>/.m2/repository/com/aliyun/openapiutil/0.2.1/openapiutil-0.2.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcpkix-jdk15on/1.70/bcpkix-jdk15on-1.70.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcutil-jdk15on/1.70/bcutil-jdk15on-1.70.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.16/lombok-1.18.16.jar:/Users/<USER>/.m2/repository/junit/junit/4.11/junit-4.11.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/eclipse/paho/org.eclipse.paho.client.mqttv3/1.2.5/org.eclipse.paho.client.mqttv3-1.2.5.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre"/>
    <property name="basedir" value="/Users/<USER>/Workspace/ymxsoft-java/socket-api"/>
    <property name="https.proxyHost" value="127.0.0.1"/>
    <property name="file.separator" value="/"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.CGraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/Workspace/ymxsoft-java/socket-api/target/surefire/surefirebooter-20250708163536237_3.jar"/>
    <property name="sun.boot.class.path" value="/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/classes"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|127.0.0.1|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com"/>
    <property name="java.runtime.version" value="1.8.0_431-b10"/>
    <property name="user.name" value="ymx"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.2"/>
    <property name="java.endorsed.dirs" value="/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/s5/0hfpj9w94bx99dp675g3d08h0000gn/T/"/>
    <property name="idea.version" value="2024.3.1.1"/>
    <property name="java.version" value="1.8.0_431"/>
    <property name="user.dir" value="/Users/<USER>/Workspace/ymxsoft-java/socket-api"/>
    <property name="os.arch" value="aarch64"/>
    <property name="socksProxyPort" value="7890"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.lwawt.macosx.CPrinterJob"/>
    <property name="sun.os.patch.level" value="unknown"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.431-b10"/>
    <property name="java.specification.maintenance.version" value="6"/>
    <property name="java.ext.dirs" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="java.class.version" value="52.0"/>
    <property name="socksNonProxyHosts" value="***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|127.0.0.1|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com"/>
    <property name="http.proxyPort" value="7890"/>
  </properties>
  <testcase name="shouldAnswerWithTrue" classname="photovoltaic.AppTest" time="0.001"/>
</testsuite>