<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee
                      http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
  version="4.0">

  <!-- ======================== Introduction ============================== -->
  <!-- This document defines default values for *all* web applications      -->
  <!-- loaded into this instance of Tomcat.  As each application is         -->
  <!-- deployed, this file is processed, followed by the                    -->
  <!-- "/WEB-INF/web.xml" deployment descriptor from your own               -->
  <!-- applications.                                                        -->
  <!--                                                                      -->
  <!-- WARNING:  Do not configure application-specific resources here!      -->
  <!-- They should go in the "/WEB-INF/web.xml" file in your application.   -->


  <!-- ================== Built In Servlet Definitions ==================== -->


  <!-- The default servlet for all web applications, that serves static     -->
  <!-- resources.  It processes all requests that are not mapped to other   -->
  <!-- servlets with servlet mappings (defined either here or in your own   -->
  <!-- web.xml file).  This servlet supports the following initialization   -->
  <!-- parameters (default values are in square brackets):                  -->
  <!--                                                                      -->
  <!--   debug               Debugging detail level for messages logged     -->
  <!--                       by this servlet. Useful values are 0, 1, and   -->
  <!--                       11 where higher values mean more detail. [0]   -->
  <!--                                                                      -->
  <!--   fileEncoding        Encoding to be used to read static resources   -->
  <!--                       [platform default]                             -->
  <!--                                                                      -->
  <!--   useBomIfPresent     If a static file contains a byte order mark    -->
  <!--                       (BOM), should this be used to determine the    -->
  <!--                       file encoding in preference to fileEncoding.   -->
  <!--                       [true]                                         -->
  <!--                                                                      -->
  <!--   input               Input buffer size (in bytes) when reading      -->
  <!--                       resources to be served.  [2048]                -->
  <!--                                                                      -->
  <!--   listings            Should directory listings be produced if there -->
  <!--                       is no welcome file in this directory?  [false] -->
  <!--                       WARNING: Listings for directories with many    -->
  <!--                       entries can be slow and may consume            -->
  <!--                       significant proportions of server resources.   -->
  <!--                                                                      -->
  <!--   output              Output buffer size (in bytes) when writing     -->
  <!--                       resources to be served.  [2048]                -->
  <!--                                                                      -->
  <!--   readonly            Is this context "read only", so HTTP           -->
  <!--                       commands like PUT and DELETE are               -->
  <!--                       rejected?  [true]                              -->
  <!--                                                                      -->
  <!--   readmeFile          File to display together with the directory    -->
  <!--                       contents. [null]                               -->
  <!--                                                                      -->
  <!--   sendfileSize        If the connector used supports sendfile, this  -->
  <!--                       represents the minimal file size in KiB for    -->
  <!--                       which sendfile will be used. Use a negative    -->
  <!--                       value to always disable sendfile.  [48]        -->
  <!--                                                                      -->
  <!--   useAcceptRanges     Should the Accept-Ranges header be included    -->
  <!--                       in responses where appropriate? [true]         -->
  <!--                       Deprecated. This option will be removed        -->
  <!--                       without replacement in Tomcat 12 onwards where -->
  <!--                       it will effectively be hard coded to true.     -->
  <!--                                                                      -->
  <!--  For directory listing customization. Checks localXsltFile, then     -->
  <!--  globalXsltFile, then defaults to original behavior.                 -->
  <!--                                                                      -->
  <!--   localXsltFile       Make directory listings an XML doc and         -->
  <!--                       pass the result to this style sheet residing   -->
  <!--                       in that directory. This overrides              -->
  <!--                       contextXsltFile and globalXsltFile[null]       -->
  <!--                                                                      -->
  <!--   contextXsltFile     Make directory listings an XML doc and         -->
  <!--                       pass the result to this style sheet which is   -->
  <!--                       relative to the context root. This overrides   -->
  <!--                       globalXsltFile[null]                           -->
  <!--                                                                      -->
  <!--   globalXsltFile      Site wide configuration version of             -->
  <!--                       localXsltFile. This argument must either be an -->
  <!--                       absolute or relative (to either                -->
  <!--                       $CATALINA_BASE/conf or $CATALINA_HOME/conf)    -->
  <!--                       path that points to a location below either    -->
  <!--                       $CATALINA_BASE/conf (checked first) or         -->
  <!--                       $CATALINA_HOME/conf (checked second).[null]    -->
  <!--                                                                      -->
  <!--   showServerInfo      Should server information be presented in the  -->
  <!--                       response sent to clients when directory        -->
  <!--                       listings is enabled? [true]                    -->
  <!--                                                                      -->
  <!--   allowPartialPut     Should the server treat an HTTP PUT request    -->
  <!--                       with a Range header as a partial PUT? Note     -->
  <!--                       that while RFC 7233 clarified that Range       -->
  <!--                       headers only valid for GET requests, RFC 9110  -->
  <!--                       (which obsoletes RFC 7233) now allows partial  -->
  <!--                       puts. [true]                                   -->

    <servlet>
        <servlet-name>default</servlet-name>
        <servlet-class>org.apache.catalina.servlets.DefaultServlet</servlet-class>
        <init-param>
            <param-name>debug</param-name>
            <param-value>0</param-value>
        </init-param>
        <init-param>
            <param-name>listings</param-name>
            <param-value>false</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>


  <!-- The JSP page compiler and execution servlet, which is the mechanism  -->
  <!-- used by Tomcat to support JSP pages.  Traditionally, this servlet    -->
  <!-- is mapped to the URL pattern "*.jsp".  This servlet supports the     -->
  <!-- following initialization parameters (default values are in square    -->
  <!-- brackets):                                                           -->
  <!--                                                                      -->
  <!--   checkInterval       If development is false and checkInterval is   -->
  <!--                       greater than zero, background compilations are -->
  <!--                       enabled. checkInterval is the time in seconds  -->
  <!--                       between checks to see if a JSP page (and its   -->
  <!--                       dependent files) needs to  be recompiled. [0]  -->
  <!--                                                                      -->
  <!--   classdebuginfo      Should the class file be compiled with         -->
  <!--                       debugging information?  [true]                 -->
  <!--                                                                      -->
  <!--   classpath           What class path should I use while compiling   -->
  <!--                       generated servlets?  [Created dynamically      -->
  <!--                       based on the current web application]          -->
  <!--                                                                      -->
  <!--   compiler            Which compiler Ant should use to compile JSP   -->
  <!--                       pages.  See the jasper documentation for more  -->
  <!--                       information.                                   -->
  <!--                                                                      -->
  <!--   compilerSourceVM    Compiler source VM. [1.8]                      -->
  <!--                                                                      -->
  <!--   compilerTargetVM    Compiler target VM. [1.8]                      -->
  <!--                                                                      -->
  <!--   development         Is Jasper used in development mode? If true,   -->
  <!--                       the frequency at which JSPs are checked for    -->
  <!--                       modification may be specified via the          -->
  <!--                       modificationTestInterval parameter. [true]     -->
  <!--                                                                      -->
  <!--   displaySourceFragment                                              -->
  <!--                       Should a source fragment be included in        -->
  <!--                       exception messages? [true]                     -->
  <!--                                                                      -->
  <!--   dumpSmap            Should the SMAP info for JSR45 debugging be    -->
  <!--                       dumped to a file? [false]                      -->
  <!--                       False if suppressSmap is true                  -->
  <!--                                                                      -->
  <!--   enablePooling       Determines whether tag handler pooling is      -->
  <!--                       enabled. This is a compilation option. It will -->
  <!--                       not alter the behaviour of JSPs that have      -->
  <!--                       already been compiled. [true]                  -->
  <!--                                                                      -->
  <!--   engineOptionsClass  Allows specifying the Options class used to    -->
  <!--                       configure Jasper. If not present, the default  -->
  <!--                       EmbeddedServletOptions will be used.           -->
  <!--                       This option is ignored when running under a    -->
  <!--                       SecurityManager.                               -->
  <!--                                                                      -->
  <!--   errorOnUseBeanInvalidClassAttribute                                -->
  <!--                       Should Jasper issue an error when the value of -->
  <!--                       the class attribute in an useBean action is    -->
  <!--                       not a valid bean class?  [true]                -->
  <!--                                                                      -->
  <!--   fork                Tell Ant to fork compiles of JSP pages so that -->
  <!--                       a separate JVM is used for JSP page compiles   -->
  <!--                       from the one Tomcat is running in. [true]      -->
  <!--                                                                      -->
  <!--   genStringAsCharArray                                               -->
  <!--                       Should text strings be generated as char       -->
  <!--                       arrays, to improve performance in some cases?  -->
  <!--                       [false]                                        -->
  <!--                                                                      -->
  <!--   ieClassId           Deprecated. Will be removed in Tomcat 10.1     -->
  <!--                       The class-id value to be sent to Internet      -->
  <!--                       Explorer when using <jsp:plugin> tags.         -->
  <!--                       [clsid:8AD9C840-044E-11D1-B3E9-00805F499D93]   -->
  <!--                                                                      -->
  <!--   javaEncoding        Java file encoding to use for generating java  -->
  <!--                       source files. [UTF8]                           -->
  <!--                                                                      -->
  <!--   keepgenerated       Should we keep the generated Java source code  -->
  <!--                       for each page instead of deleting it? [true]   -->
  <!--                                                                      -->
  <!--   mappedfile          Should we generate static content with one     -->
  <!--                       print statement per input line, to ease        -->
  <!--                       debugging?  [true]                             -->
  <!--                                                                      -->
  <!--   maxLoadedJsps       The maximum number of JSPs that will be loaded -->
  <!--                       for a web application. If more than this       -->
  <!--                       number of JSPs are loaded, the least recently  -->
  <!--                       used JSPs will be unloaded so that the number  -->
  <!--                       of JSPs loaded at any one time does not exceed -->
  <!--                       this limit. A value of zero or less indicates  -->
  <!--                       no limit. [-1]                                 -->
  <!--                                                                      -->
  <!--   jspIdleTimeout      The amount of time in seconds a JSP can be     -->
  <!--                       idle before it is unloaded. A value of zero    -->
  <!--                       or less indicates never unload. [-1]           -->
  <!--                                                                      -->
  <!--   modificationTestInterval                                           -->
  <!--                       Causes a JSP (and its dependent files) to not  -->
  <!--                       be checked for modification during the         -->
  <!--                       specified time interval (in seconds) from the  -->
  <!--                       last time the JSP was checked for              -->
  <!--                       modification. A value of 0 will cause the JSP  -->
  <!--                       to be checked on every access.                 -->
  <!--                       Used in development mode only. [4]             -->
  <!--                                                                      -->
  <!--   recompileOnFail     If a JSP compilation fails should the          -->
  <!--                       modificationTestInterval be ignored and the    -->
  <!--                       next access trigger a re-compilation attempt?  -->
  <!--                       Used in development mode only and is disabled  -->
  <!--                       by default as compilation may be expensive and -->
  <!--                       could lead to excessive resource usage.        -->
  <!--                       [false]                                        -->
  <!--                                                                      -->
  <!--   scratchdir          What scratch directory should we use when      -->
  <!--                       compiling JSP pages?  [default work directory  -->
  <!--                       for the current web application]               -->
  <!--                       This option is ignored when running under a    -->
  <!--                       SecurityManager.                               -->
  <!--                                                                      -->
  <!--   suppressSmap        Should the generation of SMAP info for JSR45   -->
  <!--                       debugging be suppressed?  [false]              -->
  <!--                                                                      -->
  <!--   trimSpaces          Should template text that consists entirely of -->
  <!--                       whitespace be removed from the output (true),  -->
  <!--                       replaced with a single space (single) or left  -->
  <!--                       unchanged (false)? Note that if a JSP page or  -->
  <!--                       tag file specifies a trimDirectiveWhitespaces  -->
  <!--                       value of true, that will take precedence over  -->
  <!--                       this configuration setting for that page/tag.  -->
  <!--                       [false]                                        -->
  <!--                                                                      -->
  <!--   xpoweredBy          Determines whether X-Powered-By response       -->
  <!--                       header is added by generated servlet.  [false] -->
  <!--                                                                      -->
  <!--   strictQuoteEscaping When scriptlet expressions are used for        -->
  <!--                       attribute values, should the rules in JSP.1.6  -->
  <!--                       for the escaping of quote characters be        -->
  <!--                       strictly applied? [true]                       -->
  <!--                                                                      -->
  <!--   quoteAttributeEL    When EL is used in an attribute value on a     -->
  <!--                       JSP page should the rules for quoting of       -->
  <!--                       attributes described in JSP.1.6 be applied to  -->
  <!--                       the expression? [true]                         -->

    <servlet>
        <servlet-name>jsp</servlet-name>
        <servlet-class>org.apache.jasper.servlet.JspServlet</servlet-class>
        <init-param>
            <param-name>fork</param-name>
            <param-value>false</param-value>
        </init-param>
        <init-param>
            <param-name>xpoweredBy</param-name>
            <param-value>false</param-value>
        </init-param>
        <load-on-startup>3</load-on-startup>
    </servlet>


  <!-- NOTE: An SSI Filter is also available as an alternative SSI          -->
  <!-- implementation. Use either the Servlet or the Filter but NOT both.   -->
  <!--                                                                      -->
  <!-- Server Side Includes processing servlet, which processes SSI         -->
  <!-- directives in HTML pages consistent with similar support in web      -->
  <!-- servers like Apache.  Traditionally, this servlet is mapped to the   -->
  <!-- URL pattern "*.shtml".  This servlet supports the following          -->
  <!-- initialization parameters (default values are in square brackets):   -->
  <!--                                                                      -->
  <!--   buffered            Should output from this servlet be buffered?   -->
  <!--                       (0=false, 1=true)  [0]                         -->
  <!--                                                                      -->
  <!--   debug               Debugging detail level for messages logged     -->
  <!--                       by this servlet.  [0]                          -->
  <!--                                                                      -->
  <!--   expires             The number of seconds before a page with SSI   -->
  <!--                       directives will expire.  [No default]          -->
  <!--                                                                      -->
  <!--   isVirtualWebappRelative                                            -->
  <!--                       Should "virtual" paths be interpreted as       -->
  <!--                       relative to the context root, instead of       -->
  <!--                       the server root? [false]                       -->
  <!--                                                                      -->
  <!--   inputEncoding       The encoding to assume for SSI resources if    -->
  <!--                       one is not available from the resource.        -->
  <!--                       [Platform default]                             -->
  <!--                                                                      -->
  <!--   outputEncoding      The encoding to use for the page that results  -->
  <!--                       from the SSI processing. [UTF-8]               -->
  <!--                                                                      -->
  <!--   allowExec           Is use of the exec command enabled? [false]    -->

<!--
    <servlet>
        <servlet-name>ssi</servlet-name>
        <servlet-class>
          org.apache.catalina.ssi.SSIServlet
        </servlet-class>
        <init-param>
          <param-name>buffered</param-name>
          <param-value>1</param-value>
        </init-param>
        <init-param>
          <param-name>debug</param-name>
          <param-value>0</param-value>
        </init-param>
        <init-param>
          <param-name>expires</param-name>
          <param-value>666</param-value>
        </init-param>
        <init-param>
          <param-name>isVirtualWebappRelative</param-name>
          <param-value>false</param-value>
        </init-param>
        <load-on-startup>4</load-on-startup>
    </servlet>
-->


  <!-- Common Gateway Includes (CGI) processing servlet, which supports     -->
  <!-- execution of external applications that conform to the CGI spec      -->
  <!-- requirements.  Typically, this servlet is mapped to the URL pattern  -->
  <!-- "/cgi-bin/*", which means that any CGI applications that are         -->
  <!-- executed must be present within the web application.  This servlet   -->
  <!-- supports the following initialization parameters (default values     -->
  <!-- are in square brackets):                                             -->
  <!--                                                                      -->
  <!--   cgiPathPrefix        The CGI search path will start at             -->
  <!--                        webAppRootDir + File.separator + this prefix. -->
  <!--                        If not set, then webAppRootDir is used.       -->
  <!--                        Recommended value: WEB-INF/cgi                -->
  <!--                                                                      -->
  <!--  cmdLineArgumentsDecoded                                             -->
  <!--                        Only used when enableCmdLineArguments is      -->
  <!--                        true. The pattern that individual decoded     -->
  <!--                        command line arguments must match else the    -->
  <!--                        request will be rejected. This is to          -->
  <!--                        work-around various issues when Java passes   -->
  <!--                        the arguments to the OS. See the CGI How-To   -->
  <!--                        for more details. The default varies by       -->
  <!--                        platform.                                     -->
  <!--                        Windows: [[\w\Q-.\\/:\E]+]                    -->
  <!--                        Others:  [.*]                                 -->
  <!--                        Note that internally the CGI Servlet treats   -->
  <!--                        [.*] as a special case to improve performance -->
  <!--                                                                      -->
  <!--   cmdLineArgumentsEncoded                                            -->
  <!--                        Only used when enableCmdLineArguments is      -->
  <!--                        true. The pattern that individual encoded     -->
  <!--                        command line arguments must match else the    -->
  <!--                        request will be rejected. The default matches -->
  <!--                        the allowed values defined by RFC3875.        -->
  <!--                        [[\w\Q%;/?:@&,$-.!~*'()\E]+]                  -->
  <!--                                                                      -->
  <!--   enableCmdLineArguments                                             -->
  <!--                        Are command line parameters generated from    -->
  <!--                        the query string as per section 4.4 of 3875   -->
  <!--                        RFC? [false]                                  -->
  <!--                                                                      -->
  <!--   executable           Name of the executable used to run the        -->
  <!--                        script. [perl]                                -->
  <!--                                                                      -->
  <!--   envHttpHeaders       A regular expression used to select the HTTP  -->
  <!--                        headers passed to the CGI process as          -->
  <!--                        environment variables. Note that headers are  -->
  <!--                        converted to upper case before matching and   -->
  <!--                        that the entire header name must match the    -->
  <!--                        pattern.                                      -->
  <!--                        [ACCEPT[-0-9A-Z]*|CACHE-CONTROL|COOKIE|HOST|  -->
  <!--                         IF-[-0-9A-Z]*|REFERER|USER-AGENT]            -->
  <!--                                                                      -->
  <!--  environment-variable- An environment to be set for the execution    -->
  <!--                        environment of the CGI script. The name of    -->
  <!--                        variable is taken from the parameter name.    -->
  <!--                        To configure an environment variable named    -->
  <!--                        FOO, configure a parameter named              -->
  <!--                        environment-variable-FOO. The parameter value -->
  <!--                        is used as the environment variable value.    -->
  <!--                        The default is no environment variables.      -->
  <!--                                                                      -->
  <!--   parameterEncoding    Name of parameter encoding to be used with    -->
  <!--                        CGI servlet.                                  -->
  <!--                        [System.getProperty("file.encoding","UTF-8")] -->
  <!--                                                                      -->
  <!--   passShellEnvironment Should the shell environment variables (if    -->
  <!--                        any) be passed to the CGI script? [false]     -->
  <!--                                                                      -->
  <!--   stderrTimeout        The time (in milliseconds) to wait for the    -->
  <!--                        reading of stderr to complete before          -->
  <!--                        terminating the CGI process. [2000]           -->

<!--
    <servlet>
        <servlet-name>cgi</servlet-name>
        <servlet-class>org.apache.catalina.servlets.CGIServlet</servlet-class>
        <init-param>
          <param-name>cgiPathPrefix</param-name>
          <param-value>WEB-INF/cgi</param-value>
        </init-param>
        <load-on-startup>5</load-on-startup>
    </servlet>
-->


  <!-- ================ Built In Servlet Mappings ========================= -->


  <!-- The servlet mappings for the built in servlets defined above.  Note  -->
  <!-- that, by default, the CGI and SSI servlets are *not* mapped.  You    -->
  <!-- must uncomment these mappings (or add them to your application's own -->
  <!-- web.xml deployment descriptor) to enable these services              -->

    <!-- The mapping for the default servlet -->
    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>

    <!-- The mappings for the JSP servlet -->
    <servlet-mapping>
        <servlet-name>jsp</servlet-name>
        <url-pattern>*.jsp</url-pattern>
        <url-pattern>*.jspx</url-pattern>
    </servlet-mapping>

    <!-- The mapping for the SSI servlet -->
<!--
    <servlet-mapping>
        <servlet-name>ssi</servlet-name>
        <url-pattern>*.shtml</url-pattern>
    </servlet-mapping>
-->

    <!-- The mapping for the CGI Gateway servlet -->

<!--
    <servlet-mapping>
        <servlet-name>cgi</servlet-name>
        <url-pattern>/cgi-bin/*</url-pattern>
    </servlet-mapping>
-->


  <!-- ================== Built In Filter Definitions ===================== -->

  <!-- A filter that sets various security related HTTP Response headers.   -->
  <!-- This filter supports the following initialization parameters         -->
  <!-- (default values are in square brackets):                             -->
  <!--                                                                      -->
  <!--   hstsEnabled         Should the HTTP Strict Transport Security      -->
  <!--                       (HSTS) header be added to the response? See    -->
  <!--                       RFC 6797 for more information on HSTS. [true]  -->
  <!--                                                                      -->
  <!--   hstsMaxAgeSeconds   The max age value that should be used in the   -->
  <!--                       HSTS header. Negative values will be treated   -->
  <!--                       as zero. [0]                                   -->
  <!--                                                                      -->
  <!--   hstsIncludeSubDomains                                              -->
  <!--                       Should the includeSubDomains parameter be      -->
  <!--                       included in the HSTS header.                   -->
  <!--                                                                      -->
  <!--   antiClickJackingEnabled                                            -->
  <!--                       Should the anti click-jacking header           -->
  <!--                       X-Frame-Options be added to every response?    -->
  <!--                       [true]                                         -->
  <!--                                                                      -->
  <!--   antiClickJackingOption                                             -->
  <!--                       What value should be used for the header. Must -->
  <!--                       be one of DENY, SAMEORIGIN, ALLOW-FROM         -->
  <!--                       (case-insensitive). [DENY]                     -->
  <!--                                                                      -->
  <!--   antiClickJackingUri IF ALLOW-FROM is used, what URI should be      -->
  <!--                       allowed? []                                    -->
  <!--                                                                      -->
  <!--   blockContentTypeSniffingEnabled                                    -->
  <!--                       Should the header that blocks content type     -->
  <!--                       sniffing be added to every response? [true]    -->
<!--
    <filter>
        <filter-name>httpHeaderSecurity</filter-name>
        <filter-class>org.apache.catalina.filters.HttpHeaderSecurityFilter</filter-class>
        <async-supported>true</async-supported>
    </filter>
-->

  <!-- A filter that sets character encoding that is used to decode -->
  <!-- parameters in a POST request -->
<!--
    <filter>
        <filter-name>setCharacterEncodingFilter</filter-name>
        <filter-class>org.apache.catalina.filters.SetCharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <async-supported>true</async-supported>
    </filter>
-->

  <!-- A filter that triggers request parameters parsing and rejects the    -->
  <!-- request if some parameters were skipped because of parsing errors or -->
  <!-- request size limitations.                                            -->
<!--
    <filter>
        <filter-name>failedRequestFilter</filter-name>
        <filter-class>
          org.apache.catalina.filters.FailedRequestFilter
        </filter-class>
        <async-supported>true</async-supported>
    </filter>
-->


  <!-- NOTE: An SSI Servlet is also available as an alternative SSI         -->
  <!-- implementation. Use either the Servlet or the Filter but NOT both.   -->
  <!--                                                                      -->
  <!-- Server Side Includes processing filter, which processes SSI          -->
  <!-- directives in HTML pages consistent with similar support in web      -->
  <!-- servers like Apache.  Traditionally, this filter is mapped to the    -->
  <!-- URL pattern "*.shtml", though it can be mapped to "*" as it will     -->
  <!-- selectively enable/disable SSI processing based on mime types. For   -->
  <!-- this to work you will need to uncomment the .shtml mime type         -->
  <!-- definition towards the bottom of this file.                          -->
  <!-- The contentType init param allows you to apply SSI processing to JSP -->
  <!-- pages, JavaScript, or any other content you wish.  This filter       -->
  <!-- supports the following initialization parameters (default values are -->
  <!-- in square brackets):                                                 -->
  <!--                                                                      -->
  <!--   contentType         A regex pattern that must be matched before    -->
  <!--                       SSI processing is applied.                     -->
  <!--                       [text/x-server-parsed-html(;.*)?]              -->
  <!--                                                                      -->
  <!--   debug               Debugging detail level for messages logged     -->
  <!--                       by this servlet.  [0]                          -->
  <!--                                                                      -->
  <!--   expires             The number of seconds before a page with SSI   -->
  <!--                       directives will expire.  [No default]          -->
  <!--                                                                      -->
  <!--   isVirtualWebappRelative                                            -->
  <!--                       Should "virtual" paths be interpreted as       -->
  <!--                       relative to the context root, instead of       -->
  <!--                       the server root? [false]                       -->
  <!--                                                                      -->
  <!--   allowExec           Is use of the exec command enabled? [false]    -->

<!--
    <filter>
        <filter-name>ssi</filter-name>
        <filter-class>
          org.apache.catalina.ssi.SSIFilter
        </filter-class>
        <init-param>
          <param-name>contentType</param-name>
          <param-value>text/x-server-parsed-html(;.*)?</param-value>
        </init-param>
        <init-param>
          <param-name>debug</param-name>
          <param-value>0</param-value>
        </init-param>
        <init-param>
          <param-name>expires</param-name>
          <param-value>666</param-value>
        </init-param>
        <init-param>
          <param-name>isVirtualWebappRelative</param-name>
          <param-value>false</param-value>
        </init-param>
    </filter>
-->


  <!-- ==================== Built In Filter Mappings ====================== -->

  <!-- The mapping for the HTTP header security Filter -->
<!--
    <filter-mapping>
        <filter-name>httpHeaderSecurity</filter-name>
        <url-pattern>/*</url-pattern>
        <dispatcher>REQUEST</dispatcher>
    </filter-mapping>
-->

  <!-- The mapping for the Set Character Encoding Filter -->
<!--
    <filter-mapping>
        <filter-name>setCharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
-->

  <!-- The mapping for the Failed Request Filter -->
<!--
    <filter-mapping>
        <filter-name>failedRequestFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
-->

  <!-- The mapping for the SSI Filter -->
<!--
    <filter-mapping>
        <filter-name>ssi</filter-name>
        <url-pattern>*.shtml</url-pattern>
    </filter-mapping>
-->


  <!-- ==================== Default Session Configuration ================= -->
  <!-- You can set the default session timeout (in minutes) for all newly   -->
  <!-- created sessions by modifying the value below.                       -->

    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>


  <!-- ===================== Default MIME Type Mappings =================== -->
  <!-- When serving static resources, Tomcat will automatically generate    -->
  <!-- a "Content-Type" header based on the resource's filename extension,  -->
  <!-- based on these mappings.  Additional mappings can be added here (to  -->
  <!-- apply to all web applications), or in your own application's web.xml -->
  <!-- deployment descriptor.                                               -->
  <!-- Note: Extensions are always matched in a case-insensitive manner.    -->

    <mime-mapping>
        <extension>123</extension>
        <mime-type>application/vnd.lotus-1-2-3</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>3dml</extension>
        <mime-type>text/vnd.in3d.3dml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>3ds</extension>
        <mime-type>image/x-3ds</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>3g2</extension>
        <mime-type>video/3gpp2</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>3gp</extension>
        <mime-type>video/3gpp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>7z</extension>
        <mime-type>application/x-7z-compressed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>aab</extension>
        <mime-type>application/x-authorware-bin</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>aac</extension>
        <mime-type>audio/x-aac</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>aam</extension>
        <mime-type>application/x-authorware-map</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>aas</extension>
        <mime-type>application/x-authorware-seg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>abs</extension>
        <mime-type>audio/x-mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>abw</extension>
        <mime-type>application/x-abiword</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ac</extension>
        <mime-type>application/pkix-attr-cert</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>acc</extension>
        <mime-type>application/vnd.americandynamics.acc</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ace</extension>
        <mime-type>application/x-ace-compressed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>acu</extension>
        <mime-type>application/vnd.acucobol</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>acutc</extension>
        <mime-type>application/vnd.acucorp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>adp</extension>
        <mime-type>audio/adpcm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>aep</extension>
        <mime-type>application/vnd.audiograph</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>afm</extension>
        <mime-type>application/x-font-type1</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>afp</extension>
        <mime-type>application/vnd.ibm.modcap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ahead</extension>
        <mime-type>application/vnd.ahead.space</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ai</extension>
        <mime-type>application/postscript</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>aif</extension>
        <mime-type>audio/x-aiff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>aifc</extension>
        <mime-type>audio/x-aiff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>aiff</extension>
        <mime-type>audio/x-aiff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>aim</extension>
        <mime-type>application/x-aim</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>air</extension>
        <mime-type>application/vnd.adobe.air-application-installer-package+zip</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ait</extension>
        <mime-type>application/vnd.dvb.ait</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ami</extension>
        <mime-type>application/vnd.amiga.ami</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>anx</extension>
        <mime-type>application/annodex</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>apk</extension>
        <mime-type>application/vnd.android.package-archive</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>appcache</extension>
        <mime-type>text/cache-manifest</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>application</extension>
        <mime-type>application/x-ms-application</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>apr</extension>
        <mime-type>application/vnd.lotus-approach</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>arc</extension>
        <mime-type>application/x-freearc</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>art</extension>
        <mime-type>image/x-jg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>asc</extension>
        <mime-type>application/pgp-signature</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>asf</extension>
        <mime-type>video/x-ms-asf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>asm</extension>
        <mime-type>text/x-asm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>aso</extension>
        <mime-type>application/vnd.accpac.simply.aso</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>asx</extension>
        <mime-type>video/x-ms-asf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>atc</extension>
        <mime-type>application/vnd.acucorp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>atom</extension>
        <mime-type>application/atom+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>atomcat</extension>
        <mime-type>application/atomcat+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>atomsvc</extension>
        <mime-type>application/atomsvc+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>atx</extension>
        <mime-type>application/vnd.antix.game-component</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>au</extension>
        <mime-type>audio/basic</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>avi</extension>
        <mime-type>video/x-msvideo</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>avx</extension>
        <mime-type>video/x-rad-screenplay</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>aw</extension>
        <mime-type>application/applixware</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>axa</extension>
        <mime-type>audio/annodex</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>axv</extension>
        <mime-type>video/annodex</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>azf</extension>
        <mime-type>application/vnd.airzip.filesecure.azf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>azs</extension>
        <mime-type>application/vnd.airzip.filesecure.azs</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>azw</extension>
        <mime-type>application/vnd.amazon.ebook</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>bat</extension>
        <mime-type>application/x-msdownload</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>bcpio</extension>
        <mime-type>application/x-bcpio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>bdf</extension>
        <mime-type>application/x-font-bdf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>bdm</extension>
        <mime-type>application/vnd.syncml.dm+wbxml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>bed</extension>
        <mime-type>application/vnd.realvnc.bed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>bh2</extension>
        <mime-type>application/vnd.fujitsu.oasysprs</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>bin</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>blb</extension>
        <mime-type>application/x-blorb</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>blorb</extension>
        <mime-type>application/x-blorb</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>bmi</extension>
        <mime-type>application/vnd.bmi</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>bmp</extension>
        <mime-type>image/bmp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>body</extension>
        <mime-type>text/html</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>book</extension>
        <mime-type>application/vnd.framemaker</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>box</extension>
        <mime-type>application/vnd.previewsystems.box</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>boz</extension>
        <mime-type>application/x-bzip2</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>bpk</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>btif</extension>
        <mime-type>image/prs.btif</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>bz</extension>
        <mime-type>application/x-bzip</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>bz2</extension>
        <mime-type>application/x-bzip2</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>c</extension>
        <mime-type>text/x-c</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>c11amc</extension>
        <mime-type>application/vnd.cluetrust.cartomobile-config</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>c11amz</extension>
        <mime-type>application/vnd.cluetrust.cartomobile-config-pkg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>c4d</extension>
        <mime-type>application/vnd.clonk.c4group</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>c4f</extension>
        <mime-type>application/vnd.clonk.c4group</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>c4g</extension>
        <mime-type>application/vnd.clonk.c4group</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>c4p</extension>
        <mime-type>application/vnd.clonk.c4group</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>c4u</extension>
        <mime-type>application/vnd.clonk.c4group</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cab</extension>
        <mime-type>application/vnd.ms-cab-compressed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>caf</extension>
        <mime-type>audio/x-caf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cap</extension>
        <mime-type>application/vnd.tcpdump.pcap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>car</extension>
        <mime-type>application/vnd.curl.car</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cat</extension>
        <mime-type>application/vnd.ms-pki.seccat</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cb7</extension>
        <mime-type>application/x-cbr</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cba</extension>
        <mime-type>application/x-cbr</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cbr</extension>
        <mime-type>application/x-cbr</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cbt</extension>
        <mime-type>application/x-cbr</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cbz</extension>
        <mime-type>application/x-cbr</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cc</extension>
        <mime-type>text/x-c</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cct</extension>
        <mime-type>application/x-director</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ccxml</extension>
        <mime-type>application/ccxml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cdbcmsg</extension>
        <mime-type>application/vnd.contact.cmsg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cdf</extension>
        <mime-type>application/x-cdf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cdkey</extension>
        <mime-type>application/vnd.mediastation.cdkey</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cdmia</extension>
        <mime-type>application/cdmi-capability</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cdmic</extension>
        <mime-type>application/cdmi-container</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cdmid</extension>
        <mime-type>application/cdmi-domain</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cdmio</extension>
        <mime-type>application/cdmi-object</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cdmiq</extension>
        <mime-type>application/cdmi-queue</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cdx</extension>
        <mime-type>chemical/x-cdx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cdxml</extension>
        <mime-type>application/vnd.chemdraw+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cdy</extension>
        <mime-type>application/vnd.cinderella</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cer</extension>
        <mime-type>application/pkix-cert</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cfs</extension>
        <mime-type>application/x-cfs-compressed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cgm</extension>
        <mime-type>image/cgm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>chat</extension>
        <mime-type>application/x-chat</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>chm</extension>
        <mime-type>application/vnd.ms-htmlhelp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>chrt</extension>
        <mime-type>application/vnd.kde.kchart</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cif</extension>
        <mime-type>chemical/x-cif</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cii</extension>
        <mime-type>application/vnd.anser-web-certificate-issue-initiation</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cil</extension>
        <mime-type>application/vnd.ms-artgalry</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cla</extension>
        <mime-type>application/vnd.claymore</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>class</extension>
        <mime-type>application/java</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>clkk</extension>
        <mime-type>application/vnd.crick.clicker.keyboard</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>clkp</extension>
        <mime-type>application/vnd.crick.clicker.palette</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>clkt</extension>
        <mime-type>application/vnd.crick.clicker.template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>clkw</extension>
        <mime-type>application/vnd.crick.clicker.wordbank</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>clkx</extension>
        <mime-type>application/vnd.crick.clicker</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>clp</extension>
        <mime-type>application/x-msclip</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cmc</extension>
        <mime-type>application/vnd.cosmocaller</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cmdf</extension>
        <mime-type>chemical/x-cmdf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cml</extension>
        <mime-type>chemical/x-cml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cmp</extension>
        <mime-type>application/vnd.yellowriver-custom-menu</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cmx</extension>
        <mime-type>image/x-cmx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cod</extension>
        <mime-type>application/vnd.rim.cod</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>com</extension>
        <mime-type>application/x-msdownload</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>conf</extension>
        <mime-type>text/plain</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cpio</extension>
        <mime-type>application/x-cpio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cpp</extension>
        <mime-type>text/x-c</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cpt</extension>
        <mime-type>application/mac-compactpro</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>crd</extension>
        <mime-type>application/x-mscardfile</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>crl</extension>
        <mime-type>application/pkix-crl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>crt</extension>
        <mime-type>application/x-x509-ca-cert</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cryptonote</extension>
        <mime-type>application/vnd.rig.cryptonote</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>csh</extension>
        <mime-type>application/x-csh</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>csml</extension>
        <mime-type>chemical/x-csml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>csp</extension>
        <mime-type>application/vnd.commonspace</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>css</extension>
        <mime-type>text/css</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cst</extension>
        <mime-type>application/x-director</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>csv</extension>
        <mime-type>text/csv</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cu</extension>
        <mime-type>application/cu-seeme</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>curl</extension>
        <mime-type>text/vnd.curl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cww</extension>
        <mime-type>application/prs.cww</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cxt</extension>
        <mime-type>application/x-director</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>cxx</extension>
        <mime-type>text/x-c</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dae</extension>
        <mime-type>model/vnd.collada+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>daf</extension>
        <mime-type>application/vnd.mobius.daf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dart</extension>
        <mime-type>application/vnd.dart</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dataless</extension>
        <mime-type>application/vnd.fdsn.seed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>davmount</extension>
        <mime-type>application/davmount+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dbk</extension>
        <mime-type>application/docbook+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dcr</extension>
        <mime-type>application/x-director</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dcurl</extension>
        <mime-type>text/vnd.curl.dcurl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dd2</extension>
        <mime-type>application/vnd.oma.dd2+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ddd</extension>
        <mime-type>application/vnd.fujixerox.ddd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>deb</extension>
        <mime-type>application/x-debian-package</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>def</extension>
        <mime-type>text/plain</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>deploy</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>der</extension>
        <mime-type>application/x-x509-ca-cert</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dfac</extension>
        <mime-type>application/vnd.dreamfactory</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dgc</extension>
        <mime-type>application/x-dgc-compressed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dib</extension>
        <mime-type>image/bmp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dic</extension>
        <mime-type>text/x-c</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dir</extension>
        <mime-type>application/x-director</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dis</extension>
        <mime-type>application/vnd.mobius.dis</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dist</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>distz</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>djv</extension>
        <mime-type>image/vnd.djvu</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>djvu</extension>
        <mime-type>image/vnd.djvu</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dll</extension>
        <mime-type>application/x-msdownload</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dmg</extension>
        <mime-type>application/x-apple-diskimage</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dmp</extension>
        <mime-type>application/vnd.tcpdump.pcap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dms</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dna</extension>
        <mime-type>application/vnd.dna</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>doc</extension>
        <mime-type>application/msword</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>docm</extension>
        <mime-type>application/vnd.ms-word.document.macroenabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>docx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.wordprocessingml.document</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dot</extension>
        <mime-type>application/msword</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dotm</extension>
        <mime-type>application/vnd.ms-word.template.macroenabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dotx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.wordprocessingml.template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dp</extension>
        <mime-type>application/vnd.osgi.dp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dpg</extension>
        <mime-type>application/vnd.dpgraph</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dra</extension>
        <mime-type>audio/vnd.dra</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dsc</extension>
        <mime-type>text/prs.lines.tag</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dssc</extension>
        <mime-type>application/dssc+der</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dtb</extension>
        <mime-type>application/x-dtbook+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dtd</extension>
        <mime-type>application/xml-dtd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dts</extension>
        <mime-type>audio/vnd.dts</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dtshd</extension>
        <mime-type>audio/vnd.dts.hd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dump</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dv</extension>
        <mime-type>video/x-dv</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dvb</extension>
        <mime-type>video/vnd.dvb.file</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dvi</extension>
        <mime-type>application/x-dvi</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dwf</extension>
        <mime-type>model/vnd.dwf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dwg</extension>
        <mime-type>image/vnd.dwg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dxf</extension>
        <mime-type>image/vnd.dxf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dxp</extension>
        <mime-type>application/vnd.spotfire.dxp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>dxr</extension>
        <mime-type>application/x-director</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ecelp4800</extension>
        <mime-type>audio/vnd.nuera.ecelp4800</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ecelp7470</extension>
        <mime-type>audio/vnd.nuera.ecelp7470</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ecelp9600</extension>
        <mime-type>audio/vnd.nuera.ecelp9600</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ecma</extension>
        <mime-type>application/ecmascript</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>edm</extension>
        <mime-type>application/vnd.novadigm.edm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>edx</extension>
        <mime-type>application/vnd.novadigm.edx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>efif</extension>
        <mime-type>application/vnd.picsel</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ei6</extension>
        <mime-type>application/vnd.pg.osasli</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>elc</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>emf</extension>
        <mime-type>application/x-msmetafile</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>eml</extension>
        <mime-type>message/rfc822</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>emma</extension>
        <mime-type>application/emma+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>emz</extension>
        <mime-type>application/x-msmetafile</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>eol</extension>
        <mime-type>audio/vnd.digital-winds</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>eot</extension>
        <mime-type>application/vnd.ms-fontobject</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>eps</extension>
        <mime-type>application/postscript</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>epub</extension>
        <mime-type>application/epub+zip</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>es3</extension>
        <mime-type>application/vnd.eszigno3+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>esa</extension>
        <mime-type>application/vnd.osgi.subsystem</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>esf</extension>
        <mime-type>application/vnd.epson.esf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>et3</extension>
        <mime-type>application/vnd.eszigno3+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>etx</extension>
        <mime-type>text/x-setext</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>eva</extension>
        <mime-type>application/x-eva</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>evy</extension>
        <mime-type>application/x-envoy</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>exe</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>exi</extension>
        <mime-type>application/exi</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ext</extension>
        <mime-type>application/vnd.novadigm.ext</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ez</extension>
        <mime-type>application/andrew-inset</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ez2</extension>
        <mime-type>application/vnd.ezpix-album</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ez3</extension>
        <mime-type>application/vnd.ezpix-package</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>f</extension>
        <mime-type>text/x-fortran</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>f4v</extension>
        <mime-type>video/x-f4v</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>f77</extension>
        <mime-type>text/x-fortran</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>f90</extension>
        <mime-type>text/x-fortran</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fbs</extension>
        <mime-type>image/vnd.fastbidsheet</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fcdt</extension>
        <mime-type>application/vnd.adobe.formscentral.fcdt</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fcs</extension>
        <mime-type>application/vnd.isac.fcs</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fdf</extension>
        <mime-type>application/vnd.fdf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fe_launch</extension>
        <mime-type>application/vnd.denovo.fcselayout-link</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fg5</extension>
        <mime-type>application/vnd.fujitsu.oasysgp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fgd</extension>
        <mime-type>application/x-director</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fh</extension>
        <mime-type>image/x-freehand</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fh4</extension>
        <mime-type>image/x-freehand</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fh5</extension>
        <mime-type>image/x-freehand</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fh7</extension>
        <mime-type>image/x-freehand</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fhc</extension>
        <mime-type>image/x-freehand</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fig</extension>
        <mime-type>application/x-xfig</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>flac</extension>
        <mime-type>audio/flac</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fli</extension>
        <mime-type>video/x-fli</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>flo</extension>
        <mime-type>application/vnd.micrografx.flo</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>flv</extension>
        <mime-type>video/x-flv</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>flw</extension>
        <mime-type>application/vnd.kde.kivio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>flx</extension>
        <mime-type>text/vnd.fmi.flexstor</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fly</extension>
        <mime-type>text/vnd.fly</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fm</extension>
        <mime-type>application/vnd.framemaker</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fnc</extension>
        <mime-type>application/vnd.frogans.fnc</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>for</extension>
        <mime-type>text/x-fortran</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fpx</extension>
        <mime-type>image/vnd.fpx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>frame</extension>
        <mime-type>application/vnd.framemaker</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fsc</extension>
        <mime-type>application/vnd.fsc.weblaunch</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fst</extension>
        <mime-type>image/vnd.fst</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ftc</extension>
        <mime-type>application/vnd.fluxtime.clip</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fti</extension>
        <mime-type>application/vnd.anser-web-funds-transfer-initiation</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fvt</extension>
        <mime-type>video/vnd.fvt</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fxp</extension>
        <mime-type>application/vnd.adobe.fxp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fxpl</extension>
        <mime-type>application/vnd.adobe.fxp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>fzs</extension>
        <mime-type>application/vnd.fuzzysheet</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>g2w</extension>
        <mime-type>application/vnd.geoplan</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>g3</extension>
        <mime-type>image/g3fax</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>g3w</extension>
        <mime-type>application/vnd.geospace</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gac</extension>
        <mime-type>application/vnd.groove-account</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gam</extension>
        <mime-type>application/x-tads</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gbr</extension>
        <mime-type>application/rpki-ghostbusters</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gca</extension>
        <mime-type>application/x-gca-compressed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gdl</extension>
        <mime-type>model/vnd.gdl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>geo</extension>
        <mime-type>application/vnd.dynageo</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gex</extension>
        <mime-type>application/vnd.geometry-explorer</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ggb</extension>
        <mime-type>application/vnd.geogebra.file</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ggs</extension>
        <mime-type>application/vnd.geogebra.slides</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ggt</extension>
        <mime-type>application/vnd.geogebra.tool</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ghf</extension>
        <mime-type>application/vnd.groove-help</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gif</extension>
        <mime-type>image/gif</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gim</extension>
        <mime-type>application/vnd.groove-identity-message</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gml</extension>
        <mime-type>application/gml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gmx</extension>
        <mime-type>application/vnd.gmx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gnumeric</extension>
        <mime-type>application/x-gnumeric</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gph</extension>
        <mime-type>application/vnd.flographit</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gpx</extension>
        <mime-type>application/gpx+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gqf</extension>
        <mime-type>application/vnd.grafeq</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gqs</extension>
        <mime-type>application/vnd.grafeq</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gram</extension>
        <mime-type>application/srgs</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gramps</extension>
        <mime-type>application/x-gramps-xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gre</extension>
        <mime-type>application/vnd.geometry-explorer</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>grv</extension>
        <mime-type>application/vnd.groove-injector</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>grxml</extension>
        <mime-type>application/srgs+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gsf</extension>
        <mime-type>application/x-font-ghostscript</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gtar</extension>
        <mime-type>application/x-gtar</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gtm</extension>
        <mime-type>application/vnd.groove-tool-message</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gtw</extension>
        <mime-type>model/vnd.gtw</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gv</extension>
        <mime-type>text/vnd.graphviz</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gxf</extension>
        <mime-type>application/gxf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gxt</extension>
        <mime-type>application/vnd.geonext</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>gz</extension>
        <mime-type>application/x-gzip</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>h</extension>
        <mime-type>text/x-c</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>h261</extension>
        <mime-type>video/h261</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>h263</extension>
        <mime-type>video/h263</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>h264</extension>
        <mime-type>video/h264</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>hal</extension>
        <mime-type>application/vnd.hal+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>hbci</extension>
        <mime-type>application/vnd.hbci</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>hdf</extension>
        <mime-type>application/x-hdf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>hh</extension>
        <mime-type>text/x-c</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>hlp</extension>
        <mime-type>application/winhlp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>hpgl</extension>
        <mime-type>application/vnd.hp-hpgl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>hpid</extension>
        <mime-type>application/vnd.hp-hpid</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>hps</extension>
        <mime-type>application/vnd.hp-hps</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>hqx</extension>
        <mime-type>application/mac-binhex40</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>htc</extension>
        <mime-type>text/x-component</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>htke</extension>
        <mime-type>application/vnd.kenameaapp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>htm</extension>
        <mime-type>text/html</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>html</extension>
        <mime-type>text/html</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>hvd</extension>
        <mime-type>application/vnd.yamaha.hv-dic</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>hvp</extension>
        <mime-type>application/vnd.yamaha.hv-voice</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>hvs</extension>
        <mime-type>application/vnd.yamaha.hv-script</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>i2g</extension>
        <mime-type>application/vnd.intergeo</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>icc</extension>
        <mime-type>application/vnd.iccprofile</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ice</extension>
        <mime-type>x-conference/x-cooltalk</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>icm</extension>
        <mime-type>application/vnd.iccprofile</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ico</extension>
        <mime-type>image/x-icon</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ics</extension>
        <mime-type>text/calendar</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ief</extension>
        <mime-type>image/ief</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ifb</extension>
        <mime-type>text/calendar</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ifm</extension>
        <mime-type>application/vnd.shana.informed.formdata</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>iges</extension>
        <mime-type>model/iges</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>igl</extension>
        <mime-type>application/vnd.igloader</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>igm</extension>
        <mime-type>application/vnd.insors.igm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>igs</extension>
        <mime-type>model/iges</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>igx</extension>
        <mime-type>application/vnd.micrografx.igx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>iif</extension>
        <mime-type>application/vnd.shana.informed.interchange</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>imp</extension>
        <mime-type>application/vnd.accpac.simply.imp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ims</extension>
        <mime-type>application/vnd.ms-ims</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>in</extension>
        <mime-type>text/plain</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ink</extension>
        <mime-type>application/inkml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>inkml</extension>
        <mime-type>application/inkml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>install</extension>
        <mime-type>application/x-install-instructions</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>iota</extension>
        <mime-type>application/vnd.astraea-software.iota</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ipfix</extension>
        <mime-type>application/ipfix</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ipk</extension>
        <mime-type>application/vnd.shana.informed.package</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>irm</extension>
        <mime-type>application/vnd.ibm.rights-management</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>irp</extension>
        <mime-type>application/vnd.irepository.package+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>iso</extension>
        <mime-type>application/x-iso9660-image</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>itp</extension>
        <mime-type>application/vnd.shana.informed.formtemplate</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ivp</extension>
        <mime-type>application/vnd.immervision-ivp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ivu</extension>
        <mime-type>application/vnd.immervision-ivu</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jad</extension>
        <mime-type>text/vnd.sun.j2me.app-descriptor</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jam</extension>
        <mime-type>application/vnd.jam</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jar</extension>
        <mime-type>application/java-archive</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>java</extension>
        <mime-type>text/x-java-source</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jisp</extension>
        <mime-type>application/vnd.jisp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jlt</extension>
        <mime-type>application/vnd.hp-jlyt</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jnlp</extension>
        <mime-type>application/x-java-jnlp-file</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>joda</extension>
        <mime-type>application/vnd.joost.joda-archive</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jpe</extension>
        <mime-type>image/jpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jpeg</extension>
        <mime-type>image/jpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jpg</extension>
        <mime-type>image/jpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jpgm</extension>
        <mime-type>video/jpm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jpgv</extension>
        <mime-type>video/jpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jpm</extension>
        <mime-type>video/jpm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>js</extension>
        <mime-type>text/javascript</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jsf</extension>
        <mime-type>text/plain</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>json</extension>
        <mime-type>application/json</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jsonml</extension>
        <mime-type>application/jsonml+json</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>jspf</extension>
        <mime-type>text/plain</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>kar</extension>
        <mime-type>audio/midi</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>karbon</extension>
        <mime-type>application/vnd.kde.karbon</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>kfo</extension>
        <mime-type>application/vnd.kde.kformula</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>kia</extension>
        <mime-type>application/vnd.kidspiration</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>kml</extension>
        <mime-type>application/vnd.google-earth.kml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>kmz</extension>
        <mime-type>application/vnd.google-earth.kmz</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>kne</extension>
        <mime-type>application/vnd.kinar</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>knp</extension>
        <mime-type>application/vnd.kinar</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>kon</extension>
        <mime-type>application/vnd.kde.kontour</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>kpr</extension>
        <mime-type>application/vnd.kde.kpresenter</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>kpt</extension>
        <mime-type>application/vnd.kde.kpresenter</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>kpxx</extension>
        <mime-type>application/vnd.ds-keypoint</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ksp</extension>
        <mime-type>application/vnd.kde.kspread</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ktr</extension>
        <mime-type>application/vnd.kahootz</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ktx</extension>
        <mime-type>image/ktx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ktz</extension>
        <mime-type>application/vnd.kahootz</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>kwd</extension>
        <mime-type>application/vnd.kde.kword</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>kwt</extension>
        <mime-type>application/vnd.kde.kword</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>lasxml</extension>
        <mime-type>application/vnd.las.las+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>latex</extension>
        <mime-type>application/x-latex</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>lbd</extension>
        <mime-type>application/vnd.llamagraphics.life-balance.desktop</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>lbe</extension>
        <mime-type>application/vnd.llamagraphics.life-balance.exchange+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>les</extension>
        <mime-type>application/vnd.hhe.lesson-player</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>lha</extension>
        <mime-type>application/x-lzh-compressed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>link66</extension>
        <mime-type>application/vnd.route66.link66+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>list</extension>
        <mime-type>text/plain</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>list3820</extension>
        <mime-type>application/vnd.ibm.modcap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>listafp</extension>
        <mime-type>application/vnd.ibm.modcap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>lnk</extension>
        <mime-type>application/x-ms-shortcut</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>log</extension>
        <mime-type>text/plain</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>lostxml</extension>
        <mime-type>application/lost+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>lrf</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>lrm</extension>
        <mime-type>application/vnd.ms-lrm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ltf</extension>
        <mime-type>application/vnd.frogans.ltf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>lvp</extension>
        <mime-type>audio/vnd.lucent.voice</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>lwp</extension>
        <mime-type>application/vnd.lotus-wordpro</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>lzh</extension>
        <mime-type>application/x-lzh-compressed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m13</extension>
        <mime-type>application/x-msmediaview</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m14</extension>
        <mime-type>application/x-msmediaview</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m1v</extension>
        <mime-type>video/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m21</extension>
        <mime-type>application/mp21</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m2a</extension>
        <mime-type>audio/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m2v</extension>
        <mime-type>video/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m3a</extension>
        <mime-type>audio/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m3u</extension>
        <mime-type>audio/x-mpegurl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m3u8</extension>
        <mime-type>application/vnd.apple.mpegurl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m4a</extension>
        <mime-type>audio/mp4</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m4b</extension>
        <mime-type>audio/mp4</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m4r</extension>
        <mime-type>audio/mp4</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m4u</extension>
        <mime-type>video/vnd.mpegurl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>m4v</extension>
        <mime-type>video/mp4</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ma</extension>
        <mime-type>application/mathematica</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mac</extension>
        <mime-type>image/x-macpaint</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mads</extension>
        <mime-type>application/mads+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mag</extension>
        <mime-type>application/vnd.ecowin.chart</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>maker</extension>
        <mime-type>application/vnd.framemaker</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>man</extension>
        <mime-type>text/troff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mar</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mathml</extension>
        <mime-type>application/mathml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mb</extension>
        <mime-type>application/mathematica</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mbk</extension>
        <mime-type>application/vnd.mobius.mbk</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mbox</extension>
        <mime-type>application/mbox</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mc1</extension>
        <mime-type>application/vnd.medcalcdata</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mcd</extension>
        <mime-type>application/vnd.mcd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mcurl</extension>
        <mime-type>text/vnd.curl.mcurl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mdb</extension>
        <mime-type>application/x-msaccess</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mdi</extension>
        <mime-type>image/vnd.ms-modi</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>me</extension>
        <mime-type>text/troff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mesh</extension>
        <mime-type>model/mesh</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>meta4</extension>
        <mime-type>application/metalink4+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>metalink</extension>
        <mime-type>application/metalink+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mets</extension>
        <mime-type>application/mets+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mfm</extension>
        <mime-type>application/vnd.mfmp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mft</extension>
        <mime-type>application/rpki-manifest</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mgp</extension>
        <mime-type>application/vnd.osgeo.mapguide.package</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mgz</extension>
        <mime-type>application/vnd.proteus.magazine</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mid</extension>
        <mime-type>audio/midi</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>midi</extension>
        <mime-type>audio/midi</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mie</extension>
        <mime-type>application/x-mie</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mif</extension>
        <mime-type>application/x-mif</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mime</extension>
        <mime-type>message/rfc822</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mj2</extension>
        <mime-type>video/mj2</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mjp2</extension>
        <mime-type>video/mj2</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mjs</extension>
        <mime-type>text/javascript</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mk3d</extension>
        <mime-type>video/x-matroska</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mka</extension>
        <mime-type>audio/x-matroska</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mks</extension>
        <mime-type>video/x-matroska</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mkv</extension>
        <mime-type>video/x-matroska</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mlp</extension>
        <mime-type>application/vnd.dolby.mlp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mmd</extension>
        <mime-type>application/vnd.chipnuts.karaoke-mmd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mmf</extension>
        <mime-type>application/vnd.smaf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mmr</extension>
        <mime-type>image/vnd.fujixerox.edmics-mmr</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mng</extension>
        <mime-type>video/x-mng</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mny</extension>
        <mime-type>application/x-msmoney</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mobi</extension>
        <mime-type>application/x-mobipocket-ebook</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mods</extension>
        <mime-type>application/mods+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mov</extension>
        <mime-type>video/quicktime</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>movie</extension>
        <mime-type>video/x-sgi-movie</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mp1</extension>
        <mime-type>audio/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mp2</extension>
        <mime-type>audio/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mp21</extension>
        <mime-type>application/mp21</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mp2a</extension>
        <mime-type>audio/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mp3</extension>
        <mime-type>audio/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mp4</extension>
        <mime-type>video/mp4</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mp4a</extension>
        <mime-type>audio/mp4</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mp4s</extension>
        <mime-type>application/mp4</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mp4v</extension>
        <mime-type>video/mp4</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpa</extension>
        <mime-type>audio/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpc</extension>
        <mime-type>application/vnd.mophun.certificate</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpe</extension>
        <mime-type>video/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpeg</extension>
        <mime-type>video/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpega</extension>
        <mime-type>audio/x-mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpg</extension>
        <mime-type>video/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpg4</extension>
        <mime-type>video/mp4</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpga</extension>
        <mime-type>audio/mpeg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpkg</extension>
        <mime-type>application/vnd.apple.installer+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpm</extension>
        <mime-type>application/vnd.blueice.multipass</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpn</extension>
        <mime-type>application/vnd.mophun.application</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpp</extension>
        <mime-type>application/vnd.ms-project</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpt</extension>
        <mime-type>application/vnd.ms-project</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpv2</extension>
        <mime-type>video/mpeg2</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mpy</extension>
        <mime-type>application/vnd.ibm.minipay</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mqy</extension>
        <mime-type>application/vnd.mobius.mqy</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mrc</extension>
        <mime-type>application/marc</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mrcx</extension>
        <mime-type>application/marcxml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ms</extension>
        <mime-type>text/troff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mscml</extension>
        <mime-type>application/mediaservercontrol+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mseed</extension>
        <mime-type>application/vnd.fdsn.mseed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mseq</extension>
        <mime-type>application/vnd.mseq</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>msf</extension>
        <mime-type>application/vnd.epson.msf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>msh</extension>
        <mime-type>model/mesh</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>msi</extension>
        <mime-type>application/x-msdownload</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>msl</extension>
        <mime-type>application/vnd.mobius.msl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>msty</extension>
        <mime-type>application/vnd.muvee.style</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mts</extension>
        <mime-type>model/vnd.mts</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mus</extension>
        <mime-type>application/vnd.musician</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>musicxml</extension>
        <mime-type>application/vnd.recordare.musicxml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mvb</extension>
        <mime-type>application/x-msmediaview</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mwf</extension>
        <mime-type>application/vnd.mfer</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mxf</extension>
        <mime-type>application/mxf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mxl</extension>
        <mime-type>application/vnd.recordare.musicxml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mxml</extension>
        <mime-type>application/xv+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mxs</extension>
        <mime-type>application/vnd.triscape.mxs</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>mxu</extension>
        <mime-type>video/vnd.mpegurl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>n-gage</extension>
        <mime-type>application/vnd.nokia.n-gage.symbian.install</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>n3</extension>
        <mime-type>text/n3</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nb</extension>
        <mime-type>application/mathematica</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nbp</extension>
        <mime-type>application/vnd.wolfram.player</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nc</extension>
        <mime-type>application/x-netcdf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ncx</extension>
        <mime-type>application/x-dtbncx+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nfo</extension>
        <mime-type>text/x-nfo</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ngdat</extension>
        <mime-type>application/vnd.nokia.n-gage.data</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nitf</extension>
        <mime-type>application/vnd.nitf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nlu</extension>
        <mime-type>application/vnd.neurolanguage.nlu</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nml</extension>
        <mime-type>application/vnd.enliven</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nnd</extension>
        <mime-type>application/vnd.noblenet-directory</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nns</extension>
        <mime-type>application/vnd.noblenet-sealer</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nnw</extension>
        <mime-type>application/vnd.noblenet-web</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>npx</extension>
        <mime-type>image/vnd.net-fpx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nsc</extension>
        <mime-type>application/x-conference</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nsf</extension>
        <mime-type>application/vnd.lotus-notes</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ntf</extension>
        <mime-type>application/vnd.nitf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>nzb</extension>
        <mime-type>application/x-nzb</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>oa2</extension>
        <mime-type>application/vnd.fujitsu.oasys2</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>oa3</extension>
        <mime-type>application/vnd.fujitsu.oasys3</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>oas</extension>
        <mime-type>application/vnd.fujitsu.oasys</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>obd</extension>
        <mime-type>application/x-msbinder</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>obj</extension>
        <mime-type>application/x-tgif</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>oda</extension>
        <mime-type>application/oda</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Database -->
        <extension>odb</extension>
        <mime-type>application/vnd.oasis.opendocument.database</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Chart -->
        <extension>odc</extension>
        <mime-type>application/vnd.oasis.opendocument.chart</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Formula -->
        <extension>odf</extension>
        <mime-type>application/vnd.oasis.opendocument.formula</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>odft</extension>
        <mime-type>application/vnd.oasis.opendocument.formula-template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Drawing -->
        <extension>odg</extension>
        <mime-type>application/vnd.oasis.opendocument.graphics</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Image -->
        <extension>odi</extension>
        <mime-type>application/vnd.oasis.opendocument.image</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Master Document -->
        <extension>odm</extension>
        <mime-type>application/vnd.oasis.opendocument.text-master</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Presentation -->
        <extension>odp</extension>
        <mime-type>application/vnd.oasis.opendocument.presentation</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Spreadsheet -->
        <extension>ods</extension>
        <mime-type>application/vnd.oasis.opendocument.spreadsheet</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Text -->
        <extension>odt</extension>
        <mime-type>application/vnd.oasis.opendocument.text</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>oga</extension>
        <mime-type>audio/ogg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ogg</extension>
        <mime-type>audio/ogg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ogv</extension>
        <mime-type>video/ogg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- xiph mime types -->
        <extension>ogx</extension>
        <mime-type>application/ogg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>omdoc</extension>
        <mime-type>application/omdoc+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>onepkg</extension>
        <mime-type>application/onenote</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>onetmp</extension>
        <mime-type>application/onenote</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>onetoc</extension>
        <mime-type>application/onenote</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>onetoc2</extension>
        <mime-type>application/onenote</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>opf</extension>
        <mime-type>application/oebps-package+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>opml</extension>
        <mime-type>text/x-opml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>oprc</extension>
        <mime-type>application/vnd.palm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>opus</extension>
        <mime-type>audio/ogg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>org</extension>
        <mime-type>application/vnd.lotus-organizer</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>osf</extension>
        <mime-type>application/vnd.yamaha.openscoreformat</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>osfpvg</extension>
        <mime-type>application/vnd.yamaha.openscoreformat.osfpvg+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>otc</extension>
        <mime-type>application/vnd.oasis.opendocument.chart-template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>otf</extension>
        <mime-type>font/otf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Drawing Template -->
        <extension>otg</extension>
        <mime-type>application/vnd.oasis.opendocument.graphics-template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- HTML Document Template -->
        <extension>oth</extension>
        <mime-type>application/vnd.oasis.opendocument.text-web</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>oti</extension>
        <mime-type>application/vnd.oasis.opendocument.image-template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Presentation Template -->
        <extension>otp</extension>
        <mime-type>application/vnd.oasis.opendocument.presentation-template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Spreadsheet Template -->
        <extension>ots</extension>
        <mime-type>application/vnd.oasis.opendocument.spreadsheet-template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- OpenDocument Text Template -->
        <extension>ott</extension>
        <mime-type>application/vnd.oasis.opendocument.text-template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>oxps</extension>
        <mime-type>application/oxps</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>oxt</extension>
        <mime-type>application/vnd.openofficeorg.extension</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>p</extension>
        <mime-type>text/x-pascal</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>p10</extension>
        <mime-type>application/pkcs10</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>p12</extension>
        <mime-type>application/x-pkcs12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>p7b</extension>
        <mime-type>application/x-pkcs7-certificates</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>p7c</extension>
        <mime-type>application/pkcs7-mime</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>p7m</extension>
        <mime-type>application/pkcs7-mime</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>p7r</extension>
        <mime-type>application/x-pkcs7-certreqresp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>p7s</extension>
        <mime-type>application/pkcs7-signature</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>p8</extension>
        <mime-type>application/pkcs8</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pas</extension>
        <mime-type>text/x-pascal</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>paw</extension>
        <mime-type>application/vnd.pawaafile</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pbd</extension>
        <mime-type>application/vnd.powerbuilder6</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pbm</extension>
        <mime-type>image/x-portable-bitmap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pcap</extension>
        <mime-type>application/vnd.tcpdump.pcap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pcf</extension>
        <mime-type>application/x-font-pcf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pcl</extension>
        <mime-type>application/vnd.hp-pcl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pclxl</extension>
        <mime-type>application/vnd.hp-pclxl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pct</extension>
        <mime-type>image/pict</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pcurl</extension>
        <mime-type>application/vnd.curl.pcurl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pcx</extension>
        <mime-type>image/x-pcx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pdb</extension>
        <mime-type>application/vnd.palm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pdf</extension>
        <mime-type>application/pdf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pfa</extension>
        <mime-type>application/x-font-type1</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pfb</extension>
        <mime-type>application/x-font-type1</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pfm</extension>
        <mime-type>application/x-font-type1</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pfr</extension>
        <mime-type>application/font-tdpfr</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pfx</extension>
        <mime-type>application/x-pkcs12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pgm</extension>
        <mime-type>image/x-portable-graymap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pgn</extension>
        <mime-type>application/x-chess-pgn</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pgp</extension>
        <mime-type>application/pgp-encrypted</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pic</extension>
        <mime-type>image/pict</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pict</extension>
        <mime-type>image/pict</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pkg</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pki</extension>
        <mime-type>application/pkixcmp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pkipath</extension>
        <mime-type>application/pkix-pkipath</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>plb</extension>
        <mime-type>application/vnd.3gpp.pic-bw-large</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>plc</extension>
        <mime-type>application/vnd.mobius.plc</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>plf</extension>
        <mime-type>application/vnd.pocketlearn</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pls</extension>
        <mime-type>audio/x-scpls</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pml</extension>
        <mime-type>application/vnd.ctc-posml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>png</extension>
        <mime-type>image/png</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pnm</extension>
        <mime-type>image/x-portable-anymap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pnt</extension>
        <mime-type>image/x-macpaint</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>portpkg</extension>
        <mime-type>application/vnd.macports.portpkg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pot</extension>
        <mime-type>application/vnd.ms-powerpoint</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>potm</extension>
        <mime-type>application/vnd.ms-powerpoint.template.macroenabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>potx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.presentationml.template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ppam</extension>
        <mime-type>application/vnd.ms-powerpoint.addin.macroenabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ppd</extension>
        <mime-type>application/vnd.cups-ppd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ppm</extension>
        <mime-type>image/x-portable-pixmap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pps</extension>
        <mime-type>application/vnd.ms-powerpoint</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ppsm</extension>
        <mime-type>application/vnd.ms-powerpoint.slideshow.macroenabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ppsx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.presentationml.slideshow</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ppt</extension>
        <mime-type>application/vnd.ms-powerpoint</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pptm</extension>
        <mime-type>application/vnd.ms-powerpoint.presentation.macroenabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pptx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.presentationml.presentation</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pqa</extension>
        <mime-type>application/vnd.palm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>prc</extension>
        <mime-type>application/x-mobipocket-ebook</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pre</extension>
        <mime-type>application/vnd.lotus-freelance</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>prf</extension>
        <mime-type>application/pics-rules</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ps</extension>
        <mime-type>application/postscript</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>psb</extension>
        <mime-type>application/vnd.3gpp.pic-bw-small</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>psd</extension>
        <mime-type>image/vnd.adobe.photoshop</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>psf</extension>
        <mime-type>application/x-font-linux-psf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pskcxml</extension>
        <mime-type>application/pskc+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ptid</extension>
        <mime-type>application/vnd.pvi.ptid1</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pub</extension>
        <mime-type>application/x-mspublisher</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pvb</extension>
        <mime-type>application/vnd.3gpp.pic-bw-var</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pwn</extension>
        <mime-type>application/vnd.3m.post-it-notes</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pya</extension>
        <mime-type>audio/vnd.ms-playready.media.pya</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>pyv</extension>
        <mime-type>video/vnd.ms-playready.media.pyv</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qam</extension>
        <mime-type>application/vnd.epson.quickanime</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qbo</extension>
        <mime-type>application/vnd.intu.qbo</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qfx</extension>
        <mime-type>application/vnd.intu.qfx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qps</extension>
        <mime-type>application/vnd.publishare-delta-tree</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qt</extension>
        <mime-type>video/quicktime</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qti</extension>
        <mime-type>image/x-quicktime</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qtif</extension>
        <mime-type>image/x-quicktime</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qwd</extension>
        <mime-type>application/vnd.quark.quarkxpress</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qwt</extension>
        <mime-type>application/vnd.quark.quarkxpress</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qxb</extension>
        <mime-type>application/vnd.quark.quarkxpress</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qxd</extension>
        <mime-type>application/vnd.quark.quarkxpress</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qxl</extension>
        <mime-type>application/vnd.quark.quarkxpress</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>qxt</extension>
        <mime-type>application/vnd.quark.quarkxpress</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ra</extension>
        <mime-type>audio/x-pn-realaudio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ram</extension>
        <mime-type>audio/x-pn-realaudio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rar</extension>
        <mime-type>application/x-rar-compressed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ras</extension>
        <mime-type>image/x-cmu-raster</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rcprofile</extension>
        <mime-type>application/vnd.ipunplugged.rcprofile</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rdf</extension>
        <mime-type>application/rdf+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rdz</extension>
        <mime-type>application/vnd.data-vision.rdz</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rep</extension>
        <mime-type>application/vnd.businessobjects</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>res</extension>
        <mime-type>application/x-dtbresource+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rgb</extension>
        <mime-type>image/x-rgb</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rif</extension>
        <mime-type>application/reginfo+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rip</extension>
        <mime-type>audio/vnd.rip</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ris</extension>
        <mime-type>application/x-research-info-systems</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rl</extension>
        <mime-type>application/resource-lists+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rlc</extension>
        <mime-type>image/vnd.fujixerox.edmics-rlc</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rld</extension>
        <mime-type>application/resource-lists-diff+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rm</extension>
        <mime-type>application/vnd.rn-realmedia</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rmi</extension>
        <mime-type>audio/midi</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rmp</extension>
        <mime-type>audio/x-pn-realaudio-plugin</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rms</extension>
        <mime-type>application/vnd.jcp.javame.midlet-rms</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rmvb</extension>
        <mime-type>application/vnd.rn-realmedia-vbr</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rnc</extension>
        <mime-type>application/relax-ng-compact-syntax</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>roa</extension>
        <mime-type>application/rpki-roa</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>roff</extension>
        <mime-type>text/troff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rp9</extension>
        <mime-type>application/vnd.cloanto.rp9</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rpss</extension>
        <mime-type>application/vnd.nokia.radio-presets</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rpst</extension>
        <mime-type>application/vnd.nokia.radio-preset</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rq</extension>
        <mime-type>application/sparql-query</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rs</extension>
        <mime-type>application/rls-services+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rsd</extension>
        <mime-type>application/rsd+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rss</extension>
        <mime-type>application/rss+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rtf</extension>
        <mime-type>application/rtf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>rtx</extension>
        <mime-type>text/richtext</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>s</extension>
        <mime-type>text/x-asm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>s3m</extension>
        <mime-type>audio/s3m</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>saf</extension>
        <mime-type>application/vnd.yamaha.smaf-audio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sbml</extension>
        <mime-type>application/sbml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sc</extension>
        <mime-type>application/vnd.ibm.secure-container</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>scd</extension>
        <mime-type>application/x-msschedule</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>scm</extension>
        <mime-type>application/vnd.lotus-screencam</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>scq</extension>
        <mime-type>application/scvp-cv-request</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>scs</extension>
        <mime-type>application/scvp-cv-response</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>scurl</extension>
        <mime-type>text/vnd.curl.scurl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sda</extension>
        <mime-type>application/vnd.stardivision.draw</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sdc</extension>
        <mime-type>application/vnd.stardivision.calc</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sdd</extension>
        <mime-type>application/vnd.stardivision.impress</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sdkd</extension>
        <mime-type>application/vnd.solent.sdkm+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sdkm</extension>
        <mime-type>application/vnd.solent.sdkm+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sdp</extension>
        <mime-type>application/sdp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sdw</extension>
        <mime-type>application/vnd.stardivision.writer</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>see</extension>
        <mime-type>application/vnd.seemail</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>seed</extension>
        <mime-type>application/vnd.fdsn.seed</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sema</extension>
        <mime-type>application/vnd.sema</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>semd</extension>
        <mime-type>application/vnd.semd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>semf</extension>
        <mime-type>application/vnd.semf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ser</extension>
        <mime-type>application/java-serialized-object</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>setpay</extension>
        <mime-type>application/set-payment-initiation</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>setreg</extension>
        <mime-type>application/set-registration-initiation</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sfd-hdstx</extension>
        <mime-type>application/vnd.hydrostatix.sof-data</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sfs</extension>
        <mime-type>application/vnd.spotfire.sfs</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sfv</extension>
        <mime-type>text/x-sfv</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sgi</extension>
        <mime-type>image/sgi</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sgl</extension>
        <mime-type>application/vnd.stardivision.writer-global</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sgm</extension>
        <mime-type>text/sgml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sgml</extension>
        <mime-type>text/sgml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sh</extension>
        <mime-type>application/x-sh</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>shar</extension>
        <mime-type>application/x-shar</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>shf</extension>
        <mime-type>application/shf+xml</mime-type>
    </mime-mapping>
    <!--
    <mime-mapping>
        <extension>shtml</extension>
        <mime-type>text/x-server-parsed-html</mime-type>
    </mime-mapping>
    -->
    <mime-mapping>
        <extension>sid</extension>
        <mime-type>image/x-mrsid-image</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sig</extension>
        <mime-type>application/pgp-signature</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sil</extension>
        <mime-type>audio/silk</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>silo</extension>
        <mime-type>model/mesh</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sis</extension>
        <mime-type>application/vnd.symbian.install</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sisx</extension>
        <mime-type>application/vnd.symbian.install</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sit</extension>
        <mime-type>application/x-stuffit</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sitx</extension>
        <mime-type>application/x-stuffitx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>skd</extension>
        <mime-type>application/vnd.koan</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>skm</extension>
        <mime-type>application/vnd.koan</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>skp</extension>
        <mime-type>application/vnd.koan</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>skt</extension>
        <mime-type>application/vnd.koan</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sldm</extension>
        <mime-type>application/vnd.ms-powerpoint.slide.macroenabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sldx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.presentationml.slide</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>slt</extension>
        <mime-type>application/vnd.epson.salt</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sm</extension>
        <mime-type>application/vnd.stepmania.stepchart</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>smf</extension>
        <mime-type>application/vnd.stardivision.math</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>smi</extension>
        <mime-type>application/smil+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>smil</extension>
        <mime-type>application/smil+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>smv</extension>
        <mime-type>video/x-smv</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>smzip</extension>
        <mime-type>application/vnd.stepmania.package</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>snd</extension>
        <mime-type>audio/basic</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>snf</extension>
        <mime-type>application/x-font-snf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>so</extension>
        <mime-type>application/octet-stream</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>spc</extension>
        <mime-type>application/x-pkcs7-certificates</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>spf</extension>
        <mime-type>application/vnd.yamaha.smaf-phrase</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>spl</extension>
        <mime-type>application/x-futuresplash</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>spot</extension>
        <mime-type>text/vnd.in3d.spot</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>spp</extension>
        <mime-type>application/scvp-vp-response</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>spq</extension>
        <mime-type>application/scvp-vp-request</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>spx</extension>
        <mime-type>audio/ogg</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sql</extension>
        <mime-type>application/x-sql</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>src</extension>
        <mime-type>application/x-wais-source</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>srt</extension>
        <mime-type>application/x-subrip</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sru</extension>
        <mime-type>application/sru+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>srx</extension>
        <mime-type>application/sparql-results+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ssdl</extension>
        <mime-type>application/ssdl+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sse</extension>
        <mime-type>application/vnd.kodak-descriptor</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ssf</extension>
        <mime-type>application/vnd.epson.ssf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ssml</extension>
        <mime-type>application/ssml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>st</extension>
        <mime-type>application/vnd.sailingtracker.track</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>stc</extension>
        <mime-type>application/vnd.sun.xml.calc.template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>std</extension>
        <mime-type>application/vnd.sun.xml.draw.template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>stf</extension>
        <mime-type>application/vnd.wt.stf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sti</extension>
        <mime-type>application/vnd.sun.xml.impress.template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>stk</extension>
        <mime-type>application/hyperstudio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>stl</extension>
        <mime-type>application/vnd.ms-pki.stl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>str</extension>
        <mime-type>application/vnd.pg.format</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>stw</extension>
        <mime-type>application/vnd.sun.xml.writer.template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sub</extension>
        <mime-type>text/vnd.dvb.subtitle</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sus</extension>
        <mime-type>application/vnd.sus-calendar</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>susp</extension>
        <mime-type>application/vnd.sus-calendar</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sv4cpio</extension>
        <mime-type>application/x-sv4cpio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sv4crc</extension>
        <mime-type>application/x-sv4crc</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>svc</extension>
        <mime-type>application/vnd.dvb.service</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>svd</extension>
        <mime-type>application/vnd.svd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>svg</extension>
        <mime-type>image/svg+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>svgz</extension>
        <mime-type>image/svg+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>swa</extension>
        <mime-type>application/x-director</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>swf</extension>
        <mime-type>application/x-shockwave-flash</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>swi</extension>
        <mime-type>application/vnd.aristanetworks.swi</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sxc</extension>
        <mime-type>application/vnd.sun.xml.calc</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sxd</extension>
        <mime-type>application/vnd.sun.xml.draw</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sxg</extension>
        <mime-type>application/vnd.sun.xml.writer.global</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sxi</extension>
        <mime-type>application/vnd.sun.xml.impress</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sxm</extension>
        <mime-type>application/vnd.sun.xml.math</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>sxw</extension>
        <mime-type>application/vnd.sun.xml.writer</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>t</extension>
        <mime-type>text/troff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>t3</extension>
        <mime-type>application/x-t3vm-image</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>taglet</extension>
        <mime-type>application/vnd.mynfc</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tao</extension>
        <mime-type>application/vnd.tao.intent-module-archive</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tar</extension>
        <mime-type>application/x-tar</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tcap</extension>
        <mime-type>application/vnd.3gpp2.tcap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tcl</extension>
        <mime-type>application/x-tcl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>teacher</extension>
        <mime-type>application/vnd.smart.teacher</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tei</extension>
        <mime-type>application/tei+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>teicorpus</extension>
        <mime-type>application/tei+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tex</extension>
        <mime-type>application/x-tex</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>texi</extension>
        <mime-type>application/x-texinfo</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>texinfo</extension>
        <mime-type>application/x-texinfo</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>text</extension>
        <mime-type>text/plain</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tfi</extension>
        <mime-type>application/thraud+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tfm</extension>
        <mime-type>application/x-tex-tfm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tga</extension>
        <mime-type>image/x-tga</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>thmx</extension>
        <mime-type>application/vnd.ms-officetheme</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tif</extension>
        <mime-type>image/tiff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tiff</extension>
        <mime-type>image/tiff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tmo</extension>
        <mime-type>application/vnd.tmobile-livetv</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>torrent</extension>
        <mime-type>application/x-bittorrent</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tpl</extension>
        <mime-type>application/vnd.groove-tool-template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tpt</extension>
        <mime-type>application/vnd.trid.tpt</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tr</extension>
        <mime-type>text/troff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tra</extension>
        <mime-type>application/vnd.trueapp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>trm</extension>
        <mime-type>application/x-msterminal</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tsd</extension>
        <mime-type>application/timestamped-data</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>tsv</extension>
        <mime-type>text/tab-separated-values</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ttc</extension>
        <mime-type>font/collection</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ttf</extension>
        <mime-type>font/ttf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ttl</extension>
        <mime-type>text/turtle</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>twd</extension>
        <mime-type>application/vnd.simtech-mindmapper</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>twds</extension>
        <mime-type>application/vnd.simtech-mindmapper</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>txd</extension>
        <mime-type>application/vnd.genomatix.tuxedo</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>txf</extension>
        <mime-type>application/vnd.mobius.txf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>txt</extension>
        <mime-type>text/plain</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>u32</extension>
        <mime-type>application/x-authorware-bin</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>udeb</extension>
        <mime-type>application/x-debian-package</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ufd</extension>
        <mime-type>application/vnd.ufdl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ufdl</extension>
        <mime-type>application/vnd.ufdl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ulw</extension>
        <mime-type>audio/basic</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ulx</extension>
        <mime-type>application/x-glulx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>umj</extension>
        <mime-type>application/vnd.umajin</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>unityweb</extension>
        <mime-type>application/vnd.unity</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uoml</extension>
        <mime-type>application/vnd.uoml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uri</extension>
        <mime-type>text/uri-list</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uris</extension>
        <mime-type>text/uri-list</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>urls</extension>
        <mime-type>text/uri-list</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>ustar</extension>
        <mime-type>application/x-ustar</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>utz</extension>
        <mime-type>application/vnd.uiq.theme</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uu</extension>
        <mime-type>text/x-uuencode</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uva</extension>
        <mime-type>audio/vnd.dece.audio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvd</extension>
        <mime-type>application/vnd.dece.data</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvf</extension>
        <mime-type>application/vnd.dece.data</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvg</extension>
        <mime-type>image/vnd.dece.graphic</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvh</extension>
        <mime-type>video/vnd.dece.hd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvi</extension>
        <mime-type>image/vnd.dece.graphic</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvm</extension>
        <mime-type>video/vnd.dece.mobile</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvp</extension>
        <mime-type>video/vnd.dece.pd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvs</extension>
        <mime-type>video/vnd.dece.sd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvt</extension>
        <mime-type>application/vnd.dece.ttml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvu</extension>
        <mime-type>video/vnd.uvvu.mp4</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvv</extension>
        <mime-type>video/vnd.dece.video</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvva</extension>
        <mime-type>audio/vnd.dece.audio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvd</extension>
        <mime-type>application/vnd.dece.data</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvf</extension>
        <mime-type>application/vnd.dece.data</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvg</extension>
        <mime-type>image/vnd.dece.graphic</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvh</extension>
        <mime-type>video/vnd.dece.hd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvi</extension>
        <mime-type>image/vnd.dece.graphic</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvm</extension>
        <mime-type>video/vnd.dece.mobile</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvp</extension>
        <mime-type>video/vnd.dece.pd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvs</extension>
        <mime-type>video/vnd.dece.sd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvt</extension>
        <mime-type>application/vnd.dece.ttml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvu</extension>
        <mime-type>video/vnd.uvvu.mp4</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvv</extension>
        <mime-type>video/vnd.dece.video</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvx</extension>
        <mime-type>application/vnd.dece.unspecified</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvvz</extension>
        <mime-type>application/vnd.dece.zip</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvx</extension>
        <mime-type>application/vnd.dece.unspecified</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>uvz</extension>
        <mime-type>application/vnd.dece.zip</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vcard</extension>
        <mime-type>text/vcard</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vcd</extension>
        <mime-type>application/x-cdlink</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vcf</extension>
        <mime-type>text/x-vcard</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vcg</extension>
        <mime-type>application/vnd.groove-vcard</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vcs</extension>
        <mime-type>text/x-vcalendar</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vcx</extension>
        <mime-type>application/vnd.vcx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vis</extension>
        <mime-type>application/vnd.visionary</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>viv</extension>
        <mime-type>video/vnd.vivo</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vob</extension>
        <mime-type>video/x-ms-vob</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vor</extension>
        <mime-type>application/vnd.stardivision.writer</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vox</extension>
        <mime-type>application/x-authorware-bin</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vrml</extension>
        <mime-type>model/vrml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vsd</extension>
        <mime-type>application/vnd.visio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vsf</extension>
        <mime-type>application/vnd.vsf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vss</extension>
        <mime-type>application/vnd.visio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vst</extension>
        <mime-type>application/vnd.visio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vsw</extension>
        <mime-type>application/vnd.visio</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vtu</extension>
        <mime-type>model/vnd.vtu</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>vxml</extension>
        <mime-type>application/voicexml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>w3d</extension>
        <mime-type>application/x-director</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wad</extension>
        <mime-type>application/x-doom</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wasm</extension>
        <mime-type>application/wasm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wav</extension>
        <mime-type>audio/x-wav</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wax</extension>
        <mime-type>audio/x-ms-wax</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- Wireless Bitmap -->
        <extension>wbmp</extension>
        <mime-type>image/vnd.wap.wbmp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wbs</extension>
        <mime-type>application/vnd.criticaltools.wbs+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wbxml</extension>
        <mime-type>application/vnd.wap.wbxml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wcm</extension>
        <mime-type>application/vnd.ms-works</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wdb</extension>
        <mime-type>application/vnd.ms-works</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wdp</extension>
        <mime-type>image/vnd.ms-photo</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>weba</extension>
        <mime-type>audio/webm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>webm</extension>
        <mime-type>video/webm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>webp</extension>
        <mime-type>image/webp</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wg</extension>
        <mime-type>application/vnd.pmi.widget</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wgt</extension>
        <mime-type>application/widget</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wks</extension>
        <mime-type>application/vnd.ms-works</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wm</extension>
        <mime-type>video/x-ms-wm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wma</extension>
        <mime-type>audio/x-ms-wma</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wmd</extension>
        <mime-type>application/x-ms-wmd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wmf</extension>
        <mime-type>application/x-msmetafile</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- WML Source -->
        <extension>wml</extension>
        <mime-type>text/vnd.wap.wml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- Compiled WML -->
        <extension>wmlc</extension>
        <mime-type>application/vnd.wap.wmlc</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- WML Script Source -->
        <extension>wmls</extension>
        <mime-type>text/vnd.wap.wmlscript</mime-type>
    </mime-mapping>
    <mime-mapping>
        <!-- Compiled WML Script -->
        <extension>wmlsc</extension>
        <mime-type>application/vnd.wap.wmlscriptc</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wmv</extension>
        <mime-type>video/x-ms-wmv</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wmx</extension>
        <mime-type>video/x-ms-wmx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wmz</extension>
        <mime-type>application/x-msmetafile</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>woff</extension>
        <mime-type>font/woff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>woff2</extension>
        <mime-type>font/woff2</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wpd</extension>
        <mime-type>application/vnd.wordperfect</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wpl</extension>
        <mime-type>application/vnd.ms-wpl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wps</extension>
        <mime-type>application/vnd.ms-works</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wqd</extension>
        <mime-type>application/vnd.wqd</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wri</extension>
        <mime-type>application/x-mswrite</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wrl</extension>
        <mime-type>model/vrml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wsdl</extension>
        <mime-type>application/wsdl+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wspolicy</extension>
        <mime-type>application/wspolicy+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wtb</extension>
        <mime-type>application/vnd.webturbo</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>wvx</extension>
        <mime-type>video/x-ms-wvx</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>x32</extension>
        <mime-type>application/x-authorware-bin</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>x3d</extension>
        <mime-type>model/x3d+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>x3db</extension>
        <mime-type>model/x3d+binary</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>x3dbz</extension>
        <mime-type>model/x3d+binary</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>x3dv</extension>
        <mime-type>model/x3d+vrml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>x3dvz</extension>
        <mime-type>model/x3d+vrml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>x3dz</extension>
        <mime-type>model/x3d+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xaml</extension>
        <mime-type>application/xaml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xap</extension>
        <mime-type>application/x-silverlight-app</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xar</extension>
        <mime-type>application/vnd.xara</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xbap</extension>
        <mime-type>application/x-ms-xbap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xbd</extension>
        <mime-type>application/vnd.fujixerox.docuworks.binder</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xbm</extension>
        <mime-type>image/x-xbitmap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xdf</extension>
        <mime-type>application/xcap-diff+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xdm</extension>
        <mime-type>application/vnd.syncml.dm+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xdp</extension>
        <mime-type>application/vnd.adobe.xdp+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xdssc</extension>
        <mime-type>application/dssc+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xdw</extension>
        <mime-type>application/vnd.fujixerox.docuworks</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xenc</extension>
        <mime-type>application/xenc+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xer</extension>
        <mime-type>application/patch-ops-error+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xfdf</extension>
        <mime-type>application/vnd.adobe.xfdf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xfdl</extension>
        <mime-type>application/vnd.xfdl</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xht</extension>
        <mime-type>application/xhtml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xhtml</extension>
        <mime-type>application/xhtml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xhvml</extension>
        <mime-type>application/xv+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xif</extension>
        <mime-type>image/vnd.xiff</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xla</extension>
        <mime-type>application/vnd.ms-excel</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlam</extension>
        <mime-type>application/vnd.ms-excel.addin.macroenabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlc</extension>
        <mime-type>application/vnd.ms-excel</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlf</extension>
        <mime-type>application/x-xliff+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlm</extension>
        <mime-type>application/vnd.ms-excel</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xls</extension>
        <mime-type>application/vnd.ms-excel</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlsb</extension>
        <mime-type>application/vnd.ms-excel.sheet.binary.macroenabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlsm</extension>
        <mime-type>application/vnd.ms-excel.sheet.macroenabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlsx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlt</extension>
        <mime-type>application/vnd.ms-excel</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xltm</extension>
        <mime-type>application/vnd.ms-excel.template.macroenabled.12</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xltx</extension>
        <mime-type>application/vnd.openxmlformats-officedocument.spreadsheetml.template</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xlw</extension>
        <mime-type>application/vnd.ms-excel</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xm</extension>
        <mime-type>audio/xm</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xml</extension>
        <mime-type>application/xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xo</extension>
        <mime-type>application/vnd.olpc-sugar</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xop</extension>
        <mime-type>application/xop+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xpi</extension>
        <mime-type>application/x-xpinstall</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xpl</extension>
        <mime-type>application/xproc+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xpm</extension>
        <mime-type>image/x-xpixmap</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xpr</extension>
        <mime-type>application/vnd.is-xpr</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xps</extension>
        <mime-type>application/vnd.ms-xpsdocument</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xpw</extension>
        <mime-type>application/vnd.intercon.formnet</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xpx</extension>
        <mime-type>application/vnd.intercon.formnet</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xsl</extension>
        <mime-type>application/xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xslt</extension>
        <mime-type>application/xslt+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xsm</extension>
        <mime-type>application/vnd.syncml+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xspf</extension>
        <mime-type>application/xspf+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xul</extension>
        <mime-type>application/vnd.mozilla.xul+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xvm</extension>
        <mime-type>application/xv+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xvml</extension>
        <mime-type>application/xv+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xwd</extension>
        <mime-type>image/x-xwindowdump</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xyz</extension>
        <mime-type>chemical/x-xyz</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>xz</extension>
        <mime-type>application/x-xz</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>yang</extension>
        <mime-type>application/yang</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>yin</extension>
        <mime-type>application/yin+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>z</extension>
        <mime-type>application/x-compress</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>z1</extension>
        <mime-type>application/x-zmachine</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>z2</extension>
        <mime-type>application/x-zmachine</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>z3</extension>
        <mime-type>application/x-zmachine</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>z4</extension>
        <mime-type>application/x-zmachine</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>z5</extension>
        <mime-type>application/x-zmachine</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>z6</extension>
        <mime-type>application/x-zmachine</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>z7</extension>
        <mime-type>application/x-zmachine</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>z8</extension>
        <mime-type>application/x-zmachine</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>zaz</extension>
        <mime-type>application/vnd.zzazz.deck+xml</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>zip</extension>
        <mime-type>application/zip</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>zir</extension>
        <mime-type>application/vnd.zul</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>zirz</extension>
        <mime-type>application/vnd.zul</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>zmm</extension>
        <mime-type>application/vnd.handheld-entertainment+xml</mime-type>
    </mime-mapping>

  <!-- ==================== Default Welcome File List ===================== -->
  <!-- When a request URI refers to a directory, the default servlet looks  -->
  <!-- for a "welcome file" within that directory and, if present, to the   -->
  <!-- corresponding resource URI for display.                              -->
  <!-- If no welcome files are present, the default servlet either serves a -->
  <!-- directory listing (see default servlet configuration on how to       -->
  <!-- customize) or returns a 404 status, depending on the value of the    -->
  <!-- listings setting.                                                    -->
  <!--                                                                      -->
  <!-- If you define welcome files in your own application's web.xml        -->
  <!-- deployment descriptor, that list *replaces* the list configured      -->
  <!-- here, so be sure to include any of the default values that you wish  -->
  <!-- to use within your application.                                      -->

    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
        <welcome-file>index.htm</welcome-file>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

</web-app>
