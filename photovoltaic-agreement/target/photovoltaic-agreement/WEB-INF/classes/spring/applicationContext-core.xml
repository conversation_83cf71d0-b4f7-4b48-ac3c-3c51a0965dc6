<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans.xsd 
		http://www.springframework.org/schema/tx 
		http://www.springframework.org/schema/tx/spring-tx.xsd 
		http://www.springframework.org/schema/aop 
		http://www.springframework.org/schema/aop/spring-aop.xsd">


	<!-- 对象转为json串 -->
	<bean id="objectMapper" class="com.fasterxml.jackson.databind.ObjectMapper"/>

	<!--加载配置文件-->
	<bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>classpath*:jdbc.properties</value>
				<value>classpath*:config.properties</value>
				<value>classpath*:mqtt.properties</value>
			</list>
		</property>
	</bean>

	
	<!-- 绑定数据源  org.apache.commons.dbcp.BasicDataSource-->
	<bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
		<property name="driverClassName" value="${jdbc.driver}" />
		<property name="url" value="${jdbc.url}" />
		<property name="username" value="${jdbc.username}" />
		<property name="password" value="${jdbc.password}" />
		<!-- 初始化连接大小 -->
		<property name="initialSize" value="${jdbc.initialSize}"/>
		<!-- 连接池最大数量 -->
		<property name="maxActive" value="${jdbc.maxActive}"/>
		<!-- 连接池最小空闲 -->
		<property name="minIdle" value="${jdbc.minIdle}"/>
		<!-- 获取连接最大等待时间 -->
		<property name="maxWait" value="${jdbc.maxWait}"/>
	</bean>

	<!-- spring和MyBatis完美整合，不需要mybatis的配置映射文件 -->
	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
		<property name="dataSource" ref="dataSource" />
		<property name="configLocation" value="classpath:mybatis/mybatis-config.xml" />
		<!-- 自动扫描mapping.xml文件 -->
		<property name="mapperLocations" value="classpath:mybatis/mapping/*Mapper.xml"/>
		<property name="typeAliasesPackage" value="com.ymx.service.photovoltaic"/>
	</bean>

	<!-- DAO接口所在包名，Spring会自动查找其下的类 -->
	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<property name="basePackage" value="com.ymx.service.*.*.mapper,com.ymx.service.**.dao,com.ymx.common.**.dao"/>
		<property name="annotationClass" value="org.springframework.stereotype.Repository" />
		<property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
	</bean>

	<!-- (事务管理)transaction manager, use JtaTransactionManager for global tx -->
	<bean id="transactionManager"  class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dataSource" />
	</bean>
	
	<tx:advice id="txAdvice" transaction-manager="transactionManager">
		<tx:attributes>
			<tx:method name="get*" read-only="true" />
			<tx:method name="find*" read-only="true" />
<!--			<tx:method name="select*" read-only="true" />-->
			<tx:method name="query*" read-only="true" />
			<tx:method name="read*" read-only="true" />
			<tx:method name="load*" read-only="true" />
			<tx:method name="contain*" read-only="true" />
			<tx:method name="exist*" read-only="true" />
			<tx:method name="count*" read-only="true" />
			<tx:method name="*" propagation="REQUIRED" rollback-for="Exception" />
		</tx:attributes>
	</tx:advice>
	
	<!-- AOP 切面对象  -->
	<aop:config>
		<aop:pointcut expression="execution(* com.ymx.service.*..service.*Service.*(..))" id="servicePointcut" />
		<aop:advisor advice-ref="txAdvice" pointcut-ref="servicePointcut" />
	</aop:config>

	<!-- spring jdbcTemplate -->
	<bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
		<property name="dataSource" ref="dataSource" />
	</bean>
	
	<!-- @Transactional annotation -->
	<tx:annotation-driven transaction-manager="transactionManager" />
	
	
</beans>