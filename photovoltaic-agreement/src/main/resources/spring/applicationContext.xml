<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:task="http://www.springframework.org/schema/task" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-3.0.xsd
           http://www.springframework.org/schema/task
           http://www.springframework.org/schema/task/spring-task-3.0.xsd">

	<context:component-scan base-package="com.ymx.common,com.ymx.service,com.ymx.socket,com.ymx.agreement">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
		<context:exclude-filter type="annotation" expression="org.springframework.web.bind.annotation.RestController"/>
	</context:component-scan>

	<context:annotation-config />


	<!-- 定时任务 配置定时线程池大小为5-->
	<task:scheduler id="scheduler" pool-size="5" />
	<!-- 支持异步方法执行 -->
	<task:annotation-driven executor="annotationExecutor" scheduler="scheduler" />
	<!-- 支持 @Async 注解 -->
	<task:executor id="annotationExecutor" pool-size="10"/>

	<!-- redis配置 -->
	<bean id="redisProperties" class="com.ymx.service.third.jedis.JedisPoolUtil">
		<property name="locations">
			<list>
				<value>classpath:jedis.properties</value>
			</list>
		</property>
	</bean>

	<!--加载配置文件 -->
	<bean id="configProperties" class="com.ymx.common.base.config.ConfigConstants">
		<property name="locations">
			<list>
				<value>classpath:config.properties</value>
			</list>
		</property>
	</bean>

	<bean id="minaConfig" class="com.ymx.socket.api.protocol.communication.mina.MinaConfig">
		<property name="SESSION_IS_KEEP" value="${mina.isKeep}" />
		<property name="READ_BUFFERSIZE" value="${mina.readBufferSize}" />
		<property name="IDELTIMEOUT" value="${mina.ideltimeout}" />
		<property name="HEARTBEATRATE" value="${mina.heartbeatrate}" />
		<property name="PORT" value="${mina.port}" />
	</bean>

</beans>