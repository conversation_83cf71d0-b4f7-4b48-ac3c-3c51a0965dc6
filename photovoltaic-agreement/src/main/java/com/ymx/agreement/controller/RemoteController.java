package com.ymx.agreement.controller;

import com.ymx.common.common.result.CallResult;
import com.ymx.service.photovoltaic.station.model.ComponentModel;
import com.ymx.service.photovoltaic.station.service.ComponentService;
import com.ymx.service.photovoltaic.warning.entity.WarningSettingModel;
import com.ymx.service.photovoltaic.warning.service.WarningSettingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;


@RestController
@RequestMapping("zteCtr")
public class RemoteController {

	private static final Logger logger = LoggerFactory.getLogger(RemoteController.class);

	/** 组件服务 */
    @Resource
    private ComponentService componentService;
	@Resource
	private WarningSettingService warningSettingService;

    /**
     * 远端遥控        单个打开或关闭
     * @param model  主要参数  组件id  ,
     *              flag 标识 1关闭电池板 2 打开电池板
     * @return
     */
    @RequestMapping("remoteControllerAck.api")
    public CallResult remoteControllerAck(ComponentModel model , Integer flag ){
        return componentService.remoteControllerAck( model, flag );
    }


    /**
     * 远端遥控
     * 批量打开/关闭所有组件
     * @param model  主要参数  status(查询使用)  , powerStationId ,
     *               flag 标识 1关闭电池板 2 打开电池板
     *   标识 1开机 2 关闭
     * @return
     */
    @RequestMapping("remoteControllerBatchAck.api")
    public CallResult remoteControllerBatchAck( ComponentModel model , Integer flag ){
        return componentService.remoteControllerBatchAck( model , flag );
    }

	// 关闭整个电站的所有优化器
	@RequestMapping("remotePowerStationAck.api")
	public CallResult remotePowerStationAck(ComponentModel model , Integer flag ){
		return componentService.remotePowerStationAck(model , flag );
	}

   // 关闭一个组串的组件
	@RequestMapping("remoteControllerGroupAck.api")
	public CallResult remoteControllerGroupAck(ComponentModel model , Integer flag ){
		return componentService.remoteControllerGroupAck(model , flag );
	}

	/**
	 * 警报设置
	 * @param warningSetting
	 * @return
	 */
	@RequestMapping("updateWarningSetting.api")
	public CallResult updateWarningSetting(WarningSettingModel warningSetting){
		try {
			return warningSettingService.doWarningSetting( warningSetting );
		}catch (Exception e){
			logger.error("updateWarningSetting api",e);
		}
		return CallResult.newInstance();
	}



}
