package com.ymx.agreement.listener;

import com.ymx.agreement.quartz.collect.OptimizerGapCollectThread;
import com.ymx.common.base.config.ConfigConstants;
import com.ymx.common.utils.DateTimeUtil;
import com.ymx.common.utils.FileUtil;
import com.ymx.service.cache.Configure;
import com.ymx.service.photovoltaic.member.mapper.PushUserMapper;
import com.ymx.service.photovoltaic.protocol.inter.DataService;
import com.ymx.service.photovoltaic.station.mapper.ComponentGroupMapper;
import com.ymx.service.photovoltaic.station.mapper.PowerStationMapper;
import com.ymx.service.photovoltaic.warning.mapper.WarningSettingMapper;
import org.apache.commons.io.monitor.FileAlterationMonitor;
import org.apache.commons.io.monitor.FileAlterationObserver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Component
public class InitDataListener implements ApplicationListener<ContextRefreshedEvent> {

    private static final Logger logger = LoggerFactory.getLogger(InitDataListener.class);

    private static final String COLLECT_GAP_FILE_DIR = ConfigConstants.getConfig("COLLECT_GAP_FILE_DIR");

    @Autowired
    private PowerStationMapper powerStationMapper;

    @Autowired
    private ComponentGroupMapper componentGroupMapper;

    @Autowired
    private CollectGapFileListener collectGapFileListener;

    @Autowired
    private WarningSettingMapper warningSettingMapper;

    @Autowired
    private PushUserMapper pushUserMapper;

    @Autowired
    private DataService dataService;


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {

        String appContextName = event.getApplicationContext().getDisplayName();
        logger.info("appContextName:{}", appContextName);

        if (appContextName.contains("Root")) {

            dataService.initData();

            // 从文件获取有多少种时间间隔
            List<Integer> nowCollectGapList = FileUtil.getNowCollectGapList(new File(COLLECT_GAP_FILE_DIR + "collect_gap.properties").toPath());

            // 设置采集线程池的最大线程数为20个 核心线程数为5个，新建时已设置
            Configure.getExecutor().setMaximumPoolSize(20);

            for (Integer collectGap : nowCollectGapList) {
                // 1秒后启动新的定时任务，后续按照collectGap定时执行
                ScheduledFuture<?> future = Configure.getExecutor().scheduleAtFixedRate(new OptimizerGapCollectThread(collectGap, powerStationMapper, componentGroupMapper),
                        DateTimeUtil.getSecondsToNextMinute(), collectGap, TimeUnit.SECONDS);
                Configure.getCollectTaskMap().put(collectGap, future);
            }

            FileAlterationMonitor monitor = new FileAlterationMonitor(5000); //检查间隔5秒
            FileAlterationObserver observer = new FileAlterationObserver(COLLECT_GAP_FILE_DIR); //监听目录
            observer.addListener(collectGapFileListener);
            monitor.addObserver(observer);
            try {
                monitor.start();
            } catch (Exception e) {
                logger.info("fileMonitor exception:", e);
                throw new RuntimeException(e);
            }
        }

    }


}

