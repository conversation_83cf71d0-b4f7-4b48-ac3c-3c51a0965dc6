package com.ymx.agreement.listener;

import com.ymx.common.base.config.ConfigConstants;
import com.ymx.common.mina.ConfigurationRead;
import com.ymx.service.constant.ConstantsExt;
import com.ymx.socket.api.BaseApp;
import com.ymx.socket.api.protocol.communication.mina.server.MinaAcceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

/**
 * @DESC
 * @DATE 2018/9/2
 * @NAME ApplicationContextListener
 * @MOUDELNAME 模块
 */
public class ApplicationContextListener implements ServletContextListener {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationContextListener.class);

    /***
     * @param servletContextEvent
     */
    @Override
    public void contextInitialized(ServletContextEvent servletContextEvent) {

        BaseApp.SPRING_CONTEXT = WebApplicationContextUtils.getWebApplicationContext(servletContextEvent.getServletContext());

        ConfigurationRead read = new ConfigurationRead("/", ConstantsExt.PROPERTIES_NAME);

        BaseApp.SYS_NUM = ConfigConstants.getConfig("sys.num");

        MinaAcceptor minaServer = (MinaAcceptor) BaseApp.SPRING_CONTEXT.getBean("minaAcceptor");
        try  {
            minaServer.init();
        }
        catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
    }

    /**
     * @param servletContextEvent
     */
    @Override
    public void contextDestroyed(ServletContextEvent servletContextEvent) {

    }


}
