package com.ymx.agreement.listener;

import com.ymx.agreement.quartz.collect.OptimizerGapCollectThread;
import com.ymx.common.utils.DateTimeUtil;
import com.ymx.common.utils.FileUtil;
import com.ymx.service.cache.Configure;
import com.ymx.service.photovoltaic.station.mapper.ComponentGroupMapper;
import com.ymx.service.photovoltaic.station.mapper.PowerStationMapper;
import org.apache.commons.io.monitor.FileAlterationListenerAdaptor;
import org.apache.commons.io.monitor.FileAlterationObserver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Component
public class CollectGapFileListener extends FileAlterationListenerAdaptor {

    private static final Logger logger = LoggerFactory.getLogger(CollectGapFileListener.class);

    @Autowired
    private PowerStationMapper powerStationMapper;

    @Autowired
    private ComponentGroupMapper componentGroupMapper;

    @Override
    public void onStart(FileAlterationObserver observer) {
        super.onStart(observer);
        logger.debug("onStart");
    }

    @Override
    public void onStop(FileAlterationObserver observer) {
        super.onStop(observer);
        logger.debug("onStop");
    }

    @Override
    public void onFileChange(File file) {
        logger.info("onFileChange");

        List<Integer> nowCollectGapList = FileUtil.getNowCollectGapList(file.toPath());

        Map<Integer, ScheduledFuture<?>> collectTaskMap = Configure.getCollectTaskMap();

        List<Integer> oldCollectGapList = new ArrayList<>(collectTaskMap.keySet());

        List<Integer> newOnlyList = new ArrayList<>(nowCollectGapList);
        newOnlyList.removeAll(oldCollectGapList);

        List<Integer> oldOnlyList = new ArrayList<>(oldCollectGapList);
        oldOnlyList.removeAll(nowCollectGapList);

        // 停止不需要的采集线程
        for (Integer oldKey : oldOnlyList) {
            ScheduledFuture<?> future = Configure.getCollectTaskMap().get(oldKey);
            if (future != null) {
                future.cancel(true);
                Configure.getCollectTaskMap().remove(oldKey);
            }
        }

        // 超过最大线程数，就不新增线程了，直接返回
        if (Configure.getCollectTaskMap().size() + newOnlyList.size() > 20) {
            logger.info("collect task over max");
            return;
        }

        // 新增新的采集线程
        for (Integer newKey : newOnlyList) {
            ScheduledFuture<?> future = Configure.getExecutor().scheduleAtFixedRate(new OptimizerGapCollectThread(newKey, powerStationMapper, componentGroupMapper),
                    DateTimeUtil.getSecondsToNextMinute(), newKey, TimeUnit.SECONDS);
            Configure.getCollectTaskMap().put(newKey, future);
        }

    }

    @Override
    public void onFileDelete(File file) {
        super.onFileDelete(file);
        logger.info("onFileDelete");
    }
}