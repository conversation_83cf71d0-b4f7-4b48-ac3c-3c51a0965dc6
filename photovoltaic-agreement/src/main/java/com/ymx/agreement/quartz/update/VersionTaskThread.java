package com.ymx.agreement.quartz.update;

import com.ymx.service.cache.Configure;
import com.ymx.service.photovoltaic.station.mapper.UpdateTaskMapper;
import com.ymx.service.photovoltaic.station.mapper.VersionQueryMapper;
import com.ymx.service.photovoltaic.station.model.UpdateComponentModel;
import com.ymx.service.photovoltaic.station.model.UpdateTaskModel;
import com.ymx.service.photovoltaic.station.model.VersionQueryTaskModel;
import com.ymx.common.utils.ByteUtil;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

public class VersionTaskThread implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(VersionTaskThread.class);

    private UpdateTaskModel updateTaskModel;

    private UpdateTaskMapper updateTaskMapper;

    private VersionQueryMapper queryTaskMapper;

    private VersionQueryTaskModel queryTaskModel;

    private List<UpdateComponentModel> updateComList;

    private List<String> relayIdList;

    private List<String> optimizerIdList;

    private String taskId;

    private String imeiStr;

    private final Long imeiLock;

    private String type;

    private final String taskType;

    private IoSession ioSession;

    private VersionTaskSend versionTaskSend;

    private short setTaskStatus = 1;

    public VersionTaskThread(UpdateTaskModel updateTaskModel, UpdateTaskMapper updateTaskMapper,
                             List<UpdateComponentModel> updateComModelList, Long imeiLock) {
        this.updateTaskModel = updateTaskModel;
        this.updateTaskMapper = updateTaskMapper;
        this.updateComList = updateComModelList;
        this.taskType = "update";
        this.imeiLock = imeiLock;
    }

    public VersionTaskThread(VersionQueryTaskModel queryTaskModel, VersionQueryMapper queryTaskMapper,
                             List<String> optimizerIdList, List<String> relayIdList, Long imeiLock) {
        this.queryTaskModel = queryTaskModel;
        this.optimizerIdList = optimizerIdList;
        this.relayIdList = relayIdList;
        this.queryTaskMapper = queryTaskMapper;
        this.taskType = "query";
        this.imeiLock = imeiLock;
    }

    @Override
    public void run() {

        taskId = updateTaskModel == null ? queryTaskModel.getTaskId() : updateTaskModel.getTaskId();
        logger.info("VersionTaskThread begin: " + taskId);

        synchronized (imeiLock) {

            initParams();

            logger.info("{} start,taskType {} type {} ",taskId, taskType,type);

            String sessionId = Configure.getSessionMap().get(imeiStr);
            // 获取缓存session
            ioSession = Configure.getMap().get(sessionId);

            if (null == ioSession) {
                logger.info("{} {} session is null",taskId,imeiStr);
                return;
            }

            if (taskType.equals("update")) {
                handleUpdateTask();
            } else {
                handleQueryTask();
            }
        }

    }

    private void handleUpdateTask() {
        String fileName = updateTaskModel.getCreateUserId() + File.separator + updateTaskModel.getFileName();
        versionTaskSend = new VersionTaskSend(taskId, imeiStr, type, fileName, ioSession);
        short updateStatus = updateTaskModel.getUpdateStatus();
        short taskStatus = updateTaskModel.getTaskStatus();
        // 要么是从头开始任务 要么是失败重试的状态
        logger.info("{} taskStatus:{} updateStatus:{} ",taskId,taskStatus, updateStatus);

        if (taskStatus == 0 || taskStatus == 3) {
            doUpdateTask(updateStatus);
        } else if (taskStatus == 4) {
            handleUpdateVersionQuery();
        }
        logger.info("setTaskStatus:{}",setTaskStatus);

        updateTaskStatusByTaskId(setTaskStatus);
    }

    /// 查询任务 status -1 查询中 0 未开始 1 查询结束 2 采集器没有响应，任务停止
    private void handleQueryTask() {
        updateStatusForQueryTask(-1);
        versionTaskSend = new VersionTaskSend(taskId, imeiStr, type, null, ioSession);
        // 查询版本有返回 把任务置为完成 否则设置采集器没有响应的状态
        if (handleNormalVersionQuery()) {
            updateStatusForQueryTask(1);
        }
    }

    private void initParams() {

        if (taskType.equals("update")) {
            imeiStr = updateTaskModel.getImei();
            type = updateTaskModel.getType();
        } else {
            imeiStr = queryTaskModel.getImei();
            switch (queryTaskModel.getType()) {
                case 1:
                    type = "collector";
                    break;
                case 2:
                    type = "repeater";
                    break;
                case 3:
                case 4:
                    type = "optimizer";
                    break;
            }
        }
    }
    /// 对于升级任务来讲  taskStatus  -1 版本发送中 -2 中继加载中 -3 升级信息发送中  -4 版本查询中  0 待执行 1 已完成 2 已结束
    ///  3 准备重试 4 准备查询版本 5 查询版本，采集器没有返回  10 待审批 11 审批拒绝
    ///  updateStatus  0 未开始  1 版本发送成功 2 版本发送失败  21 版本发送失败,采集器没有响应
    /// 4 中继加载失败 41 中继加载失败，采集器没有响应 5 升级成功  55 升级部分成功
    /// 6 升级失败  61 升级失败，采集器没有响应 7 发送升级信息成功  77  发送升级信息部分成功
    /// 8 发送升级信息失败  81 发送升级信息失败 采集器没有响应
    private void doUpdateTask(short updateStatus) {
        switch (updateStatus) {
            // 创建或者发送版本失败重试状态
            case 0:
            case 2:
            case 21:
                startFromVersionSend();
                break;
            case 1:
            // 中继加载部分成功重试或者全部失败重试状态
            case 33:
            case 4:
            case 41:
                startFromVersionLoad();
                break;
            case 3:
            // 升级部分成功重试或全部失败重试状态
            case 55:
            case 77:
            case 8:
            case 81:
                updateTaskRetry();
                break;
            default:
                logger.info("updateStatus:{} error,default do nothing", updateStatus);
                break;
        }
    }

    private void updateTaskRetry() {
        if (type.equals("optimizer")) {
            startFromVersionLoad();
        }else if(type.equals("repeater"))
        {
            handleUpdateInfoSend();
        }
    }


    private void startFromVersionLoad() {
        if (type.equals("optimizer") && !handleVersionLoad()) {
            return;
        }
        if (type.equals("optimizer")) {
            handleUpdateInfoSend();
        }
    }


    private void startFromVersionSend() {
        if (!handleVersionSend()) {
            return;
        }
        if (type.equals("repeater")) {
            handleUpdateInfoSend();
        }
        if (type.equals("optimizer")) {
            if (handleVersionLoad()) {
                handleUpdateInfoSend();
            }
        }


    }

    private boolean handleNormalVersionQuery() {
        boolean returnResult = true;
        if (queryTaskModel.getQueryType() == 1) {
            String key=taskId+"_"+queryTaskModel.getQueryId();
            Configure.getQueryVersionMap().remove(key);
            String result = versionTaskSend.sendVersionQueryCmd(queryTaskModel.getQueryId(), type);
            if (result == null) {
                updateStatusForQueryTask(2);
                returnResult = false;
            }
            Configure.getQueryVersionMap().remove(key);
        } else {
            // 批量查询优化器或中继器版本
            if (optimizerIdList != null) {
                returnResult = handleBatchVersionQuery(optimizerIdList, "optimizer");
            }

            if (relayIdList != null && returnResult) {
                returnResult = handleBatchVersionQuery(relayIdList, "repeater");
            }
        }
        return returnResult;
    }

    // 批量查询版本信息
    private boolean handleBatchVersionQuery(List<String> componentIdList, String type) {
        for (String componentId : componentIdList) {
            String key=taskId+"_"+componentId;
            Configure.getQueryVersionMap().remove(key);
            String result = versionTaskSend.sendVersionQueryCmd(componentId, type);
            if (result == null) {
                updateStatusForQueryTask(2);
                return false;
            }
            Configure.getQueryVersionMap().remove(key);
        }
        return true;
    }

    private void handleUpdateVersionQuery() {
        updateTaskStatusByTaskId(-4);

        int oldListSize=updateComList.size();
        long updateSuccessNum = updateComList.stream().filter(u->u.getUpdateStatus()==5).count();
        long sendSuccessNum = updateComList.stream().filter(u->u.getUpdateStatus()==7).count();

        logger.info("oldQueryListSize {} updateSuccessNum {} sendSuccessNum {}",
                oldListSize,updateSuccessNum,sendSuccessNum);

        // 手动查询 查询除升级成功外的所有情况
        updateComList = updateComList.stream().
                filter(u -> u.getUpdateStatus() != 5).collect(Collectors.toList());

        logger.info("update query list size:{}", updateComList.size());

        if (updateComList.size() == 0) {
            logger.info("no component need version query");
            return;
        }

        for (UpdateComponentModel updateComModel : updateComList) {
            String componentId=updateComModel.getComponentId();
            String key=taskId+"_"+componentId;
            Configure.getQueryVersionMap().remove(key);
            String result = versionTaskSend.sendVersionQueryCmd(componentId, type);
            if (result == null) {
                setTaskStatus = 5;
                updateQueryStatus(componentId);
                Configure.getQueryVersionMap().remove(key);
                return;
            }

            // 根据返回版本号判断是否全部升级成功
            if(!result.equals("0.0.0"))
            {
                if (result.equals(updateComModel.getUpdateVersion())) {
                    updateStatusAndQueryVersion(5, componentId, result, 1);
                    updateSuccessNum = updateSuccessNum + 1;
                } else {
                    // 更新查询版本和状态
                    updateStatusAndQueryVersion(-100, componentId, result, 1);
                }
            }else
            {
                updateStatusAndQueryVersion(-100, componentId, result, 1);
            }
            Configure.getQueryVersionMap().remove(key);
        }

        logger.info("oldQueryListSize {} updateSuccessNum {} sendSuccessNum {}",
                oldListSize,updateSuccessNum,sendSuccessNum);

        updateByUpdateSuccessNum(oldListSize,updateSuccessNum);

    }

    // 发送版本不用考虑组件升级表状态
    private boolean handleVersionSend() {

        updateTaskStatusByTaskId(-1);

        // 发送前先清理一下总长度map  防止重试时采集器回复的过慢造成map里面有值
        Configure.getTotalCountMap().remove(taskId);

        Integer result = versionTaskSend.sendVersionSendCmd();

        logger.info("key {} result {}", taskId, result);

        // 失败状态写表 成功状态不用写  直接进行下一步
        boolean returnFlag;
        short updateStatus;
        if (result == null) {
            updateStatus = 21;
            returnFlag = false;
        } else if (result != 1) {
            updateStatus = 2;
            returnFlag = false;
        } else {
            updateStatus = 1;
            updateComList.forEach(updateComModel -> updateComModel.setUpdateStatus((short)1));
            returnFlag = true;
        }
        updateStatusForUpdateTask(updateStatus);
        updateStatusForComUpdateTask(updateStatus, null, null);
        Configure.getTotalCountMap().remove(taskId);
        return returnFlag;
    }

    private boolean handleVersionLoad() {
        List<String> relayIdList = new ArrayList<>();
        updateComList.forEach(updateComModel ->
        {
            String relayId = updateComModel.getRelayId();
            short status = updateComModel.getUpdateStatus();
            logger.info("relayId {} status {} ",relayId,status);
            // 发送版本成功或者加载中继失败的状态才需要 进行加载中继的步骤
            // 即上一个步骤成功的状态或这个步骤需要重试的状态
            if (!relayIdList.contains(relayId) && (status == 1 || status == 4 || status == 41)) {
                relayIdList.add(relayId);
                logger.info("relayId {}",relayId);
            }
        });

        int needLoadNum=relayIdList.size();
        if(needLoadNum==0)
        {
            logger.info("no need to version load");
            return true;
        }

        updateTaskStatusByTaskId(-2);
        int totalRelayIdNum=updateTaskModel.getRelayId().split(",").length;

        logger.info("totalRelayIdNum {} needLoadNum {}",totalRelayIdNum,needLoadNum);

        int successNum = 0;
        for (String relayId : relayIdList) {
            String key = taskId + "_" + ByteUtil.getChipId(relayId);
            Configure.getLoadInfoMap().remove(key);

            Integer result = versionTaskSend.sendVersionLoadCmd(relayId);
            logger.info("key {} result {}", key, result);

            // 根据返回值不同  更新相应的表字段
            if (result == null) {
                updateStatusForUpdateTask(41);
                updateStatusForComUpdateTask(41, null, relayId);
                Configure.getLoadInfoMap().remove(key);
                return false;
            } else if (result == 1) {
                updateStatusForComUpdateTask(3, null, relayId);
                updateComList.forEach(updateComModel ->
                {
                    if (updateComModel.getRelayId().equals(relayId)) {
                        updateComModel.setUpdateStatus((short) 3);
                    }

                });
                successNum = successNum + 1;
            } else {
                updateStatusForComUpdateTask(4, null, relayId);
                updateComList.forEach(updateComModel ->
                {
                    if (updateComModel.getRelayId().equals(relayId)) {
                        updateComModel.setUpdateStatus((short) 4);
                    }
                });
            }
            Configure.getLoadInfoMap().remove(key);
        }

        logger.info("load version successNum {}",successNum);

        // 如果总中继数目等于需要加载的中继数目 说明之前没有中继加载成功过
        if (totalRelayIdNum == needLoadNum) {
            if (successNum == 0) {
                updateStatusForUpdateTask(4);
                return false;
            } else {
                int updateStatus = successNum == relayIdList.size() ? 3 : 33;
                updateStatusForUpdateTask(updateStatus);
                return true;
            }
        } else {
            return true;
        }
    }


    private void handleUpdateInfoSend() {
        updateTaskStatusByTaskId(-3);
        int oldListSize = updateComList.size();
        List<Short> statusList;
        // 可以发送升级信息的状态
        if (type.equals("optimizer")) {
            statusList = Arrays.asList((short) 3, (short) 8, (short) 81);
        } else {
            statusList = Arrays.asList((short) 1, (short) 8, (short) 81);
        }

        long updateSuccessNum = updateComList.stream().filter(u->u.getUpdateStatus()==5).count();
        long sendSuccessNum = updateComList.stream().filter(u->u.getUpdateStatus()==7).count();

        updateComList = updateComList.stream().
                filter(u -> statusList.contains(u.getUpdateStatus())).
                collect(Collectors.toList());

        logger.info("oldListSize {} newListSize {} updateSuccessNum {} sendSuccessNum {} ",
           oldListSize,updateComList.size(),updateSuccessNum,sendSuccessNum);

        // 没有可发送升级命令的组件，就直接返回，什么都不做
        if (updateComList.size() == 0) {
            logger.info("no need to send update info ");
            return;
        }

        for (UpdateComponentModel updateComModel : updateComList) {
            String componentId = updateComModel.getComponentId();
            String key = taskId + "_" + componentId;
            // 防止采集器回复慢， map有值 发送命令前先清理一下
            Configure.getUpdateInfoMap().remove(key);

            String result = versionTaskSend.sendUpdateInfoCmd(componentId);
            logger.info("key {} result {}", key, result);

            // 更新一下组件表的状态
            if (result == null) {
                int status = 81;
                updateStatusForUpdateTask(status);
                updateStatusForComUpdateTask(status, componentId, null);
                Configure.getUpdateInfoMap().remove(key);
                return;
            }

            // "0" 发送升级任务失败 “1” 成功但是没有返回正确的版本 其他 成功返回了正确的版本
            if (result.equals("0")) {
                updateStatusForComUpdateTask(8, componentId, null);
            } else if (result.equals("1")) {
                updateStatusForComUpdateTask(7, componentId, null);
                sendSuccessNum = sendSuccessNum + 1;
            } else {
                // 判断返回的版本是否是要升级的版本
                String updateVersion = updateComModel.getUpdateVersion();
                int status = updateVersion.equals(result) ? 5 : 7;
                updateStatusAndQueryVersion(status, componentId, result,0);
                updateSuccessNum = updateVersion.equals(result) ? updateSuccessNum + 1 : updateSuccessNum;
                sendSuccessNum = updateVersion.equals(result)?sendSuccessNum:sendSuccessNum + 1;
            }
            Configure.getUpdateInfoMap().remove(key);
        }

        logger.info("oldListSize {} updateSuccessNum {} sendSuccessNum {}",
                oldListSize,updateSuccessNum,sendSuccessNum);

        updateByTwoSuccessNum(oldListSize,updateSuccessNum,sendSuccessNum);
    }

    private void updateByUpdateSuccessNum(int oldListSize,long updateSuccessNum)
    {
        if (updateSuccessNum == oldListSize) {
            updateStatusForUpdateTask(5);
            setTaskStatus = 2;
            return;
        }

        if (updateSuccessNum > 0) {
            updateStatusForUpdateTask(55);
        }

    }

    private void updateByTwoSuccessNum(int oldListSize, long updateSuccessNum,long sendSuccessNum) {
        if (updateSuccessNum == oldListSize) {
            updateStatusForUpdateTask(5);
            setTaskStatus = 2;
            return;
        }

        if (updateSuccessNum > 0) {
            updateStatusForUpdateTask(55);
            return;
        }

        if (updateSuccessNum == 0 && sendSuccessNum == oldListSize) {
            updateStatusForUpdateTask(7);
            return;
        }

        if (updateSuccessNum == 0 && sendSuccessNum == 0) {
            updateStatusForUpdateTask(8);
            return;
        }

        if (updateSuccessNum == 0 && sendSuccessNum > 0) {
            updateStatusForUpdateTask(77);
        }

    }


    private void updateStatusForUpdateTask(int updateStatus) {
        Map<String, Object> map = new HashMap<>();
        map.put("taskId", taskId);
        map.put("updateStatus", updateStatus);
        updateTaskMapper.updateStatusForUpdateTask(map);
    }

    private void updateTaskStatusByTaskId(int taskStatus) {
        Map<String, Object> map = new HashMap<>();
        map.put("taskId", taskId);
        map.put("taskStatus", taskStatus);
        updateTaskMapper.updateTaskStatusByTaskId(map);
    }

    private void updateStatusForQueryTask(int status) {
        Map<String, Object> map = new HashMap<>();
        map.put("taskId", taskId);
        map.put("status", status);
        queryTaskMapper.updateTaskStatusByTaskId(map);
    }

    private void updateQueryStatus(String componentId) {
        Map<String, Object> map = new HashMap<>();
        map.put("taskId", taskId);
        // queryStatus -1 进行中  0 未开始 1 完成  2 采集器没有响应停止
        map.put("queryStatus", 2);
        map.put("componentId", componentId);
        updateTaskMapper.updateQueryStatus(map);
    }

    private void updateStatusForComUpdateTask(int status, String componentId, String relayId) {
        Map<String, Object> map = new HashMap<>();
        map.put("taskId", taskId);
        // 0 任务创建 1 版本发送成功 2 版本发送失败
        map.put("status", status);
        if (componentId != null) {
            map.put("componentId", componentId);
        }
        if (relayId != null) {
            map.put("relayId", relayId);
        }
        updateTaskMapper.updateComUpdateTaskStatus(map);
    }

    private void updateStatusAndQueryVersion(int status, String componentId, String queryVersion,int queryStatus) {
        Map<String, Object> map = new HashMap<>();
        map.put("taskId", taskId);
        map.put("componentId", componentId);
        map.put("queryVersion", queryVersion);
        map.put("queryStatus", queryStatus);
        if(status>0)
        {
            map.put("status", status);
        }
        updateTaskMapper.updateInfoForComponentUpdateTask(map);
    }

}
