package com.ymx.agreement.quartz.handle;

import com.ymx.common.base.config.ConfigConstants;
import com.ymx.common.utils.DateUtils;
import com.ymx.common.utils.IDUtil;
import com.ymx.service.cache.Configure;
import com.ymx.service.photovoltaic.station.model.ComponentCollect;
import com.ymx.service.photovoltaic.station.service.ComponentCollectService;
import com.ymx.service.third.jedis.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 采集数据入库
 */
@Component
public class CollectionDataQuartz {

	private static final Logger logger = LoggerFactory.getLogger(CollectionDataQuartz.class);

	/** 组件收集数据操作层 */
	@Resource
	private ComponentCollectService componentCollectService;

//	@Scheduled(cron = "0/5 * * * * ?")
    public void execute(){
		Integer timeStep;
		try {
			timeStep=Integer.parseInt(ConfigConstants.getConfig("TIME_STEP"));//获取间隔时间
		}catch (Exception e){
			timeStep=5;
			logger.error("timeStep parse error",e);
		}
		logger.info("启动定时任务，采集数据入库 ");
		Map<String, List<ComponentCollect>> cMap= Configure.getCollectMap();
		if(cMap!=null){
			try {
				for (String componentGroupId : cMap.keySet()) {
					List<ComponentCollect> componentCollectList=cMap.get(componentGroupId);
					if(componentCollectList!=null && componentCollectList.size()>0){
						ComponentCollect collect=componentCollectList.get(0);
						String batchNo=collect.getBatchNo();
						// 从redis中获取time值
						String time= RedisUtil.getRedisDateTime(batchNo,componentGroupId);
						List<ComponentCollect> componentCollectListAdd=new CopyOnWriteArrayList<>();
						for(ComponentCollect componentCollect:componentCollectList){
							if(batchNo!=null && batchNo.equals(componentCollect.getBatchNo())){//同一批次数据
								if(time!=null && time.length()>0){
									// 生成一个日期批次号
									// 把日期批次号放入对象中
									componentCollect.setContent(time +"_"+batchNo);
									componentCollect.setBatchNo(time);
									componentCollect.setCreateTime(new Date());
									try{
										//用日期批次号去设置创建时间
										componentCollect.setCreateTime(DateUtils.parse(time,DateUtils.DATE_All_KEY_STR));
									}catch (Exception e){
										e.printStackTrace();
									}
									componentCollect.setId(IDUtil.getUUIDStr());
									componentCollectListAdd.add(componentCollect);
									componentCollectList.remove(componentCollect);
									logger.info(time+" =    时间 采集数据入库 batchNo="+batchNo+" componentGroupId= "+componentGroupId+" ChipId= "+componentCollect.getChipId());
								}else {
									componentCollectList.remove(componentCollect);
									logger.info(time+"= 获取时间出错了************************************************  时间  batchNo="+batchNo+" key= "+componentGroupId);
								}
							}else{
								String time2= RedisUtil.getRedisDateTime(componentCollect.getBatchNo(),componentGroupId);
								if(time2!=null && time2.length()>0){
									logger.info(time2+" 非正常数据 ****************"+time+"***************************** 两个批次数据混到一起了。。。 相隔5分钟还有未处理的数据 "+batchNo);
									componentCollect.setContent(time2 +"_"+batchNo);
									componentCollect.setBatchNo(time2);
									componentCollect.setCreateTime(new Date());
									try{
										//用日期批次号去设置创建时间
										componentCollect.setCreateTime(DateUtils.parse(time2,DateUtils.DATE_All_KEY_STR));
									}catch (Exception e){
										e.printStackTrace();
									}
									componentCollect.setId(IDUtil.getUUIDStr());
									componentCollectListAdd.add(componentCollect);
									componentCollectList.remove(componentCollect);
								}else {
									componentCollectList.remove(componentCollect);
									logger.info(time2+"= 获取时间出错了***************"+time+"*********************************  时间  batchNo="+batchNo+" key= "+componentGroupId);
								}
							}
						}
						if(componentCollectListAdd!=null&&componentCollectListAdd.size()>0){
							componentCollectService.saveComponentCollectList(componentCollectListAdd,collect.getPowerStationId());
							logger.info(componentCollectListAdd.size()+"*******"+componentCollectListAdd.get(0).getChipId()+"********* 写入数据库================== "+batchNo);
						}
					}
				}
			}catch (Exception e){
				logger.error("采集数据入库出错  "+e.getMessage(),e);
			}
		}
    }

}
