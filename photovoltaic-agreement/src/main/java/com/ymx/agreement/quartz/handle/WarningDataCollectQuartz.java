package com.ymx.agreement.quartz.handle;

import com.ymx.service.photovoltaic.warning.service.KeepOutService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 遮挡数据计算
 */
@Component
public class WarningDataCollectQuartz {


	@Resource
	private KeepOutService keepOutService;

	private static final Logger logger = LoggerFactory.getLogger(WarningDataCollectQuartz.class);

	// 每10秒计算一次
	@Scheduled(cron = "0/10 * * * * ?")
    public void execute(){

		logger.info("keep out quartz start");
		keepOutService.newCoverWarning();
    }



}
