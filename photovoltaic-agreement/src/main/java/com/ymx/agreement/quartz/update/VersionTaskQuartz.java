package com.ymx.agreement.quartz.update;

import com.ymx.service.cache.Configure;
import com.ymx.service.photovoltaic.station.mapper.UpdateTaskMapper;
import com.ymx.service.photovoltaic.station.mapper.VersionQueryMapper;
import com.ymx.service.photovoltaic.station.model.UpdateComponentModel;
import com.ymx.service.photovoltaic.station.model.UpdateTaskModel;
import com.ymx.service.photovoltaic.station.model.VersionQueryTaskModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 版本升级和查询的定时任务
 */
@Component
public class VersionTaskQuartz {

    @Resource
    UpdateTaskMapper updateTaskMapper;

    @Resource
    VersionQueryMapper versionQueryMapper;

    private static final Logger logger = LoggerFactory.getLogger(VersionTaskQuartz.class);

    //采集线程池 初始化有5个线程 最大10个线程
    ExecutorService fixedThreadPool = new ThreadPoolExecutor(5, 10, 100, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10));

    List<Short> InProgressList= Arrays.asList((short)-1,(short)-2,(short)-3,(short)-4,(short)1,(short)5);

    @Scheduled(cron = "0/3 * * * * ?")
    public void execute() {

       logger.info("version quartz start");
       List<UpdateTaskModel> updateTaskModelList = updateTaskMapper.queryTaskForVersionUpdate();
       List<VersionQueryTaskModel> queryTaskModelList = versionQueryMapper.queryVersionQueryTask();
       int updateSize = updateTaskModelList.size();
       int querySize = queryTaskModelList.size();

       if (updateSize == 0 && querySize == 0) {
           return;
       }

       Map<String, List<UpdateTaskModel>> updateTaskMap = new HashMap<>();
       if (updateSize > 0) {
           updateTaskMap = updateTaskModelList.stream().
                   collect(Collectors.groupingBy(UpdateTaskModel::getImei));
       }

       Map<String, List<VersionQueryTaskModel>> queryTaskMap = new HashMap<>();
       if (querySize > 0) {
           // 按采集器过滤  每个采集器执行开始时间最早的那个任务
           queryTaskMap = queryTaskModelList.stream().
                   collect(Collectors.groupingBy(VersionQueryTaskModel::getImei));
       }
       int updateMapSize = updateTaskMap.size();
       int queryMapSize = queryTaskMap.size();

        logger.info("updateMapSize {} queryMapSize {}",updateMapSize,queryMapSize);

        // 没有升级任务 只有查询任务时
       if ( updateMapSize == 0 &&queryMapSize > 0) {
           chooseQueryTask(queryTaskMap);
           return;
       }

        // 没有查询任务 只有升级任务时
        if (updateMapSize > 0 && queryMapSize == 0) {
            chooseUpdateTask(updateTaskMap);
            return;
        }

        // 两个任务都有时
       if (updateMapSize > 0 ) {
           chooseTask(updateTaskMap, queryTaskMap);
       }
   }

   // 升级任务和采集任务同时存在 进行选择
    private void chooseTask(Map<String,List<UpdateTaskModel>> updateTaskMap,
                            Map<String,List<VersionQueryTaskModel>> queryTaskMap)
    {
        List<String> taskInProgressList=new ArrayList<>();

        // 循环升级任务 把正在执行的任务从两个任务中删除
        for (Map.Entry<String, List<UpdateTaskModel>> entry : updateTaskMap.entrySet()) {
            List<UpdateTaskModel> imeiUpdateTaskList = entry.getValue();
            boolean isUpdateTaskOn = imeiUpdateTaskList.stream().
                    anyMatch(u -> InProgressList.contains(u.getTaskStatus()));
            String imei=entry.getKey();
            List<VersionQueryTaskModel> imeiQueryTaskList =queryTaskMap.get(imei);

            logger.info("imei {}  queryList isNull {} updateTaskSize {} isUpdateTaskOn {}",
                    Arrays.asList(imei,imeiQueryTaskList==null,imeiUpdateTaskList.size(),isUpdateTaskOn).toArray());

            // 判断两个任务中是否存在这个采集器正在执行的任务 如果存在就不新建任务
            // 如果查询任务中也存在这个采集器的任务
            if(imeiQueryTaskList!=null) {
                boolean isQueryTaskOn = imeiQueryTaskList.stream().anyMatch(q -> q.getStatus() == -1 ||  q.getStatus() == 2);
                // 如果查询任务和采集任务都没有正在执行的任务 就选择一个时间早的任务执行
                if(!isUpdateTaskOn&&!isQueryTaskOn)
                {
                    // 升级任务和查询任务 哪个时间早就执行哪个
                    String updateTime = imeiUpdateTaskList.get(0).getUpdateBeginTime();
                    String queryTime = imeiQueryTaskList.get(0).getBeginTime();
                    logger.info("updateTime:{} queryTime:{}", updateTime, queryTime);
                    if (updateTime.compareTo(queryTime) < 0) {
                        startUpdateTask(imeiUpdateTaskList.get(0));
                    } else {
                        startQueryTask(imeiQueryTaskList.get(0));
                    }
                }
                taskInProgressList.add(imei);
            } else {
                if(!isUpdateTaskOn)
                {
                    startUpdateTask(imeiUpdateTaskList.get(0));
                }
            }
        }

        /// 删除了正在执行的采集器任务  在执行剩下的查询任务
        queryTaskMap = queryTaskMap.entrySet().stream()
                .filter(entry -> !taskInProgressList.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        chooseQueryTask(queryTaskMap);
    }

    // 选择升级任务
    private void chooseUpdateTask(Map<String,List<UpdateTaskModel>> updateTaskMap)
    {
        updateTaskMap.forEach((imei,imeiUpdateTaskList)->
        {
            boolean isTaskInProgress = imeiUpdateTaskList.stream().
                    anyMatch(u -> InProgressList.contains(u.getTaskStatus()) );
            /// 如果采集器没有任务在执行中 就取出开始时间最早的任务执行
            if (!isTaskInProgress) {
                startUpdateTask(imeiUpdateTaskList.get(0));
            }
        });
    }

    // 选择查询任务
    private void chooseQueryTask(Map<String,List<VersionQueryTaskModel>> queryTaskMap)
    {
            queryTaskMap.forEach((imei,imeiQueryTaskList)->
            {
                boolean isTaskInProgress = imeiQueryTaskList.stream().anyMatch(q -> q.getStatus() == -1 || q.getStatus() == 2);
                /// 如果采集器没有任务在执行中 就取出开始时间最早的任务执行
                if (!isTaskInProgress) {
                    startQueryTask(imeiQueryTaskList.get(0));
                }
            });
    }

    // 新建升级线程 放入线程池
    private void startUpdateTask(UpdateTaskModel updateTaskModel) {
        List<UpdateComponentModel> updateComModelList = updateTaskMapper.queryComForVersionUpdate(updateTaskModel.getTaskId());
        String imei = updateTaskModel.getImei();
        Long imeiLock = Configure.getImeiLockMap().get(imei);
        if (imeiLock == null) {
            imeiLock = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
            Configure.getImeiLockMap().put(imei, imeiLock);
        }

        logger.info("updateTask imei:{} imeiLock:{}", imei, imeiLock);

        VersionTaskThread versionTaskThread = new VersionTaskThread(updateTaskModel, updateTaskMapper, updateComModelList, imeiLock);
        fixedThreadPool.execute(versionTaskThread);

    }

    // 新建查询线程 放入线程池
    private void startQueryTask(VersionQueryTaskModel queryTaskModel) {

        String imei = queryTaskModel.getImei();
        Long imeiLock = Configure.getImeiLockMap().get(imei);
        if (imeiLock == null) {
            imeiLock = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
            Configure.getImeiLockMap().put(imei, imeiLock);
        }
        logger.info("queryTask imei:{} imeiLock:{}", imei, imeiLock);

        VersionTaskThread versionTaskThread;
        if (queryTaskModel.getQueryType() == 1) {
            versionTaskThread = new VersionTaskThread(queryTaskModel, versionQueryMapper,
                    null, null, imeiLock);
        } else {
            List<String> relayIdList;
            List<String> optimizerIdList;
            String queryId = queryTaskModel.getQueryId();
            switch (queryTaskModel.getType()) {
                //采集器
                case 1:
                    relayIdList = versionQueryMapper.queryRelayIdByImei(queryId);
                    optimizerIdList = versionQueryMapper.queryChipIdByImei(queryId);
                    versionTaskThread = new VersionTaskThread(queryTaskModel, versionQueryMapper,
                            optimizerIdList, relayIdList, imeiLock);
                    break;
                //中继器
                case 2:
                    optimizerIdList = versionQueryMapper.queryChipIdByRelayId(queryId);
                    versionTaskThread = new VersionTaskThread(queryTaskModel, versionQueryMapper,
                            optimizerIdList, null, imeiLock);
                    break;
                //组串
                case 4:
                    optimizerIdList = versionQueryMapper.queryChipIdByGroupId(queryId);
                    versionTaskThread = new VersionTaskThread(queryTaskModel, versionQueryMapper,
                            optimizerIdList, null, imeiLock);
                    break;
                default:
                    versionTaskThread = new VersionTaskThread(queryTaskModel, versionQueryMapper,
                            null, null, imeiLock);
                    break;
            }
        }
        fixedThreadPool.execute(versionTaskThread);
    }


}
