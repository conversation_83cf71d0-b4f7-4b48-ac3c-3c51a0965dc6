package com.ymx.agreement.quartz.collect;

import com.ymx.service.photovoltaic.station.mapper.ComponentGroupMapper;
import com.ymx.service.photovoltaic.station.mapper.PowerStationMapper;
import com.ymx.service.photovoltaic.station.model.ComponentGroupModel;
import com.ymx.service.photovoltaic.station.model.SendCollectModel;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 采集电气信息的定时任务
 */
@Component
public class OptimizerCollectQuartz {

	@Resource
	private PowerStationMapper powerStationMapper;

	@Resource
	private ComponentGroupMapper componentGroupMapper;

	private static final Logger logger = LoggerFactory.getLogger(OptimizerCollectQuartz.class);

	ExecutorService fixedThreadPool = new ThreadPoolExecutor(5, 10, 100, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10));

//	@Scheduled(cron = "0/10 * * * * ?")
	public void execute() {
		// 开启采集线程 简单数据库动态开关
		logger.info("collect quartz start");
		// 每次都要查询 是否可以存在redis中控制 控制是否执行采集任务
		int startOrClose = powerStationMapper.findIfOptimizerCollect();//开关
		if (startOrClose != 1) {
			return;
		}
		// 查询已调试的电站
		List<SendCollectModel> sendCollectModelList = powerStationMapper.queryPowerStationInfo();
		if(sendCollectModelList!=null)
		{
			sendCollectModelList=sendCollectModelList.stream().filter
					(s ->s.getStatus()==2).collect(Collectors.toList());
		}

		if (sendCollectModelList == null || sendCollectModelList.size() == 0) {
			return;
		}

		List<ComponentGroupModel> groupModelList = componentGroupMapper.queryComponentGroupInfo();

		String date = DateUtils.timestampToDate(new Date().getTime(), "yyyyMMddHHmmss");
		// rsv有7个字节
		String batchNo = CommonUtil.getRandomStringByLength16(7);
		// 循环已调试的电站列表
		for (SendCollectModel sendCollectModel : sendCollectModelList) {
			LocalTime nowTime = LocalTime.now();

			String sunUpTime = sendCollectModel.getSunuptime();
			String systemName = sendCollectModel.getSystemName();

			// 判断时间是否在采集时间之内 如果不在就不采集了
			if (sunUpTime != null && sunUpTime.contains(":")
					&& sunUpTime.length() == 5 && nowTime.isBefore(LocalTime.parse(sunUpTime))) {
				logger.info("{} too early out range", systemName);
				continue;
			}

			String sunDownTime = sendCollectModel.getSundowntime();

			if (sunDownTime != null && sunDownTime.contains(":")
					&& sunDownTime.length() == 5 && nowTime.isAfter(LocalTime.parse(sunDownTime))) {
				logger.info("{} too late out range", systemName);
				continue;
			}

			List<ComponentGroupModel> powerGroupModelList =
					groupModelList.stream().filter(comGroupModel -> comGroupModel.getPowerStationId() != null &&
							comGroupModel.getPowerStationId().equals(sendCollectModel.getId())).collect(Collectors.toList());

			// 查询电站的组串
			if (powerGroupModelList.size() == 0) {
				logger.info("{} no group", systemName);
				continue;
			}

			String imei = sendCollectModel.getImei();
			logger.info("{} imei is {} ready to send", systemName, imei);

			OptimizerCollectThread dataCollectThread = new OptimizerCollectThread
					(imei, batchNo, date, powerGroupModelList);
			fixedThreadPool.execute(dataCollectThread);
		}

	}

}
