package com.ymx.agreement.quartz.collect;

import com.ymx.common.utils.ByteUtil;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.DateUtils;
import com.ymx.service.cache.Configure;
import com.ymx.service.constant.ConstantsExt;
import com.ymx.service.photovoltaic.station.mapper.ComponentGroupMapper;
import com.ymx.service.photovoltaic.station.mapper.PowerStationMapper;
import com.ymx.service.photovoltaic.station.model.ComponentGroupModel;
import com.ymx.service.photovoltaic.station.model.SendCollectModel;
import com.ymx.service.third.jedis.RedisUtil;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

public class OptimizerGapCollectThread implements Runnable  {

	private final int collectGap;

	private final PowerStationMapper powerStationMapper;

	private final ComponentGroupMapper componentGroupMapper;

	private static final Logger logger = LoggerFactory.getLogger(OptimizerGapCollectThread.class);

	private final Random random = new Random();


	public OptimizerGapCollectThread(int collectGap,PowerStationMapper powerStationMapper,ComponentGroupMapper componentGroupMapper){
		this.collectGap=collectGap;
		this.powerStationMapper=powerStationMapper;
		this.componentGroupMapper=componentGroupMapper;
    }

    @Override
    public void run()
	{
		try
		{
			doTask();
		}
	    catch (Exception e)
		{
			logger.error("collect task exception",e);
		}
	}

	private void doTask()
	{
			logger.info("{} collect run", collectGap);

		List<SendCollectModel> sendCollectModelList = powerStationMapper.queryPowerStationInfo();
		if(sendCollectModelList!=null)
		{
			sendCollectModelList=sendCollectModelList.stream().filter
					(s ->s.getStatus()==2&&s.getCollectGap()==collectGap).collect(Collectors.toList());
		}


		if (sendCollectModelList == null || sendCollectModelList.size() == 0) {
				logger.info("{} sendCollectModelList is null", collectGap);
				return;
			}

			List<String> powerIdList = sendCollectModelList.stream().map(SendCollectModel::getId).collect(Collectors.toList());

			List<ComponentGroupModel> groupModelList = componentGroupMapper.queryGroupByPowerIdList(powerIdList);

			// rsv有7个字节
			String batchNo = CommonUtil.getRandomStringByLength16(7);

			// 循环已调试的电站列表
			for (SendCollectModel sendCollectModel : sendCollectModelList) {

				if (!checkTimeIsOk(sendCollectModel)) {
					continue;
				}

				String systemName = sendCollectModel.getSystemName();

				List<ComponentGroupModel> powerGroupModelList =
						groupModelList.stream().filter(comGroupModel -> comGroupModel.getPowerStationId() != null &&
								comGroupModel.getPowerStationId().equals(sendCollectModel.getId())).collect(Collectors.toList());

				if (!checkGroupSizeIsOk(powerGroupModelList.size(), systemName)) {
					continue;
				}

				String imei = sendCollectModel.getImei();

				String sessionId = Configure.getSessionMap().get(imei);
				// 获取缓存session
				IoSession ioSession = Configure.getMap().get(sessionId);

				if (!checkSessionIsOk(ioSession, imei)) {
					continue;
				}

//				String date;
//				if(!imei.trim().equals("0123456789516380"))
//				{
//				int randomNumber = random.nextInt(9) + 1;
//				// 转换为毫秒
//				long randomNumberLong=randomNumber*1000;
//
//				date = DateUtils.timestampToDate(new Date().getTime()+randomNumberLong, "yyyyMMddHHmmss");
//				// 1-10的随机数
//				logger.info("{} imei:{} randomNumber:{} ready to send", systemName, imei,randomNumber);
//				// 发送线程随机睡眠，保证采集命令发送时间不是规律的，满足专利要求
//				try {
//					Thread.sleep(randomNumberLong);
//				} catch (InterruptedException e) {
//					logger.error("send thread sleep error",e);
//				}
//				}
//				{
//					date = DateUtils.timestampToDate(new Date().getTime(), "yyyyMMddHHmmss");
//				}

				String date = DateUtils.timestampToDate(new Date().getTime(), "yyyyMMddHHmmss");


				logger.info("{} imei:{} date:{} set redis group info", systemName, imei,date);

				// 循环组串 多线程同时操作redis 可能会排队
				for (ComponentGroupModel model : powerGroupModelList) {
					if (model.getGroupNum() != null && model.getGroupNum() > 0) {
						RedisUtil.setRedisDate(batchNo, model.getId(), date + "_" + model.getGroupNum());
					}
				}
				sendCollectCmd(ioSession, imei, date, batchNo);
			}
	}

	private boolean checkGroupSizeIsOk(int size,String systemName)
	{
		// 查询电站的组串
		if (size == 0) {
			logger.info("{} no group", systemName);
			return false;
		}
		return true;
	}

	private boolean checkSessionIsOk(IoSession ioSession,String imei)
	{
		if (ioSession == null) {
			logger.info("send collect cmd {} session is null",imei);
			return false;
		}
		return true;
	}

	private boolean checkTimeIsOk(SendCollectModel sendCollectModel)
	{
		LocalTime nowTime = LocalTime.now();
		String systemName = sendCollectModel.getSystemName();
		String sunUpTime = sendCollectModel.getSunuptime();

		if (sunUpTime != null && sunUpTime.contains(":")
				&& sunUpTime.length() == 5 && nowTime.isBefore(LocalTime.parse(sunUpTime))) {
			logger.info("{} too early out range", systemName);
			return false;
		}
		String sunDownTime = sendCollectModel.getSundowntime();

		if (sunDownTime != null && sunDownTime.contains(":")
				&& sunDownTime.length() == 5 && nowTime.isAfter(LocalTime.parse(sunDownTime))) {
			logger.info("{} too late out range", systemName);
			return false;
		}
       return true;
	}

	private void sendCollectCmd(IoSession ioSession,String imei,String date,String batchNo)
	{
		byte[] imeiArray = ByteUtil.getByteArrayByValue(imei, ConstantsExt.ZTEDATANODELEN.IMEI_LEN);
		byte[] rvsSrc = ByteUtil.getByteArrayByValue(batchNo, ConstantsExt.ZTEDATANODELEN.RSV_LEN);
		if (rvsSrc.length > 7) {////请求标识码 过长
			rvsSrc = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.RSV_LEN);
		}
		byte[] chipID = ByteUtil.getByteChipId(imei.substring(9), ConstantsExt.ZTEDATANODELEN.CHIPID_LEN);
		// 采集电站全部组件数据
		byte[] cmd = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.CMD_LEN, new byte[]{0x66});

		byte[] data = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.DATA_LEN);
		byte[] request = ByteUtil.byteMergerAll(data, cmd, chipID, rvsSrc, imeiArray);
		// 发送数据
		ByteUtil.getDataByteArray(ioSession, request);
		logger.info("{} send collect ok,timestamp {} batchNo {}",imei,date,batchNo);
	}


}
