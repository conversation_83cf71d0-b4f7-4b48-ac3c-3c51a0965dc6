package com.ymx.agreement.quartz.collect;

import com.ymx.service.cache.Configure;
import com.ymx.service.constant.ConstantsExt;
import com.ymx.service.photovoltaic.station.model.ComponentGroupModel;
import com.ymx.service.third.jedis.RedisUtil;
import com.ymx.common.utils.ByteUtil;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;


public class OptimizerCollectThread implements Runnable  {

	/** 批次号 */
	private String batchNo;

	private String imei;

	private String date;

	private List<ComponentGroupModel> powerGroupModelList;

	private static final Logger logger = LoggerFactory.getLogger(OptimizerCollectThread.class);

	public OptimizerCollectThread(String imei,String batchNo,String date,
								  List<ComponentGroupModel> powerGroupModelList){

		this.imei=imei;
		this.batchNo=batchNo;
		this.date=date;
		this.powerGroupModelList=powerGroupModelList;
    }

    @Override
    public void run() {
		String sessionId = Configure.getSessionMap().get(imei);
		// 获取缓存session
		IoSession ioSession = Configure.getMap().get(sessionId);
		if (ioSession == null) {
			logger.info("send collect cmd {} session is null",imei);
			return;
		}

		// 循环组串 多线程同时操作redis 可能会排队
		for (ComponentGroupModel model : powerGroupModelList) {
			if (model.getGroupNum()!=null&&model.getGroupNum() > 0) {
				RedisUtil.setRedisDate(batchNo, model.getId(), date + "_" + model.getGroupNum());
			}
		}

		byte[] imeiArray = ByteUtil.getByteArrayByValue(imei, ConstantsExt.ZTEDATANODELEN.IMEI_LEN);
		byte[] rvsSrc = ByteUtil.getByteArrayByValue(batchNo, ConstantsExt.ZTEDATANODELEN.RSV_LEN);
		if (rvsSrc.length > 7) {////请求标识码 过长
			rvsSrc = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.RSV_LEN);
		}
		byte[] chipID = ByteUtil.getByteChipId(imei.substring(9), ConstantsExt.ZTEDATANODELEN.CHIPID_LEN);
		// 采集单条组件数据
		//byte[] cmd = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.CMD_LEN , new byte[]{0x1});
		// 采集电站全部组件数据
		byte[] cmd = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.CMD_LEN, new byte[]{0x66});

		byte[] data = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.DATA_LEN);
		byte[] request = ByteUtil.byteMergerAll(data, cmd, chipID, rvsSrc, imeiArray);
		// 发送数据
		ByteUtil.getDataByteArray(ioSession, request);

		logger.info("{} send collect ok,timestamp {} batchNo {}",imei,date,batchNo);

	}

}
