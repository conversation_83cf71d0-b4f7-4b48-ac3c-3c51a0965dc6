package com.ymx.agreement.quartz.update;

import com.ymx.common.base.config.ConfigConstants;
import com.ymx.service.cache.Configure;
import com.ymx.service.constant.ConstantsExt;
import com.ymx.common.utils.ByteUtil;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class VersionTaskSend {

    private static final Logger logger = LoggerFactory.getLogger(VersionTaskSend.class);

    private static final String VERSION_FILE_ROOT_PATH = ConfigConstants.getConfig("VERSION_FILE_ROOT_PATH");

    private static final short SINGLE_SEND_FILE_BYTE=1300;
    private static final short FIRST_SEND_FILE_BYTE=48;


    private String taskId;

    private String imeiStr;

    private String type;

    private String fileName;

    private IoSession session;

    public VersionTaskSend(String taskId, String imeiStr, String type, String fileName,
                           IoSession session) {
        this.taskId = taskId;
        this.imeiStr = imeiStr;
        this.type = type;
        this.session = session;
        this.fileName=fileName;
    }


    protected Integer sendVersionSendCmd()
    {
        byte[] partData = mergerPartRequestData();
        String filePath = VERSION_FILE_ROOT_PATH +fileName;

        File versionFile = new File(filePath);
        long fileLength=versionFile.length();
        logger.info("taskId {} fileLength {}",taskId,fileLength);

        byte[] buf = new byte[FIRST_SEND_FILE_BYTE];

        // 本次传输的数据长度
        int count;
        // 已发送的数据长度
        int totalCount = 0;
        // 是否有更多数据
        int isMore = 1;
        Integer result = 0;
        InputStream is=null;
        try {
             is = new FileInputStream(versionFile);
            // 循环发送文件包
            while ((count = is.read(buf)) != -1) {
                // 已经发送的数据长度+这次要发送的长度=文件长度，则说明下面没有新包了
                if(totalCount+count==fileLength)
                {
                    isMore = 0;
                    buf=ByteUtil.getByteOffset(buf,0,0,count);
                }
                logger.info("count:"+count+" totalCount:"+totalCount+" isMore:"+isMore);

                byte[] request = mergerData(count, isMore, totalCount, partData, buf);
                totalCount = totalCount + count;
                logger.info("totalCountUp: "+totalCount);
                result = sendRequestAndHandleReturn(session, request, taskId, totalCount);
                if (result ==null || result != 1) {
                    break;
                }
                Configure.getTotalCountMap().remove(taskId);
                // 第一次发送一个小包，如果采集器没有问题，第二次开始正常发送大包
                if(totalCount==FIRST_SEND_FILE_BYTE)
                {
                    buf = new byte[SINGLE_SEND_FILE_BYTE];
                }
            }
        } catch (FileNotFoundException e) {
            logger.error("FileNotFoundException", e);
        } catch (IOException e) {
            logger.error("IOException", e);
        }
        finally {
            try {
                if(is!=null)
                {
                    is.close();
                }
            } catch (IOException e) {
                logger.error("IOException", e);
            }
        }
        return result;
    }


    protected Integer sendVersionLoadCmd(String relayId){
        byte[] otherData = ByteUtil.loadData(48);
        byte[] file_dataLen = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.FILE_DATA_LEN);
        byte boardTypeByte = 0x59;
        byte cmdByte = 0x31;

        byte[] boardType = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.BOARD_LEN, boardTypeByte);
        byte[] cmd = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.CMD_LEN, cmdByte);
        byte[] beforeChipId = ByteUtil.byteMergerAll(otherData, file_dataLen, boardType,cmd);


        byte[] rvsSrc = ByteUtil.getByteArrayByString(taskId, ConstantsExt.ZTEDATANODELEN.RSV_LEN);
        byte[] imei = ByteUtil.getByteArrayByValue(imeiStr, ConstantsExt.ZTEDATANODELEN.IMEI_LEN);
        byte[] afterChipId = ByteUtil.byteMergerAll(rvsSrc, imei);
        relayId = ByteUtil.getChipId(relayId);
        byte[] chipID = ByteUtil.hexString2Bytes(relayId);
        byte[] request = ByteUtil.byteMergerAll(beforeChipId, chipID, afterChipId);
        String key = taskId + "_" + relayId;

        return (Integer)sendAndWaitReturn(request, key, "loadVersion");
    }

    // 获取优化器的chipId数组
    private byte[] getChipId(String componentId)
    {
        byte[] chipId;
        // 长度为7位且以0开头的优化器id是10进制的 16进制不以0开头，长度为实际长度
        if (componentId.length() == 7 && componentId.startsWith("0")) {
            chipId = ByteUtil.getByteChipId(componentId, ConstantsExt.ZTEDATANODELEN.CHIPID_LEN);
        } else {
            chipId = ByteUtil.hexString2Bytes(ByteUtil.getChipId(componentId));
        }
        return chipId;
    }

    protected String sendUpdateInfoCmd(String componentId)
    {

        byte[] otherData = ByteUtil.loadData(48);
        byte[] file_dataLen = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.FILE_DATA_LEN);
        byte boardTypeByte;
        byte cmdByte;

        if (type.equals("repeater")) {
            cmdByte = 0x30;
            boardTypeByte = 0x52;
        } else {
            cmdByte = 0x10;
            boardTypeByte = 0x59;
        }
        byte[] boardType = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.BOARD_LEN, boardTypeByte);
        byte[] cmd = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.CMD_LEN, cmdByte);
        byte[] beforeChipId = ByteUtil.byteMergerAll(otherData, file_dataLen, boardType,cmd);


        byte[] rvsSrc = ByteUtil.getByteArrayByString(taskId, ConstantsExt.ZTEDATANODELEN.RSV_LEN);
        byte[] imei = ByteUtil.getByteArrayByValue(imeiStr, ConstantsExt.ZTEDATANODELEN.IMEI_LEN);
        byte[] afterChipId = ByteUtil.byteMergerAll(rvsSrc, imei);

        byte[] chipId;
        if (cmdByte == 0x10) {
            chipId=getChipId(componentId);
        } else {
            String newComponentId = ByteUtil.getChipId(componentId);
            chipId = ByteUtil.hexString2Bytes(newComponentId);
        }

        byte[] request = ByteUtil.byteMergerAll(beforeChipId, chipId, afterChipId);

        String key = taskId + "_" + componentId;
        return (String)sendAndWaitReturn(request, key, "sendUpdateInfo");
    }

    protected String sendVersionQueryCmd(String componentId, String type){

        byte cmdByte = 0;
        byte[] chipId = null;
        byte boardTypeByte = 0;
        switch (type) {
            case "collector":
                cmdByte = 0x72;
                chipId = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.CHIPID_LEN);
                boardTypeByte = 0x42;
                break;
            case "repeater":
                cmdByte = 0x32;
                chipId = ByteUtil.hexString2Bytes(ByteUtil.getChipId(componentId));
                boardTypeByte = 0x52;
                break;
            case "optimizer":
                cmdByte = 0x12;
                chipId=getChipId(componentId);
                boardTypeByte = 0x59;
                break;
            default:
                logger.error("type error");
        }

        byte[] data = ByteUtil.loadData(48);
        byte[] file_dataLen = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.FILE_DATA_LEN);
        byte[] boardType = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.BOARD_LEN, boardTypeByte);
        byte[] cmd = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.CMD_LEN, cmdByte);
        byte[] rvsSrc;
        if (taskId == null) {
            rvsSrc = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.RSV_LEN);
        } else {
            rvsSrc = ByteUtil.getByteArrayByString(taskId, ConstantsExt.ZTEDATANODELEN.RSV_LEN);
        }
        byte[] imei = ByteUtil.getByteArrayByValue(imeiStr, ConstantsExt.ZTEDATANODELEN.IMEI_LEN);

        byte[] request = ByteUtil.byteMergerAll(data,file_dataLen,boardType, cmd, chipId, rvsSrc, imei);

        String key=taskId+"_"+componentId;
        return (String) sendAndWaitReturn(request,key,"queryVersion");
    }


    private Object sendAndWaitReturn(byte[] request,
                                      String key,String step) {

        logger.info("sendAndWaitReturn key："+key);
        Object result=null;
        for (int i = 0; i < 3; i++) {

            logger.info("the request message is {}, request array length is {} ",request,request.length);

            ByteUtil.getDataByteArray(session, request);
            result=getReturnResult(key,step,type);
            if(result!=null)
            {
                break;
            }

        }
        return result;
    }


    private Integer sendRequestAndHandleReturn(IoSession session, byte[] request,
                                               String taskId, int totalCount) {
        Integer result=null;
        for (int i = 0; i < 2; i++) {

            ByteUtil.getDataByteArray(session, request);
            logger.info(taskId+" totalCount: "+totalCount);

            result=(Integer) getReturnResult(taskId,"sendVersion",type);
            //  如果返回值正确或者为全F 退出循环 否则重试三次
            if(result!=null) {
                if (result == 65535) {
                    break;
                } else if (result == totalCount) {
                    result = 1;
                    break;
                }
            }
        }
        return result;
    }

    private Object getReturnResult(String key,String step,String type) {
        long waitTime = 20000;
        Object result;
        Map map=new HashMap<>();

        switch (step) {
            case "sendVersion":
                map = Configure.getTotalCountMap();
                break;
            case "loadVersion":
                map = Configure.getLoadInfoMap();
                break;
            case "sendUpdateInfo":
                if (type.equals("repeater")) {
                    waitTime = 12000;
                }
                map = Configure.getUpdateInfoMap();
                break;
            case "queryVersion":
                map = Configure.getQueryVersionMap();
                break;
        }
        long startTime = System.currentTimeMillis();
        while (true) {
            result=map.get(key);
            long breakTime=System.currentTimeMillis()-startTime;
            // 如果返回值不为null 或者超时 退出循环
            if (result != null || breakTime > waitTime) {
                logger.info("key {} step {} result {}  breakTime {}",
                        Arrays.asList(key,step,result,breakTime).toArray());
                break;
            } else {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    logger.error("InterruptedException", e);
                }
            }
        }
        return result;
    }

    private byte[] mergerData(int count, int isMore, int totalCount, byte[] partData, byte[] fileBuf) {
        // data里面的5个字段

        // 是否有更多的数据
        byte[] more_data = ByteUtil.int2Bytes(isMore, 2);
        // 已发送的数据长度
        byte[] seq_no = ByteUtil.int2Bytes(totalCount,2);
        // 本次发送的数据crc16
        int crc=ByteUtil.crcSum(fileBuf);
        logger.info("crcShort: "+crc);

        byte[] crc16 = ByteUtil.int2Bytes(crc,2);

        // 补齐data数据
        byte[] fillEmptyData = ByteUtil.loadData(42);

        // 本次传输的文件数据长度
        byte[] file_dataLen = ByteUtil.int2Bytes(count, ConstantsExt.ZTEDATANODELEN.FILE_DATA_LEN);

        return ByteUtil.byteMergerAll(more_data, seq_no,
                crc16,fillEmptyData, file_dataLen, partData, fileBuf);
    }

    private byte[] mergerPartRequestData() {
        byte cmdByte = 0;
        byte boardTypeByte = 0;
        switch (type) {
            case "collector":
                cmdByte = 0x70;
                boardTypeByte = 0x42;
                break;
            case "repeater":
                cmdByte = 0x71;
                boardTypeByte = 0x52;
                break;
            case "optimizer":
                cmdByte = 0x71;
                boardTypeByte = 0x59;
                break;
        }

        byte[] boardType = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.BOARD_LEN, boardTypeByte);
        // data 外面的4个字段
        byte[] cmd = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.CMD_LEN, cmdByte);
        byte[] chipID = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.CHIPID_LEN);
        byte[] rvsSrc = ByteUtil.getByteArrayByString(taskId, ConstantsExt.ZTEDATANODELEN.RSV_LEN);
        byte[] imei = ByteUtil.getByteArrayByValue(imeiStr, ConstantsExt.ZTEDATANODELEN.IMEI_LEN);

        return ByteUtil.byteMergerAll(boardType, cmd, chipID, rvsSrc, imei);
    }


}


