/*!
 * LABELAUTY jQuery Plugin Styles
 *
 * @file: jquery-labelauty.css
 * @author: <PERSON> (@fntneves)
 * @site: www.francisconeves.com
 * @license: MIT License
 */

/* Prevent text and blocks selection */
input.labelauty + label ::selection { background-color: rgba(255, 255, 255, 0); }
input.labelauty + label ::-moz-selection { background-color: rgba(255, 255, 255, 0); }

/* Hide original checkboxes. They are ugly! */
input.labelauty { display: none !important; }

/*
 * Let's style the input
 * Feel free to work with it as you wish!
 */
input.labelauty + label
{
	display: inline-block;
	font-size: 11px;
	background-color: white;
	color: #b3b3b3;
	cursor: pointer;
	border-radius: 3px 3px 3px 3px;
	-moz-border-radius: 3px 3px 3px 3px;
	-webkit-border-radius: 3px 3px 3px 3px;


	transition: background-color 0.25s;
	-moz-transition: background-color 0.25s;
	-webkit-transition: background-color 0.25s;
	-o-transition: background-color 0.25s;

	-moz-user-select: none;
	-khtml-user-select: none;
	-webkit-user-select: none;
	-o-user-select: none;
}

/* Stylish text inside label */

input.labelauty + label > span.labelauty-unchecked,
input.labelauty + label > span.labelauty-checked
{
	display: inline-block;
	line-height: 16px;
	vertical-align: bottom;
}

/* Stylish icons inside label */

input.labelauty + label > span.labelauty-unchecked-image,
input.labelauty + label > span.labelauty-checked-image
{
	display: inline-block;
	width: 14px;
	height: 14px;
	vertical-align: bottom;
	background-repeat: no-repeat;
	background-position: center center;

	transition: background-image 0.5s linear;
	-moz-transition: background-image 0.5s linear;
	-webkit-transition: background-image 0.5s linear;
	-o-transition: background-image 0.5s linear;
}

/* When there's a label, add a little margin to the left */
input.labelauty + label > span.labelauty-unchecked-image + span.labelauty-unchecked,
input.labelauty + label > span.labelauty-checked-image + span.labelauty-checked
{
	margin-left: 7px;
}

/* When not Checked */
input.labelauty:not(:checked):not([disabled]) + label:hover
{
	background-color: #eaeaea;
	color: #a7a7a7;
}
input.labelauty:not(:checked) + label > span.labelauty-checked-image
{
	display: none;
}

input.labelauty:not(:checked) + label > span.labelauty-checked
{
	display: none;
}

/* When Checked */
input.labelauty:checked + label
{
	background-color: #3498db;
	color: #ffffff;
}

input.labelauty:checked:not([disabled]) + label:hover
{
	background-color: #72c5fd;
}
input.labelauty:checked + label > span.labelauty-unchecked-image
{
	display: none;
}

input.labelauty:checked + label > span.labelauty-unchecked
{
	display: none;
}

input.labelauty:checked + label > span.labelauty-checked
{
	display: inline-block;
}

input.labelauty.no-label:checked + label > span.labelauty-checked
{
	display: block;
}

/* When Disabled */
input.labelauty[disabled] + label
{
	opacity: 0.5;
}

/* Add a background to (un)checked images */
input.labelauty + label > span.labelauty-unchecked-image
{
	background-image: url(../img/input-unchecked.png);
}

input.labelauty + label > span.labelauty-checked-image
{
	background-image: url(../img/input-checked.png);
	background-size: 60%;
}