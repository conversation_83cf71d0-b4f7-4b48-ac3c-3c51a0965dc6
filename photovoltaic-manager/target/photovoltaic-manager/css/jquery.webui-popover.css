/*
 *  webui popover plugin  - v1.0.5
 *  A lightWeight popover plugin with j<PERSON><PERSON> ,enchance the  popover plugin of bootstrap with some awesome new features. It works well with bootstrap ,but bootstrap is not necessary!
 *  https://github.com/sandywalker/webui-popover
 *
 *  Made by <PERSON>
 *  Under MIT License
 */
/*  webui popover  */
.webui-popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: none;
  width: 276px;
  min-height: 50px;
  padding: 1px;
  text-align: left;
  white-space: normal;
  background-color: #ffffff;
  background-clip: padding-box;
  border: 1px solid #cccccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}
.webui-popover.top,
.webui-popover.top-left,
.webui-popover.top-right {
  margin-top: -10px;
}
.webui-popover.right {
  margin-left: 10px;
}
.webui-popover.bottom,
.webui-popover.bottom-left,
.webui-popover.bottom-right {
  margin-top: 10px;
}
.webui-popover.left {
  margin-left: -10px;
}
.webui-popover-inner .close {
  font-family: arial;
  margin: 5px 10px 0 0;
  float: right;
  font-size: 20px;
  font-weight: bold;
  line-height: 20px;
  color: #000000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.2;
  filter: alpha(opacity=20);
  text-decoration: none;
}
.webui-popover-inner .close:hover,
.webui-popover-inner .close:focus {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.webui-popover-title {
  padding: 8px 14px;
  margin: 0;
  font-size: 14px;
  font-weight: normal;
  line-height: 18px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 5px 5px 0 0;
}
.webui-popover-content {
  padding: 9px 14px;
  overflow: auto;
}
.webui-popover-inverse {
  background-color: #333333;
  color: #eeeeee;
}
.webui-popover-inverse .webui-popover-title {
  background: #3b3b3b;
  border-bottom: none;
  color: #eeeeee;
}
.webui-no-padding .webui-popover-content {
  padding: 0;
}
.webui-no-padding .list-group-item {
  border-right: none;
  border-left: none;
}
.webui-no-padding .list-group-item:first-child {
  border-top: 0;
}
.webui-no-padding .list-group-item:last-child {
  border-bottom: 0;
}
.webui-popover > .arrow,
.webui-popover > .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.webui-popover > .arrow {
  border-width: 11px;
}
.webui-popover > .arrow:after {
  border-width: 10px;
  content: "";
}
.webui-popover.top > .arrow,
.webui-popover.top-right > .arrow,
.webui-popover.top-left > .arrow {
  bottom: -11px;
  left: 50%;
  margin-left: -11px;
  border-top-color: #999999;
  border-top-color: rgba(0, 0, 0, 0.25);
  border-bottom-width: 0;
}
.webui-popover.top > .arrow:after,
.webui-popover.top-right > .arrow:after,
.webui-popover.top-left > .arrow:after {
  content: " ";
  bottom: 1px;
  margin-left: -10px;
  border-top-color: #ffffff;
  border-bottom-width: 0;
}
.webui-popover.right > .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999999;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.webui-popover.right > .arrow:after {
  content: " ";
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #ffffff;
}
.webui-popover.bottom > .arrow,
.webui-popover.bottom-right > .arrow,
.webui-popover.bottom-left > .arrow {
  top: -11px;
  left: 50%;
  margin-left: -11px;
  border-bottom-color: #999999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  border-top-width: 0;
}
.webui-popover.bottom > .arrow:after,
.webui-popover.bottom-right > .arrow:after,
.webui-popover.bottom-left > .arrow:after {
  content: " ";
  top: 1px;
  margin-left: -10px;
  border-bottom-color: #ffffff;
  border-top-width: 0;
}
.webui-popover.left > .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999999;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.webui-popover.left > .arrow:after {
  content: " ";
  right: 1px;
  border-right-width: 0;
  border-left-color: #ffffff;
  bottom: -10px;
}
.webui-popover-inverse.top > .arrow,
.webui-popover-inverse.top-left > .arrow,
.webui-popover-inverse.top-right > .arrow,
.webui-popover-inverse.top > .arrow:after,
.webui-popover-inverse.top-left > .arrow:after,
.webui-popover-inverse.top-right > .arrow:after {
  border-top-color: #333333;
}
.webui-popover-inverse.right > .arrow,
.webui-popover-inverse.right > .arrow:after {
  border-right-color: #333333;
}
.webui-popover-inverse.bottom > .arrow,
.webui-popover-inverse.bottom-left > .arrow,
.webui-popover-inverse.bottom-right > .arrow,
.webui-popover-inverse.bottom > .arrow:after,
.webui-popover-inverse.bottom-left > .arrow:after,
.webui-popover-inverse.bottom-right > .arrow:after {
  border-bottom-color: #333333;
}
.webui-popover-inverse.left > .arrow,
.webui-popover-inverse.left > .arrow:after {
  border-left-color: #333333;
}
.webui-popover i.glyphicon-refresh {
  display: block;
  margin-left: -15px;
  margin-top: -15px;
  width: 20px;
  height: 20px;
  font-size: 20px;
  top: 50%;
  left: 50%;
  position: absolute;
  -webkit-animation: rotate 1s linear 0 infinite;
  -o-animation: rotate 1s linear 0 infinite;
  animation: rotate 1s linear 0 infinite;
}
@-webkit-keyframes rotate {
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}
