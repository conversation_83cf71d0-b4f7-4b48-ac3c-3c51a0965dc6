* {
    font-family: "Microsoft YaHei";
}
.CodeMirror * {
    font-family: sans-serif;
}
html {
    background-color: rgb(6, 19, 37);
}
body {
  padding-top: 50px;
  padding-bottom: 0;
  background-color: rgb(247, 247, 247);
}
body p {
    line-height: 2;
}
.navbar {
    margin-bottom: 0;
}
.navbar > .container .navbar-brand {
    margin-left:0;
}
.navbar a.navbar-brand {
    background:url('../img/echarts-logo.png') no-repeat 0 10px;
    padding-left: 48px;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 25px;
}
.navbar-default {
    background-color: rgb(247, 247, 247);
    border-color:rgb(218, 218, 218);
}
.navbar-default .navbar-nav > li.active > a {
    background-color: transparent;
    color: #3E98C5;
    font-weight: bolder;
    border-bottom: 3px solid rgb(62,152, 197);
}
@media (max-width: 980px) {
    .navbar-default .navbar-nav > li > a {
        padding-left: 8px;
        padding-right: 8px;
    }
}
.navbar-default .navbar-nav > li > a:hover {
    background-color: #eee;
}
.navbar-fixed-top {
    z-index: 999999;
}
.carousel-caption.ecx-link {
    bottom: 20px;
}
.carousel-caption.ecx-link a {
    font-size:14px;
    height: 30px;
    display: block;
    color:#999;
}
.carousel-caption.ecx-link a:hover {
    color:#eee;
    text-decoration: none;
}

blockquote {
    border-width:0;
    margin-bottom:10px;
}
.marketing .feature .col-lg-4 img {
    margin-bottom: 10px;
}
.marketing .thx .col-lg-4 {
    margin-bottom: 0;
    text-align:left;
}
.thx blockquote p {
    text-align:left;
    margin-bottom:0;
}
.thx blockquote small {
    text-align:right;
}
.thx div {
    padding:0;
}
div.user {
    margin-top: 30px;
}
.user img {
    height:50px;
    padding: 5px;
    margin: 15px 3px;
    border: 1px solid #ddd;
    opacity: .75;
}
.user a:hover img {
  opacity: 1;
  border: 1px solid #ccc;
}


.featurette {
    padding:50px 0;
    border-top: 1px solid #ddd;
}
.featurette-heading {
    margin-top: 0px
}
img {
    max-width: 100%;
}

hr {
    border-color: #ddd;
}

.row {
    margin-right: 0;
    margin-left: 0;
}
.jumbotron {
    margin: 0;
    text-align: center;
    background-color:transparent;
  }
  .jumbotron h1 {
    font-size: 100px;
    line-height: 1;
  }
  .jumbotron .lead {
    font-size: 24px;
    line-height: 1.25;
  }
  .jumbotron .btn {
    font-size: 21px;
    padding: 14px 24px;
  }
.team {
    padding-bottom:0;
}
.team .row {
    margin-bottom: 40px;
}
.team i {
    font-size:12px;
}

.e-list dt {
    float: left;
    clear: left;
    width: 60px;
}
.e-list dd {
    float: left;
    clear: right;
    color: #666;
}

#footer {
    clear:both;
    background-color: rgb(6, 19, 37);
    padding: 30px 0;
}
#footer p {
    color: #fff;
    font-size: 14px;
}
#footer li a {
    color: rgb(190, 205, 223);
    font-size: 12px;
}
#footer ul {
    list-style-type: none;
    padding: 0;
}
#footer i {
    color: rgb(190, 205, 223);
}
#footer div.flogo {
    position:relative;
}
#footer div.flogo img {
    width:220px;
    position:absolute;
    left:30px;
    top:-120px;
}
#footer div.flogo a:hover img {
    top:-130px;
}
.dropdown i {
    color:#555;
}
.nav ul.dropdown-menu{
    min-width : 0;
}
.dropdown-menu > li > a {
    padding: 3px 10px;
}


/*example*/
.CodeMirror pre{color: #f8f8f2;}
.CodeMirror-scrollbar-filler {
    background-color:rgb(247,247,247);
}
.container-fluid .example {
    padding: 25px 0;
}
.sidebar-nav {
    padding: 9px 0;
    margin-bottom: 0;
}
.nav-header {
    display: block;
    padding: 3px 15px;
    font-size: 11px;
    font-weight: bold;
    line-height: 20px;
    color: #999999;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
    text-transform: uppercase;
}
.col-md-4.ani {
    transition: width 1s;
    -moz-transition: width 1s;
    -webkit-transition: width 1s;
    -o-transition: width 1s;
}
.col-md-8.ani {
    transition: width 1s;
    -moz-transition: width 1s;
    -webkit-transition: width 1s;
    -o-transition: width 1s;
}
.main {
    height: 400px;
    /*width: 778px !important;*/
    overflow: hidden;
    padding : 10px;
    margin-bottom: 10px;
    border: 1px solid #e3e3e3;
    -webkit-border-radius: 4px;
       -moz-border-radius: 4px;
            border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
       -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
            box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}
#graphic .btn {
    width: 80px;
}
#graphic .text-primary {
    margin:0 5px 0 20px
}
#icon-resize {
    color:#000;
    float:right;
    opacity:.4;
    filter:alpha(opacity=30);
}
a#icon-resize:hover {
    opacity:.8;
    filter:alpha(opacity=60);
    text-decoration: blink;
}

/*doc*/
.panel {
    background-color:transparent;
    margin-left: -15px;
    margin-top: 10px;
    -webkit-box-shadow: none;
    box-shadow: none;
}
.panel-body {
    padding: 0 0 0 10px;
}
#toc,
#config {
    padding: 9px 0;
    overflow-y : auto;
}
.tree ul,
.tree li {
    list-style: none;
    font-size : 14px;
    line-height : 20px;
}
.tree ul {
    margin: 0;
    padding: 0 0 0 2em;
}

.tree {
    white-space: nowrap;
}
.tree strong {
    color: purple;
    font-weight: normal;
}
.tree li {
    position: relative;
}
.tree strong:hover, #content .value:hover, #content .summary:hover {
    background-color: silver;
    -webkit-transition: all .5s ease-in;
}
.tree .operator {
    position: absolute;
    left: -1em;
    top: 0;
    display: none;
    cursor: pointer;
}
.tree ul .operator {
    display: block;
}
.tree ul .value,
.tree ul .group,
.tree ul .summary {
    margin-left: .5em;
}
.tree .group {
    display: inline;
}
.tree .summary {
    display: none;
    color: black;
    font-weight: bold;
}
.tree .tree-close .group {
    display: none;
}
.tree .tree-close .summary {
    display: inline;
}
.tree .string {
    color: maroon;
}
.tree .number {
    color: blue;
}
.tree .boolean {
    color: black;
}
#doc h3 a,#doc h4 a,#doc h5 a{
    display:inline-block;
    padding-top:80px;
}
table.full {width:100%;}
.ADoc_table { border-collapse: collapse; margin-bottom:15px; }

.ADoc_table th, .ADoc_table td {
    border:1px solid rgb(23,53,81);
    padding: 3px;
    color:#222;
}

.ADoc_table th {
    border-bottom:2px solid rgb(23,53,81);
    background:rgb(37,78,117);
    color:#fff;
    text-align:center;
}
.bgRed {
    background:rgb(255,230,230);
}
.bgGreen {
    background:rgb(230,255,230);
}
.bgBlue {
    background:rgb(210,230,255);
}

.prettyprint {
  margin-top: 8px;
}
.prettyprint xmp{
  margin: 0px;
  padding: 10px;
}
.prettyprint .pln {
  line-height: 0px;
}

.ec-icon {
    display: inline-block;
    width: 35px;
    height: 25px;
    *margin-right: .3em;
    line-height: 25px;
    vertical-align: middle;
    background-image: url("../img/icon-bar.png");
    background-repeat: no-repeat;
}
.ec-icon-line {
    background-position: 0 -1px;
}
.ec-icon-bar {
    background-position: 0 -26px;
}
.ec-icon-scatter {
    background-position: 0 -51px;
}
.ec-icon-k {
    background-position: 0 -76px;
}
.ec-icon-pie {
    background-position: 0 -101px;
}
.ec-icon-radar {
    background-position: 0 -126px;
}
.ec-icon-chord {
    background-position: 0 -151px;
}
.ec-icon-force {
    background-position: 0 -176px;
}
.ec-icon-map {
    background-position: 0 -201px;
}
.ec-icon-gauge {
    background-position: 0 -226px;
}
.ec-icon-funnel {
    background-position: 0 -251px;
}
.ec-icon-mix {
    background-position: 0 -276px;
}
.ec-icon-component {
    background-position: 0 -301px;
}
.ec-icon-other {
    background-position: 0 -326px;
}
.ec-icon-theme {
    background-position: 0 -351px;
}
.ec-icon-topic {
    background-position: 0 -376px;
}
.ec-icon-eventRiver {
    background-position: 0 -401px;
}
.ec-icon-venn {
    background-position: 0 -426px;
}
.ec-icon-treemap {
    background-position: 0 -451px;
}
.ec-icon-wordCloud {
    background-position: 0 -476px;
}
.ec-icon-tree {
    background-position: 0 -501px;
}
.ec-icon-heatmap {
    background-position: 0 -526px;
}