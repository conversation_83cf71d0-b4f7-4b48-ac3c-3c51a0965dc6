/*
Navicat MySQL Data Transfer

Source Server         : *********
Source Server Version : 50547
Source Host           : *********:3306
Source Database       : security

Target Server Type    : MYSQL
Target Server Version : 50547
File Encoding         : 65001

Date: 2016-10-19 16:11:02
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for `t_fs_app`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_app`;
CREATE TABLE `t_fs_app` (
  `app_id` varchar(32) NOT NULL DEFAULT '',
  `app_name` varchar(32) DEFAULT '',
  `app_link` varchar(128) DEFAULT '',
  `app_height` int(11) DEFAULT '0',
  `app_desc` varchar(1024) DEFAULT '',
  `app_is_small` varchar(1) DEFAULT '0',
  `app_icon` varchar(48) DEFAULT '',
  `app_creator` varchar(32) DEFAULT '',
  `app_create_date` datetime DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`app_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_app
-- ----------------------------

-- ----------------------------
-- Table structure for `t_fs_app_role`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_app_role`;
CREATE TABLE `t_fs_app_role` (
  `role_id` varchar(32) NOT NULL DEFAULT '',
  `app_id` varchar(32) NOT NULL DEFAULT '',
  PRIMARY KEY (`role_id`,`app_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_app_role
-- ----------------------------

-- ----------------------------
-- Table structure for `t_fs_dept`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_dept`;
CREATE TABLE `t_fs_dept` (
  `dept_id` varchar(32) NOT NULL DEFAULT '',
  `dept_name` varchar(32) DEFAULT '',
  `dept_phone` varchar(128) DEFAULT '',
  `dept_fax` varchar(128) DEFAULT '',
  `dept_address` varchar(512) DEFAULT '',
  `p_dept_id` varchar(32) DEFAULT '',
  `dept_leader` varchar(32) DEFAULT '',
  `dept_level` int(11) DEFAULT '0',
  `dept_remark` varchar(2048) DEFAULT '',
  `creator` varchar(32) DEFAULT '',
  `create_date` datetime DEFAULT '0000-00-00 00:00:00',
  `dept_short_name` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`dept_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_dept
-- ----------------------------

-- ----------------------------
-- Table structure for `t_fs_dictionary`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_dictionary`;
CREATE TABLE `t_fs_dictionary` (
  `dic_id` varchar(32) NOT NULL DEFAULT '',
  `dic_name` varchar(32) DEFAULT '',
  `create_date` datetime DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`dic_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_dictionary
-- ----------------------------
INSERT INTO `t_fs_dictionary` VALUES ('sex', '性别', null);
INSERT INTO `t_fs_dictionary` VALUES ('signtype', '签到签退类型', '2015-02-11 12:06:26');
INSERT INTO `t_fs_dictionary` VALUES ('yesno', '是否', '2014-09-09 20:50:44');

-- ----------------------------
-- Table structure for `t_fs_dictionary_value`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_dictionary_value`;
CREATE TABLE `t_fs_dictionary_value` (
  `dic_value_id` varchar(32) NOT NULL DEFAULT '',
  `dic_value_label` varchar(64) DEFAULT '',
  `dic_value_order` int(11) DEFAULT '0',
  `dic_id` varchar(32) NOT NULL DEFAULT '',
  PRIMARY KEY (`dic_value_id`,`dic_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_dictionary_value
-- ----------------------------
INSERT INTO `t_fs_dictionary_value` VALUES ('0', '签到', '1', 'signtype');
INSERT INTO `t_fs_dictionary_value` VALUES ('0', '是', '1', 'yesno');
INSERT INTO `t_fs_dictionary_value` VALUES ('1', '男', '1', 'sex');
INSERT INTO `t_fs_dictionary_value` VALUES ('1', '签退', '2', 'signtype');
INSERT INTO `t_fs_dictionary_value` VALUES ('1', '否', '2', 'yesno');
INSERT INTO `t_fs_dictionary_value` VALUES ('2', '女', '2', 'sex');

-- ----------------------------
-- Table structure for `t_fs_duty`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_duty`;
CREATE TABLE `t_fs_duty` (
  `duty_id` varchar(32) NOT NULL DEFAULT '',
  `duty_name` varchar(32) DEFAULT '',
  `duty_desc` varchar(2000) DEFAULT '',
  PRIMARY KEY (`duty_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_duty
-- ----------------------------
INSERT INTO `t_fs_duty` VALUES ('14BC9BD6C14015A2CF0437924A14B2BA', '普通用户', '');

-- ----------------------------
-- Table structure for `t_fs_login_logger`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_login_logger`;
CREATE TABLE `t_fs_login_logger` (
  `id` varchar(32) NOT NULL DEFAULT '',
  `user_id` varchar(32) DEFAULT NULL,
  `last_login_time` datetime DEFAULT NULL,
  `login_ip` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_login_logger
-- ----------------------------
INSERT INTO `t_fs_login_logger` VALUES ('flymz', 'flymz', '2016-10-19 16:06:40', '0:0:0:0:0:0:0:1');
INSERT INTO `t_fs_login_logger` VALUES ('WN001', 'wn001', '2016-01-08 09:56:54', '**************');

-- ----------------------------
-- Table structure for `t_fs_menu`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_menu`;
CREATE TABLE `t_fs_menu` (
  `menu_id` varchar(32) NOT NULL DEFAULT '',
  `menu_name` varchar(32) DEFAULT '',
  `menu_order` int(11) DEFAULT '0',
  `menu_icon` varchar(32) DEFAULT '',
  `p_menu_id` varchar(32) DEFAULT '0',
  `menu_link` varchar(64) DEFAULT '',
  PRIMARY KEY (`menu_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_menu
-- ----------------------------
INSERT INTO `t_fs_menu` VALUES ('0001', '系统管理', '21', '61.png', '0', '');
INSERT INTO `t_fs_menu` VALUES ('0001001', '菜单管理', '1', 'application_side_boxes.png', '0002', 'menu/search.htm');
INSERT INTO `t_fs_menu` VALUES ('0001002', '权限管理', '2', 'key_go.png', '0001', 'role/search.htm');
INSERT INTO `t_fs_menu` VALUES ('0001003', '部门管理', '4', 'folder_user.png', '0001', 'dept/search.htm');
INSERT INTO `t_fs_menu` VALUES ('0001004', '用户管理', '3', '37.png', '0001', 'user/frame.htm');
INSERT INTO `t_fs_menu` VALUES ('0001005', '职务管理', '5', 'book_previous.png', '0001', 'duty/search.htm');
INSERT INTO `t_fs_menu` VALUES ('0001006', '日志管理', '6', 'book_previous.png', '0001', 'loginlogger/forwardList.htm');
INSERT INTO `t_fs_menu` VALUES ('0002', '开发者中心', '22', 'clock_pause.png', '0', '');
INSERT INTO `t_fs_menu` VALUES ('0002001', '字典维护', '0', 'application_side_boxes.png', '0002', 'dic/frame.htm');
INSERT INTO `t_fs_menu` VALUES ('13D743F542B01DC2B57C6B307B1702B2', '桌面管理', '6', 'computer.png', '0001', 'app/search.htm');
INSERT INTO `t_fs_menu` VALUES ('14034D847BE01D052DEB3FD1711D5C9E', '系统管理', '1', '61.png', '', 'menu.htm');
INSERT INTO `t_fs_menu` VALUES ('14A8580062C061F2296D78B2521DC30F', '参数设置', '5', 'cog.png', '0002', 'setter/forwardList.htm');

-- ----------------------------
-- Table structure for `t_fs_menu_function`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_menu_function`;
CREATE TABLE `t_fs_menu_function` (
  `fun_id` varchar(32) NOT NULL DEFAULT '',
  `menu_id` varchar(32) NOT NULL DEFAULT '',
  `fun_name` varchar(32) DEFAULT '',
  `fun_order` int(11) DEFAULT '0',
  `fun_link` varchar(128) DEFAULT '',
  PRIMARY KEY (`fun_id`,`menu_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_menu_function
-- ----------------------------
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '0001001', '创建菜单', '1', 'menu/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '0001002', '增加角色', '1', 'role/toadd.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '0001003', '增下级', '1', 'dept/tosave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '0001004', '创建', '1', 'user/tosave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '0001005', '创建职务', '1', 'duty/tosave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '0002001', '增加', '1', 'dic/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '13D743F542B01DC2B57C6B307B1702B2', '新增桌面应用', '1', 'app/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '14A8580062C061F2296D78B2521DC30F', '新增', '1', 'setter/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '14B7676525B0160AB3B638B63C1ACCE9', '增加', '1', 'addrbook/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '14B76787AA60FAF8CCDA2523626466A0', '新增', '1', 'notice/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-arrow-switch', '0001001', '菜单排序', '5', 'menu/toOrder.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-cancel', '0001003', '删除', '2', 'dept/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '0001001', '修改菜单', '3', 'menu/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '0001002', '修改角色', '2', 'role/toedit.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '0001004', '修改', '2', 'user/tosave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '0001005', '修改职务', '3', 'duty/update.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '0002001', '修改', '3', 'dic/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '13D743F542B01DC2B57C6B307B1702B2', '修改桌面应用', '2', 'app/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '14A8580062C061F2296D78B2521DC30F', '修改', '2', 'setter/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '14B7676525B0160AB3B638B63C1ACCE9', '修改', '2', 'addrbook/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '14B76787AA60FAF8CCDA2523626466A0', '修改', '2', 'notice/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-function', '0001001', '操作功能配置', '4', 'menu/toFunction.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-function', '0001002', '分配应用权', '5', 'role/toSetterApp.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-help', '0001003', '帮助', '3', 'dept/instruction.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-reload', '0001004', '密码初始化', '5', 'user/repswd.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '0001001', '删除菜单', '2', 'menu/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '0001002', '删除角色', '3', 'role/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '0001004', '删除', '3', 'user/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '0001005', '删除职务', '2', 'duty/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '0002001', '删除', '2', 'dic/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '13D743F542B01DC2B57C6B307B1702B2', '删除桌面应用', '3', 'app/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '14A8580062C061F2296D78B2521DC30F', '删除', '3', 'setter/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '14B7676525B0160AB3B638B63C1ACCE9', '删除', '3', 'addrbook/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '14B76787AA60FAF8CCDA2523626466A0', '删除', '3', 'notice/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-role', '0001002', '分配权限', '4', 'role/toSetterRole.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-role', '0001004', '用户授权', '4', 'user/toUserRole.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-save', '0001003', '保存', '4', 'dept/save.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '14C024656AC01DFD6F3F8CFAF91CD837', '发送短信', '1', 'sms/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '150D5C774D602A0F90FF2C6B2117524A', '增加', '1', 'goodpersion/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '150D5C774D602A0F90FF2C6B2117524A', '修改', '2', 'goodpersion/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '150D5C774D602A0F90FF2C6B2117524A', '删除', '3', 'goodpersion/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '150D5C8DEE3082EAB7244C819422A2DA', '新增', '1', 'fourVirtues/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '150D5C8DEE3082EAB7244C819422A2DA', '修改', '2', 'fourVirtues/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '150D5C8DEE3082EAB7244C819422A2DA', '删除', '3', 'fourVirtues/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '150D5C9E04301C0C8D1E875E7C269EAF', '新增', '1', 'goodThings/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '150D5C9E04301C0C8D1E875E7C269EAF', '修改', '2', 'goodThings/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '150D5C9E04301C0C8D1E875E7C269EAF', '删除', '3', 'goodThings/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '150D5CAC18B016954A1FE6B35F30236E', '新增', '1', 'volunteerService/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '150D5CAC18B016954A1FE6B35F30236E', '修改', '2', 'volunteerService/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '150D5CAC18B016954A1FE6B35F30236E', '删除', '3', 'volunteerService/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '150DBFAABEE0FE851C2B223345BC7DB2', '新增', '1', 'beautifulCountry/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '150DBFAABEE0FE851C2B223345BC7DB2', '修改', '2', 'beautifulCountry/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '150DBFAABEE0FE851C2B223345BC7DB2', '删除', '3', 'beautifulCountry/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '150DC026265016F86E65293377222CDF', '新增', '1', 'classroom/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '150DC026265016F86E65293377222CDF', '修改', '2', 'classroom/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '150DC026265016F86E65293377222CDF', '删除', '3', 'classroom/delete.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-add', '150DC07014A094604BEEA388C8263969', '新增', '1', 'impress/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-edit', '150DC07014A094604BEEA388C8263969', '修改', '2', 'impress/toSave.htm');
INSERT INTO `t_fs_menu_function` VALUES ('icon-remove', '150DC07014A094604BEEA388C8263969', '删除', '3', 'impress/delete.htm');

-- ----------------------------
-- Table structure for `t_fs_role`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_role`;
CREATE TABLE `t_fs_role` (
  `role_id` varchar(32) NOT NULL DEFAULT '',
  `role_name` varchar(64) DEFAULT '',
  `role_remark` varchar(512) DEFAULT '',
  `is_deleted` varchar(1) DEFAULT '0'
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_role
-- ----------------------------
INSERT INTO `t_fs_role` VALUES ('13D54B59C650159A6A52ED136C16E235', '系统管理员', '管理员', '0');
INSERT INTO `t_fs_role` VALUES ('13D957D27A20CE0301A01FC1D72B60F0', '普通用户', '普通用户', '0');

-- ----------------------------
-- Table structure for `t_fs_role_menu`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_role_menu`;
CREATE TABLE `t_fs_role_menu` (
  `role_id` varchar(32) NOT NULL DEFAULT '',
  `menu_id` varchar(32) NOT NULL DEFAULT '',
  PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_role_menu
-- ----------------------------
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001001');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001002');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001003');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001004');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001005');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001006');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '0002001');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '13D743F542B01DC2B57C6B307B1702B2');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '14A8580062C061F2296D78B2521DC30F');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '14B76787AA60FAF8CCDA2523626466A0');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '14B76D8DA2F04DF1372B588F6E7764C1');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '14BC3C565CC020DFC84C39A17B1525EB');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '14BC3C5E8EE0BD83ECA2241AE110B406');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '14C024656AC01DFD6F3F8CFAF91CD837');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '14C024D2E900DDE5E6120F5A412698CC');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '14C024F1273015DDEE86ECDDDD8D30A6');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5C774D602A0F90FF2C6B2117524A');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5C8DEE3082EAB7244C819422A2DA');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5C9E04301C0C8D1E875E7C269EAF');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5CAC18B016954A1FE6B35F30236E');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '150DBFAABEE0FE851C2B223345BC7DB2');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '150DC026265016F86E65293377222CDF');
INSERT INTO `t_fs_role_menu` VALUES ('13D54B59C650159A6A52ED136C16E235', '150DC07014A094604BEEA388C8263969');

-- ----------------------------
-- Table structure for `t_fs_role_menu_function`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_role_menu_function`;
CREATE TABLE `t_fs_role_menu_function` (
  `role_id` varchar(32) NOT NULL DEFAULT '',
  `menu_id` varchar(32) NOT NULL DEFAULT '',
  `fun_id` varchar(32) NOT NULL DEFAULT '',
  PRIMARY KEY (`role_id`,`menu_id`,`fun_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_role_menu_function
-- ----------------------------
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001001', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001001', 'icon-arrow-switch');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001001', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001001', 'icon-function');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001001', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001002', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001002', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001002', 'icon-function');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001002', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001002', 'icon-role');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001003', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001003', 'icon-cancel');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001003', 'icon-help');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001003', 'icon-save');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001004', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001004', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001004', 'icon-reload');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001004', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001004', 'icon-role');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001005', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001005', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0001005', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0002001', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0002001', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '0002001', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '13D743F542B01DC2B57C6B307B1702B2', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '13D743F542B01DC2B57C6B307B1702B2', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '13D743F542B01DC2B57C6B307B1702B2', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '14A8580062C061F2296D78B2521DC30F', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '14A8580062C061F2296D78B2521DC30F', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '14A8580062C061F2296D78B2521DC30F', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '14B76787AA60FAF8CCDA2523626466A0', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '14B76787AA60FAF8CCDA2523626466A0', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '14B76787AA60FAF8CCDA2523626466A0', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '14C024656AC01DFD6F3F8CFAF91CD837', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '14C301022C401062689F8B11CF15DC26', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '14C301022C401062689F8B11CF15DC26', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '14C301022C401062689F8B11CF15DC26', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5C774D602A0F90FF2C6B2117524A', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5C774D602A0F90FF2C6B2117524A', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5C774D602A0F90FF2C6B2117524A', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5C8DEE3082EAB7244C819422A2DA', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5C8DEE3082EAB7244C819422A2DA', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5C8DEE3082EAB7244C819422A2DA', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5C9E04301C0C8D1E875E7C269EAF', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5C9E04301C0C8D1E875E7C269EAF', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5C9E04301C0C8D1E875E7C269EAF', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5CAC18B016954A1FE6B35F30236E', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5CAC18B016954A1FE6B35F30236E', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150D5CAC18B016954A1FE6B35F30236E', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150DBFAABEE0FE851C2B223345BC7DB2', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150DBFAABEE0FE851C2B223345BC7DB2', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150DBFAABEE0FE851C2B223345BC7DB2', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150DC026265016F86E65293377222CDF', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150DC026265016F86E65293377222CDF', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150DC026265016F86E65293377222CDF', 'icon-remove');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150DC07014A094604BEEA388C8263969', 'icon-add');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150DC07014A094604BEEA388C8263969', 'icon-edit');
INSERT INTO `t_fs_role_menu_function` VALUES ('13D54B59C650159A6A52ED136C16E235', '150DC07014A094604BEEA388C8263969', 'icon-remove');

-- ----------------------------
-- Table structure for `t_fs_setter`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_setter`;
CREATE TABLE `t_fs_setter` (
  `id` varchar(32) NOT NULL DEFAULT '',
  `setter_name` varchar(255) DEFAULT NULL,
  `setter_value` varchar(2048) DEFAULT NULL,
  `setter_remark` varchar(4000) DEFAULT NULL,
  `create_user` varchar(32) DEFAULT NULL,
  `create_date` date DEFAULT NULL,
  `update_user` varchar(32) DEFAULT NULL,
  `update_date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_setter
-- ----------------------------
INSERT INTO `t_fs_setter` VALUES ('14A85885E49013E081431B7BAC22E07C', 'system.version.info', '版权所有：kaylves', '系统底部显示的版权信息', 'flymz', '2014-12-26', 'flymz', '2016-10-18');
INSERT INTO `t_fs_setter` VALUES ('14A85899DBD0204ACF4B3A34B319F174', 'system.name', '权限系统', '系统的名称', 'flymz', '2014-12-26', 'flymz', '2016-10-19');
INSERT INTO `t_fs_setter` VALUES ('14A85A857BA0E4BA8B62C55DD53DD73F', 'system.version', 'V1.0', '系统的版本号', 'flymz', '2014-12-26', 'flymz', '2014-12-26');
INSERT INTO `t_fs_setter` VALUES ('14A85ABD22408EBD20013AD392055124', 'login.page', 'login_blue', '系统的登录页面', 'flymz', '2014-12-26', 'flymz', '2015-03-18');
INSERT INTO `t_fs_setter` VALUES ('14A93A459DD01FCCD39383927C986FDE', 'default.main.page.url', 'index/portal.htm', '系统默认我的桌面页面URL', 'flymz', '2014-12-29', 'flymz', '2015-03-16');
INSERT INTO `t_fs_setter` VALUES ('14A93B3890F0CF664632811FAB5109C4', 'main.style', 'main', '后台管理主页样式，分为main/top_main', 'flymz', '2014-12-29', 'flymz', '2014-12-29');
INSERT INTO `t_fs_setter` VALUES ('14A93B5261408D4E4307F2088092FDD4', 'mobile.index.page', '/main.htm', '手机端登录系统的主页面', 'flymz', '2014-12-29', null, null);
INSERT INTO `t_fs_setter` VALUES ('14B76C3D5B0027D7194041035F1FD50D', 'apk.update.desc', '这个是汶南镇手机客户端的更新说明信息', 'apk更新说明信息', 'flymz', '2015-02-11', 'flymz', '2015-11-16');
INSERT INTO `t_fs_setter` VALUES ('14B76C4D195010E071560AE5261F6329', 'apk.update.newest.version', '1.1', '当前版本号', 'flymz', '2015-02-11', null, null);
INSERT INTO `t_fs_setter` VALUES ('14C2FF9F64001C0C3647F8C8C5B2B449', 'map.city', '汶南镇', '地图定位的城市', 'flymz', '2015-03-19', 'flymz', '2015-11-16');
INSERT INTO `t_fs_setter` VALUES ('14C360CC61B04237E1ED8810E9CB70AE', 'is.address.book.user', 'false', '通讯录的用户是否从用户表中获取', 'flymz', '2015-03-20', null, null);

-- ----------------------------
-- Table structure for `t_fs_user`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_user`;
CREATE TABLE `t_fs_user` (
  `user_id` varchar(32) NOT NULL DEFAULT '',
  `user_name` varchar(32) NOT NULL DEFAULT '',
  `sex` varchar(1) DEFAULT '',
  `dept_id` varchar(32) DEFAULT '',
  `duty_id` varchar(32) DEFAULT '',
  `user_pswd` varchar(32) DEFAULT '',
  `is_on_job` varchar(1) DEFAULT '0',
  `is_deleted` varchar(1) DEFAULT '0',
  `creator` varchar(32) DEFAULT '',
  `create_date` datetime DEFAULT '0000-00-00 00:00:00',
  `user_phone` varchar(255) DEFAULT NULL,
  `town` varchar(255) DEFAULT NULL,
  `village` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_user
-- ----------------------------
INSERT INTO `t_fs_user` VALUES ('flymz', '初五', '0', '', '14BC9BD6C14015A2CF0437924A14B2BA', 'e10adc3949ba59abbe56e057f20f883e', '0', '0', null, null, '123', null, null);

-- ----------------------------
-- Table structure for `t_fs_user_app`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_user_app`;
CREATE TABLE `t_fs_user_app` (
  `user_id` varchar(32) NOT NULL DEFAULT '',
  `app_id` varchar(32) NOT NULL DEFAULT '',
  `app_row` int(11) DEFAULT '0',
  `app_column` int(11) DEFAULT '0',
  PRIMARY KEY (`user_id`,`app_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_user_app
-- ----------------------------

-- ----------------------------
-- Table structure for `t_fs_user_role`
-- ----------------------------
DROP TABLE IF EXISTS `t_fs_user_role`;
CREATE TABLE `t_fs_user_role` (
  `role_id` varchar(32) NOT NULL DEFAULT '',
  `user_id` varchar(32) NOT NULL DEFAULT '',
  PRIMARY KEY (`role_id`,`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_fs_user_role
-- ----------------------------
INSERT INTO `t_fs_user_role` VALUES ('13D54B59C650159A6A52ED136C16E235', 'flymz');
