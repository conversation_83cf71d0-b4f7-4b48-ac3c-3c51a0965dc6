<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ID="{A1D1E026-6461-450F-96C1-3B1021B1AB9B}" Label="" LastModificationDate="1476861919" Name="security" Objects="120" Symbols="16" Target="MySQL 5.0" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="15.1.0.2850"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>A1D1E026-6461-450F-96C1-3B1021B1AB9B</a:ObjectID>
<a:Name>security</a:Name>
<a:Code>PhysicalDataModel_1</a:Code>
<a:CreationDate>1476861771</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861919</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=weixin_crm.sql
GenScriptName0=weixin_crm.sql
GenScriptName1=crebas.sql
GenScriptName2=oerAnal.sql
GenScriptName3=dataCenter.sql
GenScriptName4=crebas
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=E:\
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=Yes
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=Yes
Foreign key=Yes
Alternate key=Yes
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=E:\E管家平台报表\dataCenter\
GenDataSinglefile=Yes
GenDataScriptName=testdata.sql
GenDataScriptName0=testdata.sql
GenDataScriptName1=testdata
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
EnableRequirements=No
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=1
RefrUpdateConstraint=1
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE]

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Xsm]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=</a:ModelOptionsText>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>31174C29-EA62-4607-824F-BDA2AF1CE2F6</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>1476861771</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861771</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>B683F9F5-1E5E-46AA-A03C-2CA8F7226931</a:ObjectID>
<a:Name>security</a:Name>
<a:Code>PhysicalDiagram_1</a:Code>
<a:CreationDate>1476861715</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861907</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>7</a:PaperSource>
<c:Symbols>
<o:TableSymbol Id="o5">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((22041,490), (36046,9337))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o6"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o7">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((19930,21660), (30072,25659))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o8"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o9">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((3425,14338), (17429,25659))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o10"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o11">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-23405,21660), (-11719,25659))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o12"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o13">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-22246,-9234), (-9015,-4511))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o14"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o15">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-36048,-25657), (-24362,-21658))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o16"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o17">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-36048,4614), (-22817,9337))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o18"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o19">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-6514,-10883), (4787,-4511))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o20"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o21">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-20316,3789), (-9015,9337))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o22"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o23">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-6514,4614), (3808,9337))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o24"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o25">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((7288,-8510), (17430,-4511))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o26"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o27">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-9218,21660), (924,25659))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o28"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o29">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((6309,1315), (19540,9337))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o30"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o31">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((19931,-16657), (32004,-4511))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o32"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o33">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-36048,-9234), (-24747,-4511))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o34"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o35">
<a:CreationDate>1476861812</a:CreationDate>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-36048,21660), (-25906,25659))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o36"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o6">
<a:ObjectID>771F705C-3ADD-44A4-8E68-09E8CA1C4BBD</a:ObjectID>
<a:Name>t_fs_app</a:Name>
<a:Code>t_fs_app</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o37">
<a:ObjectID>850B99DC-4325-415F-95E3-06EC338D96C3</a:ObjectID>
<a:Name>app_id</a:Name>
<a:Code>app_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o38">
<a:ObjectID>EA219F39-D4DC-4794-B60E-D3C21728D060</a:ObjectID>
<a:Name>app_name</a:Name>
<a:Code>app_name</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o39">
<a:ObjectID>FE8277BE-857F-46BE-9C03-0A30B566A6FD</a:ObjectID>
<a:Name>app_link</a:Name>
<a:Code>app_link</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o40">
<a:ObjectID>435AB726-77F4-45F2-B015-C5A880AC645E</a:ObjectID>
<a:Name>app_height</a:Name>
<a:Code>app_height</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o41">
<a:ObjectID>3A099041-9F34-4D72-831B-15716ABCCD7A</a:ObjectID>
<a:Name>app_desc</a:Name>
<a:Code>app_desc</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(1024)</a:DataType>
<a:Length>1024</a:Length>
</o:Column>
<o:Column Id="o42">
<a:ObjectID>4D0CD463-72ED-44F6-9986-6FD17F7D4C45</a:ObjectID>
<a:Name>app_is_small</a:Name>
<a:Code>app_is_small</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o43">
<a:ObjectID>A18EC88A-75BC-460A-8E53-B48881034BE5</a:ObjectID>
<a:Name>app_icon</a:Name>
<a:Code>app_icon</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(48)</a:DataType>
<a:Length>48</a:Length>
</o:Column>
<o:Column Id="o44">
<a:ObjectID>177AE1D0-B533-4E72-9426-47FD230B362E</a:ObjectID>
<a:Name>app_creator</a:Name>
<a:Code>app_creator</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o45">
<a:ObjectID>30545FE8-4F23-4D86-804E-DD960248173E</a:ObjectID>
<a:Name>app_create_date</a:Name>
<a:Code>app_create_date</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0000-00-00 00:00:00</a:DefaultValue>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o46">
<a:ObjectID>9290EB4F-D199-427E-BB13-26D7432F786F</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o37"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o46"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o8">
<a:ObjectID>CA798D55-1464-405E-88DD-C8652DC6A12E</a:ObjectID>
<a:Name>t_fs_app_role</a:Name>
<a:Code>t_fs_app_role</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o47">
<a:ObjectID>A86A2F47-7BBE-465F-9D93-5EC940EF9773</a:ObjectID>
<a:Name>role_id</a:Name>
<a:Code>role_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o48">
<a:ObjectID>1738E3B4-414E-422D-B596-0EF5304F89E8</a:ObjectID>
<a:Name>app_id</a:Name>
<a:Code>app_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o49">
<a:ObjectID>FEA7D0C7-EE4A-4FDB-8374-AE22397078CC</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o47"/>
<o:Column Ref="o48"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o49"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o10">
<a:ObjectID>EF5828AF-E7C3-4D56-9F48-DCD832B8FCFD</a:ObjectID>
<a:Name>t_fs_dept</a:Name>
<a:Code>t_fs_dept</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o50">
<a:ObjectID>3847B4CE-1F3D-46E9-BC0E-14E41273DDD1</a:ObjectID>
<a:Name>dept_id</a:Name>
<a:Code>dept_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o51">
<a:ObjectID>1913A1DE-61EA-47CF-86E4-1610B8F5DB0E</a:ObjectID>
<a:Name>dept_name</a:Name>
<a:Code>dept_name</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o52">
<a:ObjectID>A8FE27FB-98F9-4110-A9CF-BF04832E46FE</a:ObjectID>
<a:Name>dept_phone</a:Name>
<a:Code>dept_phone</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o53">
<a:ObjectID>64E50287-4533-4277-8CD1-C1A6106C2903</a:ObjectID>
<a:Name>dept_fax</a:Name>
<a:Code>dept_fax</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o54">
<a:ObjectID>DC3708A2-12E3-4C9B-BC8A-E1609753E444</a:ObjectID>
<a:Name>dept_address</a:Name>
<a:Code>dept_address</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(512)</a:DataType>
<a:Length>512</a:Length>
</o:Column>
<o:Column Id="o55">
<a:ObjectID>D885DC85-62CE-42EB-97F6-2F3EA060ADEE</a:ObjectID>
<a:Name>p_dept_id</a:Name>
<a:Code>p_dept_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o56">
<a:ObjectID>F716A4D4-BFF2-4D37-B7CE-5F5BA8E1EB55</a:ObjectID>
<a:Name>dept_leader</a:Name>
<a:Code>dept_leader</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o57">
<a:ObjectID>E3F32AEE-5C70-45C9-90F0-D7FB575667AE</a:ObjectID>
<a:Name>dept_level</a:Name>
<a:Code>dept_level</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o58">
<a:ObjectID>9E19986D-FC6D-41DF-9497-A2B04583E97B</a:ObjectID>
<a:Name>dept_remark</a:Name>
<a:Code>dept_remark</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(2048)</a:DataType>
<a:Length>2048</a:Length>
</o:Column>
<o:Column Id="o59">
<a:ObjectID>D0C6F725-4C79-4EAA-8F8A-47054D96604A</a:ObjectID>
<a:Name>creator</a:Name>
<a:Code>creator</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o60">
<a:ObjectID>9C9F4E01-5879-423B-96B0-F0649DBF5A1F</a:ObjectID>
<a:Name>create_date</a:Name>
<a:Code>create_date</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0000-00-00 00:00:00</a:DefaultValue>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o61">
<a:ObjectID>0639876D-8174-42A3-9DFE-927945F5BA86</a:ObjectID>
<a:Name>dept_short_name</a:Name>
<a:Code>dept_short_name</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o62">
<a:ObjectID>AEB54101-2676-4398-B265-FE286C5F878D</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o50"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o62"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o12">
<a:ObjectID>00F83474-2ACC-40D4-8033-9FEF21438C2B</a:ObjectID>
<a:Name>t_fs_dictionary</a:Name>
<a:Code>t_fs_dictionary</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o63">
<a:ObjectID>3C5D09E9-2B7B-4CCE-8DD6-B6EA9871833A</a:ObjectID>
<a:Name>dic_id</a:Name>
<a:Code>dic_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o64">
<a:ObjectID>47E2E27D-A713-450E-817C-86293261D596</a:ObjectID>
<a:Name>dic_name</a:Name>
<a:Code>dic_name</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o65">
<a:ObjectID>43BFC47F-16FE-4465-8695-F10E0FAC6FD6</a:ObjectID>
<a:Name>create_date</a:Name>
<a:Code>create_date</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0000-00-00 00:00:00</a:DefaultValue>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o66">
<a:ObjectID>9BF48FAA-51AA-45E0-A67A-1EB07DD75F5B</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o63"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o66"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o14">
<a:ObjectID>83A689B3-2DB0-4F9A-AA9C-18626BD508AE</a:ObjectID>
<a:Name>t_fs_dictionary_value</a:Name>
<a:Code>t_fs_dictionary_value</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o67">
<a:ObjectID>C2C104C0-7341-4A41-BFE2-D45221BE7A95</a:ObjectID>
<a:Name>dic_value_id</a:Name>
<a:Code>dic_value_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o68">
<a:ObjectID>84AFD337-9C48-48CF-AC42-DCB3E9669727</a:ObjectID>
<a:Name>dic_value_label</a:Name>
<a:Code>dic_value_label</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o69">
<a:ObjectID>175BFE4B-A4E9-4DA7-9DF0-8FBF0FF80149</a:ObjectID>
<a:Name>dic_value_order</a:Name>
<a:Code>dic_value_order</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o70">
<a:ObjectID>27F30D09-E0B2-480C-9E97-FAAF1B0C8416</a:ObjectID>
<a:Name>dic_id</a:Name>
<a:Code>dic_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o71">
<a:ObjectID>BEFD03F9-8C7C-4513-80B8-A092BDFE4753</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o67"/>
<o:Column Ref="o70"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o71"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o16">
<a:ObjectID>DF563D05-4E0B-448B-90DA-1FD6CD5F11E3</a:ObjectID>
<a:Name>t_fs_duty</a:Name>
<a:Code>t_fs_duty</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o72">
<a:ObjectID>FC6CE0B1-D041-40AE-A324-E5BE60259EC8</a:ObjectID>
<a:Name>duty_id</a:Name>
<a:Code>duty_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o73">
<a:ObjectID>ED4D790F-7B67-4694-8A98-5A4E9EDB8125</a:ObjectID>
<a:Name>duty_name</a:Name>
<a:Code>duty_name</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o74">
<a:ObjectID>9BF8AB79-6377-46BD-B8B9-2359F3CBF42F</a:ObjectID>
<a:Name>duty_desc</a:Name>
<a:Code>duty_desc</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(2000)</a:DataType>
<a:Length>2000</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o75">
<a:ObjectID>5F760A58-0019-40A8-86CC-F7985C823086</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o72"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o75"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o18">
<a:ObjectID>44DE78AA-F805-438B-90FF-4948F81F86A0</a:ObjectID>
<a:Name>t_fs_login_logger</a:Name>
<a:Code>t_fs_login_logger</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o76">
<a:ObjectID>453D3A6E-784D-4544-8EBC-EA8F4C95A466</a:ObjectID>
<a:Name>id</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o77">
<a:ObjectID>C9619C0F-B070-4E71-AB0E-D2AAFC32E9BD</a:ObjectID>
<a:Name>user_id</a:Name>
<a:Code>user_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o78">
<a:ObjectID>408B79A0-B8AB-4776-B75D-B81769DB0269</a:ObjectID>
<a:Name>last_login_time</a:Name>
<a:Code>last_login_time</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o79">
<a:ObjectID>B891E543-3A5E-422B-A276-BC6399B87637</a:ObjectID>
<a:Name>login_ip</a:Name>
<a:Code>login_ip</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o80">
<a:ObjectID>04FA443E-7D3B-4151-8035-F59C4907F484</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o76"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o80"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o20">
<a:ObjectID>C0065513-DDFC-4046-A421-7CC4FCC79CBC</a:ObjectID>
<a:Name>t_fs_menu</a:Name>
<a:Code>t_fs_menu</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o81">
<a:ObjectID>7A9BE544-DE0F-4D1A-B0AE-B928E5E4D060</a:ObjectID>
<a:Name>menu_id</a:Name>
<a:Code>menu_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o82">
<a:ObjectID>F08FDCF9-E591-44F9-BEE7-84D33BC13C5A</a:ObjectID>
<a:Name>menu_name</a:Name>
<a:Code>menu_name</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o83">
<a:ObjectID>3F5914B7-7A0B-4B35-9A9F-3647B90382AF</a:ObjectID>
<a:Name>menu_order</a:Name>
<a:Code>menu_order</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o84">
<a:ObjectID>773CBF03-8958-4FCE-8FB8-DC2C612EA9A9</a:ObjectID>
<a:Name>menu_icon</a:Name>
<a:Code>menu_icon</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o85">
<a:ObjectID>1DE3FA18-DC32-49B8-88CB-1C0E9FFD32B3</a:ObjectID>
<a:Name>p_menu_id</a:Name>
<a:Code>p_menu_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o86">
<a:ObjectID>FC0834AD-E1CF-431A-B368-84716BD1469D</a:ObjectID>
<a:Name>menu_link</a:Name>
<a:Code>menu_link</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o87">
<a:ObjectID>E820E636-FC05-4FB1-94BA-326A26A144EE</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o81"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o87"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o22">
<a:ObjectID>92397154-CCB9-4B38-864D-8982949CB6DB</a:ObjectID>
<a:Name>t_fs_menu_function</a:Name>
<a:Code>t_fs_menu_function</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o88">
<a:ObjectID>1A1FFC1A-BA7E-4F6F-9868-EC3F4693B1D0</a:ObjectID>
<a:Name>fun_id</a:Name>
<a:Code>fun_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o89">
<a:ObjectID>B615C062-D0ED-40F7-A388-992D21613189</a:ObjectID>
<a:Name>menu_id</a:Name>
<a:Code>menu_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o90">
<a:ObjectID>9F05A26B-F87F-40F9-88E8-A848970C21E4</a:ObjectID>
<a:Name>fun_name</a:Name>
<a:Code>fun_name</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o91">
<a:ObjectID>2CC05A87-A47B-4308-BBA1-F444C345EECA</a:ObjectID>
<a:Name>fun_order</a:Name>
<a:Code>fun_order</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o92">
<a:ObjectID>A17BC0E8-E29C-40DC-837E-DE59998B0369</a:ObjectID>
<a:Name>fun_link</a:Name>
<a:Code>fun_link</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o93">
<a:ObjectID>3696780F-99C4-410F-A063-8D3B4B013417</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o88"/>
<o:Column Ref="o89"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o93"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o24">
<a:ObjectID>334DEFE4-8638-478C-B390-6B2B27A9871E</a:ObjectID>
<a:Name>t_fs_role</a:Name>
<a:Code>t_fs_role</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o94">
<a:ObjectID>C5C9367D-7944-4CE3-93AB-213903512FCE</a:ObjectID>
<a:Name>role_id</a:Name>
<a:Code>role_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o95">
<a:ObjectID>8FC9723B-1854-4E49-9606-B4CA1C514606</a:ObjectID>
<a:Name>role_name</a:Name>
<a:Code>role_name</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o96">
<a:ObjectID>1B29CA5E-AEAD-4A9F-83CE-D9908A699BD3</a:ObjectID>
<a:Name>role_remark</a:Name>
<a:Code>role_remark</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(512)</a:DataType>
<a:Length>512</a:Length>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>F66150F3-4707-408D-9F65-20BA3861C645</a:ObjectID>
<a:Name>is_deleted</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o26">
<a:ObjectID>D7E66FD6-43F5-4C5F-962C-148E1ECC5829</a:ObjectID>
<a:Name>t_fs_role_menu</a:Name>
<a:Code>t_fs_role_menu</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o98">
<a:ObjectID>C900F3E8-5119-453B-892D-721B27935854</a:ObjectID>
<a:Name>role_id</a:Name>
<a:Code>role_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o99">
<a:ObjectID>53A2957F-4E40-4D7E-8948-6E93505A1F26</a:ObjectID>
<a:Name>menu_id</a:Name>
<a:Code>menu_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o100">
<a:ObjectID>B44D7D2D-6BD7-4CE1-9276-6699415F5D35</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o98"/>
<o:Column Ref="o99"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o100"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o28">
<a:ObjectID>DCCD1101-A7B8-4A88-83A0-06FC6CDFC3EC</a:ObjectID>
<a:Name>t_fs_role_menu_function</a:Name>
<a:Code>t_fs_role_menu_function</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o101">
<a:ObjectID>D8A6A6D6-C4FD-4DC3-8706-13A3E786264E</a:ObjectID>
<a:Name>role_id</a:Name>
<a:Code>role_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o102">
<a:ObjectID>BDD85931-2143-4670-8F39-00922BB04DED</a:ObjectID>
<a:Name>menu_id</a:Name>
<a:Code>menu_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o103">
<a:ObjectID>6824C851-AB18-4627-8C2E-401E0393E17B</a:ObjectID>
<a:Name>fun_id</a:Name>
<a:Code>fun_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o104">
<a:ObjectID>90C5E132-72B6-4849-8B07-B6A5E37CD572</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o101"/>
<o:Column Ref="o102"/>
<o:Column Ref="o103"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o104"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o30">
<a:ObjectID>1E43A0CA-2261-4565-8992-40AB80854254</a:ObjectID>
<a:Name>t_fs_setter</a:Name>
<a:Code>t_fs_setter</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o105">
<a:ObjectID>65DCABFB-12C6-477D-98AC-134DDA9B9EFF</a:ObjectID>
<a:Name>id</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o106">
<a:ObjectID>CBB75E80-894D-40F9-BB83-7ED9D7177965</a:ObjectID>
<a:Name>setter_name</a:Name>
<a:Code>setter_name</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o107">
<a:ObjectID>51DD9DE7-26F3-440C-9969-99A8C0965B3F</a:ObjectID>
<a:Name>setter_value</a:Name>
<a:Code>setter_value</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>varchar(2048)</a:DataType>
<a:Length>2048</a:Length>
</o:Column>
<o:Column Id="o108">
<a:ObjectID>42014EEC-6A86-4D50-9459-F00EFBDCF3AA</a:ObjectID>
<a:Name>setter_remark</a:Name>
<a:Code>setter_remark</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o109">
<a:ObjectID>D9FB142A-9730-4A0E-A3AA-D370FEB864A6</a:ObjectID>
<a:Name>create_user</a:Name>
<a:Code>create_user</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o110">
<a:ObjectID>0516CD99-C6D4-4897-B2E8-335871ADF9F0</a:ObjectID>
<a:Name>create_date</a:Name>
<a:Code>create_date</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>date</a:DataType>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>09297E6E-1421-4F6E-B3B1-56C6CB9685CD</a:ObjectID>
<a:Name>update_user</a:Name>
<a:Code>update_user</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>0C3DB2C7-B9DF-4142-A65E-DDF43A681D9C</a:ObjectID>
<a:Name>update_date</a:Name>
<a:Code>update_date</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>date</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o113">
<a:ObjectID>1E8F44A3-C055-4FFE-8224-40ADE4179D15</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o105"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o113"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o32">
<a:ObjectID>C23D520F-17E4-43B0-82CA-5F44EFEA0138</a:ObjectID>
<a:Name>t_fs_user</a:Name>
<a:Code>t_fs_user</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o114">
<a:ObjectID>3C7DD00E-F19E-42E5-81D2-674170D37623</a:ObjectID>
<a:Name>user_id</a:Name>
<a:Code>user_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o115">
<a:ObjectID>86BD2F4B-5485-4818-8BE3-087F4518279E</a:ObjectID>
<a:Name>user_name</a:Name>
<a:Code>user_name</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o116">
<a:ObjectID>14B560FE-CA3A-46F9-A569-D752B8EAF8B3</a:ObjectID>
<a:Name>sex</a:Name>
<a:Code>sex</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o117">
<a:ObjectID>673BB996-99EE-4D3D-9F9A-C65D1D278E1C</a:ObjectID>
<a:Name>dept_id</a:Name>
<a:Code>dept_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o118">
<a:ObjectID>5E14769B-6DC1-45DA-BA2C-F4AC9E58608D</a:ObjectID>
<a:Name>duty_id</a:Name>
<a:Code>duty_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o119">
<a:ObjectID>E2393BCF-9B84-464E-A553-1B5922FD85E6</a:ObjectID>
<a:Name>user_pswd</a:Name>
<a:Code>user_pswd</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o120">
<a:ObjectID>44EDB3B7-1D0E-489E-8D2C-CA65658F9D8F</a:ObjectID>
<a:Name>is_on_job</a:Name>
<a:Code>is_on_job</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o121">
<a:ObjectID>47884E12-7A37-4731-A804-68345F2CDE93</a:ObjectID>
<a:Name>is_deleted</a:Name>
<a:Code>is_deleted</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o122">
<a:ObjectID>A81E84C6-E3BE-4963-9CC9-72E7C9D3CEB9</a:ObjectID>
<a:Name>creator</a:Name>
<a:Code>creator</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o123">
<a:ObjectID>F0186F46-C88B-4BB3-8A9E-8BD5A6444758</a:ObjectID>
<a:Name>create_date</a:Name>
<a:Code>create_date</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0000-00-00 00:00:00</a:DefaultValue>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>C5733DED-F8F8-4943-ADDE-29C043C76D46</a:ObjectID>
<a:Name>user_phone</a:Name>
<a:Code>user_phone</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>110280B4-011F-4DCE-9DD7-C37B5CA1FF2A</a:ObjectID>
<a:Name>town</a:Name>
<a:Code>town</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o126">
<a:ObjectID>196FCC2F-DB0E-4607-86BE-0A211344F41C</a:ObjectID>
<a:Name>village</a:Name>
<a:Code>village</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>NULL</a:DefaultValue>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o127">
<a:ObjectID>FD37D5DE-ABCC-4E8C-BF23-A73D8D0D2055</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o114"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o127"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o34">
<a:ObjectID>7000B3AF-B6A7-4C39-A2B4-AC8466E2CB83</a:ObjectID>
<a:Name>t_fs_user_app</a:Name>
<a:Code>t_fs_user_app</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o128">
<a:ObjectID>67F106B3-AAC7-4A36-84B6-CF9B74FF578D</a:ObjectID>
<a:Name>user_id</a:Name>
<a:Code>user_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o129">
<a:ObjectID>BA9FB55E-C639-4106-BBB2-FA3257EBD82C</a:ObjectID>
<a:Name>app_id</a:Name>
<a:Code>app_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o130">
<a:ObjectID>EA62ED5B-2F50-4279-A2FC-86E08AB16275</a:ObjectID>
<a:Name>app_row</a:Name>
<a:Code>app_row</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o131">
<a:ObjectID>967B7398-55EB-4DA2-93F8-CE79B8F59FF3</a:ObjectID>
<a:Name>app_column</a:Name>
<a:Code>app_column</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o132">
<a:ObjectID>A3C8CB7D-52FB-4E0F-9EAB-0C4C4C71F944</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o128"/>
<o:Column Ref="o129"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o132"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o36">
<a:ObjectID>6AACD283-F458-41DA-847B-9F058DD40B53</a:ObjectID>
<a:Name>t_fs_user_role</a:Name>
<a:Code>t_fs_user_role</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:PhysicalOptions>ENGINE=MyISAM DEFAULT CHARSET=utf8</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o133">
<a:ObjectID>F85976BD-7CE0-4AB6-B552-6A0A6A9B0F8F</a:ObjectID>
<a:Name>role_id</a:Name>
<a:Code>role_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o134">
<a:ObjectID>56628666-957C-4C14-9664-1AEFC7CC9172</a:ObjectID>
<a:Name>user_id</a:Name>
<a:Code>user_id</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o135">
<a:ObjectID>4596F9CE-2AD2-41BF-818C-2A7A814C6BBA</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1476861812</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861844</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o133"/>
<o:Column Ref="o134"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o135"/>
</c:PrimaryKey>
</o:Table>
</c:Tables>
<c:DefaultGroups>
<o:Group Id="o136">
<a:ObjectID>97EE219C-B619-4E5B-90E2-FA9B7159B0B6</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1476861714</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861714</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o137">
<a:ObjectID>CE9441C5-C79C-4377-84F1-D99FAA1BCC56</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>1476861771</a:CreationDate>
<a:Creator>kaylves</a:Creator>
<a:ModificationDate>1476861771</a:ModificationDate>
<a:Modifier>kaylves</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/mysql50.xdb</a:TargetModelURL>
<a:TargetModelID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>