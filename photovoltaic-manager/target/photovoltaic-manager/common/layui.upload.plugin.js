/***
 * 
 * layui upload 模块插件
 * 
 *
 */
(function($){
	//$.fn.layuiUpload
	$.layuiUpload = function(options) {
		var uploadFileaName = new Array();
		var realFileaName = new Array();
		var setting = $.extend({
			//选择需要上传文件的按钮
			elem : (options.elem || 'btnUpload') ,
			//图片显示区
			showElem :(options.showElem || 'demo') , 
			url : (options.url || 'file/upload.web') ,
			//错误 信息提示区
			showTextElem :(options.showTextElem || 'demoText') , 
			//上传文件成功后的文件名称(多文件以逗号分隔)  存在真实文件名称的问题      例如：格式 --> 真实名1;上传之后名称1,真实名2;上传之后名称2 或
			fileName : (options.fileName || 'uploadFileName')  ,
			realFileName : (options.realFileName || 'realFileName')  ,
			//请求上传接口的额外参数
			data : (options.data || {}) ,
            // 限制文件上传数量(多个文件上传)
            limitNum : (options.limitNum || 6) ,
			//是否是上传
			//isUpload : true ,
			//是否只是展示图片
			//isShow : false ,
			//设定文件域的字段名
			field : 'file' ,
			//是否允许多文件上传。设置 true即可开启。不支持ie8/9
			multiple : false ,
			//是否多文件列表形式
			isList : false ,
			//允许上传的文件类型            指定允许上传的文件类型，可选值有：images（图片）、file（所有文件）、video（视频）、audio（音频）
			accept : 'file',                            
			//最大允许上传的文件大小
			size : 50 * 1024 ,
			// 将每次选择的文件追加到文件队列
			files :null ,
			// 异步
            async : true,
			
			/**************************成功回调开始*******************************/
			//多文件列表式 成功回调       【可重写】
			doneMultipleListUpload : function(res, index, upload){
                //console.log("上传文件返回值 \n " + JSON.stringify( res ));
				if( res.code == 0 ){ //上传成功
			        var tr = $('#' + setting.showElem).find('tr#upload-'+ index);
			        var tds = tr.children();
			        tds.eq(2).html('<span style="color: #5FB878;">上传成功</span>');
			        tds.eq(3).html(''); //清空操作
			        //delete files[index]; //删除文件队列已经上传成功的文件
			        //  delete setting.files[index];
			        //针对文件名称处理 ,返回的是 uploadFileName , realFileName 与 input的隐藏id和name名称
                    if(document.getElementById(setting.fileName)) {
                        uploadFileaName.push(res.reModel.uploadFileName);
                        $("#" + setting.fileName).val(uploadFileaName.join(","));
                    }
                    if(document.getElementById(setting.realFileName)) {
                        realFileaName.push(res.reModel['realFileName']);
                    	$("#" + setting.realFileName).val(realFileaName.join(","));
                    }
                    //console.log("真实文件名称 \n" + JSON.stringify(realFileaName) + "\n" + realFileaName.join(",") );
                    //console.log("上传文件名称 \n" + JSON.stringify(uploadFileaName)+ "\n" + uploadFileaName.join(",") );
			        //执行结束方法
			        return delete setting.files[index];
			    }
				//执行错误方法
			    this.error(index, upload);
			} ,
			//多文件非列表 成功回调    【可重写】
			doneMultipleNotListUpload :function (res, index, upload) {
				
			} ,
			//单个普通文件上传 成功回调 【可重写】
			doneSingleUpload : function(res, index, upload){
				
			} ,
			//执行上传请求后的回调。返回三个参数，分别为：res（服务端响应信息）、index（当前文件的索引）、upload（重新上传的方法，一般在文件上传失败后使用）
			done : function(res, index, upload) {
				if(setting.multiple ){
					if (setting.isList) {
						setting.doneMultipleListUpload(res, index, upload);
					} else {
						setting.doneMultipleNotListUpload(res, index, upload) ;
					}
				} else {
					setting.doneSingleUpload(res, index, upload);
				}
			} ,
			/**************************成功回调结束*******************************/
			
			/**************************失败回调开始*******************************/
			//多文件列表式 失败回调 【可重写】
			errorMultipleListUpload : function (index , upload) {
				  var tr = $('#' + setting.showElem).find('tr#upload-' + index)
			      ,tds = tr.children();
			      tds.eq(2).html('<span style="color: #FF5722;">上传失败</span>');
			      tds.eq(3).find('.' +  setting.showElem + '-reload').removeClass('layui-hide'); //显示重传
			} ,
			//多文件非列表 失败回调  【可重写】
			errorMultipleNotListUpload :function ( index, upload) {
				
			} ,
			//单个普通文件上传 失败回调 【可重写】
			errorSingleUpload : function( index, upload){
				//演示失败状态，并实现重传 普通单个文件
				var demoText = $('#' + setting.showTextElem );
				demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini ' + setting.showElem +'-reload">重试</a>');
				demoText.find('.' + setting.showElem +'-reload').on('click', function(){
					layuiUploadInst.upload();
				});
			} ,
			//执行上传请求出现异常的回调（一般为网络异常、URL 404等）。返回两个参数，分别为：index（当前文件的索引）、upload（重新上传的方法）
			error : function(index , upload) {
				if(setting.multiple ){
					if( setting.isList ){
						 setting.errorMultipleListUpload(index , upload ) ;
					} else {
						//多文件非列表形式
						setting.errorMultipleNotListUpload(index , upload ) ;
					}
				} else {
					setting.errorSingleUpload(index , upload ) ;
				}
			} ,
			/**************************失败回调结束*******************************/
			
			
			/**************************选择回调开始*******************************/
			//多文件列表式 选择文件回调 【可重写】
			chooseMultipleListUpload : function ( obj ) {
				 //将每次选择的文件追加到文件队列
			     //var files = obj.pushFile();
			     //setting.files = obj.pushFile();
                var files = setting.files = obj.pushFile();
    	         //读取本地文件
    	         obj.preview(function(index, file, result){
	    	        var tr = $(['<tr id="upload-'+ index +'">'
	    	          ,'<td>'+ file.name +'</td>'
	    	          ,'<td>'+ (file.size/1014).toFixed(1) +'kb</td>'
	    	          ,'<td>等待上传</td>'
	    	          ,'<td>'
	    	            ,'<button class="layui-btn layui-btn-mini ' + setting.showElem +'-reload layui-hide">重传</button>'
	    	            ,'<button class="layui-btn layui-btn-mini layui-btn-danger ' + setting.showElem +'-delete">删除</button>'
	    	          ,'</td>'
	    	        ,'</tr>'].join(''));
	    	        
	    	        //单个重传
	    	        tr.find('.' + setting.showElem +'-reload').on('click', function(){
	    	          obj.upload(index, file);
	    	        });
	    	        
	    	        //删除
	    	        tr.find('.' + setting.showElem +'-delete').on('click', function(){
	    	          delete files[index]; //删除对应的文件
	    	          //delete setting.files[index]; //删除对应的文件
	    	          tr.remove();
                      //setting.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
	    	        });
	    	        $('#' + setting.showElem).append(tr);
    	       });
			} ,
			//多文件非列表 选择文件回调  【可重写】
			chooseMultipleNotListUpload :function ( obj ) {
				//多文件非列表形式
	    		obj.preview(function(index , file , result) {
	    			$('#' + setting.showElem).append('<img src="'+ result +'" alt="'+ file.name +'" class="layui-upload-img">')
	    		});
			} ,
			//单个普通文件上传 选择文件回调 【可重写】
			chooseSingleUpload : function( obj ){
				//单个文件
		        obj.preview(function(index , file , result){
		          $('#' + setting.showElem ).attr('src', result); //图片链接（base64）
		        }); 
			} ,
			//选择文件的回调
			choose: function(obj){
			    //多文件上传
			    if( setting.multiple ){
			    	//列表形式
			    	if(setting.isList){
			    		 setting.chooseMultipleListUpload(obj);
			    	} else {
			    		setting.chooseMultipleNotListUpload(obj);
			    	}
			    } else {
			    	setting.chooseSingleUpload(obj) ;
			    }
		   },
		   /**************************选择回调结束*******************************/
		   //文件上传前的回调
		   before: function(obj){ //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
               //多文件上传
               /*if( setting.multiple ){
                   //列表形式
                   if(setting.isList){
                       setting.chooseMultipleListUpload(obj);
                   } else {
                       setting.chooseMultipleNotListUpload(obj);
                   }
               } else {
                   setting.chooseSingleUpload(obj) ;
               }*/
			   	/*//多文件上传
			    if( setting.multiple ){
			    	alert(1111);
			    	//非列表形式
			    	setting.chooseMultipleNotListUpload(obj);
			    } else {
			    	setting.chooseSingleUpload(obj) ;
			    }*/
               if( !setting.multiple ){
                   setting.chooseSingleUpload(obj) ;
               }
		   }
		},options||{});
		//加载upload模块
		layui.use('upload', function(){
			  window.layuiUpload = layui.upload;
			  //console.log(JSON.stringify(setting));
			  //执行实例
			  window.layuiUploadInst = layuiUpload.render( setting );
			  
		});
		return this;
	};
})(jQuery)
