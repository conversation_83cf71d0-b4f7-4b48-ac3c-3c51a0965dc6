<div class="kit-table">
    <form class="layui-form" lay-filter="kit-search-form">
        <div class="kit-table-header">
            <div class="kit-search-btns">
                <a href="javascript:;" class="layui-btn layui-btn-small">新增</a>
                <a href="javascript:;" class="layui-btn layui-btn-small layui-btn-normal">新增</a>
                <a href="javascript:;" class="layui-btn layui-btn-small layui-btn-warm">新增</a>
                <a href="javascript:;" class="layui-btn layui-btn-small layui-btn-danger">新增</a>
            </div>
            <div class="kit-search-inputs">
                <div class="kit-search-keyword">
                    <input type="text" class="layui-input" name="keyword" placeholder="搜索关键字.." />
                    <button lay-submit lay-filter="search"><i class="layui-icon">&#xe615;</i></button>
                </div>
                <div class="kit-search-more" id="kit-search-more">更多筛选<i class="layui-icon">&#xe61a;</i></div>
            </div>
        </div>
        <div class="kit-search-mored layui-anim layui-anim-upbit">
            <div class="kit-search-body">
                <div class="layui-form-item">
                    <label class="layui-form-label">输入框</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" placeholder="请输入标题" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">验证手机</label>
                        <div class="layui-input-inline">
                            <input type="tel" name="phone" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">验证邮箱</label>
                        <div class="layui-input-inline">
                            <input type="text" name="email" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">范围</label>
                        <div class="layui-input-inline" style="width: 100px;">
                            <input type="text" name="price_min" placeholder="￥" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline" style="width: 100px;">
                            <input type="text" name="price_max" placeholder="￥" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">单行选择框</label>
                    <div class="layui-input-block">
                        <select name="interest" lay-filter="aihao">
                            <option value=""></option>
                            <option value="0">写作</option>
                            <option value="1" selected="">阅读</option>
                            <option value="2">游戏</option>
                            <option value="3">音乐</option>
                            <option value="4">旅行</option>
                          </select>
                    </div>
                </div>
            </div>
            <div class="kit-search-footer">
                <button type="reset" class="layui-btn layui-btn-small layui-btn-primary kit-btn">重置</button>
                <button lay-submit lay-filter="search" class="layui-btn layui-btn-small kit-btn">确定</button>
            </div>
        </div>
    </form>
    <div class="kit-table-body">
        <table id="demo" lay-filter="demo"></table>
        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-mini" lay-event="detail">查看</a>
            <a class="layui-btn layui-btn-mini" lay-event="edit">编辑</a>
            <a class="layui-btn layui-btn-danger layui-btn-mini" lay-event="del">删除</a>
        </script>
    </div>
</div>