
/**
 * 初始化左侧菜单功能
 */
function InitLeftMenu() {
    var menulist = "";
    for(var i = 0;i < _menus.length;i++){
		var children = _menus[i].children;
		menulist += "<li class=\"layui-nav-item\">";
		menulist += "<a class=\"javascript:;\" href=\"javascript:;\">";
		menulist += "<i class=\"fa "+ _menus[i].menuIcon +"\" aria-hidden=\"true\"></i>&nbsp;&nbsp;&nbsp;<span>"+ _menus[i].menuName + "</span>";
		//menulist += "<i class=\"fa fa-archive\" aria-hidden=\"true\"></i><span style=\"position:relative;display:inline-block;top:3px;padding-right:8px;padding-left:8px;background:url('images/menuicons/"+ _menus[i].menuIcon + "');background-repeat:no-repeat;background-position-y:80%;\"></span><span>"+ _menus[i].menuName + "</span>";
		//menulist += "<image src=\"images/menuicons/"+ _menus[i].menuIcon + "\" />&nbsp;&nbsp;&nbsp;<span>"+ _menus[i].menuName + "</span>";
		menulist += "</a>";
		menulist += "<dl class=\"layui-nav-child\" >";
		for(var j = 0;j < children.length;j++){
			menulist += "<dd  style=\"padding-left:20px;\">";
			menulist += "<a href=\"javascript:;\"";
			menulist += "kit-target data-options=\"{url:'" + children[j].menuLink + "?leftMenuId="+ children[j].menuId +"',";
			menulist += "icon:'" + children[j].menuIcon + "',"; //可变动的图标
			menulist += "title:'"+ children[j].menuName + "',";
			menulist += "id:'" + i + "" + j + "'}\">"; //可变动的id
		    menulist += "<i class=\"fa " ;
			menulist +=  children[j].menuIcon ;
			menulist += "\">";
			menulist += "</i>";
			menulist += "&nbsp;&nbsp;<span>" + children[j].menuName +"</span></a>";
			menulist += "</dd>";
		}
		menulist += " </dl>";
		menulist += "</li>";
    }
    $("#mainMenu").append(menulist);
}