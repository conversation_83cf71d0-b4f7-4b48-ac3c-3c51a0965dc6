
(function() {
	 "use strict";
	/**
	 * 数据展示构造函数  layptl 和 table
	 * @param options 参数
	 * @constructor 
	 */
	window.DataList = function( options ) {
		
		var _this = this;
		
		this.currpage = 1 ;                      //获取当前第几页
		
	    this.checkboxData = new Array() ;        //列表中 checkbox被选中的数据
	    
		/**
		 * 初始化设置
		 */
		this.config = {
			    defaultTableId : null, 								                //渲染table元素id
			    id : null,											                //lay-filter属性值
			    method: (this.method || 'post') ,                                   //this.method != null && this.method != '' ? this.method : 'post' ,
			    cols : null ,                                                       //显示列表数据
			    isTable : (this.isTable || true) ,					                //是否渲染列表
			    
			    url : null,                                                         //请求url                                        【公共】
			    limit: ( this.limit || 10) ,                                        //每页多少条记录                                                                                                   【公共】
			    selectFormToPager : false ,							                //点击分页功能上下页或跳转页　是否带上查询条件 false | true    【公共】
			    formName : (this.formName || 'formname') ,			                //查询表单id										【公共】
			    confirmInfo : ( this.confirmInfo || 'Êtes-vous sûr de vouloir supprimer?') ,                 //删除提示语										【公共】
			    sreachBtn : (this.sreachBtn || 'sreach') ,			                //查询按钮                                                                                                                【公共】
			    listLoadTip : (this.listLoadTip || 'Les données sont en cours de chargement...') ,               //列表加载提示                                                                                                          【公共】
			    layout : (this.layout || ['prev', 'page', 'next', 'limit', 'skip' , 'count']) , //分页展示效果 							【公共】
			    pager: (this.pager || 'page') , 					                //分页DOM元素id									【公共】
			    isPager : (this.isPager || true ) ,					                //数据列表是否分页									【公共】
			    even : (this.even ||  true ) ,						                //隔行背景色
			    //初始化
			    listener:function()	{										//form表单监听
			    	
			    },
			    /** 模板引擎获取被选中数据方式 */
			    dataType : (this.dataType || 1 ) ,									//取值　1 或 2 
			    isLaytpl: (this.isLaytp || false ) ,                                //是否模板引擎渲染
			    laytplPutextra : {} ,												//附加信息模板引擎渲染
			    laytplTemplet : (this.laytplTemplet || 'listDataTemplet') ,			//模板
			    dataKey : (this.dataKey || '') ,						            //模板取数据列表key
			    laytplAppendHtml : (this.laytplAppendHtml || 'dataList') ,			//模板数据展示
			    //必须模板引擎加载完成才能获取得到DOM元素
			    laytplCheckBoxInfo : function() {
					$("input[name^='" + _this.config.defaultTableId + "_check']").click(function(){
						_this.checkboxData = [] ;
						//被选中的行数
						var checkLen = $("input[name^='" + _this.config.defaultTableId + "_check']:checked").length;
						//当前页总行数
						var trLen = $("#" + _this.config.laytplAppendHtml).children().length;
						//选中数量与行数进行比较
						if(trLen == checkLen){
							$("#" + _this.config.defaultTableId + "ChooseAll").prop("checked",true);
						} else {
							$("#" + _this.config.defaultTableId + "ChooseAll").prop("checked",false);
						}
						//获取被选中的值
						$("input[name^='" + _this.config.defaultTableId + "_check']:checked").each(function(obj){
							
							var objec = {} ;
							//获取被选中的行
							var trName = this.value;
							if(!isNull( _this.config.dataType ) && ( _this.config.dataType == 2) ) {
								var data = $("tr[id='" + _this.config.defaultTableId + "_tr_" + trName + "']").attr("data-obj");
								//由JSON字符串转换为JSON对象
								objec = eval('(' + data + ')');
							} else {
								$("tr[id='" + _this.config.defaultTableId + "_tr_" + trName + "']").children().each(function(i,o) {
									var fieldValue = $(this).attr("data-field");
									if(!isNull(fieldValue)){
										var field = fieldValue.split("_")[0];
										var value = fieldValue.split("_")[1];
										if(value == "null" || value == null || value.trim() == ''){
											objec[field] = "";
										} else {
											objec[field] = value ;
										}
									}
								});
							}
							//存储数据
							_this.checkboxData.push(objec);
						});
						//console.log("模板引擎勾选后的值 \n " + JSON.stringify(_this.checkboxData));
					});
				} ,
			    laytplData : function ( data ) { 
			    	//模板引擎数据渲染
			    	if(!isNull(data)){
						if (data.rec == 'SUC') {
							//reModel 是后台map结构数据  data属性是必须是map 中   key ， 格式固定
							
							var rows = data.reModel.data;
							/** 当reMoel存放Map时，则 dataKey为 map中 key */
							if(null != _this.config.dataKey && '' != _this.config.dataKey){
								rows = data.reModel[_this.config.dataKey];
							}
							var getTpl = $("#" + this.laytplTemplet).html();
							var view = $("#" + this.laytplAppendHtml );
							com.ymx.layui.public.core.laytpl(getTpl).render( rows , function(html){
								view.html(html);
								_this.config.laytplCheckBoxInfo();
							});
			            } else {
			            }
					}else{
					   console.log("数据解析异常");
					}
			    } ,
			    /** 列表右侧操作 查看 */
			    detail : function(data){
			    	console.log(JSON.stringify(data));
			    } ,
			    /** 列表右侧操作 编辑 */
			    edit : function(data){
			    	console.log(JSON.stringify(data));
			    } ,
			    /** 列表右侧操作 删除 */
			    del : function(data){
			    	console.log(JSON.stringify(data));
			    } ,
			    /** 列表右侧操作 扩展方法(备用) */
			    extra :function(data) {
			    	console.log(JSON.stringify(data));
			    } ,
			    /** 列表右侧操作 扩展方法(备用) */
			    other :function(data) {
			    	console.log(JSON.stringify(data));
			    }
		} 
		
		//分页工具
		this.pageTools = function(data , isFlag) {
			var _this = this;
			//存储当前页码
		   	 this.currpage  = data.pageInfo.pageNo;    //后台返回当前第几页数
		   	 //总记录数
		   	 var total = data.count;
		   	 //获取查询条件
		   	 var whereFormParam = formSerialize( this.config.formName );
			 //分页渲染功能
		   	 com.ymx.layui.public.core.laypage.render({
				       elem: this.config.pager            //分页DOM元素 　config中配置　
				      ,count: total                        //总共多少条记录
				      ,limit : data.pageInfo.pageSize	   //每页多少条记录  后台传递过来
				      ,curr: data.pageInfo.pageNo          //当前页数             后台传递过来
				      ,layout: this.config.layout 
				      ,jump: function(obj , first){
				    	  
				    	  /** 先清空列表checkbox中的选中值 **/
				    	  this.checkboxData = [];
				    	  
				    	  /*** 列表点击分页操作是否增加查询条件功能  */
				    	  var whereParas  = { 
				    			  pageNo : obj.curr ,  //固定值,不做任何修改 
				    			  pageSize : obj.limit //固定值,不做任何修改
				    	  } ;
				    	  //分页是否需要查询条件
				    	  if( _this.config.selectFormToPager) {
				    		  whereParas = $.extend( true , whereParas , whereFormParam );
				    	  } 
				    	  //console.log(JSON.stringify( whereParas ));
				           //首次不执行
						   if(!first){
							   if(isFlag == 'laytpl') {
								   //模板引擎
								   _this.config.laytplPutextra = $.extend( true , _this.config.laytplPutextra , whereParas );
								   _this.laytplShow();
								   
							   } else {
								   //table方法级渲染
								   com.ymx.layui.public.core.table.reload( _this.config.defaultTableId , {   //参数值　config中配置　
									   where: whereParas
								   });
							   }
						   }
				      }
				});
		}
		
		
		$.extend(true ,  this.config , options  );
		
		this.laytplShow = function () {
	    	var _this = this;
	    	/** 取消全选状态 */
	    	$("#" + _this.config.defaultTableId + "ChooseAll").prop("checked",false);
	    	
	    	doPost( _this.config.url , null , _this.config.laytplPutextra , function( data ) {
	    		
	    		//数据展示处理
		    	_this.config.laytplData(data);

		    	 //是否分页 默认true
		    	 if( _this.config.isPager ) {
		    		 
		    		 _this.pageTools(data , 'laytpl');
		    	 }
			}, function(data){});
	    } 
		
		this.init( this.config );
	};

	/**
	 * 创建DataList对象
	 * @param options
	 */
	DataList.create = function ( options ) {
		return new DataList( options );
	};
	
	
	/**
	 * 初始化
	 */
	DataList.prototype.init = function( options) {
		var _this = this;
		layui.config({
			base: 'common/build/js/'
		}).use([ 'laypage', 'layer', 'table', 'form' , 'laytpl', 'formSelects'] , function() {
			com.ymx.layui.public.core.laypage = layui.laypage     //分页
			, com.ymx.layui.public.core.layer = layui.layer       //弹层
			, com.ymx.layui.public.core.table = layui.table       //表格
			, com.ymx.layui.public.core.form = layui.form         //表单
			, com.ymx.layui.public.core.laytpl = layui.laytpl     //模板引擎
			, com.ymx.layui.public.core.element = layui.element  //元素操作
			, com.ymx.layui.public.core.formSelects = layui.formSelects;
			//layer是独立,是全局使用(此方法外，建议加上命名空间)
			if( options.isTable || options.isLaytpl ) {
				layer.msg( options.listLoadTip );
			}
			
			/**************************************** table 渲染 开始 ***********************************************************************/
				if( options.isTable ) {
					//执行渲染
					com.ymx.layui.public.core.table.render({
						  //指定原始表格元素选择器（推荐id选择器）
						  elem: options.defaultTableId == null || options.defaultTableId == '' ?  '#showTableData' : '#' + options.defaultTableId ,
						  id:  options.id == null || options.id == '' ?  options.defaultTableId :  options.id , //( _this.config.id || _this.config.defaultTableId || 'showTableData')  , //lay-filter属性值
						  url: options.url ,            	//请求数据列表url
						  method: options.method ,      	// get | post
						  skin: options.skin ,          	//表格风格
						  even : options.even ,
						  response: {
							 msgName: 'errMsg' ,			//错误信息提示
						     dataName: 'reModel'            //数据 
						  } ,   
						  request: {
							  pageName: 'pageNo' ,         //页码的参数名称，默认：page
							  limitName: 'pageSize'        //每页数据量的参数名，默认：limit
						 } ,
						 
						 limit: options.limit ,		       //每页显示多少记录
						 
						 cols : options.cols  ,            //动态设置表头
						 
						 //完成方法
					     done: function(res, curr, total){
					    	 //分页
					    	 if( options.isPager ){
					    		 _this.pageTools(res , 'table');
					    	 }
						}
					});
					 
					/**  列表数据绑定操作功能 (查看|编辑|删除)  */
					com.ymx.layui.public.core.table.on('tool('+ options.defaultTableId +')', function(obj){   //注：table原始容器的属性 lay-filter="对应的值"
						  var data = obj.data;       //获得当前行数据  【 标记1】
						  var layEvent = obj.event;  //获得 lay-event 对应的值
						  var tr = obj.tr;           //获得当前行 tr 的DOM对象
						  if(layEvent === 'detail'){ 
							   //查看
							  _this.config.detail(data);   //注意 ：data 值指向的是 obj.data值  【 标记1】
						       
						  } else if(layEvent === 'del'){ 
							  //删除
						      layer.confirm( options.confirmInfo , function(index){
						         obj.del();          //删除对应行（tr）的DOM结构
						         layer.close(index);
						         //向服务端发送删除指令
						         options.delete(data);
						     });
						  } else if(layEvent === 'edit'){ 
							   //编辑
							  options.edit(data);
						 } else if(layEvent === 'other'){ 
							 //附加其他操作功能
							 options.other(data);
						 } else if(layEvent === 'extra'){ 
							  //扩展功能
							 options.extra(data);
						 }
					});
					/** 列表勾选数据 **/
					com.ymx.layui.public.core.table.on('checkbox(' + options.defaultTableId + ')', function(obj){
						console.log("勾选  \n " + JSON.stringify(obj));
						if(obj.type == 'all') {
							if(obj.checked){
								
							} else {
								_this.checkboxData = [] ;
							}
						} else {
							if(obj.checked){
								if( _this.checkboxData.indexOf(obj.data) == -1){
									_this.checkboxData.push(obj.data);
								}
							} else {
								_this.checkboxData.remove(obj.data);
							}
						}
						console.log("勾选的数据 >> \n "  + _this.checkboxData.length +  " \n "+   JSON.stringify(_this.checkboxData));
					});
				}
				
				/**************************************** Laytpl 渲染 开始 ***********************************************************************/
				if( _this.config.isLaytpl ) {
					
					_this.laytplShow();
					
				}
				/**************************************** Laytpl 渲染 完成 ***********************************************************************/
				
				/****  查询条件处理 */
				com.ymx.layui.public.core.form.on('submit(' + _this.config.sreachBtn + ')', function(data){
					//序列化表单参数
					var whereParam = formSerialize( _this.config.formName );
					//console.log(JSON.stringify(whereParam));
					 
					if( _this.config.isTable ){
						com.ymx.layui.public.core.table.reload( _this.config.defaultTableId , {   //参数值　config中配置　
							   //设定异步数据接口的额外参数，任意设
							   where : whereParam
						});
					} 
					
					//模板引擎列表查询条件
					if( _this.config.isLaytpl ){
						
						_this.config.laytplPutextra = $.extend( true , _this.config.laytplPutextra , whereParam );
						
						_this.laytplShow();
					}
				    //防止
				    return false;
			    });

				 //初始化相关功能操作
				 _this.config.listener();
				 

				
				/******************************* 表单校验 ***************************************************************/
				com.ymx.layui.public.core.form.verify({
					 /** 案例 */
 					 username : function(value, item){ //value：表单的值、item：表单的DOM对象
					    if(!new RegExp("^[a-zA-Z0-9_\u4e00-\u9fa5\\s·]+$").test(value)){
					      return 'Le nom d’utilisateur ne peut pas contenir de caractères spéciaux.';
					    }
					    if(/(^\_)|(\__)|(\_+$)/.test(value)){
					      return 'Le nom d\'utilisateur ne peut pas commencer ou se terminer par le tiret bas.\'_\'';
					    }
					    if(/^\d+\d+\d$/.test(value)){
					      return 'Le nom d’utilisateur ne peut pas contenir uniquement des chiffres.';
					    }
					  }
					  //我们既支持上述函数式的方式，也支持下述数组的形式
					  //数组的两个值分别代表：[正则匹配、匹配不符时的提示文字]
					  ,pass: [
					    /^[\S]{6,12}$/
					    ,'Le mot de passe doit comporter entre 6 et 12 caractères et ne peut pas contenir d’espaces.'
					  ] ,
					  /** 不允许为空  **/
					  isEmpty : function( value , item ){
						  if(isEmpty(value)){
							  var desc = $(item).attr("desc");
							  if(isEmpty(desc)){
								  return "Non autorisé à être vide."
							  }
							  return desc + "Non autorisé à être vide." ;
						  }
					 } ,
					 /** 正整数 */
					 numInt : function( value , item ) {
						 var desc = $(item).attr("desc");
						 if(!new RegExp("^\\d+$").test(value)){
						      if(isEmpty(desc)){
								  return "Veuillez entrez un nombre entier." ;
							  }
							  return desc + "Un nombre entier" ;
						 }
					 } ,
					 /** 手机电话号码校验 */
					 phone : function( value , item ) {
						 //  /^1[3|4|5|8][0-9]\d{4,8}$/ 
						 if(!new RegExp("/^(\d{3,4}-)?\d{7,8})$|1[3|4|5|8][0-9]\d{4,8}$/").test(value)){ 
							   return "Veuillez entrez les informations de contact correctes." ;
					     } 
					 } ,
					 /** 两位小数 */
					 twoFloat : function( value , item ) {
						 if (!(/^(\d{1,20}\.?\d{0,2}|0)$/.test(value))) {
							return "Veuillez entrez les chiffres correctement." ;
						}
					 } ,
					 /** 六位小数 */
					 florFloat : function( value , item ) {
						 if (!(/^(\d{1,20}\.?\d{0,6}|0)$/.test(value))) {
							return "Veuillez entrez les chiffres correctement." ;
						}
					 } ,
					 /** 邮箱 **/
					 email : function (value , item) {
						 if (!(/^(\w)+(\.\w+)*@(\w)+((\.\w+)+)$/.test(value))) {
								return "Veuillez entrer l’email correctement." ;
						 }
					 }
			   }); 
		});
		
		//模板引擎初始化点击触发全选 或 非全选
		if( _this.config.isLaytpl ) {
			
			_this.laytplCheckBox();
		}
		
		return this;
	};
	
	
	/**
	 * 模板引擎列表所选择的数据
	 * @param options
	 */
	DataList.prototype.laytplCheckBox = function() {
		var _this = this ;
		//全选或全不选
		$("#" + _this.config.defaultTableId + "ChooseAll").click(function(){
			if(this.checked){
				$('input[name^="' + _this.config.defaultTableId +'_check_"]').prop("checked",true);
				
				_this.checkboxData = [] ;
				//获取被选中的值
				$("input[name^='" + _this.config.defaultTableId + "_check']:checked").each(function(obj) {
					
					var objec = {} ;
					//获取被选中的行
					var trName = this.value;
					if(!isNull( _this.config.dataType ) && ( _this.config.dataType == 2) ) {
						var data = $("tr[id='" + _this.config.defaultTableId + "_tr_" + trName + "']").attr("data-obj");
						//由JSON字符串转换为JSON对象
						objec = eval('(' + data + ')');
					} else {
						$("tr[id='" + _this.config.defaultTableId + "_tr_" + trName + "']").children().each(function(i,o) {
							var fieldValue = $(this).attr("data-field");
							if(!isNull(fieldValue)){
								var field = fieldValue.split("_")[0];
								var value = fieldValue.split("_")[1];
								if(value == "null" || value == null || value.trim() == ''){
									objec[field] = "";
								} else {
									objec[field] = value ;
								}
							}
						});
					}
					//存储数据
					_this.checkboxData.push(objec);
				});
				//console.log("模板引擎勾选后的值 \n " + JSON.stringify(_this.checkboxData));
			} else {
				//清空所有的选择数据
				$('input[name^="' + _this.config.defaultTableId +'_check_"]').prop("checked",false);
				_this.checkboxData = [] ;
			}
		});
	}
	
	
}());
