/***
 * 基于layui定制 layer封装　
 *  *  1、增加  <jsp:include page="/include.htm"/> 
 *    引入JS文件　<script src="<%=PageUtil.getBasePath(request) %>common/public.core.js"></script>
 *    引入JS文件　<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
 *    注　：　如果非iframe形式 只需要父页面有引入以上文件 , 子页面无需引入
 *    
 */
(function($){
	//$.fn.layerOpenCustom
	$.layerOpenCustom = function(options) {
		this._layer_index = 0;
		options = $.extend({
			type : 1 ,
			title  : '标题' ,
			content : '' ,		　　//弹出层内容 url 或　静态的 html
			skin : '' ,            // skin不仅允许你传入layer内置的样式class名，还可以传入您自定义的class名
			area : 'auto' , 
			btnAlign : 'r' ,
			closeBtn : 1 ,
			shadeClose : false ,
			shade : 0.3 ,
			id : '',
			anim  : 0 ,
			fixed : true ,
			maxmin : false ,
			resize : false ,
			scrollbar : true ,
			maxWidth : 360 ,
			moveType : 1 ,
			resizing : function (layero) {
				
			} ,
		},options||{});
		var _this_layer = this ;
		_this_layer._layer_index = com.ymx.layui.public.core.layer.open(options);
		
		return _this_layer ; 
	};
	
})(jQuery)