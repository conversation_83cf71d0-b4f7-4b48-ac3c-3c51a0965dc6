/**
 *  JS 动态加载下拉多级联动
 */
(function() {
    "use strict";
    window.SelectLink = function (options) {
        this.config = $.extend(true, SelectLink.setting , options , { total: options.selectItems.length } );
        // 绑定事件
        this.EvenChange();
        // 初始化
        this.initSelected();
    }

    // 动态创建函数
    SelectLink.fn = SelectLink.prototype;

    /**
     * 初始化下拉框
     */
    SelectLink.fn.initSelected = function() {
        var that = this;
        var _config = this.config;
       $.each( _config['selectItems'] , function (index , obj ) {
           if (0 == index){
               that.initSelect( 0 );
           }
           if (index !== 0 && !isEmpty( obj.selected)) {
               that.initSelect( index );
           }
        });
    }

    /***
     * 默认参数值
     * @type {{}}
     */
    SelectLink.setting = {
        // 下拉选项 数组对象集
        //          对象属性 { url 请求url,
        //                   selectElem  :下拉元素,
        //                   selectId    :下拉option value值 ,
        //                   selectName  :下拉option text 值 ,
        //                   selectNameEn:下拉option text 值 英文 ,
        //                   queData     :附加查询参数,
        //                   selected    :默认被选中值
        //                   parentElem  :父节点元素
        //                 }
        selectItems : []  , //  此对象集需要有顺序 先后排列
        selectLayFilter : ''
    };

    /**
     * 事件改变
     */
    SelectLink.fn.EvenChange = function () {
        var that = this;
        var _config = that.config;
        com.ymx.layui.public.core.form.on("select(" + _config['selectLayFilter'] + ")", function(obj) {
            var $elem = $(obj.elem);
            var index = parseInt($elem.attr("select-index"));
            if(index + 1 < _config.total) {
                that.initSelect(index + 1);
            }
        });
    }

    /**
     * 创建对象   加载下拉框
     * @param options
     */
    SelectLink.create = function(options) {
        if(isEmpty(options) || isEmpty(options['selectItems']) || options['selectItems'].length == 0) {
            alert("options 下拉选择项 不能为空 ! ");
            return;
        }
        // 默认 lay-filter
        if(isEmpty(options) || isEmpty(options['selectLayFilter']) || options.selectLayFilter === undefined ) {
            options['selectLayFilter'] = 'selectLayFilter';
        }
        $.each( options['selectItems'] , function (index , obj ) {
            $("#" + obj['selectElem']).attr("select-index", index);
            $("#" + obj['selectElem']).attr("lay-filter", options['selectLayFilter']);
        });
       return new SelectLink(options);
    }


    /**
     * 初始化
     * @param position 位置标识
     */
    SelectLink.fn.initSelect = function( position ) {
        var that = this;
        // 获取配置
        var _config = that.config;
        // 获取下拉数组集合
        var items  = _config['selectItems'];
        // 前一个下拉选择对象 , 前一个下拉选择对象被选中的值 , 附加查询条件
        var preSelect , preVal , params = {};
        // 如果当前下拉选项不是第一个,则获取前一个下拉项值,进行查询
        if (position !== 0 ){
            // 前一个下拉选择对象
            preSelect = items[position - 1];
            // 前一个下拉选择对象被选中的值
            preVal = $("#"+ preSelect.selectElem).val();
        }
        // 当前需要初始化的下拉选项
        var currentSelect = items[position];

        if(position !== 0 && isEmpty(preVal)) {
            $("#" + currentSelect["selectElem"]).html('<option value="">请选择</option>');
            com.ymx.layui.public.core.form.render("select");
            return;
        }
        if(position !== 0 && currentSelect['parentElem']) {
            // 设置父级参数值
            params[currentSelect['parentElem']] = preVal;
        }
        //console.log("params >>>>>> " + JSON.stringify(params));
        $.extend(true , currentSelect.queData , params );
        $.extend( currentSelect ,  { async:false } );
        //console.log("queData >>>>>> " + JSON.stringify(currentSelect.queData));
        // 显示下拉框内容
        $.selectOption( currentSelect );
    }

    /**
     * 判断对象是否为空
     * @param obj
     * @returns {boolean}
     */
    function isEmpty( obj ) {
        return obj === undefined || obj === null || obj === '' || obj.length === 0;
    }

}());
