/**
 * JS 动态加载 select 下拉框
 * @Date 2017-10-17
 *
 *
 * 使用方式之一
 *  $(function(){
		   $.selectOption({
             	url : 'dic/dicGroupList.web' ,
           	    selectElem : 'testSelecte' ,
           	    selectId : 'dicId' ,
           	    selectName : 'dicName'
           });
     });
 页面
 <select name="testSelecte"  id="testSelecte" class="layui-input" >
 <option value="">请选择</option>
 </select>
 */
(function($) {
	//扩展JQUERY
	//$.fn.selectOption $("#a").selectOption({});
	//$.selectOption({});
	$.selectOption = function(options) {
		var setting = $.extend({
			selectElem: (options.selectElem || 'selecteId'),	   //dom元素id
			url: '',											   //发送请求url
			queData: (isNull(options.queData) ? {} : options.queData), //附加参数
			type: (options.type == null || options.type == '' || options.type == 'undefined' ? 'json' : options.type),
			selectId: (options.selectId || 'id'),        //存储内容
			selectName: (options.selectName || 'name'),  //显示内容
			selected: (options.selected || ''),          //被选中的值
			isFormRenderSelect: (options.isFormRenderSelect || false),
			dataKey: (options.dataKey || ''),           //采用map时
            async : (options.async || true ),
            filter: function(data) {  				   //监听下拉框

			},
			doData: function(data) {
				var html = "";
				if(data.rec == 'SUC') {
					$("#" + setting.selectElem).children().first().nextAll().remove();
					/**直接reModel存在结果集 */
					var rows = data.reModel;

					/** 当reMoel存放Map时，则 dataKey为 map中 key */
					if(null != setting.dataKey && '' != setting.dataKey) {
						rows = data.reModel[setting.dataKey];
					}
					$.each(rows, function(key, obj) {
						var id = obj[setting.selectId];
						html += "<option value= '" + id + "'";
						if(id == setting.selected) {
							html += " selected='selected' ";
						}
						html += " >" + obj[setting.selectName] + "</option>";
					});
					$("#" + setting.selectElem).append(html);
					if(setting.isFormRenderSelect) {
						//弹出层需要重新渲染
						com.ymx.layui.public.core.form.render('select');
						if(options.callback) {
							options.callback();
						}
					}
				} else {
				}
			},
			errData: function(data) {
				$("#" + setting.selectElem).children().first().nextAll().remove();
			}
		}, options || {});
		//console.log(" 下拉框参数值 >>> \n " + JSON.stringify(setting.queData));
		/** ajax 请求 */
		/*doPost(setting.url, null, setting.queData, function(data) {
			setting.doData(data);
		}, function(data) {
			setting.errData(data);
		});*/
        doRequest({ url:setting.url , queData:setting.queData , async : setting.async,
            sucFunc : function (data) {
                setting.doData(data);
            } ,
            errFunc : function (data) {
                setting.doData(data);
            }
        });
		//监听
		com.ymx.layui.public.core.form.on('select(' + setting.selectElem + ')', function(data) {
			setting.filter(data);
		});
		return this;
	};
})(jQuery)