.text-left {
    text-align:left;
}
.text-center {
    text-align:center;
}
.text-right {
    text-align:right;
}

.layui-table th {
    text-align:inherit;
}
tr.selected td {
    background-color:#f2f2f2;
}
tr.focus td {
    background-color:#eee;
}

.layui-table.table-edit td {
    padding:2px;
}

.grid {
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border: solid 1px #e2e2e2;
}
.grid-content {
    position:relative;
    height:100%;
}
    .grid-content table {
        margin: 0;
        width: auto;
        min-width: 100%;
    }
.grid-content table td,
.grid-content table th {
    white-space:nowrap;
}
    .grid-content td.edit,.grid-content th.edit {
        background-color:floralwhite;
    }
    .grid-content .edit input[type="text"],
    .grid-content .edit input[type="number"],
    .grid-content .edit select,
    .grid-content .edit textarea {
        width:100%;
        height:100%;
        border:none;
        background-color:floralwhite;
        outline: 0;
        -webkit-transition: border-color .3s cubic-bezier(.65,.05,.35,.5);
        transition: border-color .3s cubic-bezier(.65,.05,.35,.5);
        -webkit-box-sizing: border-box !important;
        -moz-box-sizing: border-box !important;
        box-sizing: border-box !important;
    }
    
.grid-content td.grid-danger,
.grid-content th.grid-danger,
.grid-content td.grid-danger *,
.grid-content th.grid-danger * {
    background-color:#f2dede !important;
    color:red;
}
.grid-head {
    overflow:hidden;
    background-color:#f2f2f2;
}
    .grid-head table {
        table-layout:fixed;
        background-color:#f2f2f2;
    }
    .grid-head table thead,.grid-body table thead {
        background-color:#f2f2f2;
        text-align:center;
    }
    .grid-head thead {
        float:left !important;
    }
.grid-body {
    overflow:hidden;
}
.grid .grid-body {
    position:absolute;
    top:40px;
    left:0;
    right:0;
    overflow:auto;
}
.grid-body thead th,
.grid-body thead td {
    border-top: none;
}
    .grid-body thead th *,
    .grid-body thead td * {
    }
.grid-title {
    border:solid 1px #e2e2e2;
    border-bottom:none;
    line-height:20px;
    padding:5px 10px;
}
.grid .grid-page {
    position:absolute;
    bottom:0;
    left:0;
    right:0;
    background-color:#eee;
}
    .grid-page > div {
        float:right;
    }
.grid .layui-laypage a, .grid .layui-laypage span {
    background-color: #eee;
}

.grid-page .grid-tool {
    float: left;
    line-height: 40px;
    margin-left: 5px;
}


.layui-laypage {
    margin:7px 0 2px 0;
}

.loading_bg {
    position:absolute;
    top:0;
    right:0;
    bottom:0;
    left:0;    
    background-color:#fff;
    opacity: 0.3;
    filter: alpha(opacity=30);
}
.loading {
    position:absolute;
    top:50%;
    left:0;
    right:0;
    margin-top:-20px;
    text-align:center;
}
    .loading .layui-icon {
        display:inline-block;
        font-size:40px; 
        font-weight:400;
    }


.grid-order {
    display:inline-block;
    width:10px;
    height:20px;
    margin-left:5px;
    vertical-align:middle
}
    .grid-order:before {
        display:block;
        content:'';
        width:0;
        height:0;
        border-style:dashed dashed solid;
        border-width:4px;
        border-color:transparent transparent #aaa;
    }
    .grid-order:after {
        display:block;
        content:'';
        width:0;
        height:0;
        margin-top:3px;
        border-style:solid dashed dashed;
        border-width:4px;
        border-color:#aaa transparent transparent;
    }
    .grid-order.grid-order-asc:before {
        border-color:transparent transparent #222;
    }
    .grid-order.grid-order-desc:after {
        border-color:#222 transparent transparent;
    }

/*treegrid*/
.treegrid-indent {width:16px; height: 16px; display: block;float:left; }
.treegrid-expander {width:16px; height: 16px; display: block;float:left; cursor: pointer;}
span.treegrid-expander { background-repeat:no-repeat;background-size:100%;}
.treegrid-expanded .treegrid-expander{background-image: url(../images/collapse.png); }
.treegrid-collapse .treegrid-expander{background-image: url(../images/expand.png);}
.treegrid-loading .treegrid-expander{background-image:url(../images/loading.gif);}
.treegrid-wrong .treegrid-expander {background-image:url('../images/wrong.png');}
table.tree tbody tr td:nth-child(1),table.tree tbody tr th:nth-child(1) {text-align:left !important;}