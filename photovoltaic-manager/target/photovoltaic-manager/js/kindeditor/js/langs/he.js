/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */

/**
 * Hebrew
 */

$.Editable.LANGS['he'] = {
  translation: {
    "Bold": "\u05de\u05d5\u05d3\u05d2\u05e9",
    "Italic": "\u05de\u05d5\u05d8\u05d4",
    "Underline": "\u05e7\u05d5 \u05ea\u05d7\u05ea\u05d9",
    "Strikethrough": "\u05e7\u05d5 \u05d0\u05de\u05e6\u05e2\u05d9",
    "Font Size": "\u05d2\u05d5\u05d3\u05dc \u05d4\u05d2\u05d5\u05e4\u05df",
    "Color": "\u05e6\u05d1\u05e2",
    "Background Color": "\u05e6\u05d1\u05e2 \u05e8\u05e7\u05e2",
    "Text Color": "\u05e6\u05d1\u05e2 \u05d4\u05d8\u05e1\u05d8",
    "Format Block": "\u05e4\u05d5\u05e8\u05de\u05d8",
    "Normal": "\u05e8\u05d2\u05d9\u05dc",
    "Paragraph": "\u05e4\u05e1\u05e7\u05d4",
    "Code": "\u05e7\u05d5\u05d3",
    "Quote": "\u05e6\u05d9\u05d8\u05d5\u05d8",
    "Heading 1": "1 \u05db\u05d5\u05ea\u05e8\u05ea",
    "Heading 2": "2 \u05db\u05d5\u05ea\u05e8\u05ea",
    "Heading 3": "3 \u05db\u05d5\u05ea\u05e8\u05ea",
    "Heading 4": "4 \u05db\u05d5\u05ea\u05e8\u05ea",
    "Heading 5": "5 \u05db\u05d5\u05ea\u05e8\u05ea",
    "Heading 6": "6 \u05db\u05d5\u05ea\u05e8\u05ea",
    "Block Style": "\u05e1\u05d2\u05e0\u05d5\u05df \u05d1\u05dc\u05d5\u05e7",
    "Alignment": "\u05d9\u05d9\u05e9\u05d5\u05e8",
    "Align Left": "\u05d9\u05d9\u05e9\u05d5\u05e8 \u05dc\u05e9\u05de\u05d0\u05dc",
    "Align Center": "\u05d9\u05d9\u05e9\u05d5\u05e8 \u05dc\u05de\u05e8\u05db\u05d6",
    "Align Right": "\u05d9\u05d9\u05e9\u05d5\u05e8 \u05dc\u05d9\u05de\u05d9\u05df",
    "Justify": "\u05d9\u05d9\u05e9\u05d5\u05e8 \u05de\u05dc\u05d0",
    "Numbered List": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05e8\u05e9\u05d9\u05de\u05d4 \u05de\u05de\u05d5\u05e1\u05e4\u05e8\u05ea",
    "Bulleted List": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05e8\u05e9\u05d9\u05de\u05d4",
    "Indent Less": "\u05d4\u05e7\u05d8\u05e0\u05ea \u05db\u05e0\u05d9\u05e1\u05d4",
    "Indent More": "\u05d4\u05d2\u05d3\u05dc\u05ea \u05db\u05e0\u05d9\u05e1\u05d4",
    "Select All": "\u05d1\u05d7\u05e8 \u05d4\u05db\u05dc",
    "Insert Link": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05e7\u05d9\u05e9\u05d5\u05e8",
    "Insert Image": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05ea\u05de\u05d5\u05e0\u05d4",
    "Insert Video": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05d5\u05d9\u05d3\u05d9\u05d0\u05d5",
    "Undo": "\u05d1\u05d9\u05d8\u05d5\u05dc",
    "Redo": "\u05d1\u05e6\u05e2 \u05e9\u05d5\u05d1",
    "Show HTML": "HTML \u05d4\u05e6\u05d2",
    "Float Left": "\u05e9\u05de\u05d0\u05dc",
    "Float None": "\u05db\u05dc\u05d5\u05dd",
    "Float Right": "\u05d9\u05de\u05d9\u05df",
    "Replace Image": "\u05d4\u05d7\u05dc\u05e4\u05ea \u05ea\u05de\u05d5\u05e0\u05d4",
    "Remove Image": "\u05d4\u05e1\u05e8\u05ea \u05ea\u05de\u05d5\u05e0\u05d4",
    "Title": "\u05db\u05d5\u05ea\u05e8\u05ea",
    "Drop image": "\u05e9\u05d7\u05e8\u05e8 \u05d0\u05ea \u05d4\u05ea\u05de\u05d5\u05e0\u05d4 \u05db\u05d0\u05df",
    "or click": "\u05d0\u05d5 \u05dc\u05d7\u05e5",
    "or": "\u05d0\u05d5",
    "Enter URL": "\u05d4\u05db\u05e0\u05e1 \u05d4\u05e7\u05d9\u05e9\u05d5\u05e8",
    "Please wait!": "\u05e0\u05d0 \u05dc\u05d4\u05de\u05ea\u05d9\u05df",
    "Are you sure? Image will be deleted.": "\u05d4\u05d0\u05dd \u05d0\u05ea\u05d4 \u05d1\u05d8\u05d5\u05d7\u003f \u05d4\u05ea\u05de\u05d5\u05e0\u05d4 \u05ea\u05de\u05d7\u05e7\u002e",
    "UNLINK": "\u05d4\u05e1\u05e8\u05ea \u05d4\u05e7\u05d9\u05e9\u05d5\u05e8",
    "Open in new tab": "\u05dc\u05e4\u05ea\u05d5\u05d7 \u05d1\u05d8\u05d0\u05d1 \u05d7\u05d3\u05e9",
    "Type something": "\u05d4\u05e7\u05dc\u05d3 \u05db\u05d0\u05df",
    "Cancel": "\u05d1\u05d9\u05d8\u05d5\u05dc",
    "OK": "\u05d1\u05e6\u05e2",
    "Manage images": "\u05e0\u05d9\u05d4\u05d5\u05dc \u05d4\u05ea\u05de\u05d5\u05e0\u05d5\u05ea",
    "Delete": "\u05de\u05d7\u05d9\u05e7\u05d4",
    "Font Family": "\u05d2\u05d5\u05e4\u05df",
    "Insert Horizontal Line": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05e7\u05d5 \u05d0\u05d5\u05e4\u05e7\u05d9",
    "Table": "\u05d8\u05d1\u05dc\u05d4",
    "Insert table": "\u05d4\u05db\u05e0\u05e1 \u05d8\u05d1\u05dc\u05d4",
    "Cell": "\u05ea\u05d0",
    "Row": "\u05e9\u05d5\u05e8\u05d4",
    "Column": "\u05d8\u05d5\u05e8",
    "Delete table": "\u05d4\u05e1\u05e8\u05ea \u05d4\u05d8\u05d1\u05dc\u05d4",
    "Insert cell before": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05ea\u05d0 \u05dc\u05e4\u05e0\u05d9",
    "Insert cell after": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05ea\u05d0 \u05d0\u05d7\u05e8\u05d9",
    "Delete cell": "\u05de\u05d7\u05d9\u05e7\u05ea \u05d4\u05ea\u05d0",
    "Merge cells": "\u05de\u05d6\u05d2 \u05ea\u05d0\u05d9\u05dd",
    "Horizontal split": "\u05e4\u05e6\u05dc \u05d0\u05d5\u05e4\u05e7\u05d9",
    "Vertical split": "\u05e4\u05e6\u05dc \u05d0\u05e0\u05db\u05d9",
    "Insert row above": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05e9\u05d5\u05e8\u05d4 \u05dc\u05e4\u05e0\u05d9",
    "Insert row below": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05e9\u05d5\u05e8\u05d4 \u05d0\u05d7\u05e8\u05d9",
    "Delete row": "\u05de\u05d7\u05d9\u05e7\u05ea \u05e9\u05d5\u05e8\u05d4",
    "Insert column before": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05d8\u05d5\u05e8 \u05dc\u05e4\u05e0\u05d9",
    "Insert column after": "\u05d4\u05d5\u05e1\u05e4\u05ea \u05d8\u05d5\u05e8 \u05d0\u05d7\u05e8\u05d9",
    "Delete column": "\u05de\u05d7\u05d9\u05e7\u05ea \u05d8\u05d5\u05e8"
  },
  direction: "rtl"
};
