/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */

/**
 * Traditional Chinese spoken in Taiwan.
 */

$.Editable.LANGS['zh_tw'] = {
  translation: {
    "Bold": "\u7c97\u9ad4",
    "Italic": "\u659c\u9ad4",
    "Underline": "\u5e95\u7dda",
    "Strikethrough": "\u522a\u9664\u7dda",
    "Font Size": "\u5b57\u578b\u5927\u5c0f",
    "Color": "\u984f\u8272",
    "Background Color": "\u80cc\u666f\u984f\u8272",
    "Text Color": "\u6587\u5b57\u984f\u8272",
    "Format Block": "\u683c\u5f0f",
    "Normal": "\u6b63\u5e38",
    "Paragraph": "\u6bb5\u843d",
    "Code": "\u7a0b\u5f0f\u78bc",
    "Quote": "\u5f15\u7528",
    "Heading 1": "\u6a19\u984c 1",
    "Heading 2": "\u6a19\u984c 2",
    "Heading 3": "\u6a19\u984c 3",
    "Heading 4": "\u6a19\u984c 4",
    "Heading 5": "\u6a19\u984c 5",
    "Heading 6": "\u6a19\u984c 6",
    "Block Style": "\u5ea7\u5f0f",
    "Alignment": "\u5c0d\u9f4a",
    "Align Left": "\u7f6e\u5de6\u5c0d\u9f4a",
    "Align Center": "\u7f6e\u4e2d\u5c0d\u9f4a",
    "Align Right": "\u7f6e\u53f3\u5c0d\u9f4a",
    "Justify": "\u5de6\u53f3\u5c0d\u9f4a",
    "Numbered List": "\u6578\u5b57\u6e05\u55ae",
    "Bulleted List": "\u9805\u76ee\u6e05\u55ae",
    "Indent Less": "\u6e1b\u5c11\u7e2e\u6392",
    "Indent More": "\u589e\u52a0\u7e2e\u6392",
    "Select All": "\u5168\u9078",
    "Insert Link": "\u63d2\u5165\u9023\u7d50",
    "Insert Image": "\u63d2\u5165\u5716\u7247",
    "Insert Video": "\u63d2\u5165\u5f71\u97f3",
    "Undo": "\u5fa9\u539f",
    "Redo": "\u53d6\u6d88\u5fa9\u539f",
    "Show HTML": "\u663e\u793a\u7684\u0048\u0054\u004d\u004c",
    "Float Left": "\u5de6\u908a",
    "Float None": "\u7121",
    "Float Right": "\u53f3\u908a",
    "Replace Image": "\u66f4\u6362\u56fe\u50cf",
    "Remove Image": "\u5220\u9664\u56fe\u50cf",
    "Title": "\u6a19\u984c",
    "Insert image": "\u63d2\u5165\u5716\u7247",
    "Drop image": "\u56fe\u50cf\u62d6\u653e",
    "or click": "\u6216\u70b9\u51fb",
    "or": "\u6216",
    "Enter URL": "\u8f93\u5165\u7f51\u5740",
    "Please wait!": "\u8bf7\u7a0d\u7b49\uff01",
    "Are you sure? Image will be deleted.": "\u4f60\u786e\u5b9a\u5417\uff1f\u56fe\u50cf\u5c06\u88ab\u5220\u9664\u3002",
    "UNLINK": "\u79fb\u9664\u9023\u7d50",
    "Open in new tab": "\u5f00\u542f\u5728\u65b0\u6807\u7b7e\u9875",
    "Type something": "\u8f93\u5165\u4e00\u4e9b\u5185\u5bb9",
    "Cancel": "\u53d6\u6d88",
    "OK": "\u78ba\u5b9a",
    "Manage images": "\u7ba1\u7406\u5716\u50cf",
    "Delete": "\u522a\u9664",
    "Font Family": "\u5b57\u9ad4",
    "Insert Horizontal Line": "\u63d2\u5165\u6c34\u5e73\u7dda",
    "Table": "\u8868\u683c",
    "Insert table": "\u63d2\u5165\u8868\u683c",
    "Cell": "\u5355\u5143\u683c",
    "Row": "\u884c",
    "Column": "\u5217",
    "Delete table": "\u5220\u9664\u8868\u683c",
    "Insert cell before": "\u524d\u63d2\u5165\u55ae\u5143\u683c",
    "Insert cell after": "\u5f8c\u63d2\u5165\u96fb\u6c60",
    "Delete cell": "\u522a\u9664\u55ae\u5143\u683c",
    "Merge cells": "\u5408\u4f75\u55ae\u5143\u683c",
    "Horizontal split": "\u6c34\u5e73\u5206\u5272",
    "Vertical split": "\u5782\u76f4\u5206\u5272",
    "Insert row above": "\u5728\u4e0a\u65b9\u63d2\u5165",
    "Insert row below": "\u5728\u4e0b\u65b9\u63d2\u5165",
    "Delete row": "\u5220\u9664\u884c",
    "Insert column before": "\u5728\u5de6\u4fa7\u63d2\u5165",
    "Insert column after": "\u5728\u53f3\u4fa7\u63d2\u5165",
    "Delete column": "\u5220\u9664\u5217"
  },
  direction: "ltr"
};