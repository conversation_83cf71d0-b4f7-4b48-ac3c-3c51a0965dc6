/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */

/**
 * Korean
 */

$.Editable.LANGS['ko'] = {
  translation: {
    "Bold": "\uad75\uac8c",
    "Italic": "\uae30\uc6b8\uc784\uaf34",
    "Underline": "\ubc11\uc904",
    "Strikethrough": "\ucde8\uc18c\uc120",
    "Font Size": "\ud3f0\ud2b8 \ud06c\uae30",
    "Color": "\uc0c9",
    "Background Color": "\ubc30\uacbd\uc0c9",
    "Text Color": "\ubb38\uc790 \uc0c9\uae54",
    "Format Block": "\ud3ec\ub9f7",
    "Normal": "\ud45c\uc900",
    "Paragraph": "\ub2e8\ub77d",
    "Code": "\ucf54\ub4dc",
    "Quote": "\uc778\uc6a9\uad6c",
    "Heading 1": "\uc81c\ubaa9 1",
    "Heading 2": "\uc81c\ubaa9 2",
    "Heading 3": "\uc81c\ubaa9 3",
    "Heading 4": "\uc81c\ubaa9 4",
    "Heading 5": "\uc81c\ubaa9 5",
    "Heading 6": "\uc81c\ubaa9 6",
    "Block Style": "\ube14\ub85d \uc2a4\ud0c0\uc77c",
    "Alignment": "\uc815\ub82c",
    "Align Left": "\uc67c\ucabd\uc815\ub82c",
    "Align Center": "\uac00\uc6b4\ub370\uc815\ub82c",
    "Align Right": "\uc624\ub978\ucabd\uc815\ub82c",
    "Justify": "\uc591\ucabd\uc815\ub82c",
    "Numbered List": "\uc22b\uc790\ub9ac\uc2a4\ud2b8",
    "Bulleted List": "\uc22b\uc790\ub9ac\uc2a4\ud2b8",
    "Indent Less": "\ub0b4\uc5b4\uc4f0\uae30",
    "Indent More": "\ub4e4\uc5ec\uc4f0\uae30",
    "Select All": "\uc804\uccb4\uc120\ud0dd",
    "Insert Link": "\ub9c1\ud06c \uc0bd\uc785",
    "Insert Image": "\uc774\ubbf8\uc9c0 \uc0bd\uc785",
    "Insert Video": "\ube44\ub514\uc624 \uc0bd\uc785",
    "Undo": "\uc2e4\ud589\ucde8\uc18c",
    "Redo": "\ub2e4\uc2dc\uc2e4\ud589",
    "Show HTML": "\ud45c\uc2dc HTML",
    "Float Left": "\uc67c\ucabd",
    "Float None": "\uc5c6\uc74c",
    "Float Right": "\uc624\ub978\ucabd",
    "Replace Image": "\uc774\ubbf8\uc9c0 \uad50\uccb4",
    "Remove Image": "\uc774\ubbf8\uc9c0\ub97c \uc81c\uac70",
    "Title": "\uc81c\ubaa9",
    "Insert image": "\uc774\ubbf8\uc9c0 \uc0bd\uc785",
    "Drop image": "\uc774\ubbf8\uc9c0\ub97c \uc0ad\uc81c",
    "or click": "\ub610\ub294 \ud074\ub9ad",
    "or": "\ub610\ub294",
    "Enter URL": "URL \uc744 \uc785\ub825",
    "Please wait!": "\uae30\ub2e4\ub824\uc8fc\uc2ed\uc2dc\uc624!",
    "Are you sure? Image will be deleted.": "\ub2f9\uc2e0\uc740 \ud655\uc2e4\ud55c\uac00\uc694\u003f \uc774\ubbf8\uc9c0\uac00 \uc0ad\uc81c\ub429\ub2c8\ub2e4\u002e",
    "UNLINK": "\ub9c1\ud06c\uc0ad\uc81c",
    "Open in new tab": "\uc0c8 \ud0ed\uc5d0\uc11c \uc5f4\uae30",
    "Type something": "\ubb54\uac00\ub97c \uc785\ub825",
    "Cancel": "\ucde8\uc18c",
    "OK": "\uc2b9\uc778",
    "Manage images": "\uc774\ubbf8\uc9c0 \uad00\ub9ac",
    "Delete": "\uc0ad\uc81c",
    "Font Family": "\uae00\uaf34",
    "Insert Horizontal Line": "\uc218\ud3c9\uc120\uc744 \uc0bd\uc785",
    "Table": "\ud14c\uc774\ube14",
    "Insert table": "\ud14c\uc774\ube14 \uc0bd\uc785",
    "Cell": "\uc140",
    "Row": "\uc5f4",
    "Column": "\ud589",
    "Delete table": "\ud14c\uc774\ube14 \uc0ad\uc81c",
    "Insert cell before": "\uc804\uc5d0 \uc140\uc744 \uc0bd\uc785",
    "Insert cell after": "\ud6c4 \uc140\uc744 \uc0bd\uc785",
    "Delete cell": "\uc140\uc744 \uc0ad\uc81c",
    "Merge cells": "\uc140 \ud569\uce58\uae30",
    "Horizontal split": "\uc218\ud3c9 \ubd84\ud560",
    "Vertical split": "\uc218\uc9c1 \ubd84\ud560",
    "Insert row above": "\uc774\uc804\uc5d0 \ud589 \uc0bd\uc785",
    "Insert row below": "\ub2e4\uc74c\uc5d0 \ud589 \uc0bd\uc785",
    "Delete row": "\ud589 \uc9c0\uc6b0\uae30",
    "Insert column before": "\uc774\uc804\uc5d0 \ud589 \uc0bd\uc785",
    "Insert column after": "\ub2e4\uc74c\uc5d0 \uc5f4 \uc0bd\uc785",
    "Delete column": "\uc5f4 \uc9c0\uc6b0\uae30"
  },
  direction: "ltr"
};