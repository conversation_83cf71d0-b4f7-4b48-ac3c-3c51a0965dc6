/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */
!function(a){a.Editable.commands=a.extend(a.Editable.commands,{table:{title:"Table",icon:"fa fa-table",callback:function(a,b,c){this.table_commands[b]?this.table_commands[b].apply(this,[b,c]):this.table_commands.insertTable.apply(this,[b,c])}}}),a.Editable.DEFAULTS.buttons[a.Editable.DEFAULTS.buttons.indexOf("insertHorizontalRule")]="table",a.Editable.prototype.table_commands={insertTable:function(a,b){this.insertTable(a,b)},insertRowAbove:function(){this.insertRow("above")},insertRowBelow:function(){this.insertRow("below")},insertColumnBefore:function(){this.insertColumn("before")},insertColumnAfter:function(){this.insertColumn("after")},deleteColumn:function(){this.deleteColumn()},deleteRow:function(){this.deleteRow()},insertCellBefore:function(){this.insertCell("before")},insertCellAfter:function(){this.insertCell("after")},mergeCells:function(){this.mergeCells()},deleteCell:function(){this.deleteCell()},splitVertical:function(){this.splitVertical()},splitHorizontal:function(){this.splitHorizontal()},insertHeader:function(){this.insertHeader()},deleteHeader:function(){this.deleteHeader()},deleteTable:function(){this.deleteTable()}},a.Editable.prototype.command_dispatcher=a.extend(a.Editable.prototype.command_dispatcher,{table:function(a){var b=this.buildDropdownTable(),c=this.buildDropdownButton(a,b,"fr-table");return this.bindTableDropdownEvents(),c}}),a.Editable.prototype.tableTab=function(){var b;this.currentCell&&(b=this.currentCell());var c=a(this.getSelectionElement());return c.parents("ul, ol").length>0?!0:b&&this.nextCell()?(this.setSelection(this.nextCell()),!1):void 0},a.Editable.prototype.tableShiftTab=function(){var b;this.currentCell&&(b=this.currentCell());var c=a(this.getSelectionElement());return c.parents("ul, ol").length>0?!0:b&&this.prevCell()?(this.setSelection(this.prevCell()),!1):void 0},a.Editable.prototype.initTable=function(){var b=this;this.$editor.on("click mouseup touch touchend",".fr-table a",function(c){c.preventDefault(),c.stopPropagation(),b.android()&&(a(this).parent().siblings().removeClass("hover"),a(this).parent().addClass("hover"))}),this.addListener("tab",this.tableTab),this.addListener("shift+tab",this.tableShiftTab)},a.Editable.initializers.push(a.Editable.prototype.initTable),a.Editable.prototype.buildDropdownTable=function(){var a='<ul class="fr-dropdown-menu fr-table">';a+='<li> <a href="#"><span data-text="true">Insert table</span> <i class="fa fa-chevron-right"></i></a><div class="select-table"> ',a+='<div class="fr-t-info">1 x 1</div>';for(var b=1;10>=b;b++){for(var c=1;10>=c;c++){var d="inline-block";(b>5||c>5)&&(d="none");var e="fr-bttn ";1==b&&1==c&&(e+=" hover"),a+='<span class="'+e+'" data-cmd="table" data-val="'+b+'" data-param="'+c+'" style="display: '+d+';"><span></span></span>'}a+='<div class="new-line"></div>'}return a+="</div> </li>",a+='<li><a href="#"><span data-text="true">Cell</span> <i class="fa fa-chevron-right"></i></a> <ul> <li data-cmd="table" data-val="insertCellBefore"><a href="#" data-text="true">Insert cell before</a></li><li data-cmd="table" data-val="insertCellAfter"><a href="#" data-text="true">Insert cell after</a></li><li data-cmd="table" data-val="deleteCell"><a href="#" data-text="true">Delete cell</a></li><li data-cmd="table" data-val="mergeCells"><a href="#" data-text="true">Merge cells</a></li><li data-cmd="table" data-val="splitHorizontal"><a href="#" data-text="true">Horizontal split</a></li><li data-cmd="table" data-val="splitVertical"><a href="#" data-text="true">Vertical split</a></li></ul></li>',a+='<li><a href="#"><span data-text="true">Row</span> <i class="fa fa-chevron-right"></i></a> <ul><li data-cmd="table" data-val="insertRowAbove"><a href="#" data-text="true">Insert row above</a></li><li data-cmd="table" data-val="insertRowBelow"><a href="#" data-text="true">Insert row below</a></li><li data-cmd="table" data-val="deleteRow"><a href="#" data-text="true">Delete row</a></li></ul></li>',a+='<li><a href="#"><span data-text="true">Column</span> <i class="fa fa-chevron-right"></i></a> <ul> <li data-cmd="table" data-val="insertColumnBefore"><a href="#" data-text="true">Insert column before</a></li> <li data-cmd="table" data-val="insertColumnAfter"><a href="#" data-text="true">Insert column after</a></li> <li data-cmd="table" data-val="deleteColumn"><a href="#" data-text="true">Delete column</a></li> </ul></li>',a+='<li data-cmd="table" data-val="deleteTable"><a href="#" data-text="true">Delete table</a></li>',a+="</ul>"},a.Editable.prototype.bindTableDropdownEvents=function(){var b=this;this.$bttn_wrapper.on("mouseenter",".fr-table .select-table > span",function(){var c=a(this).data("val"),d=a(this).data("param");b.$bttn_wrapper.find(".fr-table .select-table .fr-t-info").text(c+" x "+d),b.$bttn_wrapper.find(".fr-table .select-table > span").removeClass("hover");for(var e=1;10>=e;e++)for(var f=0;10>=f;f++){var g=b.$bttn_wrapper.find('.fr-table .select-table > span[data-val="'+e+'"][data-param="'+f+'"]');c>=e&&d>=f?g.addClass("hover"):(c+1>=e||5>=e)&&(d+1>=f||5>=f)?g.css("display","inline-block"):(e>5||f>5)&&g.css("display","none")}}),this.$bttn_wrapper.on("mouseleave",".fr-table .select-table",function(){b.$bttn_wrapper.find('.fr-table .select-table > span[data-val="1"][data-param="1"]').trigger("mouseenter")}),this.android()&&this.$bttn_wrapper.on("touchend",".fr-table .fr-trigger",function(){a(this).parents(".fr-table").find(".hover").removeClass("hover")})},a.Editable.prototype.tableMap=function(){var b=this.currentTable(),c=[];return b&&b.find("tr:not(:empty)").each(function(b,d){var e=a(d),f=0;e.find("td").each(function(d,e){for(var g=a(e),h=parseInt(g.attr("colspan"),10)||1,i=parseInt(g.attr("rowspan"),10)||1,j=b;b+i>j;j++)for(var k=f;f+h>k;k++)c[j]||(c[j]=[]),c[j][k]?f++:c[j][k]=e;f+=h})}),c},a.Editable.prototype.cellOrigin=function(a,b){for(var c=0;c<b.length;c++)for(var d=0;d<b[c].length;d++)if(b[c][d]==a)return{row:c,col:d}},a.Editable.prototype.canMergeCells=function(){var b=this.getSelectionCells();if(b.length<2)return!1;for(var c=this.tableMap(),d=0,e=32e3,f=0,g=32e3,h=0,i=0;i<b.length;i++){var j=a(b[i]),k=parseInt(j.attr("colspan"),10)||1,l=parseInt(j.attr("rowspan"),10)||1,m=this.cellOrigin(b[i],c);d+=k*l,e=Math.min(e,m.col),f=Math.max(f,m.col+k),g=Math.min(g,m.row),h=Math.max(h,m.row+l)}return d==(f-e)*(h-g)?{row:g,col:e,colspan:f-e,rowspan:h-g,map:c,cells:b}:null},a.Editable.prototype.getSelectionCells=function(){var b,c=[];if(this.browser.webkit||this.browser.msie){var d=this.getSelectionElements();for(b=0;b<d.length;b++)("TD"==d[b].tagName||"TH"==d[b].tagName)&&c.push(d[b])}else{var e=this.getRanges();for(b=0;b<e.length;b++){var f=e[b],g=!1;if("TD"==f.startContainer.tagName||"TH"==f.startContainer.tagName)c.push(f.startContainer),g=!0;else{var h=f.startContainer.childNodes,i=f.startOffset;if(h.length>i&&i>=0){var j=h[i];("TD"==j.tagName||"TH"==j.tagName)&&(c.push(j),g=!0)}}if(g===!1){var k=a(f.startContainer).parents("td:first, th:first");k.length>0&&c.push(k.get(0))}}}return c},a.Editable.prototype.currentCell=function(){var a=this.getSelectionCells();return a.length>0?a[0]:null},a.Editable.prototype.prevCell=function(){var b=this.currentCell();if(b){if(a(b).prev("td").length)return a(b).prev("td").get(0);if(a(b).parent().prev("tr").find("td").length)return a(b).parent().prev("tr").find("td:last").get(0)}return null},a.Editable.prototype.nextCell=function(){var b=this.currentCell();if(b){if(a(b).next("td").length)return a(b).next("td").get(0);if(a(b).parent().next("tr").find("td").length)return a(b).parent().next("tr").find("td").get(0)}return null},a.Editable.prototype.currentTable=function(){for(var b=a(this.getSelectionElement());b.get(0)!=this.$element.get(0)&&b.get(0)!=a("body").get(0)&&"TABLE"!=b.get(0).tagName;)b=b.parent();return b.get(0)!=this.$element.get(0)?b:null},a.Editable.prototype.focusOnTable=function(){var a=this.currentTable();if(a){var b=a.find("td:first");this.setSelection(b.get(0))}},a.Editable.prototype.insertCell=function(b){for(var c=this.getSelectionCells(),d=0;d<c.length;d++){var e=a(c[d]);"before"==b?e.before(e.clone().removeAttr("colspan").removeAttr("rowspan").html("<br/>")):"after"==b&&e.after(e.clone().removeAttr("colspan").removeAttr("rowspan").html("<br/>"))}"before"==b?this.triggerEvent("cellInsertedBefore"):"after"==b&&this.triggerEvent("cellInsertedAfter")},a.Editable.prototype.mergeCells=function(){var b=this.canMergeCells();if(b){var c=a(b.map[b.row][b.col]);c.attr("colspan",b.colspan),c.attr("rowspan",b.rowspan);for(var d=0;d<b.cells.length;d++){var e=b.cells[d];if(c.get(0)!=e){var f=a(e);c.append(f.html()),f.remove()}}this.setSelection(c.get(0))}this.hide(),this.triggerEvent("cellsMerged")},a.Editable.prototype.deleteCell=function(){for(var b=this.getSelectionCells(),c=0;c<b.length;c++){var d=a(b[c]);d.remove()}this.focusOnTable(),this.hide(),this.triggerEvent("cellDeleted")},a.Editable.prototype.insertHeader=function(){var a=this.currentTable();a&&a.find(" > thead").length>0&&this.triggerEvent("headerInserted")},a.Editable.prototype.deleteHeader=function(){},a.Editable.prototype.insertColumn=function(b){var c=this.currentCell();if(c)for(var d=a(c),e=this.tableMap(),f=this.cellOrigin(d.get(0),e),g=0;g<e.length;g++){var h=e[g][f.col],i=parseInt(a(h).attr("colspan"),10)||1,j=parseInt(a(h).attr("rowspan"),10)||1;if("before"==b){var k=e[g][f.col-1];k?k==h?a(k).attr("colspan",i+1):j>1?a(k).after("<td><br/></td>"):a(h).before("<td><br/></td>"):a(h).before("<td><br/></td>")}else if("after"==b){var l=e[g][f.col+1];l?l==h?a(l).attr("colspan",i+1):j>1?a(l).before("<td><br/></td>"):a(h).after("<td><br/></td>"):a(h).after("<td><br/></td>")}}this.hide(),"before"==b?this.triggerEvent("columnInsertedBefore"):"after"==b&&this.triggerEvent("columnInsertedAfter")},a.Editable.prototype.deleteColumn=function(){for(var b=this.getSelectionCells(),c=0;c<b.length;c++)for(var d=a(b[c]),e=this.tableMap(),f=this.cellOrigin(d.get(0),e),g=0;g<e.length;g++){var h=e[g][f.col],i=parseInt(a(h).attr("colspan"),10)||1;1==i?a(h).remove():a(h).attr("colspan",i-1)}this.focusOnTable(),this.hide(),this.triggerEvent("columnDeleted")},a.Editable.prototype.insertRow=function(b){var c,d=this.currentCell();if(d){var e=a(d),f=this.tableMap(),g=this.cellOrigin(e.get(0),f),h=0,i=null;for(c=0;c<f[g.row].length;c++){var j=f[g.row][c],k=parseInt(a(j).attr("rowspan"),10)||1;if("above"==b)if(0===g.row)h++;else{var l=f[g.row-1][c];l==j&&i!=j?a(j).attr("rowspan",k+1):h++}else if("below"==b)if(g.row==f.length-1)h++;else{var m=f[g.row+1][c];m==j&&i!=j?a(j).attr("rowspan",k+1):h++}i=f[g.row][c]}var n="<tr>";for(c=0;h>c;c++)n+="<td><br/></td>";n+="</tr>","below"==b?e.closest("tr").after(n):"above"==b&&e.closest("tr").before(n)}this.hide(),"below"==b?this.triggerEvent("rowInsertedBelow"):"above"==b&&this.triggerEvent("rowInsertedAbove")},a.Editable.prototype.deleteRow=function(){for(var b=this.getSelectionCells(),c=0;c<b.length;c++){for(var d=a(b[c]),e=this.tableMap(),f=this.cellOrigin(d.get(0),e),g=d.parents("tr:first"),h=0;h<e[f.row].length;h++){var i=e[f.row][h],j=parseInt(a(i).attr("rowspan"),10)||1;if(1==j)a(i).remove();else{var k=this.cellOrigin(i,e);if(a(i).attr("rowspan",j-1),k.row==f.row){var l=e[f.row+1];l&&l[h-1]&&(a(l[h-1]).after(a(i).clone()),a(i).remove())}}}g.remove()}this.focusOnTable(),this.hide(),this.triggerEvent("rowDeleted")},a.Editable.prototype.splitVertical=function(){for(var b=this.getSelectionCells(),c=0;c<b.length;c++){var d=a(b[c]),e=this.tableMap(),f=this.cellOrigin(d.get(0),e),g=parseInt(d.attr("colspan"),10)||1,h=parseInt(d.attr("rowspan"),10)||1;if(h>1){var i=Math.floor(h/2),j=f.row+(h-i),k=e[j][f.col-1];k||(k=e[j][f.col+g]),k?a(k).before(d.clone().attr("rowspan",i).html("<br/>")):d.parents("tr:first").after(a("<tr>").append(d.clone().attr("rowspan",i).html("<br/>"))),d.attr("rowspan",h-i)}else{for(var l=a("<tr>").append(d.clone().html("<br/>")),m=null,n=0;n<e[f.row].length;n++){var o=e[f.row][n],p=parseInt(a(o).attr("rowspan"),10)||1;m!=o&&o!=d.get(0)&&a(o).attr("rowspan",p+1),m=o}d.parents("tr:first").after(l)}}this.hide(),this.triggerEvent("cellVerticalSplit")},a.Editable.prototype.splitHorizontal=function(){for(var b=this.getSelectionCells(),c=0;c<b.length;c++){var d=a(b[c]),e=this.tableMap(),f=this.cellOrigin(d.get(0),e),g=parseInt(d.attr("colspan"),10)||1;if(g>1){var h=Math.floor(g/2);d.after(d.clone().attr("colspan",h).html("<br/>")),d.attr("colspan",g-h)}else{for(var i=null,j=0;j<e.length;j++){var k=e[j][f.col],l=parseInt(a(k).attr("colspan"),10)||1;i!=k&&k!=d.get(0)&&a(k).attr("colspan",l+1),i=k}d.after(d.clone().html("<br/>"))}}this.hide(),this.triggerEvent("cellHorizontalSplit")},a.Editable.prototype.insertTable=function(a,b){for(var c='<table data-last-table="true" width="100%">',d=0;a>d;d++){c+="<tr>";for(var e=0;b>e;e++)c+="<td><br/></td>";c+="</tr>"}c+="</table>",this.focus(),this.breakHTML(c);var f=this.$element.find('table[data-last-table="true"]');f.removeAttr("data-last-table"),this.setSelection(f.find("td:first").get(0)),this.hide(),this.triggerEvent("tableInserted")},a.Editable.prototype.deleteTable=function(){var a=this.currentTable();a&&(a.remove(),this.focus(),this.hide(),this.triggerEvent("tableDeleted"))}}(jQuery);