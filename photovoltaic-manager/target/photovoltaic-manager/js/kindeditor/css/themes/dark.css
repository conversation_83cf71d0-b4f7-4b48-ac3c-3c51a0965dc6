/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */
.dark-theme.froala-box .html-switch {
  border-color: #aaaaaa;
}
.dark-theme.froala-box .froala-element hr {
  border-top-color: #aaaaaa;
}
.dark-theme.froala-box .froala-element.f-placeholder:before {
  color: #aaaaaa;
}
.dark-theme.froala-box .froala-element pre {
  border: solid 1px #aaaaaa;
  background: #fcfcfc;
  border-radius: 4px;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.dark-theme.froala-box .froala-element blockquote {
  border-left: solid 5px #aaaaaa;
}
.dark-theme.froala-box .froala-element img {
  min-width: 32px !important;
  min-height: 32px !important;
}
.dark-theme.froala-box .froala-element img.fr-fil {
  padding: 10px 10px 10px 3px;
}
.dark-theme.froala-box .froala-element img.fr-fir {
  padding: 10px 3px 10px 10px;
}
.dark-theme.froala-box .froala-element img.fr-fin {
  padding: 10px 0;
}
.dark-theme.froala-box .froala-element img::selection {
  color: #ffffff;
}
.dark-theme.froala-box .froala-element img::-moz-selection {
  color: #ffffff;
}
.dark-theme.froala-box .froala-element span.f-img-editor:before {
  border: solid 2px #ffffff !important;
  outline: solid 1px #252525 !important;
}
.dark-theme.froala-box .froala-element span.f-img-editor.fr-fil {
  margin: 10px 10px 10px 3px;
}
.dark-theme.froala-box .froala-element span.f-img-editor.fr-fir {
  margin: 10px 3px 10px 10px;
}
.dark-theme.froala-box .froala-element span.f-img-editor.fr-fin {
  margin: 10px 0;
}
.dark-theme.froala-box .froala-element span.f-img-handle {
  height: 13px;
  width: 13px;
  border: solid 1px #252525 !important;
  background: #ffffff;
}
.dark-theme.froala-box .froala-element span.f-video-editor.active:after {
  border: solid 1px #252525;
}
.dark-theme.froala-box .froala-element.f-basic {
  border: solid 1px #252525;
}
.dark-theme.froala-box .froala-element table td {
  border: solid 1px #aaaaaa;
}
.dark-theme.froala-box.f-html .froala-element {
  background: #202020;
  color: #ffffff;
  font-family: 'Courier New', Monospace;
  font-size: 13px;
}
.dark-theme.froala-editor {
  background: #353535;
  border: solid 1px #252525;
  border-top: solid 5px #252525;
}
.dark-theme.froala-editor hr {
  border-top-color: #aaaaaa;
}
.dark-theme.froala-editor span.f-sep {
  border-right: solid 1px #aaaaaa;
  height: 35px;
}
.dark-theme.froala-editor button.fr-bttn,
.dark-theme.froala-editor button.fr-trigger {
  background: transparent;
  color: #ffffff;
  font-size: 16px;
  line-height: 35px;
  width: 40px;
}
.dark-theme.froala-editor button.fr-bttn img,
.dark-theme.froala-editor button.fr-trigger img {
  max-width: 40px;
  max-height: 35px;
}
.dark-theme.froala-editor button.fr-bttn:disabled,
.dark-theme.froala-editor button.fr-trigger:disabled {
  color: #aaaaaa !important;
}
.dark-theme.froala-editor button.fr-bttn:disabled:after,
.dark-theme.froala-editor button.fr-trigger:disabled:after {
  border-top-color: #aaaaaa !important;
}
.dark-theme.froala-editor.ie8 button.fr-bttn:hover,
.dark-theme.froala-editor.ie8 button.fr-trigger:hover {
  background: #2c82c9;
  color: #ffffff;
}
.dark-theme.froala-editor.ie8 button.fr-bttn:hover:after,
.dark-theme.froala-editor.ie8 button.fr-trigger:hover:after {
  border-top-color: #ffffff;
}
.dark-theme.froala-editor .bttn-wrapper:not(.touch) button.fr-bttn:hover,
.dark-theme.froala-editor .froala-popup button.fr-bttn:hover,
.dark-theme.froala-editor .bttn-wrapper:not(.touch) button.fr-trigger:hover,
.dark-theme.froala-editor .froala-popup button.fr-trigger:hover {
  background: #2c82c9;
  color: #ffffff;
}
.dark-theme.froala-editor .bttn-wrapper:not(.touch) button.fr-bttn:hover:after,
.dark-theme.froala-editor .froala-popup button.fr-bttn:hover:after,
.dark-theme.froala-editor .bttn-wrapper:not(.touch) button.fr-trigger:hover:after,
.dark-theme.froala-editor .froala-popup button.fr-trigger:hover:after {
  border-top-color: #ffffff;
}
.dark-theme.froala-editor .fr-bttn.active {
  color: #2c82c9;
  background: transparent;
}
.dark-theme.froala-editor .fr-trigger:after {
  border-top-color: #ffffff;
}
.dark-theme.froala-editor .fr-trigger.active {
  color: #ffffff;
  background: #2c82c9;
}
.dark-theme.froala-editor .fr-trigger.active:after {
  border-top-color: #ffffff !important;
}
.dark-theme.froala-editor .fr-dropdown .fr-dropdown-menu {
  background: #353535;
  border: solid 1px #252525;
}
.dark-theme.froala-editor .fr-dropdown .fr-dropdown-menu li.active a {
  background: #ffffff !important;
  color: #353535 !important;
}
.dark-theme.froala-editor .fr-dropdown .fr-dropdown-menu li a {
  color: #ffffff;
}
.dark-theme.froala-editor .fr-dropdown .fr-dropdown-menu li a:hover {
  background: #ffffff !important;
  color: #353535 !important;
}
.dark-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li:hover > a,
.dark-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li.hover > a {
  background: #ffffff;
  color: #353535;
}
.dark-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li > ul {
  background: #353535;
  color: #ffffff;
  border: solid 1px #252525;
}
.dark-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li > div {
  background: #353535;
  color: #ffffff;
  border: solid 1px #252525;
}
.dark-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li > div > span > span {
  border: solid 1px #aaaaaa;
}
.dark-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li > div > span:hover > span,
.dark-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li > div > span.hover > span {
  background: rgba(61, 142, 185, 0.3);
  border: solid 1px #3d8eb9;
}
.dark-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > hr {
  border-top: solid 1px #252525;
}
.dark-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu p {
  color: #ffffff;
}
.dark-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu p a.fr-bttn {
  color: #ffffff;
}
.dark-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu p a.fr-bttn:hover {
  color: #2c82c9;
}
.dark-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu .fr-color-bttn {
  border: solid 1px #353535;
}
.dark-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu .fr-color-bttn.active {
  border: solid 1px #353535;
  outline: solid 1px #ffffff;
}
.dark-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu .fr-color-bttn.active:after {
  color: #ffffff;
}
.dark-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu .fr-color-bttn:hover:not(:focus):not(:active) {
  border: solid 1px #ffffff;
}
.dark-theme.froala-editor .froala-popup {
  background: #353535;
}
.dark-theme.froala-editor .froala-popup h4 {
  color: #ffffff;
}
.dark-theme.froala-editor .froala-popup h4 i {
  color: #aaaaaa;
}
.dark-theme.froala-editor .froala-popup h4 i.fa-external-link {
  color: #ffffff;
}
.dark-theme.froala-editor .froala-popup h4 i.fa-external-link:hover {
  color: #2c82c9;
}
.dark-theme.froala-editor .froala-popup h4 i:hover {
  color: #ffffff;
}
.dark-theme.froala-editor .froala-popup button:not(.fr-bttn):not(.fr-trigger) {
  color: #ffffff;
}
.dark-theme.froala-editor .froala-popup button:not(.fr-bttn):not(.fr-trigger).f-ok {
  background: #2c82c9;
  color: #ffffff;
}
.dark-theme.froala-editor .froala-popup button:not(.fr-bttn):not(.fr-trigger).f-unlink {
  background: #b8312f;
  color: #ffffff;
}
.dark-theme.froala-editor .froala-popup button:not(.fr-bttn):not(.fr-trigger):hover,
.dark-theme.froala-editor .froala-popup button:not(.fr-bttn):not(.fr-trigger):focus {
  background: #ffffff;
  color: #353535;
}
.dark-theme.froala-editor .froala-popup div.f-popup-line.f-popup-toolbar {
  background: #353535;
}
.dark-theme.froala-editor .froala-popup div.f-popup-line label {
  color: #ffffff;
}
.dark-theme.froala-editor .froala-popup div.f-popup-line input[type="text"] {
  border: solid 1px #aaaaaa;
}
.dark-theme.froala-editor .froala-popup div.f-popup-line input[type="text"]:focus {
  border: solid 1px #54acd2;
}
.dark-theme.froala-editor .froala-popup div.f-popup-line input[type="text"]:disabled {
  background: #ffffff;
  color: #aaaaaa;
}
.dark-theme.froala-editor .froala-popup div.f-popup-line textarea {
  border: solid 1px #aaaaaa;
}
.dark-theme.froala-editor .froala-popup div.f-popup-line textarea:focus {
  border: solid 1px #54acd2;
}
.dark-theme.froala-editor .froala-popup.froala-image-editor-popup div.f-popup-line + div.f-popup-line {
  border-top: solid 1px #aaaaaa;
}
.dark-theme.froala-editor .froala-popup.froala-video-popup p.or {
  color: #ffffff;
}
.dark-theme.froala-editor .froala-popup.froala-image-popup div.f-popup-line.drop-upload div.f-upload {
  color: #ffffff;
  border: dashed 2px #aaaaaa;
}
.dark-theme.froala-editor .froala-popup.froala-image-popup div.f-popup-line.drop-upload div.f-upload:hover {
  border: dashed 2px #eeeeee;
}
.dark-theme.froala-editor .froala-popup.froala-image-popup div.f-popup-line.drop-upload div.f-upload.f-hover {
  border: dashed 2px #61bd6d;
}
.dark-theme.froala-editor .froala-popup.froala-image-popup div.f-popup-line button.f-browse {
  background: #475577;
  color: #ffffff;
}
.dark-theme.froala-editor .froala-popup.froala-image-popup div.f-popup-line button.f-browse:hover {
  background: #ffffff;
  color: #353535;
}
.dark-theme.froala-editor .froala-popup.froala-image-popup div.f-popup-line + div.f-popup-line {
  border-top: solid 1px #aaaaaa;
}
.dark-theme.froala-editor .froala-popup.froala-image-popup p.f-progress {
  background-color: #61bd6d;
}
.dark-theme.froala-editor .froala-popup.froala-image-popup p.f-progress span {
  background-color: #61bd6d;
  color: #ffffff;
}
.dark-theme.froala-editor .froala-popup.froala-link-popup div.f-popup-line .f-browse-links {
  background: #475577;
  color: #ffffff;
}
.dark-theme.froala-editor .froala-popup.froala-link-popup div.f-popup-line .f-browse-links:hover {
  background: #ffffff;
  color: #353535;
}
.dark-theme.froala-editor .froala-popup.froala-link-popup div.f-popup-line ul {
  background: #353535;
  border: solid 1px #252525;
}
.dark-theme.froala-editor .froala-popup.froala-link-popup div.f-popup-line ul li {
  color: #ffffff;
}
.dark-theme.froala-editor .froala-popup.froala-link-popup div.f-popup-line ul li + li {
  border-top: solid 1px #252525;
}
.dark-theme.froala-editor .froala-popup.froala-link-popup div.f-popup-line ul li:hover {
  background: #ffffff;
  color: #353535;
}
.dark-theme.froala-modal .f-modal-wrapper {
  background: #353535;
  border: solid 1px #252525;
  border-top: solid 5px #252525;
}
.dark-theme.froala-modal .f-modal-wrapper h4 {
  color: #ffffff;
}
.dark-theme.froala-modal .f-modal-wrapper h4 i {
  color: #aaaaaa;
}
.dark-theme.froala-modal .f-modal-wrapper h4 i:hover {
  color: #ffffff;
}
.dark-theme.froala-modal .f-modal-wrapper div.f-image-list div.f-empty {
  background: #aaaaaa;
}
.dark-theme.froala-modal .f-modal-wrapper div.f-image-list div .f-delete-img {
  background: #b8312f;
  color: #ffffff;
}
.dark-theme.froala-modal .f-modal-wrapper div.f-image-list:not(.f-touch) div:hover .f-delete-img:hover {
  background: #ffffff;
  color: #353535;
}
.froala-overlay {
  background: #000000;
}
