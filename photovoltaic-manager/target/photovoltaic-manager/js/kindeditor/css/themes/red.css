/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */
.red-theme.froala-box .html-switch {
  border-color: #aaaaaa;
}
.red-theme.froala-box .froala-element hr {
  border-top-color: #cccccc;
}
.red-theme.froala-box .froala-element.f-placeholder:before {
  color: #cccccc;
}
.red-theme.froala-box .froala-element pre {
  border: solid 1px #cccccc;
  background: #fcfcfc;
  border-radius: 4px;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.red-theme.froala-box .froala-element blockquote {
  border-left: solid 5px #cccccc;
}
.red-theme.froala-box .froala-element img {
  min-width: 32px !important;
  min-height: 32px !important;
}
.red-theme.froala-box .froala-element img.fr-fil {
  padding: 10px 10px 10px 3px;
}
.red-theme.froala-box .froala-element img.fr-fir {
  padding: 10px 3px 10px 10px;
}
.red-theme.froala-box .froala-element img.fr-fin {
  padding: 10px 0;
}
.red-theme.froala-box .froala-element img::selection {
  color: #ffffff;
}
.red-theme.froala-box .froala-element img::-moz-selection {
  color: #ffffff;
}
.red-theme.froala-box .froala-element span.f-img-editor:before {
  border: solid 2px #ffffff !important;
  outline: solid 1px #252525 !important;
}
.red-theme.froala-box .froala-element span.f-img-editor.fr-fil {
  margin: 10px 10px 10px 3px;
}
.red-theme.froala-box .froala-element span.f-img-editor.fr-fir {
  margin: 10px 3px 10px 10px;
}
.red-theme.froala-box .froala-element span.f-img-editor.fr-fin {
  margin: 10px 0;
}
.red-theme.froala-box .froala-element span.f-img-handle {
  height: 13px;
  width: 13px;
  border: solid 1px #252525 !important;
  background: #ffffff;
}
.red-theme.froala-box .froala-element span.f-video-editor.active:after {
  border: solid 1px #252525;
}
.red-theme.froala-box .froala-element.f-basic {
  border: solid 1px #252525;
}
.red-theme.froala-box .froala-element table td {
  border: solid 1px #cccccc;
}
.red-theme.froala-box.f-html .froala-element {
  background: #202020;
  color: #ffffff;
  font-family: 'Courier New', Monospace;
  font-size: 13px;
}
.red-theme.froala-editor {
  background: #b8312f;
  border: solid 1px #252525;
  border-top: solid 5px #252525;
}
.red-theme.froala-editor hr {
  border-top-color: #cccccc;
}
.red-theme.froala-editor span.f-sep {
  border-right: solid 1px #cccccc;
  height: 35px;
}
.red-theme.froala-editor button.fr-bttn,
.red-theme.froala-editor button.fr-trigger {
  background: transparent;
  color: #ffffff;
  font-size: 16px;
  line-height: 35px;
  width: 40px;
}
.red-theme.froala-editor button.fr-bttn img,
.red-theme.froala-editor button.fr-trigger img {
  max-width: 40px;
  max-height: 35px;
}
.red-theme.froala-editor button.fr-bttn:disabled,
.red-theme.froala-editor button.fr-trigger:disabled {
  color: #aaaaaa !important;
}
.red-theme.froala-editor button.fr-bttn:disabled:after,
.red-theme.froala-editor button.fr-trigger:disabled:after {
  border-top-color: #aaaaaa !important;
}
.red-theme.froala-editor.ie8 button.fr-bttn:hover,
.red-theme.froala-editor.ie8 button.fr-trigger:hover {
  background: #f7da64;
  color: #b8312f;
}
.red-theme.froala-editor.ie8 button.fr-bttn:hover:after,
.red-theme.froala-editor.ie8 button.fr-trigger:hover:after {
  border-top-color: #b8312f;
}
.red-theme.froala-editor .bttn-wrapper:not(.touch) button.fr-bttn:hover,
.red-theme.froala-editor .froala-popup button.fr-bttn:hover,
.red-theme.froala-editor .bttn-wrapper:not(.touch) button.fr-trigger:hover,
.red-theme.froala-editor .froala-popup button.fr-trigger:hover {
  background: #f7da64;
  color: #b8312f;
}
.red-theme.froala-editor .bttn-wrapper:not(.touch) button.fr-bttn:hover:after,
.red-theme.froala-editor .froala-popup button.fr-bttn:hover:after,
.red-theme.froala-editor .bttn-wrapper:not(.touch) button.fr-trigger:hover:after,
.red-theme.froala-editor .froala-popup button.fr-trigger:hover:after {
  border-top-color: #b8312f;
}
.red-theme.froala-editor .fr-bttn.active {
  color: #f7da64;
  background: transparent;
}
.red-theme.froala-editor .fr-trigger:after {
  border-top-color: #ffffff;
}
.red-theme.froala-editor .fr-trigger.active {
  color: #b8312f;
  background: #f7da64;
}
.red-theme.froala-editor .fr-trigger.active:after {
  border-top-color: #b8312f !important;
}
.red-theme.froala-editor .fr-dropdown .fr-dropdown-menu {
  background: #ffffff;
  border: solid 1px #b8312f;
}
.red-theme.froala-editor .fr-dropdown .fr-dropdown-menu li.active a {
  background: #444444 !important;
  color: #ffffff !important;
}
.red-theme.froala-editor .fr-dropdown .fr-dropdown-menu li a {
  color: #444444;
}
.red-theme.froala-editor .fr-dropdown .fr-dropdown-menu li a:hover {
  background: #444444 !important;
  color: #ffffff !important;
}
.red-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li:hover > a,
.red-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li.hover > a {
  background: #444444;
  color: #ffffff;
}
.red-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li > ul {
  background: #ffffff;
  color: #444444;
  border: solid 1px #b8312f;
}
.red-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li > div {
  background: #ffffff;
  color: #444444;
  border: solid 1px #b8312f;
}
.red-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li > div > span > span {
  border: solid 1px #cccccc;
}
.red-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li > div > span:hover > span,
.red-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > li > div > span.hover > span {
  background: rgba(247, 218, 100, 0.3);
  border: solid 1px #f7da64;
}
.red-theme.froala-editor .fr-dropdown .fr-dropdown-menu.fr-table > hr {
  border-top: solid 1px #b8312f;
}
.red-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu p {
  color: #444444;
}
.red-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu p a.fr-bttn {
  color: #444444;
}
.red-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu p a.fr-bttn:hover {
  color: #f7da64;
}
.red-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu .fr-color-bttn {
  border: solid 1px #ffffff;
}
.red-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu .fr-color-bttn.active {
  border: solid 1px #ffffff;
  outline: solid 1px #252525;
}
.red-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu .fr-color-bttn.active:after {
  color: #ffffff;
}
.red-theme.froala-editor .fr-dropdown.fr-color-picker .fr-dropdown-menu .fr-color-bttn:hover:not(:focus):not(:active) {
  border: solid 1px #252525;
}
.red-theme.froala-editor .froala-popup {
  background: #ffffff;
}
.red-theme.froala-editor .froala-popup h4 {
  color: #444444;
}
.red-theme.froala-editor .froala-popup h4 i {
  color: #cccccc;
}
.red-theme.froala-editor .froala-popup h4 i.fa-external-link {
  color: #444444;
}
.red-theme.froala-editor .froala-popup h4 i.fa-external-link:hover {
  color: #f7da64;
}
.red-theme.froala-editor .froala-popup h4 i:hover {
  color: #444444;
}
.red-theme.froala-editor .froala-popup button:not(.fr-bttn):not(.fr-trigger) {
  color: #ffffff;
}
.red-theme.froala-editor .froala-popup button:not(.fr-bttn):not(.fr-trigger).f-ok {
  background: #b8312f;
  color: #ffffff;
}
.red-theme.froala-editor .froala-popup button:not(.fr-bttn):not(.fr-trigger).f-unlink {
  background: #353535;
  color: #ffffff;
}
.red-theme.froala-editor .froala-popup button:not(.fr-bttn):not(.fr-trigger):hover,
.red-theme.froala-editor .froala-popup button:not(.fr-bttn):not(.fr-trigger):focus {
  background: #f7da64;
  color: #b8312f;
}
.red-theme.froala-editor .froala-popup div.f-popup-line.f-popup-toolbar {
  background: #b8312f;
}
.red-theme.froala-editor .froala-popup div.f-popup-line label {
  color: #444444;
}
.red-theme.froala-editor .froala-popup div.f-popup-line input[type="text"] {
  border: solid 1px #cccccc;
}
.red-theme.froala-editor .froala-popup div.f-popup-line input[type="text"]:focus {
  border: solid 1px #f7da64;
}
.red-theme.froala-editor .froala-popup div.f-popup-line input[type="text"]:disabled {
  background: #ffffff;
  color: #aaaaaa;
}
.red-theme.froala-editor .froala-popup div.f-popup-line textarea {
  border: solid 1px #cccccc;
}
.red-theme.froala-editor .froala-popup div.f-popup-line textarea:focus {
  border: solid 1px #f7da64;
}
.red-theme.froala-editor .froala-popup.froala-image-editor-popup div.f-popup-line + div.f-popup-line {
  border-top: solid 1px #cccccc;
}
.red-theme.froala-editor .froala-popup.froala-video-popup p.or {
  color: #444444;
}
.red-theme.froala-editor .froala-popup.froala-image-popup div.f-popup-line.drop-upload div.f-upload {
  color: #444444;
  border: dashed 2px #cccccc;
}
.red-theme.froala-editor .froala-popup.froala-image-popup div.f-popup-line.drop-upload div.f-upload:hover {
  border: dashed 2px #353535;
}
.red-theme.froala-editor .froala-popup.froala-image-popup div.f-popup-line.drop-upload div.f-upload.f-hover {
  border: dashed 2px #61bd6d;
}
.red-theme.froala-editor .froala-popup.froala-image-popup div.f-popup-line button.f-browse {
  background: #f7da64;
  color: #b8312f;
}
.red-theme.froala-editor .froala-popup.froala-image-popup div.f-popup-line button.f-browse:hover {
  background: #b8312f;
  color: #ffffff;
}
.red-theme.froala-editor .froala-popup.froala-image-popup div.f-popup-line + div.f-popup-line {
  border-top: solid 1px #cccccc;
}
.red-theme.froala-editor .froala-popup.froala-image-popup p.f-progress {
  background-color: #61bd6d;
}
.red-theme.froala-editor .froala-popup.froala-image-popup p.f-progress span {
  background-color: #61bd6d;
  color: #ffffff;
}
.red-theme.froala-editor .froala-popup.froala-link-popup div.f-popup-line .f-browse-links {
  background: #f7da64;
  color: #b8312f;
}
.red-theme.froala-editor .froala-popup.froala-link-popup div.f-popup-line .f-browse-links:hover {
  background: #b8312f;
  color: #ffffff;
}
.red-theme.froala-editor .froala-popup.froala-link-popup div.f-popup-line ul {
  background: #ffffff;
  border: solid 1px #b8312f;
}
.red-theme.froala-editor .froala-popup.froala-link-popup div.f-popup-line ul li {
  color: #444444;
}
.red-theme.froala-editor .froala-popup.froala-link-popup div.f-popup-line ul li + li {
  border-top: solid 1px #b8312f;
}
.red-theme.froala-editor .froala-popup.froala-link-popup div.f-popup-line ul li:hover {
  background: #444444;
  color: #ffffff;
}
.red-theme.froala-modal .f-modal-wrapper {
  background: #ffffff;
  border: solid 1px #252525;
  border-top: solid 5px #252525;
}
.red-theme.froala-modal .f-modal-wrapper h4 {
  color: #444444;
}
.red-theme.froala-modal .f-modal-wrapper h4 i {
  color: #cccccc;
}
.red-theme.froala-modal .f-modal-wrapper h4 i:hover {
  color: #444444;
}
.red-theme.froala-modal .f-modal-wrapper div.f-image-list div.f-empty {
  background: #cccccc;
}
.red-theme.froala-modal .f-modal-wrapper div.f-image-list div .f-delete-img {
  background: #353535;
  color: #ffffff;
}
.red-theme.froala-modal .f-modal-wrapper div.f-image-list:not(.f-touch) div:hover .f-delete-img:hover {
  background: #f7da64;
  color: #b8312f;
}
.froala-overlay {
  background: #000000;
}
