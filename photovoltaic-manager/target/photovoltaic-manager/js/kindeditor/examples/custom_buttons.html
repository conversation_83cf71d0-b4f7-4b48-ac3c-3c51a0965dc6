<!DOCTYPE html>
<html>
<head>
	<link href="../css/font-awesome.min.css" rel="stylesheet" type="text/css">
	<link href="../css/froala_editor.min.css" rel="stylesheet" type="text/css">


    <style>
        body {
            text-align: center;
        }

        section {
            width: 80%;
            margin: auto;
            text-align: left;
        }
    </style>
</head>

<body>
  <section id="editor">
      <div id='edit' style="margin-top: 30px;">
          <img class="fr-fir" src="../img/old_clock.jpg" alt="Old Clock" width="300"/>

          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec facilisis diam in odio iaculis blandit. Nunc eu mauris sit amet purus viverra gravida ut a dui. Vivamus nec rutrum augue, pharetra faucibus purus. Maecenas non orci sagittis, vehicula lorem et, dignissim nunc. Suspendisse suscipit, diam non varius facilisis, enim libero tincidunt magna, sit amet iaculis eros libero sit amet eros. Vestibulum a rhoncus felis. Nam lacus nulla, consequat ac lacus sit amet, accumsan pellentesque risus. Aenean viverra mi at urna mattis fermentum. Curabitur porta metus in tortor elementum, in semper nulla ullamcorper. Vestibulum mattis tempor tortor quis gravida. In rhoncus risus nibh. Nullam condimentum dapibus massa vel fringilla. Sed hendrerit sed est quis facilisis. Ut sit amet nibh sem. Pellentesque imperdiet mollis libero.</p>

          <p><a href="http://google.com" title="Aenean sed hendrerit">Aenean sed hendrerit</a> velit. Nullam eu mi dolor. Maecenas et erat risus. Nulla ac auctor diam, non aliquet ante. Fusce ullamcorper, ipsum id tempor lacinia, sem tellus malesuada libero, quis ornare sem massa in orci. Sed dictum dictum tristique. Proin eros turpis, ultricies eu sapien eget, ornare rutrum ipsum. Pellentesque eros nisl, ornare nec ipsum sed, aliquet sollicitudin erat. Nulla tincidunt porta vehicula.</p>

          <p>Nullam laoreet imperdiet orci ac euismod. Curabitur vel lectus nisi. Phasellus accumsan aliquet augue, eu rutrum tellus iaculis in. Nunc viverra ultrices mollis. Curabitur malesuada nunc massa, ut imperdiet arcu lobortis sed. Cras ac arcu mauris. Maecenas id lectus nisl. Donec consectetur scelerisque quam at ultricies. Nam quis magna iaculis, condimentum metus ut, elementum metus. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vivamus id tempus nisi.</p>
      </div>
  </section>

  <script src="../js/libs/jquery-1.11.1.min.js"></script>
  <script src="../js/froala_editor.min.js"></script>
  <!--[if lt IE 9]>
    <script src="../js/froala_editor_ie8.min.js"></script>
  <![endif]-->
  <script src="../js/plugins/tables.min.js"></script>
  <script src="../js/plugins/lists.min.js"></script>
  <script src="../js/plugins/colors.min.js"></script>
  <script src="../js/plugins/media_manager.min.js"></script>
  <script src="../js/plugins/font_family.min.js"></script>
  <script src="../js/plugins/font_size.min.js"></script>
  <script src="../js/plugins/block_styles.min.js"></script>
  <script src="../js/plugins/video.min.js"></script>

  <script>
      $(function(){
          $('#edit').editable({inlineMode: false, buttons: ['bold', 'italic', 'subscript', 'superscript', 'formatBlock', 'alert', 'clear', 'image', 'add'], blockTags: ['p'], customButtons: {
            alert: {
              title: 'Alert',
              icon: {
                type: 'font',
                value: 'fa fa-info'
              },
              callback: function () {
                alert ('Hello!')
              },
              refresh: function () {
                console.log ('do alert refresh');
              }
            },

            clear: {
              title: 'Remove HTML',
              icon: {
                type: 'txt',
                value: 'x'
              },
              callback: function (){
                this.setHTML('');
              }
            },

            image: {
              title: 'Save',
              icon: {
                type: 'img', // Recommended size: 40 x 35
                value: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCI+PHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjZWVlIi8+PHRleHQgdGV4dC1hbmNob3I9Im1pZGRsZSIgeD0iMzIiIHk9IjMyIiBzdHlsZT0iZmlsbDojYWFhO2ZvbnQtd2VpZ2h0OmJvbGQ7Zm9udC1zaXplOjEycHg7Zm9udC1mYW1pbHk6QXJpYWwsSGVsdmV0aWNhLHNhbnMtc2VyaWY7ZG9taW5hbnQtYmFzZWxpbmU6Y2VudHJhbCI+NjR4NjQ8L3RleHQ+PC9zdmc+'
              },
              callback: function (){
                alert('This is an image button');
              }
            },

            add: {
              title: 'Insert HTML',
              icon: {
                type: 'font',
                value: 'fa fa-plus'
              },
              callback: function (){
                this.insertHTML('My HTML');
              }
            }
          }
          })
      });
  </script>
</body>
</html>
