!function(t){"function"==typeof define&&define.amd?define(["jquery","lodash"],t):t(jQ<PERSON>y,_)}(function(t,e){var i=window,n={is_intercepted:function(t,e){return!(t.x+t.width<=e.x||e.x+e.width<=t.x||t.y+t.height<=e.y||e.y+e.height<=t.y)},sort:function(t,i,n){return n=n||e.chain(t).map(function(t){return t.x+t.width}).max().value(),i=-1!=i?1:-1,e.sortBy(t,function(t){return i*(t.x+t.y*n)})},create_stylesheet:function(t){var e=document.createElement("style");return e.setAttribute("type","text/css"),e.setAttribute("data-gs-id",t),e.styleSheet?e.styleSheet.cssText="":e.appendChild(document.createTextNode("")),document.getElementsByTagName("head")[0].appendChild(e),e.sheet},insert_css_rule:function(t,e,i,n){"function"==typeof t.insertRule?t.insertRule(e+"{"+i+"}",n):"function"==typeof t.addRule&&t.addRule(e,i,n)},toBool:function(t){return"boolean"==typeof t?t:"string"==typeof t?(t=t.toLowerCase(),!(""==t||"no"==t||"false"==t||"0"==t)):Boolean(t)}},o=0,s=function(t,e,i,n,o){this.width=t,this["float"]=i||!1,this.height=n||0,this.nodes=o||[],this.onchange=e||function(){},this._update_counter=0,this._float=this["float"]};s.prototype.batch_update=function(){this._update_counter=1,this["float"]=!0},s.prototype.commit=function(){this._update_counter=0,0==this._update_counter&&(this["float"]=this._float,this._pack_nodes(),this._notify())},s.prototype._fix_collisions=function(t){this._sort_nodes(-1);var i=t,o=Boolean(e.find(this.nodes,function(t){return t.locked}));for(this["float"]||o||(i={x:0,y:t.y,width:this.width,height:t.height});;){var s=e.find(this.nodes,function(e){return e!=t&&n.is_intercepted(e,i)},this);if("undefined"==typeof s)return;this.move_node(s,s.x,t.y+t.height,s.width,s.height,!0)}},s.prototype.is_area_empty=function(t,i,o,s){var a={x:t||0,y:i||0,width:o||1,height:s||1},h=e.find(this.nodes,function(t){return n.is_intercepted(t,a)},this);return null==h},s.prototype._sort_nodes=function(t){this.nodes=n.sort(this.nodes,t,this.width)},s.prototype._pack_nodes=function(){this._sort_nodes(),this["float"]?e.each(this.nodes,function(t){if(!t._updating&&"undefined"!=typeof t._orig_y&&t.y!=t._orig_y)for(var i=t.y;i>=t._orig_y;){var o=e.chain(this.nodes).find(function(e){return t!=e&&n.is_intercepted({x:t.x,y:i,width:t.width,height:t.height},e)}).value();o||(t._dirty=!0,t.y=i),--i}},this):e.each(this.nodes,function(t,i){if(!t.locked)for(;t.y>0;){var o=t.y-1,s=0==i;if(i>0){var a=e.chain(this.nodes).take(i).find(function(e){return n.is_intercepted({x:t.x,y:o,width:t.width,height:t.height},e)}).value();s="undefined"==typeof a}if(!s)break;t._dirty=t.y!=o,t.y=o}},this)},s.prototype._prepare_node=function(t,i){return t=e.defaults(t||{},{width:1,height:1,x:0,y:0}),t.x=parseInt(""+t.x),t.y=parseInt(""+t.y),t.width=parseInt(""+t.width),t.height=parseInt(""+t.height),t.auto_position=t.auto_position||!1,t.no_resize=t.no_resize||!1,t.no_move=t.no_move||!1,t.width>this.width?t.width=this.width:t.width<1&&(t.width=1),t.height<1&&(t.height=1),t.x<0&&(t.x=0),t.x+t.width>this.width&&(i?t.width=this.width-t.x:t.x=this.width-t.width),t.y<0&&(t.y=0),t},s.prototype._notify=function(){if(!this._update_counter){var t=Array.prototype.slice.call(arguments,1).concat(this.get_dirty_nodes());t=t.concat(this.get_dirty_nodes()),this.onchange(t)}},s.prototype.clean_nodes=function(){e.each(this.nodes,function(t){t._dirty=!1})},s.prototype.get_dirty_nodes=function(){return e.filter(this.nodes,function(t){return t._dirty})},s.prototype.add_node=function(t){if(t=this._prepare_node(t),"undefined"!=typeof t.max_width&&(t.width=Math.min(t.width,t.max_width)),"undefined"!=typeof t.max_height&&(t.height=Math.min(t.height,t.max_height)),"undefined"!=typeof t.min_width&&(t.width=Math.max(t.width,t.min_width)),"undefined"!=typeof t.min_height&&(t.height=Math.max(t.height,t.min_height)),t._id=++o,t._dirty=!0,t.auto_position){this._sort_nodes();for(var i=0;;++i){var s=i%this.width,a=Math.floor(i/this.width);if(!(s+t.width>this.width||e.find(this.nodes,function(e){return n.is_intercepted({x:s,y:a,width:t.width,height:t.height},e)}))){t.x=s,t.y=a;break}}}return this.nodes.push(t),this._fix_collisions(t),this._pack_nodes(),this._notify(),t},s.prototype.remove_node=function(t){t._id=null,this.nodes=e.without(this.nodes,t),this._pack_nodes(),this._notify(t)},s.prototype.can_move_node=function(i,n,o,a,h){var d=Boolean(e.find(this.nodes,function(t){return t.locked}));if(!this.height&&!d)return!0;var r,_=new s(this.width,null,this["float"],0,e.map(this.nodes,function(e){return e==i?r=t.extend({},e):t.extend({},e)}));_.move_node(r,n,o,a,h);var l=!0;return d&&(l&=!Boolean(e.find(_.nodes,function(t){return t!=r&&Boolean(t.locked)&&Boolean(t._dirty)}))),this.height&&(l&=_.get_grid_height()<=this.height),l},s.prototype.can_be_placed_with_respect_to_height=function(i){if(!this.height)return!0;var n=new s(this.width,null,this["float"],0,e.map(this.nodes,function(e){return t.extend({},e)}));return n.add_node(i),n.get_grid_height()<=this.height},s.prototype.move_node=function(t,e,i,n,o,s){if("number"!=typeof e&&(e=t.x),"number"!=typeof i&&(i=t.y),"number"!=typeof n&&(n=t.width),"number"!=typeof o&&(o=t.height),"undefined"!=typeof t.max_width&&(n=Math.min(n,t.max_width)),"undefined"!=typeof t.max_height&&(o=Math.min(o,t.max_height)),"undefined"!=typeof t.min_width&&(n=Math.max(n,t.min_width)),"undefined"!=typeof t.min_height&&(o=Math.max(o,t.min_height)),t.x==e&&t.y==i&&t.width==n&&t.height==o)return t;var a=t.width!=n;return t._dirty=!0,t.x=e,t.y=i,t.width=n,t.height=o,t=this._prepare_node(t,a),this._fix_collisions(t),s||(this._pack_nodes(),this._notify()),t},s.prototype.get_grid_height=function(){return e.reduce(this.nodes,function(t,e){return Math.max(t,e.y+e.height)},0)},s.prototype.begin_update=function(t){e.each(this.nodes,function(t){t._orig_y=t.y}),t._updating=!0},s.prototype.end_update=function(){e.each(this.nodes,function(t){t._orig_y=t.y});var t=e.find(this.nodes,function(t){return t._updating});t&&(t._updating=!1)};var a=function(i,n){var o,a=this;this.container=t(i),n.item_class=n.item_class||"grid-stack-item";var h=this.container.closest("."+n.item_class).size()>0;if(this.opts=e.defaults(n||{},{width:parseInt(this.container.attr("data-gs-width"))||12,height:parseInt(this.container.attr("data-gs-height"))||0,item_class:"grid-stack-item",placeholder_class:"grid-stack-placeholder",handle:".grid-stack-item-content",cell_height:60,vertical_margin:20,auto:!0,min_width:768,"float":!1,_class:"grid-stack-"+(1e4*Math.random()).toFixed(0),animate:Boolean(this.container.attr("data-gs-animate"))||!1,always_show_resize_handle:n.always_show_resize_handle||!1,resizable:e.defaults(n.resizable||{},{autoHide:!n.always_show_resize_handle,handles:"se"}),draggable:e.defaults(n.draggable||{},{handle:".grid-stack-item-content",scroll:!1,appendTo:"body"})}),this.opts.is_nested=h,this.container.addClass(this.opts._class),h&&this.container.addClass("grid-stack-nested"),this._init_styles(),this.grid=new s(this.opts.width,function(t){var i=0;e.each(t,function(t){null==t._id?t.el.remove():(t.el.attr("data-gs-x",t.x).attr("data-gs-y",t.y).attr("data-gs-width",t.width).attr("data-gs-height",t.height),i=Math.max(i,t.y+t.height))}),a._update_styles(i+10)},this.opts["float"],this.opts.height),this.opts.auto){var d=[],r=this;this.container.children("."+this.opts.item_class).each(function(e,i){i=t(i),d.push({el:i,i:parseInt(i.attr("data-gs-x"))+parseInt(i.attr("data-gs-y"))*r.opts.width})}),e.chain(d).sortBy(function(t){return t.i}).each(function(t){a._prepare_element(t.el)}).value()}this.set_animation(this.opts.animate),this.placeholder=t('<div class="'+this.opts.placeholder_class+" "+this.opts.item_class+'"><div class="placeholder-content" /></div>').hide(),this.container.append(this.placeholder),this.container.height(this.grid.get_grid_height()*(this.opts.cell_height+this.opts.vertical_margin)-this.opts.vertical_margin);var _=function(){if(a._is_one_column_mode()){if(o)return;o=!0,a.grid._sort_nodes(),e.each(a.grid.nodes,function(t){a.container.append(t.el),t.no_move||t.el.draggable("disable"),t.no_resize||t.el.resizable("disable")})}else{if(!o)return;o=!1,e.each(a.grid.nodes,function(t){t.no_move||t.el.draggable("enable"),t.no_resize||t.el.resizable("enable")})}};t(window).resize(_),_()};return a.prototype._init_styles=function(){this._styles_id&&t('[data-gs-id="'+this._styles_id+'"]').remove(),this._styles_id="gridstack-style-"+(1e5*Math.random()).toFixed(),this._styles=n.create_stylesheet(this._styles_id),null!=this._styles&&(this._styles._max=0)},a.prototype._update_styles=function(t){if(null!=this._styles){var e="."+this.opts._class+" ."+this.opts.item_class;if("undefined"==typeof t&&(t=this._styles._max,this._init_styles(),this._update_container_height()),0==this._styles._max&&n.insert_css_rule(this._styles,e,"min-height: "+this.opts.cell_height+"px;",0),t>this._styles._max){for(var i=this._styles._max;t>i;++i)n.insert_css_rule(this._styles,e+'[data-gs-height="'+(i+1)+'"]',"height: "+(this.opts.cell_height*(i+1)+this.opts.vertical_margin*i)+"px;",i),n.insert_css_rule(this._styles,e+'[data-gs-min-height="'+(i+1)+'"]',"min-height: "+(this.opts.cell_height*(i+1)+this.opts.vertical_margin*i)+"px;",i),n.insert_css_rule(this._styles,e+'[data-gs-max-height="'+(i+1)+'"]',"max-height: "+(this.opts.cell_height*(i+1)+this.opts.vertical_margin*i)+"px;",i),n.insert_css_rule(this._styles,e+'[data-gs-y="'+i+'"]',"top: "+(this.opts.cell_height*i+this.opts.vertical_margin*i)+"px;",i);this._styles._max=t}}},a.prototype._update_container_height=function(){this.grid._update_counter||this.container.height(this.grid.get_grid_height()*(this.opts.cell_height+this.opts.vertical_margin)-this.opts.vertical_margin)},a.prototype._is_one_column_mode=function(){return t(window).width()<=this.opts.min_width},a.prototype._prepare_element=function(i){var o=this;i=t(i),i.addClass(this.opts.item_class);var s=o.grid.add_node({x:i.attr("data-gs-x"),y:i.attr("data-gs-y"),width:i.attr("data-gs-width"),height:i.attr("data-gs-height"),max_width:i.attr("data-gs-max-width"),min_width:i.attr("data-gs-min-width"),max_height:i.attr("data-gs-max-height"),min_height:i.attr("data-gs-min-height"),auto_position:n.toBool(i.attr("data-gs-auto-position")),no_resize:n.toBool(i.attr("data-gs-no-resize")),no_move:n.toBool(i.attr("data-gs-no-move")),locked:n.toBool(i.attr("data-gs-locked")),el:i});i.data("_gridstack_node",s);var a,h,d=function(){var e=t(this);o.grid.clean_nodes(),o.grid.begin_update(s),a=Math.ceil(e.outerWidth()/e.attr("data-gs-width")),h=o.opts.cell_height+o.opts.vertical_margin,o.placeholder.attr("data-gs-x",e.attr("data-gs-x")).attr("data-gs-y",e.attr("data-gs-y")).attr("data-gs-width",e.attr("data-gs-width")).attr("data-gs-height",e.attr("data-gs-height")).show(),s.el=o.placeholder,i.resizable("option","minWidth",a*(s.min_width||1)),i.resizable("option","minHeight",o.opts.cell_height*(s.min_height||1))},r=function(){var e=t(this);s.el=e,o.placeholder.hide(),e.attr("data-gs-x",s.x).attr("data-gs-y",s.y).attr("data-gs-width",s.width).attr("data-gs-height",s.height).removeAttr("style"),o._update_container_height(),o.container.trigger("change",[o.grid.get_dirty_nodes()]),o.grid.end_update()};i.draggable(e.extend(this.opts.draggable,{start:d,stop:r,drag:function(t,e){var i=Math.round(e.position.left/a),n=Math.floor((e.position.top+h/2)/h);o.grid.can_move_node(s,i,n,s.width,s.height)&&(o.grid.move_node(s,i,n),o._update_container_height())},containment:this.opts.is_nested?this.container.parent():null})).resizable(e.extend(this.opts.resizable,{start:d,stop:r,resize:function(t,e){var i=Math.round(e.position.left/a),n=Math.floor((e.position.top+h/2)/h),d=Math.round(e.size.width/a),r=Math.round(e.size.height/h);o.grid.can_move_node(s,i,n,d,r)&&(o.grid.move_node(s,i,n,d,r),o._update_container_height())}})),(s.no_move||this._is_one_column_mode())&&i.draggable("disable"),(s.no_resize||this._is_one_column_mode())&&i.resizable("disable"),i.attr("data-gs-locked",s.locked?"yes":null)},a.prototype.set_animation=function(t){t?this.container.addClass("grid-stack-animate"):this.container.removeClass("grid-stack-animate")},a.prototype.add_widget=function(e,i,n,o,s,a){return e=t(e),"undefined"!=typeof i&&e.attr("data-gs-x",i),"undefined"!=typeof n&&e.attr("data-gs-y",n),"undefined"!=typeof o&&e.attr("data-gs-width",o),"undefined"!=typeof s&&e.attr("data-gs-height",s),"undefined"!=typeof a&&e.attr("data-gs-auto-position",a?"yes":null),this.container.append(e),this._prepare_element(e),this._update_container_height(),e},a.prototype.will_it_fit=function(t,e,i,n,o){var s={x:t,y:e,width:i,height:n,auto_position:o};return this.grid.can_be_placed_with_respect_to_height(s)},a.prototype.remove_widget=function(e,i){i="undefined"==typeof i?!0:i,e=t(e);var n=e.data("_gridstack_node");this.grid.remove_node(n),e.removeData("_gridstack_node"),this._update_container_height(),i&&e.remove()},a.prototype.remove_all=function(t){e.each(this.grid.nodes,function(e){this.remove_widget(e.el,t)},this),this.grid.nodes=[],this._update_container_height()},a.prototype.resizable=function(e,i){return e=t(e),e.each(function(e,n){n=t(n);var o=n.data("_gridstack_node");"undefined"!=typeof o&&null!=o&&(o.no_resize=!i,n.resizable(o.no_resize?"disable":"enable"))}),this},a.prototype.movable=function(e,i){return e=t(e),e.each(function(e,n){n=t(n);var o=n.data("_gridstack_node");"undefined"!=typeof o&&null!=o&&(o.no_move=!i,n.draggable(o.no_move?"disable":"enable"))}),this},a.prototype.disable=function(){this.movable(this.container.children("."+this.opts.item_class),!1),this.resizable(this.container.children("."+this.opts.item_class),!1)},a.prototype.enable=function(){this.movable(this.container.children("."+this.opts.item_class),!0),this.resizable(this.container.children("."+this.opts.item_class),!0)},a.prototype.locked=function(e,i){return e=t(e),e.each(function(e,n){n=t(n);var o=n.data("_gridstack_node");"undefined"!=typeof o&&null!=o&&(o.locked=i||!1,n.attr("data-gs-locked",o.locked?"yes":null))}),this},a.prototype._update_element=function(e,i){e=t(e).first();var n=e.data("_gridstack_node");if("undefined"!=typeof n&&null!=n){var o=this;o.grid.clean_nodes(),o.grid.begin_update(n),i.call(this,e,n),o._update_container_height(),o.container.trigger("change",[o.grid.get_dirty_nodes()]),o.grid.end_update()}},a.prototype.resize=function(t,e,i){this._update_element(t,function(t,n){e=null!=e&&"undefined"!=typeof e?e:n.width,i=null!=i&&"undefined"!=typeof i?i:n.height,this.grid.move_node(n,n.x,n.y,e,i)})},a.prototype.move=function(t,e,i){this._update_element(t,function(t,n){e=null!=e&&"undefined"!=typeof e?e:n.x,i=null!=i&&"undefined"!=typeof i?i:n.y,this.grid.move_node(n,e,i,n.width,n.height)})},a.prototype.update=function(t,e,i,n,o){this._update_element(t,function(t,s){e=null!=e&&"undefined"!=typeof e?e:s.x,i=null!=i&&"undefined"!=typeof i?i:s.y,n=null!=n&&"undefined"!=typeof n?n:s.width,o=null!=o&&"undefined"!=typeof o?o:s.height,this.grid.move_node(s,e,i,n,o)})},a.prototype.cell_height=function(t){return"undefined"==typeof t?this.opts.cell_height:(t=parseInt(t),void(t!=this.opts.cell_height&&(this.opts.cell_height=t||this.opts.cell_height,this._update_styles())))},a.prototype.cell_width=function(){var t=this.container.children("."+this.opts.item_class).first();return Math.ceil(t.outerWidth()/t.attr("data-gs-width"))},a.prototype.get_cell_from_pixel=function(t){var e=this.container.position(),i=t.left-e.left,n=t.top-e.top,o=Math.floor(this.container.width()/this.opts.width),s=this.opts.cell_height+this.opts.vertical_margin;return{x:Math.floor(i/o),y:Math.floor(n/s)}},a.prototype.batch_update=function(){this.grid.batch_update()},a.prototype.commit=function(){this.grid.commit(),this._update_container_height()},a.prototype.is_area_empty=function(t,e,i,n){return this.grid.is_area_empty(t,e,i,n)},i.GridStackUI=a,i.GridStackUI.Utils=n,t.fn.gridstack=function(e){return this.each(function(){t(this).data("gridstack")||t(this).data("gridstack",new a(this,e))})},i.GridStackUI});
//# sourceMappingURL=gridstack.min.map