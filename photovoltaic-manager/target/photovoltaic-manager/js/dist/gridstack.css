:root .grid-stack-item > .ui-resizable-handle {
  filter: none;
}

.grid-stack {
  position: relative;
}
.grid-stack .grid-stack-placeholder > .placeholder-content {
  border: 8px dashed lightgray;
  margin: 0;
  position: absolute;
  top: 0;
  left: 10px;
  right: 10px;
  bottom: 0;
  width: 60px;
  height:80px;
  z-index: 0 !important;
}
.grid-stack > .grid-stack-item {
  min-width: 0.125%;
  position: absolute;
  padding: 0;
}
.grid-stack > .grid-stack-item > .grid-stack-item-content {
  margin: 0;
  position: absolute;
  top: 0;
  left: 5px;
  right: 10px;
  bottom: 0;
  width: auto;
  z-index: 0 !important;
  overflow-x: hidden;
  overflow-y: auto;
}
.grid-stack > .grid-stack-item > .ui-resizable-handle {
  position: absolute;
  font-size: 0.1px;
  display: block;
  -ms-touch-action: none;
  touch-action: none;
}
.grid-stack > .grid-stack-item.ui-resizable-disabled > .ui-resizable-handle, .grid-stack > .grid-stack-item.ui-resizable-autohide > .ui-resizable-handle {
  display: none;
}
.grid-stack > .grid-stack-item.ui-draggable-dragging, .grid-stack > .grid-stack-item.ui-resizable-resizing {
  z-index: 100;
}
.grid-stack > .grid-stack-item.ui-draggable-dragging > .grid-stack-item-content,
.grid-stack > .grid-stack-item.ui-draggable-dragging > .grid-stack-item-content, .grid-stack > .grid-stack-item.ui-resizable-resizing > .grid-stack-item-content,
.grid-stack > .grid-stack-item.ui-resizable-resizing > .grid-stack-item-content {
  box-shadow: 1px 4px 6px rgba(0, 0, 0, 0.2);
  opacity: 0.8;
}
.grid-stack > .grid-stack-item > .ui-resizable-se,
.grid-stack > .grid-stack-item > .ui-resizable-sw {
  text-align: right;
  color: gray;
  padding: 2px 3px 0 0;
  margin: 0;
  font: normal normal normal 10px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.grid-stack > .grid-stack-item > .ui-resizable-se::before,
.grid-stack > .grid-stack-item > .ui-resizable-sw::before {
  content: "\f065";
}
.grid-stack > .grid-stack-item > .ui-resizable-se {
  display: inline-block;
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}
.grid-stack > .grid-stack-item > .ui-resizable-nw {
  cursor: nw-resize;
  width: 20px;
  height: 20px;
  left: 10px;
  top: 0;
}
.grid-stack > .grid-stack-item > .ui-resizable-n {
  cursor: n-resize;
  height: 10px;
  top: 0;
  left: 25px;
  right: 25px;
}
.grid-stack > .grid-stack-item > .ui-resizable-ne {
  cursor: ne-resize;
  width: 20px;
  height: 20px;
  right: 10px;
  top: 0;
}
.grid-stack > .grid-stack-item > .ui-resizable-e {
  cursor: e-resize;
  width: 10px;
  right: 10px;
  top: 15px;
  bottom: 15px;
}
.grid-stack > .grid-stack-item > .ui-resizable-se {
  cursor: se-resize;
  width: 20px;
  height: 20px;
  right: 10px;
  bottom: 0;
}
.grid-stack > .grid-stack-item > .ui-resizable-s {
  cursor: s-resize;
  height: 10px;
  left: 25px;
  bottom: 0;
  right: 25px;
}
.grid-stack > .grid-stack-item > .ui-resizable-sw {
  cursor: sw-resize;
  width: 20px;
  height: 20px;
  left: 10px;
  bottom: 0;
}
.grid-stack > .grid-stack-item > .ui-resizable-w {
  cursor: w-resize;
  width: 10px;
  left: 10px;
  top: 15px;
  bottom: 15px;
}
.grid-stack > .grid-stack-item[data-gs-width='1'] {
	width: 0.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='1'] {
	left: 0.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='1'] {
	min-width: 0.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='1'] {
	max-width: 0.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='2'] {
	width: 0.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='2'] {
	left: 0.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='2'] {
	min-width: 0.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='2'] {
	max-width: 0.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='3'] {
	width: 0.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='3'] {
	left: 0.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='3'] {
	min-width: 0.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='3'] {
	max-width: 0.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='4'] {
	width: 1%;
}

.grid-stack > .grid-stack-item[data-gs-x='4'] {
	left: 1%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='4'] {
	min-width: 1%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='4'] {
	max-width: 1%;
}

.grid-stack > .grid-stack-item[data-gs-width='5'] {
	width: 1.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='5'] {
	left: 1.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='5'] {
	min-width: 1.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='5'] {
	max-width: 1.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='6'] {
	width: 1.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='6'] {
	left: 1.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='6'] {
	min-width: 1.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='6'] {
	max-width: 1.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='7'] {
	width: 1.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='7'] {
	left: 1.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='7'] {
	min-width: 1.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='7'] {
	max-width: 1.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='8'] {
	width: 2%;
}

.grid-stack > .grid-stack-item[data-gs-x='8'] {
	left: 2%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='8'] {
	min-width: 2%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='8'] {
	max-width: 2%;
}

.grid-stack > .grid-stack-item[data-gs-width='9'] {
	width: 2.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='9'] {
	left: 2.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='9'] {
	min-width: 2.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='9'] {
	max-width: 2.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='10'] {
	width: 2.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='10'] {
	left: 2.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='10'] {
	min-width: 2.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='10'] {
	max-width: 2.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='11'] {
	width: 2.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='11'] {
	left: 2.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='11'] {
	min-width: 2.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='11'] {
	max-width: 2.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='12'] {
	width: 3%;
}

.grid-stack > .grid-stack-item[data-gs-x='12'] {
	left: 3%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='12'] {
	min-width: 3%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='12'] {
	max-width: 3%;
}

.grid-stack > .grid-stack-item[data-gs-width='13'] {
	width: 3.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='13'] {
	left: 3.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='13'] {
	min-width: 3.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='13'] {
	max-width: 3.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='14'] {
	width: 3.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='14'] {
	left: 3.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='14'] {
	min-width: 3.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='14'] {
	max-width: 3.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='15'] {
	width: 3.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='15'] {
	left: 3.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='15'] {
	min-width: 3.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='15'] {
	max-width: 3.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='16'] {
	width: 4%;
}

.grid-stack > .grid-stack-item[data-gs-x='16'] {
	left: 4%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='16'] {
	min-width: 4%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='16'] {
	max-width: 4%;
}

.grid-stack > .grid-stack-item[data-gs-width='17'] {
	width: 4.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='17'] {
	left: 4.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='17'] {
	min-width: 4.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='17'] {
	max-width: 4.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='18'] {
	width: 4.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='18'] {
	left: 4.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='18'] {
	min-width: 4.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='18'] {
	max-width: 4.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='19'] {
	width: 4.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='19'] {
	left: 4.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='19'] {
	min-width: 4.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='19'] {
	max-width: 4.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='20'] {
	width: 5%;
}

.grid-stack > .grid-stack-item[data-gs-x='20'] {
	left: 5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='20'] {
	min-width: 5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='20'] {
	max-width: 5%;
}

.grid-stack > .grid-stack-item[data-gs-width='21'] {
	width: 5.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='21'] {
	left: 5.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='21'] {
	min-width: 5.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='21'] {
	max-width: 5.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='22'] {
	width: 5.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='22'] {
	left: 5.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='22'] {
	min-width: 5.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='22'] {
	max-width: 5.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='23'] {
	width: 5.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='23'] {
	left: 5.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='23'] {
	min-width: 5.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='23'] {
	max-width: 5.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='24'] {
	width: 6%;
}

.grid-stack > .grid-stack-item[data-gs-x='24'] {
	left: 6%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='24'] {
	min-width: 6%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='24'] {
	max-width: 6%;
}

.grid-stack > .grid-stack-item[data-gs-width='25'] {
	width: 6.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='25'] {
	left: 6.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='25'] {
	min-width: 6.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='25'] {
	max-width: 6.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='26'] {
	width: 6.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='26'] {
	left: 6.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='26'] {
	min-width: 6.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='26'] {
	max-width: 6.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='27'] {
	width: 6.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='27'] {
	left: 6.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='27'] {
	min-width: 6.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='27'] {
	max-width: 6.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='28'] {
	width: 7%;
}

.grid-stack > .grid-stack-item[data-gs-x='28'] {
	left: 7%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='28'] {
	min-width: 7%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='28'] {
	max-width: 7%;
}

.grid-stack > .grid-stack-item[data-gs-width='29'] {
	width: 7.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='29'] {
	left: 7.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='29'] {
	min-width: 7.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='29'] {
	max-width: 7.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='30'] {
	width: 7.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='30'] {
	left: 7.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='30'] {
	min-width: 7.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='30'] {
	max-width: 7.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='31'] {
	width: 7.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='31'] {
	left: 7.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='31'] {
	min-width: 7.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='31'] {
	max-width: 7.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='32'] {
	width: 8%;
}

.grid-stack > .grid-stack-item[data-gs-x='32'] {
	left: 8%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='32'] {
	min-width: 8%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='32'] {
	max-width: 8%;
}

.grid-stack > .grid-stack-item[data-gs-width='33'] {
	width: 8.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='33'] {
	left: 8.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='33'] {
	min-width: 8.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='33'] {
	max-width: 8.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='34'] {
	width: 8.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='34'] {
	left: 8.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='34'] {
	min-width: 8.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='34'] {
	max-width: 8.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='35'] {
	width: 8.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='35'] {
	left: 8.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='35'] {
	min-width: 8.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='35'] {
	max-width: 8.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='36'] {
	width: 9%;
}

.grid-stack > .grid-stack-item[data-gs-x='36'] {
	left: 9%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='36'] {
	min-width: 9%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='36'] {
	max-width: 9%;
}

.grid-stack > .grid-stack-item[data-gs-width='37'] {
	width: 9.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='37'] {
	left: 9.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='37'] {
	min-width: 9.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='37'] {
	max-width: 9.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='38'] {
	width: 9.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='38'] {
	left: 9.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='38'] {
	min-width: 9.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='38'] {
	max-width: 9.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='39'] {
	width: 9.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='39'] {
	left: 9.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='39'] {
	min-width: 9.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='39'] {
	max-width: 9.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='40'] {
	width: 10%;
}

.grid-stack > .grid-stack-item[data-gs-x='40'] {
	left: 10%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='40'] {
	min-width: 10%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='40'] {
	max-width: 10%;
}

.grid-stack > .grid-stack-item[data-gs-width='41'] {
	width: 10.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='41'] {
	left: 10.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='41'] {
	min-width: 10.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='41'] {
	max-width: 10.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='42'] {
	width: 10.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='42'] {
	left: 10.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='42'] {
	min-width: 10.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='42'] {
	max-width: 10.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='43'] {
	width: 10.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='43'] {
	left: 10.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='43'] {
	min-width: 10.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='43'] {
	max-width: 10.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='44'] {
	width: 11%;
}

.grid-stack > .grid-stack-item[data-gs-x='44'] {
	left: 11%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='44'] {
	min-width: 11%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='44'] {
	max-width: 11%;
}

.grid-stack > .grid-stack-item[data-gs-width='45'] {
	width: 11.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='45'] {
	left: 11.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='45'] {
	min-width: 11.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='45'] {
	max-width: 11.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='46'] {
	width: 11.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='46'] {
	left: 11.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='46'] {
	min-width: 11.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='46'] {
	max-width: 11.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='47'] {
	width: 11.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='47'] {
	left: 11.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='47'] {
	min-width: 11.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='47'] {
	max-width: 11.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='48'] {
	width: 12%;
}

.grid-stack > .grid-stack-item[data-gs-x='48'] {
	left: 12%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='48'] {
	min-width: 12%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='48'] {
	max-width: 12%;
}

.grid-stack > .grid-stack-item[data-gs-width='49'] {
	width: 12.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='49'] {
	left: 12.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='49'] {
	min-width: 12.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='49'] {
	max-width: 12.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='50'] {
	width: 12.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='50'] {
	left: 12.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='50'] {
	min-width: 12.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='50'] {
	max-width: 12.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='51'] {
	width: 12.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='51'] {
	left: 12.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='51'] {
	min-width: 12.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='51'] {
	max-width: 12.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='52'] {
	width: 13%;
}

.grid-stack > .grid-stack-item[data-gs-x='52'] {
	left: 13%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='52'] {
	min-width: 13%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='52'] {
	max-width: 13%;
}

.grid-stack > .grid-stack-item[data-gs-width='53'] {
	width: 13.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='53'] {
	left: 13.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='53'] {
	min-width: 13.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='53'] {
	max-width: 13.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='54'] {
	width: 13.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='54'] {
	left: 13.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='54'] {
	min-width: 13.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='54'] {
	max-width: 13.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='55'] {
	width: 13.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='55'] {
	left: 13.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='55'] {
	min-width: 13.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='55'] {
	max-width: 13.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='56'] {
	width: 14%;
}

.grid-stack > .grid-stack-item[data-gs-x='56'] {
	left: 14%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='56'] {
	min-width: 14%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='56'] {
	max-width: 14%;
}

.grid-stack > .grid-stack-item[data-gs-width='57'] {
	width: 14.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='57'] {
	left: 14.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='57'] {
	min-width: 14.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='57'] {
	max-width: 14.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='58'] {
	width: 14.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='58'] {
	left: 14.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='58'] {
	min-width: 14.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='58'] {
	max-width: 14.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='59'] {
	width: 14.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='59'] {
	left: 14.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='59'] {
	min-width: 14.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='59'] {
	max-width: 14.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='60'] {
	width: 15%;
}

.grid-stack > .grid-stack-item[data-gs-x='60'] {
	left: 15%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='60'] {
	min-width: 15%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='60'] {
	max-width: 15%;
}

.grid-stack > .grid-stack-item[data-gs-width='61'] {
	width: 15.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='61'] {
	left: 15.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='61'] {
	min-width: 15.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='61'] {
	max-width: 15.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='62'] {
	width: 15.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='62'] {
	left: 15.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='62'] {
	min-width: 15.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='62'] {
	max-width: 15.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='63'] {
	width: 15.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='63'] {
	left: 15.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='63'] {
	min-width: 15.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='63'] {
	max-width: 15.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='64'] {
	width: 16%;
}

.grid-stack > .grid-stack-item[data-gs-x='64'] {
	left: 16%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='64'] {
	min-width: 16%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='64'] {
	max-width: 16%;
}

.grid-stack > .grid-stack-item[data-gs-width='65'] {
	width: 16.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='65'] {
	left: 16.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='65'] {
	min-width: 16.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='65'] {
	max-width: 16.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='66'] {
	width: 16.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='66'] {
	left: 16.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='66'] {
	min-width: 16.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='66'] {
	max-width: 16.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='67'] {
	width: 16.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='67'] {
	left: 16.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='67'] {
	min-width: 16.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='67'] {
	max-width: 16.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='68'] {
	width: 17%;
}

.grid-stack > .grid-stack-item[data-gs-x='68'] {
	left: 17%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='68'] {
	min-width: 17%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='68'] {
	max-width: 17%;
}

.grid-stack > .grid-stack-item[data-gs-width='69'] {
	width: 17.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='69'] {
	left: 17.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='69'] {
	min-width: 17.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='69'] {
	max-width: 17.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='70'] {
	width: 17.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='70'] {
	left: 17.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='70'] {
	min-width: 17.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='70'] {
	max-width: 17.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='71'] {
	width: 17.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='71'] {
	left: 17.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='71'] {
	min-width: 17.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='71'] {
	max-width: 17.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='72'] {
	width: 18%;
}

.grid-stack > .grid-stack-item[data-gs-x='72'] {
	left: 18%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='72'] {
	min-width: 18%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='72'] {
	max-width: 18%;
}

.grid-stack > .grid-stack-item[data-gs-width='73'] {
	width: 18.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='73'] {
	left: 18.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='73'] {
	min-width: 18.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='73'] {
	max-width: 18.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='74'] {
	width: 18.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='74'] {
	left: 18.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='74'] {
	min-width: 18.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='74'] {
	max-width: 18.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='75'] {
	width: 18.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='75'] {
	left: 18.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='75'] {
	min-width: 18.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='75'] {
	max-width: 18.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='76'] {
	width: 19%;
}

.grid-stack > .grid-stack-item[data-gs-x='76'] {
	left: 19%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='76'] {
	min-width: 19%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='76'] {
	max-width: 19%;
}

.grid-stack > .grid-stack-item[data-gs-width='77'] {
	width: 19.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='77'] {
	left: 19.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='77'] {
	min-width: 19.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='77'] {
	max-width: 19.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='78'] {
	width: 19.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='78'] {
	left: 19.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='78'] {
	min-width: 19.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='78'] {
	max-width: 19.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='79'] {
	width: 19.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='79'] {
	left: 19.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='79'] {
	min-width: 19.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='79'] {
	max-width: 19.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='80'] {
	width: 20%;
}

.grid-stack > .grid-stack-item[data-gs-x='80'] {
	left: 20%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='80'] {
	min-width: 20%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='80'] {
	max-width: 20%;
}

.grid-stack > .grid-stack-item[data-gs-width='81'] {
	width: 20.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='81'] {
	left: 20.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='81'] {
	min-width: 20.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='81'] {
	max-width: 20.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='82'] {
	width: 20.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='82'] {
	left: 20.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='82'] {
	min-width: 20.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='82'] {
	max-width: 20.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='83'] {
	width: 20.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='83'] {
	left: 20.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='83'] {
	min-width: 20.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='83'] {
	max-width: 20.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='84'] {
	width: 21%;
}

.grid-stack > .grid-stack-item[data-gs-x='84'] {
	left: 21%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='84'] {
	min-width: 21%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='84'] {
	max-width: 21%;
}

.grid-stack > .grid-stack-item[data-gs-width='85'] {
	width: 21.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='85'] {
	left: 21.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='85'] {
	min-width: 21.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='85'] {
	max-width: 21.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='86'] {
	width: 21.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='86'] {
	left: 21.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='86'] {
	min-width: 21.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='86'] {
	max-width: 21.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='87'] {
	width: 21.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='87'] {
	left: 21.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='87'] {
	min-width: 21.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='87'] {
	max-width: 21.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='88'] {
	width: 22%;
}

.grid-stack > .grid-stack-item[data-gs-x='88'] {
	left: 22%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='88'] {
	min-width: 22%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='88'] {
	max-width: 22%;
}

.grid-stack > .grid-stack-item[data-gs-width='89'] {
	width: 22.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='89'] {
	left: 22.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='89'] {
	min-width: 22.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='89'] {
	max-width: 22.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='90'] {
	width: 22.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='90'] {
	left: 22.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='90'] {
	min-width: 22.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='90'] {
	max-width: 22.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='91'] {
	width: 22.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='91'] {
	left: 22.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='91'] {
	min-width: 22.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='91'] {
	max-width: 22.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='92'] {
	width: 23%;
}

.grid-stack > .grid-stack-item[data-gs-x='92'] {
	left: 23%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='92'] {
	min-width: 23%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='92'] {
	max-width: 23%;
}

.grid-stack > .grid-stack-item[data-gs-width='93'] {
	width: 23.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='93'] {
	left: 23.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='93'] {
	min-width: 23.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='93'] {
	max-width: 23.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='94'] {
	width: 23.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='94'] {
	left: 23.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='94'] {
	min-width: 23.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='94'] {
	max-width: 23.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='95'] {
	width: 23.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='95'] {
	left: 23.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='95'] {
	min-width: 23.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='95'] {
	max-width: 23.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='96'] {
	width: 24%;
}

.grid-stack > .grid-stack-item[data-gs-x='96'] {
	left: 24%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='96'] {
	min-width: 24%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='96'] {
	max-width: 24%;
}

.grid-stack > .grid-stack-item[data-gs-width='97'] {
	width: 24.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='97'] {
	left: 24.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='97'] {
	min-width: 24.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='97'] {
	max-width: 24.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='98'] {
	width: 24.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='98'] {
	left: 24.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='98'] {
	min-width: 24.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='98'] {
	max-width: 24.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='99'] {
	width: 24.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='99'] {
	left: 24.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='99'] {
	min-width: 24.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='99'] {
	max-width: 24.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='100'] {
	width: 25%;
}

.grid-stack > .grid-stack-item[data-gs-x='100'] {
	left: 25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='100'] {
	min-width: 25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='100'] {
	max-width: 25%;
}

.grid-stack > .grid-stack-item[data-gs-width='101'] {
	width: 25.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='101'] {
	left: 25.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='101'] {
	min-width: 25.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='101'] {
	max-width: 25.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='102'] {
	width: 25.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='102'] {
	left: 25.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='102'] {
	min-width: 25.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='102'] {
	max-width: 25.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='103'] {
	width: 25.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='103'] {
	left: 25.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='103'] {
	min-width: 25.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='103'] {
	max-width: 25.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='104'] {
	width: 26%;
}

.grid-stack > .grid-stack-item[data-gs-x='104'] {
	left: 26%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='104'] {
	min-width: 26%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='104'] {
	max-width: 26%;
}

.grid-stack > .grid-stack-item[data-gs-width='105'] {
	width: 26.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='105'] {
	left: 26.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='105'] {
	min-width: 26.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='105'] {
	max-width: 26.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='106'] {
	width: 26.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='106'] {
	left: 26.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='106'] {
	min-width: 26.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='106'] {
	max-width: 26.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='107'] {
	width: 26.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='107'] {
	left: 26.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='107'] {
	min-width: 26.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='107'] {
	max-width: 26.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='108'] {
	width: 27%;
}

.grid-stack > .grid-stack-item[data-gs-x='108'] {
	left: 27%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='108'] {
	min-width: 27%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='108'] {
	max-width: 27%;
}

.grid-stack > .grid-stack-item[data-gs-width='109'] {
	width: 27.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='109'] {
	left: 27.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='109'] {
	min-width: 27.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='109'] {
	max-width: 27.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='110'] {
	width: 27.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='110'] {
	left: 27.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='110'] {
	min-width: 27.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='110'] {
	max-width: 27.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='111'] {
	width: 27.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='111'] {
	left: 27.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='111'] {
	min-width: 27.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='111'] {
	max-width: 27.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='112'] {
	width: 28%;
}

.grid-stack > .grid-stack-item[data-gs-x='112'] {
	left: 28%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='112'] {
	min-width: 28%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='112'] {
	max-width: 28%;
}

.grid-stack > .grid-stack-item[data-gs-width='113'] {
	width: 28.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='113'] {
	left: 28.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='113'] {
	min-width: 28.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='113'] {
	max-width: 28.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='114'] {
	width: 28.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='114'] {
	left: 28.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='114'] {
	min-width: 28.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='114'] {
	max-width: 28.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='115'] {
	width: 28.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='115'] {
	left: 28.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='115'] {
	min-width: 28.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='115'] {
	max-width: 28.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='116'] {
	width: 29%;
}

.grid-stack > .grid-stack-item[data-gs-x='116'] {
	left: 29%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='116'] {
	min-width: 29%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='116'] {
	max-width: 29%;
}

.grid-stack > .grid-stack-item[data-gs-width='117'] {
	width: 29.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='117'] {
	left: 29.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='117'] {
	min-width: 29.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='117'] {
	max-width: 29.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='118'] {
	width: 29.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='118'] {
	left: 29.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='118'] {
	min-width: 29.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='118'] {
	max-width: 29.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='119'] {
	width: 29.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='119'] {
	left: 29.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='119'] {
	min-width: 29.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='119'] {
	max-width: 29.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='120'] {
	width: 30%;
}

.grid-stack > .grid-stack-item[data-gs-x='120'] {
	left: 30%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='120'] {
	min-width: 30%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='120'] {
	max-width: 30%;
}

.grid-stack > .grid-stack-item[data-gs-width='121'] {
	width: 30.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='121'] {
	left: 30.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='121'] {
	min-width: 30.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='121'] {
	max-width: 30.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='122'] {
	width: 30.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='122'] {
	left: 30.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='122'] {
	min-width: 30.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='122'] {
	max-width: 30.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='123'] {
	width: 30.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='123'] {
	left: 30.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='123'] {
	min-width: 30.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='123'] {
	max-width: 30.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='124'] {
	width: 31%;
}

.grid-stack > .grid-stack-item[data-gs-x='124'] {
	left: 31%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='124'] {
	min-width: 31%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='124'] {
	max-width: 31%;
}

.grid-stack > .grid-stack-item[data-gs-width='125'] {
	width: 31.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='125'] {
	left: 31.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='125'] {
	min-width: 31.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='125'] {
	max-width: 31.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='126'] {
	width: 31.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='126'] {
	left: 31.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='126'] {
	min-width: 31.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='126'] {
	max-width: 31.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='127'] {
	width: 31.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='127'] {
	left: 31.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='127'] {
	min-width: 31.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='127'] {
	max-width: 31.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='128'] {
	width: 32%;
}

.grid-stack > .grid-stack-item[data-gs-x='128'] {
	left: 32%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='128'] {
	min-width: 32%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='128'] {
	max-width: 32%;
}

.grid-stack > .grid-stack-item[data-gs-width='129'] {
	width: 32.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='129'] {
	left: 32.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='129'] {
	min-width: 32.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='129'] {
	max-width: 32.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='130'] {
	width: 32.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='130'] {
	left: 32.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='130'] {
	min-width: 32.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='130'] {
	max-width: 32.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='131'] {
	width: 32.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='131'] {
	left: 32.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='131'] {
	min-width: 32.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='131'] {
	max-width: 32.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='132'] {
	width: 33%;
}

.grid-stack > .grid-stack-item[data-gs-x='132'] {
	left: 33%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='132'] {
	min-width: 33%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='132'] {
	max-width: 33%;
}

.grid-stack > .grid-stack-item[data-gs-width='133'] {
	width: 33.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='133'] {
	left: 33.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='133'] {
	min-width: 33.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='133'] {
	max-width: 33.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='134'] {
	width: 33.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='134'] {
	left: 33.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='134'] {
	min-width: 33.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='134'] {
	max-width: 33.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='135'] {
	width: 33.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='135'] {
	left: 33.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='135'] {
	min-width: 33.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='135'] {
	max-width: 33.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='136'] {
	width: 34%;
}

.grid-stack > .grid-stack-item[data-gs-x='136'] {
	left: 34%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='136'] {
	min-width: 34%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='136'] {
	max-width: 34%;
}

.grid-stack > .grid-stack-item[data-gs-width='137'] {
	width: 34.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='137'] {
	left: 34.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='137'] {
	min-width: 34.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='137'] {
	max-width: 34.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='138'] {
	width: 34.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='138'] {
	left: 34.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='138'] {
	min-width: 34.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='138'] {
	max-width: 34.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='139'] {
	width: 34.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='139'] {
	left: 34.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='139'] {
	min-width: 34.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='139'] {
	max-width: 34.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='140'] {
	width: 35%;
}

.grid-stack > .grid-stack-item[data-gs-x='140'] {
	left: 35%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='140'] {
	min-width: 35%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='140'] {
	max-width: 35%;
}

.grid-stack > .grid-stack-item[data-gs-width='141'] {
	width: 35.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='141'] {
	left: 35.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='141'] {
	min-width: 35.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='141'] {
	max-width: 35.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='142'] {
	width: 35.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='142'] {
	left: 35.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='142'] {
	min-width: 35.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='142'] {
	max-width: 35.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='143'] {
	width: 35.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='143'] {
	left: 35.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='143'] {
	min-width: 35.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='143'] {
	max-width: 35.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='144'] {
	width: 36%;
}

.grid-stack > .grid-stack-item[data-gs-x='144'] {
	left: 36%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='144'] {
	min-width: 36%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='144'] {
	max-width: 36%;
}

.grid-stack > .grid-stack-item[data-gs-width='145'] {
	width: 36.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='145'] {
	left: 36.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='145'] {
	min-width: 36.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='145'] {
	max-width: 36.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='146'] {
	width: 36.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='146'] {
	left: 36.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='146'] {
	min-width: 36.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='146'] {
	max-width: 36.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='147'] {
	width: 36.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='147'] {
	left: 36.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='147'] {
	min-width: 36.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='147'] {
	max-width: 36.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='148'] {
	width: 37%;
}

.grid-stack > .grid-stack-item[data-gs-x='148'] {
	left: 37%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='148'] {
	min-width: 37%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='148'] {
	max-width: 37%;
}

.grid-stack > .grid-stack-item[data-gs-width='149'] {
	width: 37.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='149'] {
	left: 37.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='149'] {
	min-width: 37.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='149'] {
	max-width: 37.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='150'] {
	width: 37.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='150'] {
	left: 37.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='150'] {
	min-width: 37.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='150'] {
	max-width: 37.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='151'] {
	width: 37.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='151'] {
	left: 37.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='151'] {
	min-width: 37.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='151'] {
	max-width: 37.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='152'] {
	width: 38%;
}

.grid-stack > .grid-stack-item[data-gs-x='152'] {
	left: 38%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='152'] {
	min-width: 38%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='152'] {
	max-width: 38%;
}

.grid-stack > .grid-stack-item[data-gs-width='153'] {
	width: 38.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='153'] {
	left: 38.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='153'] {
	min-width: 38.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='153'] {
	max-width: 38.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='154'] {
	width: 38.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='154'] {
	left: 38.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='154'] {
	min-width: 38.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='154'] {
	max-width: 38.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='155'] {
	width: 38.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='155'] {
	left: 38.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='155'] {
	min-width: 38.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='155'] {
	max-width: 38.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='156'] {
	width: 39%;
}

.grid-stack > .grid-stack-item[data-gs-x='156'] {
	left: 39%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='156'] {
	min-width: 39%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='156'] {
	max-width: 39%;
}

.grid-stack > .grid-stack-item[data-gs-width='157'] {
	width: 39.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='157'] {
	left: 39.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='157'] {
	min-width: 39.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='157'] {
	max-width: 39.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='158'] {
	width: 39.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='158'] {
	left: 39.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='158'] {
	min-width: 39.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='158'] {
	max-width: 39.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='159'] {
	width: 39.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='159'] {
	left: 39.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='159'] {
	min-width: 39.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='159'] {
	max-width: 39.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='160'] {
	width: 40%;
}

.grid-stack > .grid-stack-item[data-gs-x='160'] {
	left: 40%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='160'] {
	min-width: 40%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='160'] {
	max-width: 40%;
}

.grid-stack > .grid-stack-item[data-gs-width='161'] {
	width: 40.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='161'] {
	left: 40.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='161'] {
	min-width: 40.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='161'] {
	max-width: 40.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='162'] {
	width: 40.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='162'] {
	left: 40.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='162'] {
	min-width: 40.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='162'] {
	max-width: 40.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='163'] {
	width: 40.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='163'] {
	left: 40.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='163'] {
	min-width: 40.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='163'] {
	max-width: 40.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='164'] {
	width: 41%;
}

.grid-stack > .grid-stack-item[data-gs-x='164'] {
	left: 41%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='164'] {
	min-width: 41%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='164'] {
	max-width: 41%;
}

.grid-stack > .grid-stack-item[data-gs-width='165'] {
	width: 41.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='165'] {
	left: 41.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='165'] {
	min-width: 41.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='165'] {
	max-width: 41.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='166'] {
	width: 41.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='166'] {
	left: 41.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='166'] {
	min-width: 41.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='166'] {
	max-width: 41.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='167'] {
	width: 41.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='167'] {
	left: 41.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='167'] {
	min-width: 41.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='167'] {
	max-width: 41.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='168'] {
	width: 42%;
}

.grid-stack > .grid-stack-item[data-gs-x='168'] {
	left: 42%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='168'] {
	min-width: 42%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='168'] {
	max-width: 42%;
}

.grid-stack > .grid-stack-item[data-gs-width='169'] {
	width: 42.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='169'] {
	left: 42.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='169'] {
	min-width: 42.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='169'] {
	max-width: 42.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='170'] {
	width: 42.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='170'] {
	left: 42.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='170'] {
	min-width: 42.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='170'] {
	max-width: 42.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='171'] {
	width: 42.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='171'] {
	left: 42.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='171'] {
	min-width: 42.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='171'] {
	max-width: 42.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='172'] {
	width: 43%;
}

.grid-stack > .grid-stack-item[data-gs-x='172'] {
	left: 43%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='172'] {
	min-width: 43%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='172'] {
	max-width: 43%;
}

.grid-stack > .grid-stack-item[data-gs-width='173'] {
	width: 43.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='173'] {
	left: 43.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='173'] {
	min-width: 43.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='173'] {
	max-width: 43.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='174'] {
	width: 43.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='174'] {
	left: 43.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='174'] {
	min-width: 43.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='174'] {
	max-width: 43.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='175'] {
	width: 43.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='175'] {
	left: 43.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='175'] {
	min-width: 43.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='175'] {
	max-width: 43.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='176'] {
	width: 44%;
}

.grid-stack > .grid-stack-item[data-gs-x='176'] {
	left: 44%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='176'] {
	min-width: 44%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='176'] {
	max-width: 44%;
}

.grid-stack > .grid-stack-item[data-gs-width='177'] {
	width: 44.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='177'] {
	left: 44.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='177'] {
	min-width: 44.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='177'] {
	max-width: 44.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='178'] {
	width: 44.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='178'] {
	left: 44.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='178'] {
	min-width: 44.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='178'] {
	max-width: 44.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='179'] {
	width: 44.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='179'] {
	left: 44.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='179'] {
	min-width: 44.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='179'] {
	max-width: 44.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='180'] {
	width: 45%;
}

.grid-stack > .grid-stack-item[data-gs-x='180'] {
	left: 45%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='180'] {
	min-width: 45%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='180'] {
	max-width: 45%;
}

.grid-stack > .grid-stack-item[data-gs-width='181'] {
	width: 45.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='181'] {
	left: 45.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='181'] {
	min-width: 45.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='181'] {
	max-width: 45.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='182'] {
	width: 45.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='182'] {
	left: 45.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='182'] {
	min-width: 45.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='182'] {
	max-width: 45.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='183'] {
	width: 45.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='183'] {
	left: 45.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='183'] {
	min-width: 45.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='183'] {
	max-width: 45.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='184'] {
	width: 46%;
}

.grid-stack > .grid-stack-item[data-gs-x='184'] {
	left: 46%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='184'] {
	min-width: 46%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='184'] {
	max-width: 46%;
}

.grid-stack > .grid-stack-item[data-gs-width='185'] {
	width: 46.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='185'] {
	left: 46.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='185'] {
	min-width: 46.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='185'] {
	max-width: 46.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='186'] {
	width: 46.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='186'] {
	left: 46.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='186'] {
	min-width: 46.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='186'] {
	max-width: 46.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='187'] {
	width: 46.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='187'] {
	left: 46.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='187'] {
	min-width: 46.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='187'] {
	max-width: 46.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='188'] {
	width: 47%;
}

.grid-stack > .grid-stack-item[data-gs-x='188'] {
	left: 47%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='188'] {
	min-width: 47%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='188'] {
	max-width: 47%;
}

.grid-stack > .grid-stack-item[data-gs-width='189'] {
	width: 47.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='189'] {
	left: 47.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='189'] {
	min-width: 47.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='189'] {
	max-width: 47.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='190'] {
	width: 47.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='190'] {
	left: 47.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='190'] {
	min-width: 47.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='190'] {
	max-width: 47.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='191'] {
	width: 47.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='191'] {
	left: 47.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='191'] {
	min-width: 47.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='191'] {
	max-width: 47.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='192'] {
	width: 48%;
}

.grid-stack > .grid-stack-item[data-gs-x='192'] {
	left: 48%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='192'] {
	min-width: 48%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='192'] {
	max-width: 48%;
}

.grid-stack > .grid-stack-item[data-gs-width='193'] {
	width: 48.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='193'] {
	left: 48.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='193'] {
	min-width: 48.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='193'] {
	max-width: 48.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='194'] {
	width: 48.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='194'] {
	left: 48.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='194'] {
	min-width: 48.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='194'] {
	max-width: 48.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='195'] {
	width: 48.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='195'] {
	left: 48.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='195'] {
	min-width: 48.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='195'] {
	max-width: 48.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='196'] {
	width: 49%;
}

.grid-stack > .grid-stack-item[data-gs-x='196'] {
	left: 49%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='196'] {
	min-width: 49%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='196'] {
	max-width: 49%;
}

.grid-stack > .grid-stack-item[data-gs-width='197'] {
	width: 49.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='197'] {
	left: 49.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='197'] {
	min-width: 49.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='197'] {
	max-width: 49.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='198'] {
	width: 49.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='198'] {
	left: 49.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='198'] {
	min-width: 49.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='198'] {
	max-width: 49.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='199'] {
	width: 49.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='199'] {
	left: 49.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='199'] {
	min-width: 49.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='199'] {
	max-width: 49.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='200'] {
	width: 50%;
}

.grid-stack > .grid-stack-item[data-gs-x='200'] {
	left: 50%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='200'] {
	min-width: 50%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='200'] {
	max-width: 50%;
}

.grid-stack > .grid-stack-item[data-gs-width='201'] {
	width: 50.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='201'] {
	left: 50.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='201'] {
	min-width: 50.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='201'] {
	max-width: 50.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='202'] {
	width: 50.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='202'] {
	left: 50.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='202'] {
	min-width: 50.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='202'] {
	max-width: 50.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='203'] {
	width: 50.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='203'] {
	left: 50.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='203'] {
	min-width: 50.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='203'] {
	max-width: 50.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='204'] {
	width: 51%;
}

.grid-stack > .grid-stack-item[data-gs-x='204'] {
	left: 51%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='204'] {
	min-width: 51%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='204'] {
	max-width: 51%;
}

.grid-stack > .grid-stack-item[data-gs-width='205'] {
	width: 51.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='205'] {
	left: 51.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='205'] {
	min-width: 51.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='205'] {
	max-width: 51.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='206'] {
	width: 51.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='206'] {
	left: 51.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='206'] {
	min-width: 51.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='206'] {
	max-width: 51.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='207'] {
	width: 51.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='207'] {
	left: 51.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='207'] {
	min-width: 51.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='207'] {
	max-width: 51.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='208'] {
	width: 52%;
}

.grid-stack > .grid-stack-item[data-gs-x='208'] {
	left: 52%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='208'] {
	min-width: 52%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='208'] {
	max-width: 52%;
}

.grid-stack > .grid-stack-item[data-gs-width='209'] {
	width: 52.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='209'] {
	left: 52.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='209'] {
	min-width: 52.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='209'] {
	max-width: 52.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='210'] {
	width: 52.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='210'] {
	left: 52.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='210'] {
	min-width: 52.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='210'] {
	max-width: 52.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='211'] {
	width: 52.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='211'] {
	left: 52.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='211'] {
	min-width: 52.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='211'] {
	max-width: 52.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='212'] {
	width: 53%;
}

.grid-stack > .grid-stack-item[data-gs-x='212'] {
	left: 53%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='212'] {
	min-width: 53%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='212'] {
	max-width: 53%;
}

.grid-stack > .grid-stack-item[data-gs-width='213'] {
	width: 53.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='213'] {
	left: 53.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='213'] {
	min-width: 53.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='213'] {
	max-width: 53.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='214'] {
	width: 53.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='214'] {
	left: 53.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='214'] {
	min-width: 53.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='214'] {
	max-width: 53.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='215'] {
	width: 53.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='215'] {
	left: 53.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='215'] {
	min-width: 53.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='215'] {
	max-width: 53.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='216'] {
	width: 54%;
}

.grid-stack > .grid-stack-item[data-gs-x='216'] {
	left: 54%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='216'] {
	min-width: 54%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='216'] {
	max-width: 54%;
}

.grid-stack > .grid-stack-item[data-gs-width='217'] {
	width: 54.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='217'] {
	left: 54.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='217'] {
	min-width: 54.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='217'] {
	max-width: 54.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='218'] {
	width: 54.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='218'] {
	left: 54.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='218'] {
	min-width: 54.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='218'] {
	max-width: 54.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='219'] {
	width: 54.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='219'] {
	left: 54.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='219'] {
	min-width: 54.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='219'] {
	max-width: 54.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='220'] {
	width: 55%;
}

.grid-stack > .grid-stack-item[data-gs-x='220'] {
	left: 55%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='220'] {
	min-width: 55%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='220'] {
	max-width: 55%;
}

.grid-stack > .grid-stack-item[data-gs-width='221'] {
	width: 55.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='221'] {
	left: 55.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='221'] {
	min-width: 55.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='221'] {
	max-width: 55.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='222'] {
	width: 55.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='222'] {
	left: 55.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='222'] {
	min-width: 55.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='222'] {
	max-width: 55.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='223'] {
	width: 55.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='223'] {
	left: 55.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='223'] {
	min-width: 55.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='223'] {
	max-width: 55.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='224'] {
	width: 56%;
}

.grid-stack > .grid-stack-item[data-gs-x='224'] {
	left: 56%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='224'] {
	min-width: 56%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='224'] {
	max-width: 56%;
}

.grid-stack > .grid-stack-item[data-gs-width='225'] {
	width: 56.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='225'] {
	left: 56.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='225'] {
	min-width: 56.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='225'] {
	max-width: 56.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='226'] {
	width: 56.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='226'] {
	left: 56.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='226'] {
	min-width: 56.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='226'] {
	max-width: 56.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='227'] {
	width: 56.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='227'] {
	left: 56.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='227'] {
	min-width: 56.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='227'] {
	max-width: 56.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='228'] {
	width: 57%;
}

.grid-stack > .grid-stack-item[data-gs-x='228'] {
	left: 57%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='228'] {
	min-width: 57%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='228'] {
	max-width: 57%;
}

.grid-stack > .grid-stack-item[data-gs-width='229'] {
	width: 57.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='229'] {
	left: 57.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='229'] {
	min-width: 57.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='229'] {
	max-width: 57.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='230'] {
	width: 57.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='230'] {
	left: 57.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='230'] {
	min-width: 57.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='230'] {
	max-width: 57.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='231'] {
	width: 57.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='231'] {
	left: 57.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='231'] {
	min-width: 57.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='231'] {
	max-width: 57.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='232'] {
	width: 58%;
}

.grid-stack > .grid-stack-item[data-gs-x='232'] {
	left: 58%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='232'] {
	min-width: 58%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='232'] {
	max-width: 58%;
}

.grid-stack > .grid-stack-item[data-gs-width='233'] {
	width: 58.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='233'] {
	left: 58.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='233'] {
	min-width: 58.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='233'] {
	max-width: 58.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='234'] {
	width: 58.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='234'] {
	left: 58.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='234'] {
	min-width: 58.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='234'] {
	max-width: 58.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='235'] {
	width: 58.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='235'] {
	left: 58.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='235'] {
	min-width: 58.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='235'] {
	max-width: 58.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='236'] {
	width: 59%;
}

.grid-stack > .grid-stack-item[data-gs-x='236'] {
	left: 59%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='236'] {
	min-width: 59%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='236'] {
	max-width: 59%;
}

.grid-stack > .grid-stack-item[data-gs-width='237'] {
	width: 59.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='237'] {
	left: 59.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='237'] {
	min-width: 59.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='237'] {
	max-width: 59.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='238'] {
	width: 59.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='238'] {
	left: 59.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='238'] {
	min-width: 59.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='238'] {
	max-width: 59.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='239'] {
	width: 59.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='239'] {
	left: 59.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='239'] {
	min-width: 59.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='239'] {
	max-width: 59.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='240'] {
	width: 60%;
}

.grid-stack > .grid-stack-item[data-gs-x='240'] {
	left: 60%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='240'] {
	min-width: 60%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='240'] {
	max-width: 60%;
}

.grid-stack > .grid-stack-item[data-gs-width='241'] {
	width: 60.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='241'] {
	left: 60.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='241'] {
	min-width: 60.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='241'] {
	max-width: 60.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='242'] {
	width: 60.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='242'] {
	left: 60.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='242'] {
	min-width: 60.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='242'] {
	max-width: 60.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='243'] {
	width: 60.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='243'] {
	left: 60.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='243'] {
	min-width: 60.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='243'] {
	max-width: 60.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='244'] {
	width: 61%;
}

.grid-stack > .grid-stack-item[data-gs-x='244'] {
	left: 61%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='244'] {
	min-width: 61%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='244'] {
	max-width: 61%;
}

.grid-stack > .grid-stack-item[data-gs-width='245'] {
	width: 61.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='245'] {
	left: 61.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='245'] {
	min-width: 61.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='245'] {
	max-width: 61.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='246'] {
	width: 61.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='246'] {
	left: 61.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='246'] {
	min-width: 61.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='246'] {
	max-width: 61.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='247'] {
	width: 61.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='247'] {
	left: 61.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='247'] {
	min-width: 61.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='247'] {
	max-width: 61.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='248'] {
	width: 62%;
}

.grid-stack > .grid-stack-item[data-gs-x='248'] {
	left: 62%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='248'] {
	min-width: 62%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='248'] {
	max-width: 62%;
}

.grid-stack > .grid-stack-item[data-gs-width='249'] {
	width: 62.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='249'] {
	left: 62.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='249'] {
	min-width: 62.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='249'] {
	max-width: 62.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='250'] {
	width: 62.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='250'] {
	left: 62.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='250'] {
	min-width: 62.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='250'] {
	max-width: 62.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='251'] {
	width: 62.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='251'] {
	left: 62.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='251'] {
	min-width: 62.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='251'] {
	max-width: 62.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='252'] {
	width: 63%;
}

.grid-stack > .grid-stack-item[data-gs-x='252'] {
	left: 63%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='252'] {
	min-width: 63%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='252'] {
	max-width: 63%;
}

.grid-stack > .grid-stack-item[data-gs-width='253'] {
	width: 63.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='253'] {
	left: 63.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='253'] {
	min-width: 63.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='253'] {
	max-width: 63.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='254'] {
	width: 63.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='254'] {
	left: 63.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='254'] {
	min-width: 63.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='254'] {
	max-width: 63.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='255'] {
	width: 63.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='255'] {
	left: 63.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='255'] {
	min-width: 63.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='255'] {
	max-width: 63.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='256'] {
	width: 64%;
}

.grid-stack > .grid-stack-item[data-gs-x='256'] {
	left: 64%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='256'] {
	min-width: 64%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='256'] {
	max-width: 64%;
}

.grid-stack > .grid-stack-item[data-gs-width='257'] {
	width: 64.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='257'] {
	left: 64.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='257'] {
	min-width: 64.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='257'] {
	max-width: 64.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='258'] {
	width: 64.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='258'] {
	left: 64.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='258'] {
	min-width: 64.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='258'] {
	max-width: 64.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='259'] {
	width: 64.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='259'] {
	left: 64.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='259'] {
	min-width: 64.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='259'] {
	max-width: 64.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='260'] {
	width: 65%;
}

.grid-stack > .grid-stack-item[data-gs-x='260'] {
	left: 65%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='260'] {
	min-width: 65%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='260'] {
	max-width: 65%;
}

.grid-stack > .grid-stack-item[data-gs-width='261'] {
	width: 65.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='261'] {
	left: 65.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='261'] {
	min-width: 65.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='261'] {
	max-width: 65.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='262'] {
	width: 65.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='262'] {
	left: 65.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='262'] {
	min-width: 65.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='262'] {
	max-width: 65.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='263'] {
	width: 65.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='263'] {
	left: 65.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='263'] {
	min-width: 65.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='263'] {
	max-width: 65.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='264'] {
	width: 66%;
}

.grid-stack > .grid-stack-item[data-gs-x='264'] {
	left: 66%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='264'] {
	min-width: 66%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='264'] {
	max-width: 66%;
}

.grid-stack > .grid-stack-item[data-gs-width='265'] {
	width: 66.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='265'] {
	left: 66.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='265'] {
	min-width: 66.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='265'] {
	max-width: 66.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='266'] {
	width: 66.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='266'] {
	left: 66.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='266'] {
	min-width: 66.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='266'] {
	max-width: 66.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='267'] {
	width: 66.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='267'] {
	left: 66.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='267'] {
	min-width: 66.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='267'] {
	max-width: 66.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='268'] {
	width: 67%;
}

.grid-stack > .grid-stack-item[data-gs-x='268'] {
	left: 67%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='268'] {
	min-width: 67%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='268'] {
	max-width: 67%;
}

.grid-stack > .grid-stack-item[data-gs-width='269'] {
	width: 67.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='269'] {
	left: 67.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='269'] {
	min-width: 67.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='269'] {
	max-width: 67.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='270'] {
	width: 67.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='270'] {
	left: 67.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='270'] {
	min-width: 67.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='270'] {
	max-width: 67.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='271'] {
	width: 67.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='271'] {
	left: 67.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='271'] {
	min-width: 67.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='271'] {
	max-width: 67.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='272'] {
	width: 68%;
}

.grid-stack > .grid-stack-item[data-gs-x='272'] {
	left: 68%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='272'] {
	min-width: 68%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='272'] {
	max-width: 68%;
}

.grid-stack > .grid-stack-item[data-gs-width='273'] {
	width: 68.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='273'] {
	left: 68.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='273'] {
	min-width: 68.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='273'] {
	max-width: 68.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='274'] {
	width: 68.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='274'] {
	left: 68.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='274'] {
	min-width: 68.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='274'] {
	max-width: 68.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='275'] {
	width: 68.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='275'] {
	left: 68.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='275'] {
	min-width: 68.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='275'] {
	max-width: 68.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='276'] {
	width: 69%;
}

.grid-stack > .grid-stack-item[data-gs-x='276'] {
	left: 69%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='276'] {
	min-width: 69%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='276'] {
	max-width: 69%;
}

.grid-stack > .grid-stack-item[data-gs-width='277'] {
	width: 69.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='277'] {
	left: 69.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='277'] {
	min-width: 69.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='277'] {
	max-width: 69.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='278'] {
	width: 69.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='278'] {
	left: 69.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='278'] {
	min-width: 69.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='278'] {
	max-width: 69.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='279'] {
	width: 69.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='279'] {
	left: 69.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='279'] {
	min-width: 69.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='279'] {
	max-width: 69.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='280'] {
	width: 70%;
}

.grid-stack > .grid-stack-item[data-gs-x='280'] {
	left: 70%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='280'] {
	min-width: 70%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='280'] {
	max-width: 70%;
}

.grid-stack > .grid-stack-item[data-gs-width='281'] {
	width: 70.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='281'] {
	left: 70.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='281'] {
	min-width: 70.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='281'] {
	max-width: 70.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='282'] {
	width: 70.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='282'] {
	left: 70.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='282'] {
	min-width: 70.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='282'] {
	max-width: 70.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='283'] {
	width: 70.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='283'] {
	left: 70.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='283'] {
	min-width: 70.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='283'] {
	max-width: 70.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='284'] {
	width: 71%;
}

.grid-stack > .grid-stack-item[data-gs-x='284'] {
	left: 71%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='284'] {
	min-width: 71%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='284'] {
	max-width: 71%;
}

.grid-stack > .grid-stack-item[data-gs-width='285'] {
	width: 71.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='285'] {
	left: 71.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='285'] {
	min-width: 71.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='285'] {
	max-width: 71.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='286'] {
	width: 71.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='286'] {
	left: 71.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='286'] {
	min-width: 71.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='286'] {
	max-width: 71.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='287'] {
	width: 71.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='287'] {
	left: 71.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='287'] {
	min-width: 71.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='287'] {
	max-width: 71.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='288'] {
	width: 72%;
}

.grid-stack > .grid-stack-item[data-gs-x='288'] {
	left: 72%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='288'] {
	min-width: 72%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='288'] {
	max-width: 72%;
}

.grid-stack > .grid-stack-item[data-gs-width='289'] {
	width: 72.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='289'] {
	left: 72.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='289'] {
	min-width: 72.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='289'] {
	max-width: 72.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='290'] {
	width: 72.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='290'] {
	left: 72.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='290'] {
	min-width: 72.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='290'] {
	max-width: 72.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='291'] {
	width: 72.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='291'] {
	left: 72.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='291'] {
	min-width: 72.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='291'] {
	max-width: 72.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='292'] {
	width: 73%;
}

.grid-stack > .grid-stack-item[data-gs-x='292'] {
	left: 73%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='292'] {
	min-width: 73%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='292'] {
	max-width: 73%;
}

.grid-stack > .grid-stack-item[data-gs-width='293'] {
	width: 73.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='293'] {
	left: 73.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='293'] {
	min-width: 73.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='293'] {
	max-width: 73.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='294'] {
	width: 73.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='294'] {
	left: 73.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='294'] {
	min-width: 73.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='294'] {
	max-width: 73.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='295'] {
	width: 73.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='295'] {
	left: 73.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='295'] {
	min-width: 73.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='295'] {
	max-width: 73.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='296'] {
	width: 74%;
}

.grid-stack > .grid-stack-item[data-gs-x='296'] {
	left: 74%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='296'] {
	min-width: 74%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='296'] {
	max-width: 74%;
}

.grid-stack > .grid-stack-item[data-gs-width='297'] {
	width: 74.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='297'] {
	left: 74.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='297'] {
	min-width: 74.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='297'] {
	max-width: 74.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='298'] {
	width: 74.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='298'] {
	left: 74.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='298'] {
	min-width: 74.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='298'] {
	max-width: 74.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='299'] {
	width: 74.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='299'] {
	left: 74.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='299'] {
	min-width: 74.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='299'] {
	max-width: 74.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='300'] {
	width: 75%;
}

.grid-stack > .grid-stack-item[data-gs-x='300'] {
	left: 75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='300'] {
	min-width: 75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='300'] {
	max-width: 75%;
}

.grid-stack > .grid-stack-item[data-gs-width='301'] {
	width: 75.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='301'] {
	left: 75.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='301'] {
	min-width: 75.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='301'] {
	max-width: 75.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='302'] {
	width: 75.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='302'] {
	left: 75.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='302'] {
	min-width: 75.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='302'] {
	max-width: 75.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='303'] {
	width: 75.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='303'] {
	left: 75.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='303'] {
	min-width: 75.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='303'] {
	max-width: 75.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='304'] {
	width: 76%;
}

.grid-stack > .grid-stack-item[data-gs-x='304'] {
	left: 76%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='304'] {
	min-width: 76%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='304'] {
	max-width: 76%;
}

.grid-stack > .grid-stack-item[data-gs-width='305'] {
	width: 76.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='305'] {
	left: 76.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='305'] {
	min-width: 76.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='305'] {
	max-width: 76.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='306'] {
	width: 76.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='306'] {
	left: 76.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='306'] {
	min-width: 76.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='306'] {
	max-width: 76.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='307'] {
	width: 76.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='307'] {
	left: 76.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='307'] {
	min-width: 76.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='307'] {
	max-width: 76.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='308'] {
	width: 77%;
}

.grid-stack > .grid-stack-item[data-gs-x='308'] {
	left: 77%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='308'] {
	min-width: 77%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='308'] {
	max-width: 77%;
}

.grid-stack > .grid-stack-item[data-gs-width='309'] {
	width: 77.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='309'] {
	left: 77.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='309'] {
	min-width: 77.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='309'] {
	max-width: 77.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='310'] {
	width: 77.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='310'] {
	left: 77.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='310'] {
	min-width: 77.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='310'] {
	max-width: 77.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='311'] {
	width: 77.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='311'] {
	left: 77.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='311'] {
	min-width: 77.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='311'] {
	max-width: 77.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='312'] {
	width: 78%;
}

.grid-stack > .grid-stack-item[data-gs-x='312'] {
	left: 78%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='312'] {
	min-width: 78%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='312'] {
	max-width: 78%;
}

.grid-stack > .grid-stack-item[data-gs-width='313'] {
	width: 78.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='313'] {
	left: 78.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='313'] {
	min-width: 78.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='313'] {
	max-width: 78.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='314'] {
	width: 78.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='314'] {
	left: 78.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='314'] {
	min-width: 78.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='314'] {
	max-width: 78.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='315'] {
	width: 78.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='315'] {
	left: 78.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='315'] {
	min-width: 78.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='315'] {
	max-width: 78.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='316'] {
	width: 79%;
}

.grid-stack > .grid-stack-item[data-gs-x='316'] {
	left: 79%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='316'] {
	min-width: 79%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='316'] {
	max-width: 79%;
}

.grid-stack > .grid-stack-item[data-gs-width='317'] {
	width: 79.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='317'] {
	left: 79.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='317'] {
	min-width: 79.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='317'] {
	max-width: 79.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='318'] {
	width: 79.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='318'] {
	left: 79.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='318'] {
	min-width: 79.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='318'] {
	max-width: 79.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='319'] {
	width: 79.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='319'] {
	left: 79.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='319'] {
	min-width: 79.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='319'] {
	max-width: 79.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='320'] {
	width: 80%;
}

.grid-stack > .grid-stack-item[data-gs-x='320'] {
	left: 80%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='320'] {
	min-width: 80%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='320'] {
	max-width: 80%;
}

.grid-stack > .grid-stack-item[data-gs-width='321'] {
	width: 80.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='321'] {
	left: 80.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='321'] {
	min-width: 80.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='321'] {
	max-width: 80.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='322'] {
	width: 80.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='322'] {
	left: 80.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='322'] {
	min-width: 80.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='322'] {
	max-width: 80.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='323'] {
	width: 80.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='323'] {
	left: 80.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='323'] {
	min-width: 80.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='323'] {
	max-width: 80.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='324'] {
	width: 81%;
}

.grid-stack > .grid-stack-item[data-gs-x='324'] {
	left: 81%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='324'] {
	min-width: 81%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='324'] {
	max-width: 81%;
}

.grid-stack > .grid-stack-item[data-gs-width='325'] {
	width: 81.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='325'] {
	left: 81.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='325'] {
	min-width: 81.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='325'] {
	max-width: 81.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='326'] {
	width: 81.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='326'] {
	left: 81.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='326'] {
	min-width: 81.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='326'] {
	max-width: 81.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='327'] {
	width: 81.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='327'] {
	left: 81.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='327'] {
	min-width: 81.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='327'] {
	max-width: 81.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='328'] {
	width: 82%;
}

.grid-stack > .grid-stack-item[data-gs-x='328'] {
	left: 82%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='328'] {
	min-width: 82%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='328'] {
	max-width: 82%;
}

.grid-stack > .grid-stack-item[data-gs-width='329'] {
	width: 82.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='329'] {
	left: 82.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='329'] {
	min-width: 82.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='329'] {
	max-width: 82.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='330'] {
	width: 82.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='330'] {
	left: 82.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='330'] {
	min-width: 82.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='330'] {
	max-width: 82.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='331'] {
	width: 82.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='331'] {
	left: 82.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='331'] {
	min-width: 82.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='331'] {
	max-width: 82.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='332'] {
	width: 83%;
}

.grid-stack > .grid-stack-item[data-gs-x='332'] {
	left: 83%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='332'] {
	min-width: 83%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='332'] {
	max-width: 83%;
}

.grid-stack > .grid-stack-item[data-gs-width='333'] {
	width: 83.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='333'] {
	left: 83.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='333'] {
	min-width: 83.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='333'] {
	max-width: 83.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='334'] {
	width: 83.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='334'] {
	left: 83.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='334'] {
	min-width: 83.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='334'] {
	max-width: 83.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='335'] {
	width: 83.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='335'] {
	left: 83.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='335'] {
	min-width: 83.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='335'] {
	max-width: 83.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='336'] {
	width: 84%;
}

.grid-stack > .grid-stack-item[data-gs-x='336'] {
	left: 84%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='336'] {
	min-width: 84%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='336'] {
	max-width: 84%;
}

.grid-stack > .grid-stack-item[data-gs-width='337'] {
	width: 84.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='337'] {
	left: 84.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='337'] {
	min-width: 84.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='337'] {
	max-width: 84.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='338'] {
	width: 84.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='338'] {
	left: 84.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='338'] {
	min-width: 84.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='338'] {
	max-width: 84.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='339'] {
	width: 84.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='339'] {
	left: 84.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='339'] {
	min-width: 84.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='339'] {
	max-width: 84.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='340'] {
	width: 85%;
}

.grid-stack > .grid-stack-item[data-gs-x='340'] {
	left: 85%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='340'] {
	min-width: 85%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='340'] {
	max-width: 85%;
}

.grid-stack > .grid-stack-item[data-gs-width='341'] {
	width: 85.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='341'] {
	left: 85.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='341'] {
	min-width: 85.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='341'] {
	max-width: 85.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='342'] {
	width: 85.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='342'] {
	left: 85.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='342'] {
	min-width: 85.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='342'] {
	max-width: 85.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='343'] {
	width: 85.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='343'] {
	left: 85.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='343'] {
	min-width: 85.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='343'] {
	max-width: 85.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='344'] {
	width: 86%;
}

.grid-stack > .grid-stack-item[data-gs-x='344'] {
	left: 86%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='344'] {
	min-width: 86%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='344'] {
	max-width: 86%;
}

.grid-stack > .grid-stack-item[data-gs-width='345'] {
	width: 86.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='345'] {
	left: 86.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='345'] {
	min-width: 86.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='345'] {
	max-width: 86.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='346'] {
	width: 86.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='346'] {
	left: 86.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='346'] {
	min-width: 86.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='346'] {
	max-width: 86.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='347'] {
	width: 86.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='347'] {
	left: 86.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='347'] {
	min-width: 86.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='347'] {
	max-width: 86.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='348'] {
	width: 87%;
}

.grid-stack > .grid-stack-item[data-gs-x='348'] {
	left: 87%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='348'] {
	min-width: 87%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='348'] {
	max-width: 87%;
}

.grid-stack > .grid-stack-item[data-gs-width='349'] {
	width: 87.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='349'] {
	left: 87.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='349'] {
	min-width: 87.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='349'] {
	max-width: 87.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='350'] {
	width: 87.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='350'] {
	left: 87.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='350'] {
	min-width: 87.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='350'] {
	max-width: 87.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='351'] {
	width: 87.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='351'] {
	left: 87.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='351'] {
	min-width: 87.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='351'] {
	max-width: 87.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='352'] {
	width: 88%;
}

.grid-stack > .grid-stack-item[data-gs-x='352'] {
	left: 88%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='352'] {
	min-width: 88%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='352'] {
	max-width: 88%;
}

.grid-stack > .grid-stack-item[data-gs-width='353'] {
	width: 88.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='353'] {
	left: 88.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='353'] {
	min-width: 88.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='353'] {
	max-width: 88.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='354'] {
	width: 88.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='354'] {
	left: 88.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='354'] {
	min-width: 88.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='354'] {
	max-width: 88.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='355'] {
	width: 88.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='355'] {
	left: 88.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='355'] {
	min-width: 88.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='355'] {
	max-width: 88.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='356'] {
	width: 89%;
}

.grid-stack > .grid-stack-item[data-gs-x='356'] {
	left: 89%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='356'] {
	min-width: 89%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='356'] {
	max-width: 89%;
}

.grid-stack > .grid-stack-item[data-gs-width='357'] {
	width: 89.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='357'] {
	left: 89.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='357'] {
	min-width: 89.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='357'] {
	max-width: 89.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='358'] {
	width: 89.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='358'] {
	left: 89.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='358'] {
	min-width: 89.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='358'] {
	max-width: 89.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='359'] {
	width: 89.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='359'] {
	left: 89.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='359'] {
	min-width: 89.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='359'] {
	max-width: 89.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='360'] {
	width: 90%;
}

.grid-stack > .grid-stack-item[data-gs-x='360'] {
	left: 90%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='360'] {
	min-width: 90%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='360'] {
	max-width: 90%;
}

.grid-stack > .grid-stack-item[data-gs-width='361'] {
	width: 90.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='361'] {
	left: 90.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='361'] {
	min-width: 90.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='361'] {
	max-width: 90.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='362'] {
	width: 90.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='362'] {
	left: 90.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='362'] {
	min-width: 90.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='362'] {
	max-width: 90.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='363'] {
	width: 90.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='363'] {
	left: 90.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='363'] {
	min-width: 90.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='363'] {
	max-width: 90.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='364'] {
	width: 91%;
}

.grid-stack > .grid-stack-item[data-gs-x='364'] {
	left: 91%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='364'] {
	min-width: 91%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='364'] {
	max-width: 91%;
}

.grid-stack > .grid-stack-item[data-gs-width='365'] {
	width: 91.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='365'] {
	left: 91.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='365'] {
	min-width: 91.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='365'] {
	max-width: 91.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='366'] {
	width: 91.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='366'] {
	left: 91.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='366'] {
	min-width: 91.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='366'] {
	max-width: 91.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='367'] {
	width: 91.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='367'] {
	left: 91.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='367'] {
	min-width: 91.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='367'] {
	max-width: 91.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='368'] {
	width: 92%;
}

.grid-stack > .grid-stack-item[data-gs-x='368'] {
	left: 92%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='368'] {
	min-width: 92%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='368'] {
	max-width: 92%;
}

.grid-stack > .grid-stack-item[data-gs-width='369'] {
	width: 92.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='369'] {
	left: 92.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='369'] {
	min-width: 92.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='369'] {
	max-width: 92.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='370'] {
	width: 92.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='370'] {
	left: 92.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='370'] {
	min-width: 92.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='370'] {
	max-width: 92.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='371'] {
	width: 92.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='371'] {
	left: 92.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='371'] {
	min-width: 92.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='371'] {
	max-width: 92.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='372'] {
	width: 93%;
}

.grid-stack > .grid-stack-item[data-gs-x='372'] {
	left: 93%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='372'] {
	min-width: 93%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='372'] {
	max-width: 93%;
}

.grid-stack > .grid-stack-item[data-gs-width='373'] {
	width: 93.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='373'] {
	left: 93.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='373'] {
	min-width: 93.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='373'] {
	max-width: 93.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='374'] {
	width: 93.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='374'] {
	left: 93.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='374'] {
	min-width: 93.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='374'] {
	max-width: 93.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='375'] {
	width: 93.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='375'] {
	left: 93.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='375'] {
	min-width: 93.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='375'] {
	max-width: 93.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='376'] {
	width: 94%;
}

.grid-stack > .grid-stack-item[data-gs-x='376'] {
	left: 94%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='376'] {
	min-width: 94%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='376'] {
	max-width: 94%;
}

.grid-stack > .grid-stack-item[data-gs-width='377'] {
	width: 94.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='377'] {
	left: 94.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='377'] {
	min-width: 94.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='377'] {
	max-width: 94.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='378'] {
	width: 94.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='378'] {
	left: 94.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='378'] {
	min-width: 94.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='378'] {
	max-width: 94.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='379'] {
	width: 94.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='379'] {
	left: 94.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='379'] {
	min-width: 94.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='379'] {
	max-width: 94.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='380'] {
	width: 95%;
}

.grid-stack > .grid-stack-item[data-gs-x='380'] {
	left: 95%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='380'] {
	min-width: 95%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='380'] {
	max-width: 95%;
}

.grid-stack > .grid-stack-item[data-gs-width='381'] {
	width: 95.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='381'] {
	left: 95.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='381'] {
	min-width: 95.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='381'] {
	max-width: 95.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='382'] {
	width: 95.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='382'] {
	left: 95.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='382'] {
	min-width: 95.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='382'] {
	max-width: 95.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='383'] {
	width: 95.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='383'] {
	left: 95.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='383'] {
	min-width: 95.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='383'] {
	max-width: 95.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='384'] {
	width: 96%;
}

.grid-stack > .grid-stack-item[data-gs-x='384'] {
	left: 96%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='384'] {
	min-width: 96%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='384'] {
	max-width: 96%;
}

.grid-stack > .grid-stack-item[data-gs-width='385'] {
	width: 96.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='385'] {
	left: 96.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='385'] {
	min-width: 96.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='385'] {
	max-width: 96.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='386'] {
	width: 96.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='386'] {
	left: 96.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='386'] {
	min-width: 96.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='386'] {
	max-width: 96.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='387'] {
	width: 96.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='387'] {
	left: 96.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='387'] {
	min-width: 96.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='387'] {
	max-width: 96.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='388'] {
	width: 97%;
}

.grid-stack > .grid-stack-item[data-gs-x='388'] {
	left: 97%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='388'] {
	min-width: 97%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='388'] {
	max-width: 97%;
}

.grid-stack > .grid-stack-item[data-gs-width='389'] {
	width: 97.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='389'] {
	left: 97.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='389'] {
	min-width: 97.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='389'] {
	max-width: 97.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='390'] {
	width: 97.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='390'] {
	left: 97.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='390'] {
	min-width: 97.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='390'] {
	max-width: 97.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='391'] {
	width: 97.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='391'] {
	left: 97.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='391'] {
	min-width: 97.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='391'] {
	max-width: 97.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='392'] {
	width: 98%;
}

.grid-stack > .grid-stack-item[data-gs-x='392'] {
	left: 98%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='392'] {
	min-width: 98%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='392'] {
	max-width: 98%;
}

.grid-stack > .grid-stack-item[data-gs-width='393'] {
	width: 98.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='393'] {
	left: 98.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='393'] {
	min-width: 98.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='393'] {
	max-width: 98.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='394'] {
	width: 98.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='394'] {
	left: 98.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='394'] {
	min-width: 98.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='394'] {
	max-width: 98.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='395'] {
	width: 98.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='395'] {
	left: 98.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='395'] {
	min-width: 98.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='395'] {
	max-width: 98.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='396'] {
	width: 99%;
}

.grid-stack > .grid-stack-item[data-gs-x='396'] {
	left: 99%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='396'] {
	min-width: 99%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='396'] {
	max-width: 99%;
}

.grid-stack > .grid-stack-item[data-gs-width='397'] {
	width: 99.25%;
}

.grid-stack > .grid-stack-item[data-gs-x='397'] {
	left: 99.25%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='397'] {
	min-width: 99.25%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='397'] {
	max-width: 99.25%;
}

.grid-stack > .grid-stack-item[data-gs-width='398'] {
	width: 99.5%;
}

.grid-stack > .grid-stack-item[data-gs-x='398'] {
	left: 99.5%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='398'] {
	min-width: 99.5%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='398'] {
	max-width: 99.5%;
}

.grid-stack > .grid-stack-item[data-gs-width='399'] {
	width: 99.75%;
}

.grid-stack > .grid-stack-item[data-gs-x='399'] {
	left: 99.75%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='399'] {
	min-width: 99.75%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='399'] {
	max-width: 99.75%;
}

.grid-stack > .grid-stack-item[data-gs-width='400'] {
	width: 100%;
}

.grid-stack > .grid-stack-item[data-gs-x='400'] {
	left: 100%;
}

.grid-stack > .grid-stack-item[data-gs-min-width='400'] {
	min-width: 100%;
}

.grid-stack > .grid-stack-item[data-gs-max-width='400'] {
	max-width: 100%;
}






.grid-stack.grid-stack-animate, .grid-stack.grid-stack-animate .grid-stack-item {
  -webkit-transition: left 0.3s, top 0.3s, height 0.3s, width 0.3s;
  -moz-transition: left 0.3s, top 0.3s, height 0.3s, width 0.3s;
  -ms-transition: left 0.3s, top 0.3s, height 0.3s, width 0.3s;
  -o-transition: left 0.3s, top 0.3s, height 0.3s, width 0.3s;
  transition: left 0.3s, top 0.3s, height 0.3s, width 0.3s;
}
.grid-stack.grid-stack-animate .grid-stack-item.ui-draggable-dragging, .grid-stack.grid-stack-animate .grid-stack-item.ui-resizable-resizing, .grid-stack.grid-stack-animate .grid-stack-item.grid-stack-placeholder {
  -webkit-transition: left 0s, top 0s, height 0s, width 0s;
  -moz-transition: left 0s, top 0s, height 0s, width 0s;
  -ms-transition: left 0s, top 0s, height 0s, width 0s;
  -o-transition: left 0s, top 0s, height 0s, width 0s;
  transition: left 0s, top 0s, height 0s, width 0s;
}

/** Uncomment this to show bottom-left resize handle **/
/*
.grid-stack > .grid-stack-item > .ui-resizable-sw {
    display: inline-block;
    filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
    @include vendor(transform, rotate(180deg));
}
*/
@media (max-width: 768px) {
  .grid-stack-item {
    position: relative !important;
    width: auto !important;
    left: 0 !important;
    top: auto !important;
    margin-bottom: 20px;
  }
  .grid-stack-item .ui-resizable-handle {
    display: none;
  }

  .grid-stack {
    height: auto !important;
  }
}
