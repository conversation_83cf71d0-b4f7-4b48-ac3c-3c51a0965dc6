﻿/**  写入js */
document.write('<script language=javascript src="' + BASE_PATH + 'js/jquery.form.js"></script>');
/**
 * 判断数组中元素是否存在
 */
Array.prototype.indexOf = Array.prototype.indexOf || function(item) {
	for(var i = 0, j = this.length; i < j; i++) {
		if(this[i] === item) {
			return i;
		}
	}
	return -1;
}

/***
 * 从数组中删除指定的数据
 */
Array.prototype.remove = function(val) {
	var index = this.indexOf(val);
	if(index > -1) {
		this.splice(index, 1);
	}
}

Array.prototype.contains = function(item) {
	for(var i = 0; i < this.length; i++) {
		if(this[i] === item) {
			return true;
		}
	}
	return false;
};

/**
 * 数组去重
 * @returns {Array}
 */
Array.prototype.delRepeat = function() {
	var temp = {}, len = this.length;
	for(var i = 0; i < len; i++) {
		var tmp = this[i];
		if(!temp.hasOwnProperty(tmp)) {   //hasOwnProperty 用来判断一个对象是否有你给出名称的属性或对象
			temp[this[i]] = "yes";
		}
	}
	len = 0;
	var tempArr = [];
	for(var i in temp) {
		tempArr[len++] = i;
	}
	return tempArr;
}

//去空格
function trimRL(val) {
	return val.replace(/(^\s*)|(\s*$)/g, "");
}

//是否为空对像
function isObjEmpty(obj) {
	return obj === undefined || obj === null || obj === '' || obj.length === 0;
}

//是否为空
function isEmpty(val) {
	if(val == null || val == '' || trimRL(val) == '') {
		return true;
	}
	return false;
}

/***
 * 获取html中的body中内容
 * @param content
 * @returns {*}
 */
function getBodyContentByHTML(content) {
	var REG_BODY = /<body[^>]*>([\s\S]*)<\/body>/,
		result = REG_BODY.exec(content);
	if(result && result.length === 2)
		return result[1];
	return content;
}

/**
 * 一个字符串是否包含另一个子字符串
 * @param char
 * @returns {boolean}
 */
String.prototype.contains = function(char) {
	return this.indexOf(char) >= 0;
}

/***
 * @param url 链接
 * @param params &a=1&b=2b格式
 */
function windowLocaltionHref(url, params) {
	var realUrl = BASE_PATH + url;
	if(url.contains("?")) {
		realUrl += "&v=" + new Date().getTime();
	} else {
		realUrl += "?v=" + new Date().getTime();
	}
	if(!isEmpty(params.trim())) {
		realUrl = realUrl + params;
	}
	window.location.href = realUrl;
}

/**
 * doPost 请求
 * @param options.url     请求url
 * @param options.errFunc 失败函数
 * @param options.sucFunc 成功函数
 */
function doRequest(options){
    if(options.url === undefined || isEmpty(options.url)) {
        layer.msg("伝えてください options.url パラメータ値");
        return;
    }
    if(options.errFunc === undefined) {
        layer.msg("伝えてください options.errFunc 関数");
        return;
    }
    if(options.sucFunc === undefined) {
        layer.msg("伝えてください options.sucFunc 関数");
        return;
    }
    var absoluteUrl = BASE_PATH + options.url + "?d=" + new Date().getTime();
    var setting = {async : true , type : 'json' , queData : {} };
        setting = $.extend( true , setting , options);
    //console.info( "doRequest 的参数值" + "\t" +  JSON.stringify(setting) +  " >>>> " + setting.async );
    $.ajax({
        type: 'POST',
        url: absoluteUrl,
        data: setting.queData ,
        dataType: setting.type,
        async : setting.async ,
        success: function(data) {
            if(data.reModel == 'F2002') {
	            layer.msg("ページが失効しました。再登録してください");
                window.top.location.href = BASE_PATH;
            } else {
                setting.sucFunc(data);
            }
        },
        error: function(data) {
            console.log("戻る情報が間違いましだ：" + data + "\t" + JSON.stringify(data));
            if(setting.errFunc) {
                setting.errFunc(data);
            }
        }
    });
}

/**
 * 处理AJAX
 * @param url     发送请求url
 * @param queData 传递参数值
 * @param sucFunc 成功函数
 * @param errFunc 错误函数
 */
function doPost(url, type, queData, sucFunc, errFunc) {
	var absoluteUrl = BASE_PATH + url + "?d=" + new Date().getTime();
	//处理默认自带的token值

	$.ajax({
		type: 'POST',
		url: absoluteUrl,
		data: queData,
		dataType: (type == null || type == '' || type == 'undefined' ? 'json' : type),
		success: function(data) {
			if(data.reModel == 'F2002') {
				layer.msg("ページが失効しました。再登録してください");
				window.top.location.href = BASE_PATH;
			} else {
				sucFunc(data);
			}
		},
		error: function(data) {
			console.log("戻る情報が間違いましだ：" + data + "\t," + JSON.stringify(data));
			if(errFunc) {
				errFunc(data);
			}
		}
	});
}

/**
 * form表单 ajax submit 提交
 * @param url       发送请求url
 * @param formName  form表单id
 * @param queData   附加参数
 * @param sucFunc   成功函数
 * @param errFunc   错误函数
 */
function ajaxSubmit(url, formName, queData, sucFunc, errFunc) {
	var absoluteUrl = BASE_PATH + url + "?d=" + new Date().getTime();
	$("#" + formName).ajaxSubmit({
		type: "POST",
		url: absoluteUrl,
		dataType: "json",
		data: queData,
		async: false,
		success: function(data) {
			if(data.reModel == 'F2002') {
				layer.msg("ページが失効しました。再登録してください");
				window.top.location.href = BASE_PATH;
			} else {
				sucFunc(data);
			}
		},
		error: function(data) {
			console.log("戻る情報が間違いましだ：" + data + "\t," + JSON.stringify(data));
			errFunc(data);
		}
	});
}

/**
 * 新增 , 修改 , 删除
 * @param eleId     元素点击按钮id
 * @param dataType  数据返回类型
 * @param module    所属模块
 * @param dataParam 数据信息
 */
function operationCRUD(eleId, dataType, module, dataParam ) {
	var url = $('#' + eleId).attr("url");
	//获取左侧菜单id
	var leftMenuId = $("#" + module + "_menuId").val();
	//左侧菜单id
	var data = {"leftMenuId": leftMenuId};
	$.extend(true , data , dataParam );

	console.log("operationCRUD 参数值 \n " + JSON.stringify(data));

	doPost(url, dataType, data, function(data) {
		//填充内容
		var bodyContent = getBodyContentByHTML(data);
		//填充区 (模块加名称  module + "_main_content") 固定写法
		$("#" + module + "_main_content").hide();

		$("#" + module + "_dynamic_content").html(bodyContent);

		$("#" + module + "_dynamic_content").show();
		//回调
		if (data.callBack){
            data.callBack(eleId, bodyContent , data);
		}
	}, function(data) {
	});
}

/***
 * 基于layui iframe 表单提交简单封装
 * @param options
 *        说明: formId      此值为form 的id
 *             url         此值表单提交url
 *             backListUrl 返回列表的url
 *             leftMenuId  左侧菜单id
 *             backType    默认跳转(undefined 或  1 ） 其他值不跳转
 */
function formSubmit( options) {
    if(options.formId === undefined || isEmpty(options.formId)) {
        layer.msg("伝えてください options.formId パラメータ値");
        return;
    }
    if(options.url === undefined || isEmpty(options.url)) {
		layer.msg("伝えてください options.url パラメータ値");
		return;
	}
    if (options.backType === undefined || options.backType == 1) {
        if (options.backListUrl === undefined || isEmpty(options.backListUrl)) {
            layer.msg("伝えてください options.backListUrl パラメータ値");
            return;
        }
        if (options.leftMenuId === undefined || isEmpty(options.leftMenuId)) {
            layer.msg("伝えてください options.leftMenuId パラメータ値");
            return;
        }
    }
    com.ymx.layui.public.core.form.on('submit(' + options.formId + ')', function(data) {
    	// 获取表单参数值
        var obj = formSerialize( options.formId );
        doPost( options.url , null, obj, function(data) {
            if(data.rec === 'SUC') {
            	if (options.backType === undefined || options.backType == 1){
                    windowLocaltionHref( options.backListUrl , "&leftMenuId=" + options.leftMenuId );
				}
                if (options.callBack &&  options.backType !== undefined && options.backType != 1){
                    options.callBack(data);
                }
            } else {
                layer.msg(data.errMsg);
            }
        }, function(data) { });
        //防止跳转
        return false;
    });
}

/***
 * 跳转页面
 * @param dataList 列表数据勾选项
 * @param options  附加值
 */
function goToUrl(dataList , options) {
    if(options.url === undefined || isEmpty(options.url)) {
        layer.msg("伝えてください options.url パラメータ値");
        return;
    }
    if(options.leftMenuId === undefined || isEmpty(options.leftMenuId)) {
        layer.msg("伝えてください options.leftMenuId パラメータ値");
        return;
    }
    var length = dataList.checkboxData.length;
    if(length != 1){
        com.ymx.layui.public.core.layer.msg("操作項目を正しく選択してください");
        return;
    }
	windowLocaltionHref(options.url , "&leftMenuId=" + options.leftMenuId + "&id=" + dataList.checkboxData[0].id );

}

/***
 * 跳转页面
 * @param dataList 列表数据勾选项
 * @param options  附加值
 */
function updateToUrl(url,dataList , options) {
	if(url === undefined || isEmpty(url)) {
		layer.msg("伝えてください options.url パラメータ値");
		return;
	}
	if(options.leftMenuId === undefined || isEmpty(options.leftMenuId)) {
		layer.msg("伝えてください options.leftMenuId パラメータ値");
		return;
	}
	var length = dataList.checkboxData.length;
	if(length != 1){
		com.ymx.layui.public.core.layer.msg("操作項目を正しく選択してください!");
		return;
	}
	if(dataList.checkboxData[0].publish!=1){
		var realData = { "id" : dataList.checkboxData[0].id };
		$.extend(true , realData , options);
		layer.confirm('選択したデータを操作しますか?', {icon: 3 , title:'ヒント'}, function( index ){
			doPost( url ,  null , realData , function(data){
				if(data.rec == 'SUC')
				{
					com.ymx.layui.public.core.layer.msg('操作が成功しました');
					dataList.checkboxData = [];
					dataList.laytplShow();
				}
				if(data.rec == 'FAL')
				{
					com.ymx.layui.public.core.layer.msg(data.errMsg);
					dataList.checkboxData = [];
					dataList.laytplShow();
				}
			},function(data){ });
		});
	}else{
		com.ymx.layui.public.core.layer.msg("繰り返し投稿することができる!");
		return;
	}

}



/***
 * 公共列表操作功能
 * @param url       请求url地址
 * @param dataList  列表数据
 * @param obj      {必须包括leftMenuId 参数 值(左侧菜单id)} json对象
 */
function operationDb( url , dataList ,  obj ) {
    var ids = getStrAttrVal({data :dataList.checkboxData , "attrName":"id"})  ;
    var length = dataList.checkboxData.length;
    //id 是多个以逗分隔值
    var realData = { "id" : ids };
    $.extend(true , realData , obj);
    if (length > 0 ){
        layer.confirm('選択したデータを操作しますか?', {icon: 3 , title:'ヒント'}, function( index ){
            doPost( url ,  null , realData , function(data){
                if(data.rec == 'SUC') 
                {
                    com.ymx.layui.public.core.layer.msg('操作が成功しました');
                    dataList.checkboxData = [];
                    dataList.laytplShow();
                }
                if(data.rec == 'FAL')
            	{
                	com.ymx.layui.public.core.layer.msg(data.errMsg);
                    dataList.checkboxData = [];
                    dataList.laytplShow();
            	}
            },function(data){ });
        });
    } else {
        com.ymx.layui.public.core.layer.msg('操作するデータを選択してください');
    }
}
function operationDbs( url , dataList ,  obj ) {
	var ids = getStrAttrVal({data :dataList.checkboxData , "attrName":"id"})  ;
	var length = dataList.checkboxData.length;
	//id 是多个以逗分隔值
	var realData = { "ids" : ids };
	$.extend(true , realData , obj);
	if (length > 0 ){
		layer.confirm('選択したデータを操作しますか?', {icon: 3 , title:'ヒント'}, function( index ){
			doPost( url ,  null , realData , function(data){
				if(data.rec == 'SUC')
				{
					com.ymx.layui.public.core.layer.msg('操作が成功しました');
					dataList.checkboxData = [];
					dataList.laytplShow();
				}
				if(data.rec == 'FAL')
				{
					com.ymx.layui.public.core.layer.msg(data.errMsg);
					dataList.checkboxData = [];
					dataList.laytplShow();
				}
			},function(data){ });
		});
	} else {
		com.ymx.layui.public.core.layer.msg('操作するデータを選択してください');
	}
}

/**
 * 右左去空格
 */
String.prototype.trim = function() {
	return this.replace(/(^\s*)|(\s*$)/g, "");
}

/**
 * 左去空格
 */
String.prototype.ltrim = function() {
	return this.replace(/(^\s*)/g, "");
}

/**
 * 右去空格
 */
String.prototype.rtrim = function() {
	return this.replace(/(\s*$)/g, "");
}

/**
 * JS 加载 获取数组对象中某元素值集合【返回一个属性值元素数组】
 * @param options
 * @returns {Array}
 */
function getArrayAttrVal(options) {
	var setting = $.extend({
		data: (options.data || []),
		attrName: (options.attrName || 'id'),
	}, options || {});
	var temp = new Array();
	$.each(setting.data, function(key, obj) {
		var val = obj[setting.attrName];
		temp.push(val);
	});
	return temp;
}

/**
 * JS 加载 获取数组对象中某元素值集合【返回一个属性值元素逗号分隔字符串】
 * @param options
 * @returns {str}
 */
function getStrAttrVal(options) {
	var setting = $.extend({
		data: (options.data || []),
		attrName: (options.attrName || 'id'),
	}, options || {});
	var temp = "";
	$.each(setting.data, function(key, obj) {
		var val = obj[setting.attrName];
		temp += val + ","
	});
	if(null != temp && temp.trim() != '' && temp.length > 0) {
		temp = temp.substring(0, temp.length - 1);
	}
	return temp;
}

/***
 * 获取某table 中 tbody tr 元素值
 * @param tableId
 * @returns
 */
function getTableValueBackJsonArray(tableId) {
	var _tableData = [];
	$("tr[id^='" + tableId + "_tr']").each(function(obj) {
		var objec = {};
		$(this).children().each(function(i, o) {
			var fieldValue = $(this).attr("data-field");
			if(!isNull(fieldValue)) {
				var field = fieldValue.split("_")[0];
				var value = fieldValue.split("_")[1];
				if(value == "null" || value == null || value.trim() == '') {
					objec[field] = "";
				} else {
					objec[field] = value;
				}
			}
		});
		_tableData.push(objec);
	});
	return JSON.stringify(_tableData);
}

/**
 * 请求接口不加密 url：地址 queDate：参数 sucFunc：回调函数 专用
 function doPostJSON(url, queData, sucFunc,errFunc) {
    var absoluteUrl = BASE_PATH + url;
     console.log("请求参数：" + JSON.stringify(queData));
    $.ajax({
        type: "POST",
        url: absoluteUrl ,
        timeout: new Date().getTime(),
        data: queData,
        dataType: "json",
        success: function(data) {
			if(!isNull(data)){
				if (data.rec == 'SUC') {
                	//console.log("response success:" + JSON.stringify(data));
                	sucFunc(data);
	            } else {
	            	errFunc(data);
	            }
			}else{
			   console.log("数据解析异常");
			}
        },
        error: function(data) {
            console.log("戻る情報が間違いましだ：" + data + "\t," + JSON.stringify(data))
        }
    });
}*/

//表单JSON数据
function formSerialize(form) {
	var jform = $("#" + form);						   //form表单id属性值
	var obj = {};
	$.each(jform.serializeArray(), function() {
		if(obj[this['name']]) {
			obj[this['name']] = obj[this['name']] + "," + this['value'];
		} else {
			obj[this['name']] = this['value'];
		}
	})
	return obj;
}

/**
 * 获取id数组
 * @param data
 * @param propertyName
 * @returns {String}
 */
function backStringData(data) {
	var pro = new Array();
	$.each(data, function(i, n) {
		pro.push(n.id);
	});
	return pro;
}

/**
 * 填补请求参数
 * @param {[key]}
 * @param {value}
 */
function toJson(key, value) {
	var str = "";
	str += key + ":" + value;
	return str;
}

/* 补位 */
function PrefixInteger(num, n) {
	if((num + "").length >= n) return num;
	return PrefixInteger("0" + num, n);
}

/**
 * 判断非空
 *
 * @param {[type]}
 *            content [内容]
 * @return {Boolean} [false 为空true非空]
 */
function isNull(content) {
	if("" == content || typeof(content) == content || null == content || "null" == content || "," == content || "undefined" == content) {
		return true;
	} else {
		return false;
	}
}

/**
 * 显示字符串，null显示空
 */
function showString(content) {
	if(null == content) {
		return "";
	} else {
		return content;
	}
}

/**
 *
 * @param time
 * @returns yyyy-mm
 */
function formatDate(time) {
	if(time == null || time == "") {
		return "";
	}
	var now = new Date(time);
	var year = now.getFullYear();
	var month = now.getMonth() + 1 + "";
	if(month.length == 1) {
		month = "0" + month;
	}
	var date = now.getDate() + "";
	if(date.length == 1) {
		date = "0" + date;
	}
	return year + month;
}

/**
 *
 * @param time
 * @param date,datetime
 * @returns yyyy-mm-dd, yyyy-mm-dd hh:mm;ss
 */
function formatDateTime(time, type) {
	if(time == null || time == "") {
		return "";
	}
	var now = new Date(time);
	var year = now.getFullYear();
	var month = now.getMonth() + 1 + "";
	var date = now.getDate() + "";
	var hh = now.getHours() + "";
	var min = now.getMinutes() + "";
	var sed = now.getSeconds() + "";
	if(month.length == 1) {
		month = "0" + month;
	}
	if(date.length == 1) {
		date = "0" + date;
	}
	if(hh.length == 1) {
		hh = "0" + hh;
	}
	if(min.length == 1) {
		min = "0" + min;
	}
	if(sed.length == 1) {
		sed = "0" + sed;
	}
	if(type == 'date') {
		return year + "-" + month + "-" + date;
	} else if(type == 'datetime') {
		return year + "-" + month + "-" + date + " " + hh + ":" + min + ":" + sed;
	} else if(type == 'month') {
		return year + "-" + month;
	} else if(type == 'minute') {
		return year + "-" + month + "-" + date + " " + hh + ":" + min;
	}

}

/**
 * 获取url里面的传值
 *
 * @param name
 * @returns {null}
 */
function getUrlParam(name) {
	// 构造一个含有目标参数的正则表达式对象
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
	// 匹配目标参数
	var r = window.location.search.substr(1).match(reg);
	// 返回参数值
	if(r != null) return unescape(r[2]);
	return null;
}

function getParentUrlParam(name) {
	// 构造一个含有目标参数的正则表达式对象
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
	// 匹配目标参数
	var r = window.parent.document.URL.search.substr(1).match(reg);
	// 返回参数值
	if(r != null) return unescape(r[2]);
	return null;
}

// 计算月份差格式 200303 - 200305
function getMonthNumber(date1, date2) {
	// 默认格式为"20030303",根据自己需要改格式和方法
	var year1 = date1.substr(0, 4);
	var year2 = date2.substr(0, 4);
	var month1 = date1.substr(4, 2);
	var month2 = date2.substr(4, 2);
	var len = (year2 - year1) * 12 + (month2 - month1) + 1;
	return len;
}

/**
 * @param id
 *            弹框的Id唯一 不能重复
 * @param title
 *            弹框的提示title
 * @param content
 *            内容（可以是一段文字/也可以是一个对象） document.getElementById("choseKuang")
 * @param lock
 *            true/false 是否页面背景锁住
 * @param height
 *            窗口的高度 （10,20,30）默认是自适应'auto'
 * @param width
 *            窗口的宽度 （10,20,30）默认是自适应'auto'
 */

/**
 * 判断是否正确手机号
 *
 * @param phone
 * @returns {boolean}
 */
function vailPhone(phone) {
	var flag = false;
	var message = "";
	var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(17[0-9]{1})|(15[0-9]{1})|(15[0-9]{1})|(18[0-9]{1}))+\d{8})$/;
	if(phone == '') {
		message = "携帯の番号は空いてはいけません！";
	} else if(phone.length != 11) {
		message = "有効な携帯電話番号を入力してください！";
	} else if(!myreg.test(phone)) {
		message = "有効な携帯電話番号を入力してください！";
	} else {
		flag = true;
	}
	if(!flag) {
		// 提示错误效果
	} else {
		// 提示正确效果

	}
	return flag;
}

/**
 * 初始化表单数据
 */
function initFormData(jqSelector, data) {
	var form = $(jqSelector);
	var nameTimes = {};
	form.find(":input").each(function(index, input) {
		var _this = $(this);
		var _this_name = _this.attr("name");

		nameTimes[_this_name] = [_this_name] == 0 ? nameTimes[_this_name] + 1 : 0;

		var _this_value = null;
		if(Object.prototype.toString.call(data[_this_name]) === '[object Array]') {
			_this_value = data[_this_name][nameTimes[_this_name]];
		} else {
			_this_value = data[_this_name];
		}
		if(_this.is("input")) {
			var _this_type = _this.attr("type")
			_this.val(_this_value);

		} else if(_this.is("select")) {
			_this.find("option[value=\"" + _this_value + "\"]").attr("selected", true);
		}
	});
}

/**
 * js获取项目根路径，如： http://localhost:8080/SerlfService
 * @returns
 */
function getRootPath() {
	var curWwwPath = window.document.location.href;
	var pathName = window.document.location.pathname;
	var pos = curWwwPath.indexOf(pathName);
	// 获取主机地址
	var localhostPaht = curWwwPath.substring(0, pos);
	// 获取带"/"的项目名，
	var projectName = pathName.substring(0, pathName.substr(1).indexOf('/') + 1);
	return (localhostPaht + projectName);
}

// 社会统一信用代码验证
// 判断是否为统一社会信用代码格式
function isEnterpriseSCCode(obj) {
	// 代码字符集-代码字符
	var charCode = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "J", "K", "L", "M", "N", "P", "Q", "R", "T", "U", "W", "X", "Y"];
	// 代码字符集-代码字符数值
	var charVal = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30];
	// 各位置序号上的加权因子
	var posWi = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28];
	// 统一代码由十八位的数字或大写英文字母（不适用I、O、Z、S、V）组成，第18位为校验位。
	// 第1位为数字或大写英文字母，登记管理部门代码
	// 第2位为数字或大写英文字母，机构类别代码
	// 第3到8位共6位全为数字登记管理机关行政区划码
	// 第9-17位共9位为数字或大写英文字母组织机构代码
	// 第18为为数字或者大写的Y
	var city = {
		11: "北京",
		12: "天津",
		13: "河北",
		14: "山西",
		15: "内蒙古",
		21: "辽宁",
		22: "吉林",
		23: "黑龙江 ",
		31: "上海",
		32: "江苏",
		33: "浙江",
		34: "安徽",
		35: "福建",
		36: "江西",
		37: "山东",
		41: "河南",
		42: "湖北 ",
		43: "湖南",
		44: "广东",
		45: "广西",
		46: "海南",
		50: "重庆",
		51: "四川",
		52: "贵州",
		53: "云南",
		54: "西藏 ",
		61: "陕西",
		62: "甘肃",
		63: "青海",
		64: "宁夏",
		65: "新疆",
		71: "台湾",
		81: "香港",
		82: "澳门",
		91: "国外"
	};
	var reg = /^([0-9ABCDEFGY][1239]\d{6})([0-9ABCDEFGHJKLMNPQRTUWXY]{9})([0-9A-Z])$/;
	if(obj.length != 0) {
		if(!reg.test(obj)) {
			return false;
		}
		else if(!city[obj.substr(2, 2)]) {
			return false;
		}
		else {
			// //校验位校验
			// obj = obj.split('');
			// //∑(ci×Wi)(mod 31)
			// var sum = 0;
			// var ci = 0;
			// var Wi = 0;
			// for (var i = 0; i < 17; i++) {
			// ci = charVal[charCode.indexOf(obj[i])];
			// Wi = posWi[i];
			// sum += ci * Wi;
			// }
			// var c10 = 31 - (sum % 31);
			// if (c10 != obj[17]) {
			// return false;
			// }
			return true;
		}
		return true;
	}
}

/**
 * 将数组转换成Spring可以识别的key[n].key1形式
 * @param arr 待转换数组
 */
function arrayToListObj(arr, keyWord) {
	if(arr === null || arr === undefined || !$.isArray(arr)) {
		return null;
	}
	var result = {};
	var len = arr.length;
	result.length = len;
	for(var i = 0; i < len; i++) {
		var objTemp = arr[i];
		for(var key in objTemp) {
			result[keyWord + "[" + i + "]." + key] = objTemp[key];
		}
	}
	return result;
}