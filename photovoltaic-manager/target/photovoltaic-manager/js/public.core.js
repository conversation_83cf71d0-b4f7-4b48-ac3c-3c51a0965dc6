/**
 *  处理
 * 
 */
//注册多级命名空间的机制：
//1、命名空间注册工具类     
var Namespace = new Object();               
Namespace.register = function(path){     
    var arr = path.split(".");     
    var ns = "";     
	for ( var i = 0; i < arr.length; i++) {     
        if(i>0) ns += ".";     
        ns += arr[i];     
        eval("if(typeof(" + ns + ") == 'undefined') " + ns + " = new Object();");     
    }     
}     














