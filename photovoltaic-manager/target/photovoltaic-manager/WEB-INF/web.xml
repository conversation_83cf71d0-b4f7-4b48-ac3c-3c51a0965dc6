<?xml version="1.0" encoding="UTF-8"?>
<web-app version="3.0" xmlns="http://java.sun.com/xml/ns/javaee"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee 
	http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd">

	<display-name>tinghucourt-manager</display-name>

	<filter>
		<filter-name>SetCharacterEncoding</filter-name>
		<filter-class>com.ymx.common.common.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>SetCharacterEncoding</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<!-- 配置监听 -->
	<listener>  
        <listener-class>org.springframework.web.context.request.RequestContextListener</listener-class>  
	</listener>
	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>classpath*:spring/applicationContext.xml;classpath*:spring/applicationContext-*.xml</param-value>
	</context-param>
	
	<!-- dispatcherServlet 注入  -->
	<servlet>
		<servlet-name>dispatcherServlet</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>classpath*:spring/spring-mvc.xml</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>dispatcherServlet</servlet-name>
		<url-pattern>/</url-pattern>
	</servlet-mapping>
	
	<!-- session 30分钟  -->
	<session-config>
		<session-timeout>30</session-timeout>
	</session-config>
	
	<error-page>
		<error-code>404</error-code>
		<location>/error/404.jsp</location>
	</error-page>
	<error-page>
		<error-code>500</error-code>
		<location>/error/500.jsp</location>
	</error-page>
	
	<welcome-file-list>
		<welcome-file>index.jsp</welcome-file>
	</welcome-file-list>
	
</web-app>
