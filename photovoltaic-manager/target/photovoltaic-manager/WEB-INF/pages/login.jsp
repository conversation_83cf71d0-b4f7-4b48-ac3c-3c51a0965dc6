<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %> 
<!DOCTYPE html>
<html>
 <head>
     <meta charset="utf-8">
    <%--  <title><%=CacheFactory.getInstance( ).getSetter( "system.name" ) %></title> --%>
     <link rel="icon" type="image/x-icon" href="<%=PageUtil.getBasePath(request) %>images/log_icon_title.png"/>
     <meta name="renderer" content="webkit">
	 <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	 <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	 <meta name="apple-mobile-web-app-status-bar-style" content="black">
	 <meta name="apple-mobile-web-app-capable" content="yes">
	 <meta name="format-detection" content="telephone=no">
     <meta charset="utf-8">
     <!-- CSS -->
     <link rel="stylesheet" type="text/css" href="<%=PageUtil.getBasePath(request) %>assets/css/reset.css">
     <link rel="stylesheet" type="text/css" href="<%=PageUtil.getBasePath(request) %>assets/css/supersized.css">
     <link rel="stylesheet" type="text/css" href="<%=PageUtil.getBasePath(request) %>assets/css/style.css">
     <!-- Javascript -->
     <script type="text/javascript" src="<%=PageUtil.getBasePath(request) %>js/jquery-1.9.1.min.js"></script>
     <script type="text/javascript" src="<%=PageUtil.getBasePath(request) %>assets/js/supersized.3.2.7.min.js"></script>
     <script type="text/javascript" src="<%=PageUtil.getBasePath(request) %>assets/js/supersized-init.js"></script>
     <script type="text/javascript" src="<%=PageUtil.getBasePath(request) %>assets/js/scripts.js"></script>
     <script type="text/javascript">
     	var basePath = '<%=PageUtil.getBasePath(request) %>';
     </script>
          <style type="text/css">
            /*css*/
            /*通用样式*/
            ul,li{
                list-style: none;
                padding: 0;
                margin: 0;
            }
            /*下拉框样式*/
            #select{
                margin-top: 20px;
                background: rgba(0,0,0,0);
                width: 300px;
                height: 42px;
                font-family: "微软雅黑";
                font-size: 18px;
                color: white;
                border: 1px white solid;
                border-radius: 5px;
            }
            .select-head{
                overflow: hidden;
                width: 300px;
                height: 42px;
                box-sizing: border-box;
                padding: 0 10px;
                line-height: 42px;
            }
            .select-head .select-head-cont{
                float: left;
            }
            .select-head .select-icon{
                float: right;
            }
            .option{
                text-indent: 10px;
                margin-top: 1px;
                width: 300px;
                color: black;
                background: rgba(45,45,45,0.15);
                line-height: 25px;
                border: 1px #cfcfcf solid;
                display: none;
            }
            .option-item:hover{
                background: rgba(45,45,45,0.15);
            }
        </style>
 </head>
 <body>
     <div class="page-container">
         <div >
         <h1 id="dl">登录</h1>
         </div>
	     <%--
		<form id="loginForm" method="post" action="<%=PageUtil.getBasePath(request) %>free/login.htm">
		--%>
             <input type="text" name="userId" class="userId" id="userId" placeholder="请输入用户名" /> </br>
             <input type="password" name="password" id="password" class="password" placeholder="请输入密码"/> </br>
             <input type="hidden" name="isbn" id="isbn"  />
	         <ul id="select" style="text-align: center;display:inline-block">
	            <li>
	                <div class="select-head" >
	                    <span id="i18n" name="i18n" class="select-head-cont"></span>
	                    <span class="select-icon">▼</span>
	                </div>
	                <ul class="option" >
	                    <li class="option-item" value="1">中文</li>
	                    <li class="option-item" value="2">English</li>
		                <li class="option-item" value="3">عربي ،</li>
		                <li class="option-item" value="4">日本語</li>
		                <li class="option-item" value="5">Français</li>
		                <li class="option-item" value="6">Español</li>
	                </ul>
	            </li>
	        </ul>
	        </br>
	         <button onclick="login()" style="margin-top:40px;" id="btndl" >登录</button>
             <%--<button onclick="login()" id="btndl" style="margin-top:40px;">登录</button>--%>
             <div class="error"><span></span></div>
	     <%--
         </form>
	     --%>
         <div style="padding:5px 0;text-align: center;color: red;" id="showMsg">${error}</div>
     </div>
 </body>
 <script type="text/javascript">
	document.onkeydown = function(e){
	    var event = e || window.event;
	    var code = event.keyCode || event.which || event.charCode;
	    if (code == 13) {
	        login();
	    }
	}
	$(function(){
	    $("input[name='userId']").focus();
	});

	function cleardata(){
	    $('#loginForm').form('clear');
	}
	function login()
	{
	     if($("input[name='userId']").val()=="" || $("input[name='password']").val()=="")
	     {
	     	if($("#isbn").val()=="中文"){
		        $("#showMsg").html('帐号或密码为空，请重新输入!');
	        }else if($("#isbn").val()=="عربي ،"){
		        $("#showMsg").html('الحساب أو كلمة السر فارغة ، الرجاء إدخال');
	        }else if($("#isbn").val()=="日本語"){
		        $("#showMsg").html('アカウントまたはパスワードが空です。もう一度入力してください。');
	        }else if($("#isbn").val()=="Français"){
		        $("#showMsg").html('Le numéro de compte ou le mot de passe est vide.');
	        }else if($("#isbn").val()=="Español"){
		        $("#showMsg").html('La cuenta o la contraseña están vacías');
	        }else if($("#isbn").val()=="English"){
		        $("#showMsg").html('The account number or password is empty. Please re-enter it.');
	        }
	         $("input[name='userId']").focus();
	    }
	    else
	    {
		    $.post("free/check.web",{userId:$("input[name='userId']").val(),password:$("input[name='password']").val(),isbn:$("#isbn").val()},function(result){
			     if(result.code==0){
//				    $('#loginForm').submit();
				     window.location.href='main.htm';
			    }else{
				    if($("#isbn").val()=="中文"){
					    $("#showMsg").html('用户名或者密码错误!');
				    }else if($("#isbn").val()=="عربي ،"){
					    $("#showMsg").html('اسم المستخدم أو كلمة السر خاطئة');
				    }else if($("#isbn").val()=="日本語"){
					    $("#showMsg").html('ユーザ名またはパスワードが間違っています');
				    }else if($("#isbn").val()=="Français"){
					    $("#showMsg").html('Nom d \'utilisateur ou mot de passe');
				    }else if($("#isbn").val()=="Español"){
					    $("#showMsg").html('Nombre de usuario o contraseña incorrecta');
				    }else if($("#isbn").val()=="English"){
					    $("#showMsg").html('Error in username or password!');
				    }
			    }
		    });
	    }
	}
    var selectHead = document.getElementsByClassName('select-head')[0];
    var selectHeadCont = document.getElementsByClassName('select-head-cont');
    var Option = document.getElementsByClassName('option')[0];
    var optionItem = document.getElementsByClassName('option-item');

    /*默认是第一个选项*/
    selectHeadCont[0].innerHTML = optionItem[0].innerHTML;
    /*点击后出现下拉框*/
    selectHead.addEventListener('click',function(){
    	$("#btndl").attr("style","margin-top:40px;");
        Option.style.display = 'block';
    },false);
    /*点击选项后出现在下拉框*/
    var len = optionItem.length;
    for(var i=0;i<len;i++)
    {
        optionItem[i].index = i;
        optionItem[i].addEventListener('click',function(){
            selectHeadCont[0].innerHTML = optionItem[this.index].innerHTML;
            document.getElementById('isbn').value=optionItem[this.index].innerHTML;
            //alert(optionItem[this.index].innerHTML+"----"+optionItem[this.index].value);
	        $("#btndl").attr("style","margin-top:40px;");
           //  alert(optionItem[this.index].innerHTML);
             if(optionItem[this.index].innerHTML=='English')
            {
                document.getElementById('dl').innerHTML="Login";
                document.getElementById('btndl').innerHTML="Login";
                document.getElementById('userId').setAttribute('placeholder','please enter user name');
                document.getElementById('password').setAttribute('placeholder','Please enter your password');
            }else if(optionItem[this.index].innerHTML=='عربي ،'){//Arabic 阿拉伯语
                 document.getElementById('dl').innerHTML="تسجيل الدخول";
                 document.getElementById('btndl').innerHTML="تسجيل الدخول";
                 document.getElementById('userId').setAttribute('placeholder','الرجاء إدخال اسم المستخدم');
                 document.getElementById('password').setAttribute('placeholder',' الرجاء إدخال كلمة السر');
            }else if(optionItem[this.index].innerHTML=='日本語'){//Japanese 日文
                 document.getElementById('dl').innerHTML="ログイン";
                 document.getElementById('btndl').innerHTML="ログイン";
                 document.getElementById('userId').setAttribute('placeholder','ユーザ名を入力してください。');
                 document.getElementById('password').setAttribute('placeholder','パスワードを入力してください');
             }else if(optionItem[this.index].innerHTML=='Français'){//French 法语
                 document.getElementById('dl').innerHTML="Connecter";
                 document.getElementById('btndl').innerHTML="Connecter";
                 document.getElementById('userId').setAttribute('placeholder','Veuillez saisir un nom d \'utilisateur');
                 document.getElementById('password').setAttribute('placeholder','Entrez le mot de passe');
             }else if(optionItem[this.index].innerHTML=='Español'){//Spanish 西班牙语
                 document.getElementById('dl').innerHTML="Entrada";
                 document.getElementById('btndl').innerHTML="Entrada";
                 document.getElementById('userId').setAttribute('placeholder','Introduzca el nombre de usuario');
                 document.getElementById('password').setAttribute('placeholder','Introduzca la contraseña.');
             }else{
                document.getElementById('dl').innerHTML="登录";
                document.getElementById('btndl').innerHTML="登录";
                 document.getElementById('userId').setAttribute('placeholder','请输入用户名');
                document.getElementById('password').setAttribute('placeholder','请输入密码');
            }
            $("#showMsg").html('');
            Option.style.display = 'none';
        },false);
    }
    /*点击其他地方时，select会收起来*/
    document.body.addEventListener('click',function(){
	    $("#btndl").attr("style","margin-top:40px;");
        Option.style.display = 'none';
    }.false);
 </script>
</html>