<%@ page language="java" contentType="text/html; charset=UTF-8"   pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
<title><s:message code="地图面板"/></title>
<link rel="stylesheet" href="http://libs.baidu.com/bootstrap/3.2.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://www.jq22.com/jquery/font-awesome.4.6.0.css"/>
    <script src="http://www.jq22.com/jquery/1.11.1/jquery.min.js"></script>
    <script src="http://libs.baidu.com/jqueryui/1.10.4/jquery-ui.min.js"></script>
    <script src="http://libs.baidu.com/bootstrap/3.2.0/js/bootstrap.min.js"></script>
    
	<script src="<%=PageUtil.getBasePath(request) %>js/lodash.min.js"></script>
    <script src="<%=PageUtil.getBasePath(request) %>js/dist/gridstack.js"></script>
    <link rel="stylesheet" href="<%=PageUtil.getBasePath(request) %>js/dist/gridstack.css"/>
	<link rel="stylesheet" type="text/css" href="<%=PageUtil.getBasePath(request) %>css/default.css">
  <link href="<%=PageUtil.getBasePath(request) %>css/map.css" rel="stylesheet">
   <link href="<%=PageUtil.getBasePath(request) %>css/bootstrap.min.css" rel="stylesheet">
	<link rel="stylesheet" href="<%=PageUtil.getBasePath(request) %>css/jquery.webui-popover.min.css">
	<script src="<%=PageUtil.getBasePath(request) %>js/jquery.webui-popover.min.js"></script>	
	<style>
			
			header{				
				color:#fff;		
				background-color: #3366aa;	
				padding-top:30px; 	
				padding-bottom:30px;
				font-size: 24px;
				margin-bottom:40px;
			}
			header h1{
				margin-right: 380px;
				font-size:60px;
				line-height: 1;
			}
			h1 i{
				font-size: 120px;
			}
			header p{
				color:#aaccff;
			}
			.demo-container{
				background: #fff;
				padding-top: 20px;
				padding-bottom: 20px;
				margin-bottom: 40px;
			}
			.row-middle{
				margin-top:200px;
				margin-bottom: 200px;
			}
			.webui-popover-demo.top{
				width:350px;
				height:150px;
				margin-top:-60px;
				margin-left: 25px;
			}
			.webui-popover-demo h3{
				font-weight:bold;
				color:#09e;
			}
		</style>
<script type='text/javascript'>
    function sbover()
	{
	    /// alert("fewe");
	         initPopover();
	}
    function loadMapScenario() 
    {
    
           var map = new Microsoft.Maps.Map(document.getElementById('myMap'), {}); 
            map.setOptions({
                    maxZoom: 6,
                    minZoom: 5
                });
        
     }
     function initPopover(){	
       
               var settings = {
						trigger:'hover',
						title:'<s:message code="电站数据"/>',
						content:'<p>数据展示</p>',
						width:300,						
						multi:true,						
						closeable:true,
						style:'',
						padding:true
				};				
		          $('div.show-pop').webuiPopover('destroy').webuiPopover(settings);	
		          			
					var tableContent = $('#166102A019506CDD66686BCCDB1D0153').html(),
						tableSettings = {content:tableContent,
											width:500
										};
					 $('div.show-pop-table').webuiPopover('destroy').webuiPopover($.extend({},settings,tableSettings));
					var listContent = $('#'+id).html(),
						listSettings = {content:listContent,
											title:'',
											padding:false
						};  					
				 
		
	}
     function initdata()
     {   
         
				
							
				
         var map = new Microsoft.Maps.Map(document.getElementById('myMap'), {}); 
         map.setOptions({
                    maxZoom: 6,
                    minZoom: 5
                });
			      var username =  $("#username").val();
			        $.ajax({
						url: "powerMapView/findlist.htm?name="+username,
						type: "post",
						dataType: "json",
						async : false,   
						success: function(data)
						{
                          $.each(data,function(i,item)
                          {
                              $.each(item,function(j,val)
                              {     
                                   // alert(val.latitude);
                                    var dzid=val.id;
                                     var infoboxTemplate ='<div class=" region-list active postition-7 online-node show-pop show-pop-table" ><div class="area-box "><span class="dot "></span><span class="pulse delay-11 "></span><span class="pulse delay-10 show-pop"></span><span class="pulse delay-09 show-pop"></span></div></div></div>';	
                                    
                                    var p = new Microsoft.Maps.Location(val.longitude,val.latitude); 
				                    var pushpinid="pushpin_"+val.id;
									var infoboxid="infobox_"+val.id;
					                window['pushpinid'] = new Microsoft.Maps.Pushpin(p, null);
					                window['infoboxid'] = new Microsoft.Maps.Infobox(p, { htmlContent: infoboxTemplate });
					                window['infoboxid'].setMap(map);
					                Microsoft.Maps.Events.addHandler(window['pushpinid'], 'click', function () { highlight(dzid); });
									function highlight(id) {
									    //  windowLocaltionHref("systemView/findmoveView.htm?ymd="+ymd+"&powerStationId="+dzid , "&leftMenuId=${leftMenuId}");
					                }
					                map.entities.push(window['pushpinid']);
					                 window['pushpinid'].setOptions({ enableHoverStyle: true, enableClickedStyle: true });
					                 
					                 });

                                }); 
						       		      
						     }
						});
					//	initPopover();
     
     }
</script>
 <script type='text/javascript' src='https://cn.bing.com/api/maps/mapcontrol?key=AuSqp8yFdgmqAlwip_2oCe9zgBFrFIOQ3nXIibLG-U0IjjW0myJqUQ1G1Eiklmtn&callback=loadMapScenario&mkt=${mapmkt}' async defer></script>

</head>
<body onload="initdata()">
<input type="hidden" name="username" id="username" value="${mid}"/>
 <div id='myMap' style='width: 100vw; height: 100vh;'></div>
 <div id="166102A019506CDD66686BCCDB1D0153" style="display:none;">
	<ul class="list-group">
		<li class="list-group-item">芯片ID:1654069923E019710F1AFD</li>
		<li class="list-group-item">功率：3222</li>
		 <li class="list-group-item">输出电流:333</li>
		  <li class="list-group-item">输出电压:22</li> 
		  <li class="list-group-item">输入电流:11</li> 
		  <li class="list-group-item">输入电压:44</li>
  	</ul>
 </div>
</body>
</html>