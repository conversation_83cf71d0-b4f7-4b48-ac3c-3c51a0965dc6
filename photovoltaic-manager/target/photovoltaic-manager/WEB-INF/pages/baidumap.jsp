<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<%@ page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="initial-scale=1.0, user-scalable=no" />
 	<script src="http://www.jq22.com/jquery/1.11.1/jquery.min.js"></script>
    <script src="http://libs.baidu.com/jqueryui/1.10.4/jquery-ui.min.js"></script>
    <script src="http://libs.baidu.com/bootstrap/3.2.0/js/bootstrap.min.js"></script>
	<style type="text/css">
	body, html,#allmap {width: 100%;height: 100%;overflow: hidden;margin:0;font-family:"微软雅黑";}
	</style>
	<title>异步加载地图</title>
	<script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=RIQtRNBGe7WrW4NDFwhx7UzQdW1bDnGP"></script>
</head>
<body  onload="initdata()">
	<div id="allmap"></div>
</body>
</html>
<script type="text/javascript">
	function initdata()
	{
	    var map = new BMap.Map("allmap");
			        $.ajax({
						url: "powerMapView/findlist.htm?name=${mid}",
						type: "post",
						dataType: "json",
						async : false,   
						success: function(data)
						{
                          $.each(data,function(i,item)
                          {
                              $.each(item,function(j,val)
                              {
                                if(val.longitude != '')
                                {
                                   var point = new BMap.Point(val.longitude,val.latitude);
                                  // alert(val.longitude);
                                 addMarker(point,map);
                                }
                                
                              });

                         }); 
						       		      
				 }
		});          // 创建Map实例
		//var point = new BMap.Point(116.404, 39.915); // 创建点坐标
		var geolocation = new BMap.Geolocation();
	    geolocation.getCurrentPosition(function(r){
		if(this.getStatus() == BMAP_STATUS_SUCCESS){
			var mk = new BMap.Marker(r.point);
			// map.addOverlay(mk);
			map.panTo(r.point);
			map.centerAndZoom(r.point,7);    
			// alert('您的位置：'+r.point.lng+','+r.point.lat);
		}
		else {
			alert('failed'+this.getStatus());
		}        
	},{enableHighAccuracy: true})
		             
		map.enableScrollWheelZoom();                 //启用滚轮放大缩小
	
	}
		

	function addMarker(point,map){
	
	  var marker = new BMap.Marker(point);
	  map.addOverlay(marker);
	  marker.setAnimation(BMAP_ANIMATION_BOUNCE); //跳动的动画
	}
	
</script>
