<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@page import="com.ymx.common.utils.PageUtil" %>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.upload.plugin.js"></script>
<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>

<link rel="stylesheet" href="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/themes/base/jquery.ui.all.css">
<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.core.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.widget.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.position.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.menu.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.autocomplete.js"></script>
<script type="text/javascript">
	(function($) {
		$(function() {
			// 保存
			$('#icon-save').click(function() {
				/* formSubmit({
					"formId":"componentGroupForm" ,
					"url":"componentGroup/saveOrUpdate.web" ,
					"backListUrl":"componentGroup/list.htm" ,
					"leftMenuId":'${leftMenuId}'
				}); */
//				var num = $("#groupNo").val();
//			   var url = "componentGroup/validatecomponentGroup.web";
				<%--$.ajax({--%>
	              <%--url:url+'?id=${info.id}&num='+num,--%>
	              <%--data:'',--%>
	              <%--type:'POST',--%>
	              <%--dataType:'json',--%>
	              <%--async:false,--%>
	              <%--success:function(data){--%>
	                  <%--if(data.message == 'success')--%>
	                  <%--{--%>
	                     <%--formSubmit({--%>
							<%--"formId":"componentGroupForm" ,--%>
							<%--"url":"componentGroup/saveOrUpdate.web" ,--%>
							<%--"backListUrl":"componentGroup/list.htm" ,--%>
							<%--"leftMenuId":'${leftMenuId}'--%>
						<%--});--%>
	                  <%--}--%>
	                  <%--else--%>
	                  <%--{--%>
	                      <%--alert(data.message);--%>
	                  <%--}--%>
	                 <%----%>
	              <%--}--%>
         		 <%--});--%>
				formSubmit({
					"formId":"componentGroupForm" ,
					"url":"componentGroup/saveOrUpdate.web" ,
					"backListUrl":"componentGroup/list.htm" ,
					"leftMenuId":'${leftMenuId}'
				});
			});
			// 返回
			$("#icon-back").click(function() {
				windowLocaltionHref("componentGroup/list.htm", "&leftMenuId=${leftMenuId}")
			});
            // 逆变器自动补全
            $("#inverterName").autocomplete({
                source:function(request,response){
                    var inverterName = $("#inverterName").val();
                    doPost("inverter/selectInverterData.web" , null , { "inverterName": inverterName },function(datas){
                        if(datas.rec == 'SUC'){
                            var data  = datas.reModel;
                            response($.map(data,function(item){
                                var name = item.inverterName;
                                var code = item.inverterId;
                                return {
                                    label:item.inverterName,
                                    value:item.inverterName,
                                    code:item.id
                                }
                            }));
                        }else{
                            alert(datas.msg);
                        }
                    },function(data){});
                },
                delay: 500,
                select : function(event, ui) {
                    $("#inverterId").val(ui.item.code);
                }
            });
        })
	})(jQuery)
</script>
</head>
<body>
<div class="admin-main" id="componentGroupSaveOrUpdate">
	<form action="" class="layui-form" id="componentGroupForm" name="componentGroupForm">
		<%-- 隐藏属性 --%>
		<input type="hidden" name="id" id="id" value="${info.id}"/>
		<input type="hidden" name="picture" id="picture"/>
		<%-- <div class="layui-form-item">
			<label class="layui-form-label"><s:message code="编号" /></label>
			<div class="layui-input-block">
				<input type="text" name="groupNo" id="groupNo" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					   value="${info.groupNo}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入系统编号" />" class="layui-input" />
			</div>
		</div> --%>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="组串名称" /></label>
			<div class="layui-input-block">
				<input type="text" name="groupName" id="groupName" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					   value="${info.groupName}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入组串名称" />" class="layui-input" />
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="组串类型" /></label>
			<div class="layui-input-block">
				<select id="groupType" name="groupType" lay-verify="isEmpty" value="${info.groupType}"
				        <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>>
					<option value=""><s:message code="请选择" /></option>
					<option value="1" ><s:message code="独立" /></option>
					<option value="2"><s:message code="并联" /></option>
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="额定功率" />(W)</label>
			<div class="layui-input-block">
				<input type="text" name="power" onkeyup="this.value=this.value.replace(/\D/g,'')" id="power" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
				       value="${info.power}" maxlength="11" autocomplete="off" placeholder="<s:message code="请输入额定功率" />(W)" class="layui-input" />
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="组串标识" /></label>
			<div class="layui-input-block">
				<input type="text" name="chipId" id="chipId"  <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
				       value="${info.chipId}" autocomplete="off" placeholder="<s:message code="请输入组串标识" />" class="layui-input" />
			</div>
		</div>
		<c:if test="${operator == 'detail'}">
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="逆变器" /></label>
			<div class="layui-input-block"><input type="hidden" id="inverterId" name="inverterId" />
				<input type="text" name="inverterName" id="inverterName" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					   value="${info.inverterName}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入逆变器" />" class="layui-input" />
			</div>
		</div>
		</c:if>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="创建人" /></label>
			<div class="layui-input-block">
				<input type="text" name="createUserName" id="createUserName" lay-verify="isEmpty" disabled="disabled" <c:if test="${operator == 'detail'}"></c:if>
					   value="${info.createUserName}" maxlength="30" autocomplete="off"   class="layui-input" />
			</div>
		</div>
		<c:if test="${operator == 'detail'}">
			<div class="layui-form-item">
				<label class="layui-form-label"><s:message code="创建时间" /></label>
				<div class="layui-input-block">
					<input type="text" name="createTimeCh" id="createTimeCh" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
						   value="${info.createTimeCh}" maxlength="30" autocomplete="off"   class="layui-input" />
				</div>
			</div>
		</c:if>
		<%--操作按钮 --%>
		<div class="layui-form-item">
			<div class="layui-input-block">
				<c:if test="${operator != 'detail'}"><a id="icon-save" class="layui-btn" lay-submit lay-filter="componentGroupForm"><s:message code="保存" /></a></c:if>
				<a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a>
			</div>
		</div>
	</form>
</div>
<script type="text/javascript">
	(function() {
		DataList.create({ isLaytpl: false, isTable: false, isPager: false,
			listener:function()	{
				$.selectOption({
					url : 'dic/queryDicList.web' ,
					selectElem : 'groupType' ,
					queData:{"dicId":"groupType"},
					selectId : 'dicValueId' ,
					selectName : 'dicValueLabel' ,
					selected : '${info.groupType}',
					isFormRenderSelect: true
				});
			}
		});
	}());
</script>
</body>
</html>


