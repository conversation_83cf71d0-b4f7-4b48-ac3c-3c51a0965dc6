<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@page import="com.ymx.common.utils.PageUtil"%>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
	    // 新增页面
		$("#componentGroup-add").click(function () {
            var url = $('#componentGroup-add').attr("url");
            windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
        });
		// 编辑
        $("#componentGroup-edit").click(function () {
            var url = $('#componentGroup-edit').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 查看
        $("#componentGroup-detail").click(function () {
            var url = $('#componentGroup-detail').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 请选择组件
        $("#componentGroup-select").click(function () {
            var url = $('#componentGroup-select').attr("url");
            var length = dataList.checkboxData.length;
            if(length != 1){
                com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
                return;
            }
            var id = dataList.checkboxData[0].id;
            layer.open({
                title: '<s:message code="请选择组件" />',
                type: 2,
                area: ['100%', '100%'],
                content: [url + '?id=' + id + "&belongsGroupFlag=selectComponent", 'no']
            });
        });
        // 查看组件
        $("#componentGroup-component").click(function () {
            var url = $('#componentGroup-component').attr("url");
            var length = dataList.checkboxData.length;
            if(length != 1){
                com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
                return;
            }
            var id = dataList.checkboxData[0].id;
            layer.open({
                title: '<s:message code="查看组件" />',
                type: 2,
                area: ['100%', '100%'],
                content: [url + '?id=' + id + "&belongsGroupFlag=detailComponent", 'no']
            });
        });
        // 删除
        $("#componentGroup-delete").click(function () {
            var url = $('#componentGroup-delete').attr("url");
            operationDb( url , dataList ,  { "isDelete" : 2 , "leftMenuId": '${leftMenuId}'} );
        });

                              // 导入
                             $('#componentGroup-import').click(function(){
                     			var url = $('#componentGroup-import').attr("url");
                     			doPost(url , 'text' ,  {} , function(data){
                     				 componentUpload = $.layerOpenCustom({
                     			        title: '<s:message code="导入组串" />' , //不显示标题栏
                     			        area: '600px;' ,
                     			        id: 'setterSave' ,//设定一个id，防止重复弹出
                     			        content: data
                     				});
                     			},function(data){

                     			});
                     		});

                     		 // 查询版本
                 $("#version-query").click(function () {
			     if(checkIsSingle())
			       {
                   layer.open({
                                 title: '查询版本',
                                 offset: 'auto',
                               //area : ['380px', '170px'],
                                 btn: ['查询所属组件版本','取消'],
                                 btnAlign: 'c',
                                 btn1: function (index, layero) {
                                    var queryType=2;
                                      versionQuery(queryType);
                                      layer.close(index);
                                     },
                                   btn2: function (index, layero) {
                                       layer.close(index);
                                              },
                             });		 
				 }
                 });
				 
				 
    });

     function versionQuery(queryType)
           {
            var url = $('#version-query').attr("url");
            var groupId = getStrAttrVal({data :dataList.checkboxData , "attrName":"id"}) ;
            var cloudId = getStrAttrVal({data :dataList.checkboxData , "attrName":"cloudId"}) ;
            operationDb( url , dataList ,  { "isDelete" : 2 , "leftMenuId": '${leftMenuId}',
                "queryId": groupId,"queryType":queryType,"type":4,"imei":cloudId } );
           }
		   
		     // 检查操作了几条数据
		       function checkIsSingle() {
                       var length = dataList.checkboxData.length;
                        if(length != 1){
                         com.ymx.layui.public.core.layer.msg('<s:message code="一次只能操作一条数据"/>!');
                        return false;
                        }
                        return true;
                  }
	</script>
</head>
<body>
	<div class="admin-main" id="componentGroup_list">
		<%-- <s:message code="查询条件" />区域 --%>
		<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="componentGroupListForm" id="componentGroupListForm">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="录入时间" /></label>
							<div class="layui-input-inline" >
								<input type="text" name="createTimeBegin" id="createTimeBegin" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'createTimeEnd\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" placeholder="<s:message code="录入开始时间" />" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
							<div class="layui-form-mid">-</div>
							<div class="layui-input-inline">
								<input type="text" name="createTimeEnd" id="createTimeEnd" placeholder="<s:message code="录入结束时间" />" onclick="WdatePicker({minDate:'#F{$dp.$D(\'createTimeBegin\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="组串名称" /></label>
							<div class="layui-input-inline">
								<input type="text" name="groupName" id="groupName" placeholder="<s:message code="请输入组串名称" />" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="电站名称" /></label>
							<div class="layui-input-inline">
								<input type="text" name="powerStationName" id="powerStationName" placeholder="<s:message code="电站名称" />"  autocomplete="off" class="layui-input"/>
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		</blockquote>
		<%-- 操作功能 --%>
		<div>
			<jsp:include page="/functionBuilder.htm"></jsp:include>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="componentGroupList" lay-filter="componentGroupList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="componentGroupListChooseAll" id="componentGroupListChooseAll" lay-skin="primary" /></th>
					<%-- <th><s:message code="编号" /></th> --%>
					<th><s:message code="组串名称" /></th>
					<th><s:message code="组串标识" /></th>
					<th><s:message code="电站名称" /></th>
					<th><s:message code="逆变器" /></th>
					<th><s:message code="采集器" /></th>
					<th><s:message code="额定功率" />(W)</th>
					<th><s:message code="组件数量" /></th>
					<th><s:message code="类型" /></th>
					<th><s:message code="录入人" /></th>
					<th><s:message code="录入时间" /></th>
				</tr>
			</thead>
			<tbody id="componentGroupListBody"></tbody>
		</table>
		<script type="text/html" id="componentGroupListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="componentGroupList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="componentGroupList_check_{{r.id}}" id="componentGroupList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>

				<td data-field="id_{{r.id}}" style="display:none">{{r.id}}</td>
				<td data-field="cloudId_{{r.cloudId}}" style="display:none">{{r.cloudId}}</td>
				<td data-field="groupName_{{r.groupName}}">{{r.groupName}}</td>
				<td data-field="chipId_{{r.chipId}}">
					{{# if(!isNull(r.chipId)){ }}
					{{r.chipId}}
					{{# }  }}
				</td>
				<td data-field="powerStationName_{{r.powerStationName}}">{{r.powerStationName}}</td>
				<td data-field="inverterName_{{r.inverterName}}">{{r.inverterName}}</td>
				<td data-field="cloudName_{{r.cloudName}}">{{r.cloudName}}</td>
				<td data-field="power_{{r.power}}">
					{{# if(!isNull(r.power)){ }}
					{{r.power}}
					{{# }  }}
				</td>
				<td data-field="groupNum_{{r.groupNum}}">
					{{# if(!isNull(r.groupNum)){ }}
					{{r.groupNum}}
					{{# }  }}
				</td>
				<td data-field="groupType_{{r.groupType}}">
					{{# if(!isNull(r.groupType) && 1 == r.groupType ){ }}
						<s:message code="独立"/>
					{{# }  }}
					{{# if(!isNull(r.groupType) && 2 == r.groupType ){ }}
					    <s:message code="并联"/>
					{{# }  }}
				</td>
				<td data-field="createUserName_{{r.createUserName}}">
					{{# if(!isNull(r.createUserName)){ }}
						{{r.createUserName}}
					{{# }  }}
				</td>
				<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="10"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "componentGroup/queryComponentGroupList.web",
					defaultTableId : 'componentGroupList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'componentGroupListBody',
					formName : 'componentGroupListForm',
					isTable : false,
					laytplTemplet : 'componentGroupListTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>