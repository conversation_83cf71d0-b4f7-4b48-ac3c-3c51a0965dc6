<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
        // 确认
        $("#selectComponentComfirm").click(function () {
            var url = $('#selectComponentComfirm').attr("url");
            // 列表数据id  是多个以逗分隔值
            var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
            var length = dataList.checkboxData.length;
            var realData = { "belongsGroupId":'${belongsGroupId}'  , "id":ids ,"belongsGroupFlag":'${belongsGroupFlag}'};
            if (length > 0 ){
                layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index ){
                    doPost( url ,  null , realData , function(data){
                        if(data.rec == 'SUC') {
                            com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
                            dataList.checkboxData = [];
                            dataList.laytplShow();
                            layer.closeAll();
                        }
                    },function(data){ });
                });
            } else {
                layer.msg('<s:message code="请选择需要操作的数据"/>');
            }
        });
        // 移除
        $("#moveComponentComfirm").click(function () {
            var url = $('#moveComponentComfirm').attr("url");
            // 列表数据id  是多个以逗分隔值
            var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
            var length = dataList.checkboxData.length;
            var realData = {  "id":ids , "belongsGroupFlag":'${belongsGroupFlag}'};
            if (length > 0 ){
                layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index ){
                    doPost( url ,  null , realData , function(data){
                        if(data.rec == 'SUC') {
                            com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
                            dataList.checkboxData = [];
                            dataList.laytplShow();
                            layer.closeAll();
                        }
                    },function(data){ });
                });
            } else {
                layer.msg('<s:message code="请选择需要操作的数据"/>');
            }
        });
    });
	</script>
</head>
<body>
	<div class="admin-main" id="selectComponentGroupList">
		<%-- 操作功能 --%>
		<div>
			<div class="layui-btn-group">
				<c:if test="${belongsGroupFlag eq 'selectComponent'}">
					<button class="layui-btn" id="selectComponentComfirm" name="selectComponentComfirm" url="component/selectOrMoveComponent.web">
						<i class="layui-icon">&#xe640;</i><s:message code="确定" />
					</button>
				</c:if>
				<c:if test="${belongsGroupFlag eq 'detailComponent'}">
					<button class="layui-btn" id="moveComponentComfirm" name="moveComponentComfirm" url="component/selectOrMoveComponent.web">
						<i class="layui-icon">&#xe640;</i><s:message code="移除" />
					</button>
				</c:if>
			</div>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="selectComponentList" lay-filter="selectComponentList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="selectComponentListChooseAll" id="selectComponentListChooseAll" lay-skin="primary" /></th>
					<%-- <th><s:message code="编号" /></th> --%>
					<th><s:message code="生产商" /></th>
					<th><s:message code="出厂前序列号" /></th>
					<th><s:message code="型号" /></th>
					<th><s:message code="组件坐标" /></th>
					<th><s:message code="所属组串" /></th>
					<th><s:message code="电站名称" /></th>
					<th><s:message code="状态" /></th>
					<th><s:message code="录入人" /></th>
					<th><s:message code="录入时间" /></th>
				</tr>
			</thead>
			<tbody id="selectComponentListBody"></tbody>
		</table>
		<script type="text/html" id="selectComponentListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="selectComponentList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="selectComponentList_check_{{r.id}}" id="selectComponentList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				
				<td data-field="producers_{{r.producers}}">{{r.producers}}</td>
				<td data-field="serialNo_{{r.serialNo}}">{{r.serialNo}}</td>
				<td data-field="model_{{r.model}}">{{r.model}}</td>
				<td data-field="chipId_{{r.chipId}}">{{r.chipId}}</td>
				<td data-field="belongsGroupName_{{r.belongsGroupName}}">{{r.belongsGroupName}}</td>
				<td data-field="powerStationName_{{r.powerStationName}}">{{r.powerStationName}}</td>
				<td data-field="status_{{r.status}}">
					{{# if(!isNull(r.status) && r.status == 1 ){ }}
						<s:message code="打开"/>
					{{# }  }}
					{{# if(!isNull(r.status) && r.status == 2 ){ }}
						<s:message code="关闭"/>
					{{# }  }}
				</td>
				<td data-field="createUserName_{{r.createUserName}}">
					{{# if(!isNull(r.createUserName)){ }}
						{{r.createUserName}}
					{{# }  }}
				</td>
				<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="10"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "component/queryComponentList.web",
					defaultTableId : 'selectComponentList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'selectComponentListBody',
					formName : 'selectComponentListForm',
                    laytplPutextra : {"belongsGroupFlag":'${belongsGroupFlag}' , "belongsGroupId":'${belongsGroupId}'},
					isTable : false,
					laytplTemplet : 'selectComponentListTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>