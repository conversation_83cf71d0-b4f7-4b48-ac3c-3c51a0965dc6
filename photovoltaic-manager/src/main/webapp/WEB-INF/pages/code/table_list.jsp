<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@page import="com.ymx.common.utils.PageUtil"%>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
        // 新增页面
        $("#tableter-add").click(function () {
            var url = $('#tableter-add').attr("url");
            windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
        });
        // 编辑
        $("#tableter-edit").click(function () {
            var url = $('#tableter-edit').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 查看
        $("#tableter-detail").click(function () {
            var url = $('#tableter-detail').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 删除
        $("#tableter-delete").click(function () {
            var url = $('#tableter-delete').attr("url");
            operationDb( url , dataList ,  { "isDelete" : 2 , "leftMenuId": '${leftMenuId}'} );
        });
    });
    
	</script>
</head>
<body>
	<div class="admin-main" id="table_list">
		<%-- <s:message code="查询条件" />区域 --%>
		<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="tableListForm" id="tableListForm">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="名称" /></label>
							<div class="layui-input-inline">
								<input type="text" name="tableName" id="tableName" placeholder="<s:message code="请输入名称" />" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		</blockquote>
		<%-- 操作功能 --%>
		<div>
			<jsp:include page="/functionBuilder.htm"></jsp:include>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="tableList" lay-filter="tableList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="tableListChooseAll" id="tableListChooseAll" lay-skin="primary" /></th>
					<%-- <th><s:message code="编号" /></th> --%>
					<th>库名</th>
					<th>表名</th>
					<th>备注</th>
				</tr>
			</thead>
			<tbody id="tableListBody"></tbody>
		</table>
		<script type="text/html" id="tableListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="tableList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="tableList_check_{{r.id}}" id="tableList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				<td data-field="dbName_{{r.dbName}}">{{r.dbName}}</td>
				<td data-field="tableName_{{r.tableName}}">{{r.tableName}}</td>
				<td data-field="tableText_{{r.tableText}}">{{r.tableText}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="6"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "table/queryTableList.web",
					defaultTableId : 'tableList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'tableListBody',
					formName : 'tableListForm',
					isTable : false,
					laytplTemplet : 'tableListTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>