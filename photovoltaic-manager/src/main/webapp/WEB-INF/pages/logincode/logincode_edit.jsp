<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<div id="productLoginCode" style="margin: 10px;">
      <form id="loginCodeForm" name="loginCodeForm"  method="post" class="layui-form">
	    <div class="layui-form-item">
			<label class="layui-form-label"><s:message code="生成数量" /></label>
			<div class="layui-input-block">
			  <input type="text" name="codeNum" id="codeNum" lay-verify="required" placeholder="<s:message code="请输入0至1000的数字"/>" class="layui-input" />
			</div>
		</div>
	  </form>
	  <div class="layui-form-item">
		  <div class="layui-input-block">
			 <%-- <button class="layui-btn" onclick="productLoginCode();">保存</button>--%>
			  <a id="loginCode-save" class="layui-btn" lay-submit lay-filter="loginCodeForm"><s:message code="保存" /></a>
		  </div>
	  </div>
	  <script type="text/javascript" >
		  $(function(){
              // 保存
              $("#loginCode-save").click(function () {
                  formSubmit({
                      "formId":"loginCodeForm" ,
                      "url":"loginCode/saveLoginCode.web" ,
                      "backType": 2 ,
                      callBack : function (data) {
                          layer.msg('<s:message code="操作成功"/>');
                          dataList.checkboxData = [];
                          dataList.laytplShow();
                          layer.closeAll();
                      }
                  });
              });
		  }) ;
	</script>
</div>
