<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script type="text/javascript">
	$(function(){
		$("#loginCode-add").click(function () {
            //生成
			var url = $('#loginCode-add').attr("url");
            doPost(url , 'text' ,  {} , function(data){
                loginCodeLayer = $.layerOpenCustom({
                    title: '<s:message code="生成登录码"/>' , //不显示标题栏
                    area: '600px;' ,
                    id: 'loginCodeDiv' ,//设定一个id，防止重复弹出
                    content: data
                });
            },function(data){ });
        });
    });
	</script>
</head>
<body>
	<div class="admin-main" id="member_detail_list">
		<%-- <s:message code="查询条件" />区域 --%>
		<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="loginCodeListForm" id="loginCodeListForm">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label"><s:message code="登录码" /></label>
							<div class="layui-input-inline">
								<input type="text" name="loginCode" placeholder="<s:message code="请输入登录码" />" class="layui-input" style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label"><s:message code="使用状态" /></label>
							<div class="layui-input-inline" >
								<select id="status" name="status" >
									<option value="0"><s:message code="请选择"/></option>
								</select>
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		</blockquote>
		<%-- 操作功能 --%>
		<div>
			<jsp:include page="/functionBuilder.htm"></jsp:include>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="loginCodeList"
			lay-filter="memberListData" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="loginCodeListChooseAll" id="loginCodeListChooseAll" lay-skin="primary" /></th>
					<th><s:message code="登录码" /></th>
					<th><s:message code="状态" /></th>
					<th><s:message code="创建人" /></th>
					<th><s:message code="创建时间" /></th>
				</tr>
			</thead>
			<tbody id="loginCodeBody"></tbody>
		</table>
		<script type="text/html" id="loginCodeListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="loginCodeList_tr_{{r.loginCode}}">
				<td data-field="id_{{r.loginCode}}"><input type="checkbox" name="loginCodeList_check_{{r.loginCode}}" id="loginCodeList_check_{{r.loginCode}}" lay-skin="primary" value="{{r.loginCode}}" /></td>
				<td data-field="loginCode_{{r.loginCode}}">{{r.loginCode}}</td>
				<td data-field="status_{{r.status}}">
					{{r.lgstatus}}
				</td>
				<td data-field="createUserName_{{r.createUserName}}">
					{{# if(!isNull(r.createUserName)){ }}
						{{r.createUserName}}
					{{# }  }}
				</td>
				<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="5"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "loginCode/queryLoginCodeList.web",
					defaultTableId : 'loginCodeList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'loginCodeBody',
					formName : 'loginCodeListForm',
					isTable : false,
					laytplTemplet : 'loginCodeListTemplet',
                    listener :function(){
                        $.selectOption({
                            url : 'dic/queryDicList.web' ,
                            selectElem : 'status' ,
                            queData:{"dicId":"loginCode"},
                            selectId : 'dicValueId' ,
                            selectName : 'dicValueLabel' ,
                            isFormRenderSelect: true
                        });
                    }
				});
			}())
		</script>
	</div>
</body>
</html>