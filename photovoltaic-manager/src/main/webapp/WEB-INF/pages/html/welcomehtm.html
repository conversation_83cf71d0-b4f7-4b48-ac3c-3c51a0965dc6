
<html>
<head>

<script type='text/javascript'>
    
    function loadMapScenario() 
    {
    
           var map = new Microsoft.Maps.Map(document.getElementById('myMap'), {}); 
          
        
     }
     
     
</script>
 <script type='text/javascript' src='https://cn.bing.com/api/maps/mapcontrol?key=AuSqp8yFdgmqAlwip_2oCe9zgBFrFIOQ3nXIibLG-U0IjjW0myJqUQ1G1Eiklmtn &callback=loadMapScenario' async defer></script>

</head>
<body onload='loadMapScenario();'>
<div id='printoutPanel'></div>
        
        <div id='myMap' style='width: 100vw; height: 100vh;'></div>
 
</body>
</html>