<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
	    // 新增页面
		$("#equipment-add").click(function () {
            var url = $('#equipment-add').attr("url");
            windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
        });
		// 编辑
        $("#equipment-edit").click(function () {
            var url = $('#equipment-edit').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 查看
        $("#equipment-detail").click(function () {
            var url = $('#equipment-detail').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 删除
        $("#equipment-delete").click(function () {
            var url = $('#equipment-delete').attr("url");
	        operationDbs( url , dataList ,  { "isDelete" : 2 , "leftMenuId": '${leftMenuId}'} );
        });
    });
	</script>
</head>
<body>
	<div class="admin-main" id="equipment_list">
		<%-- <s:message code="查询条件" />区域 --%>
		<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="equipmentListForm" id="equipmentListForm">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="版本号" /></label>
							<div class="layui-input-inline">
								<input type="text" name="versionNum" id="versionNum" placeholder="<s:message code="请输入版本号" />" class="layui-input">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="版本编号" /></label>
							<div class="layui-input-inline">
								<input type="text" name="versionNo" id="versionNo" placeholder="<s:message code="请输入版本编号" />" class="layui-input">
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		</blockquote>
		<%-- 操作功能 --%>
		<div>
			<jsp:include page="/functionBuilder.htm"></jsp:include>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="equipmentList" lay-filter="equipmentList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="equipmentListChooseAll" id="equipmentListChooseAll" lay-skin="primary" /></th>
					<th><s:message code="编号" /></th>
					<th><s:message code="版本号" /></th>
					<th><s:message code="文件" /></th>
					<%--<th><s:message code="是否更新" /></th>--%>
					<th><s:message code="更新说明" /></th>
					<th><s:message code="发生时间" /></th>
				</tr>
			</thead>
			<tbody id="equipmentListBody"></tbody>
		</table>
		<script type="text/html" id="equipmentListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="equipmentList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="equipmentList_check_{{r.id}}" id="equipmentList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				<td data-field="versionNo_{{r.versionNo}}">{{r.versionNo}}</td>
				<td data-field="versionNum_{{r.versionNum}}">{{r.versionNum}}</td>
				<td data-field="url_{{r.url}}">{{r.url}}</td>
				<%--<td data-field="status_{{r.status}}">--%>
					<%--{{# if(!isNull(r.status) && 1 == r.status ){ }}--%>
						<%--<s:message code="是"/>--%>
					<%--{{# }  }}--%>
					<%--{{# if(!isNull(r.status) && 2 == r.status ){ }}--%>
						<%--<s:message code="否"/>--%>
					<%--{{# }  }}--%>
				<%--</td>--%>
				<td data-field="content_{{r.content}}">
					{{# if(!isNull(r.content)){ }}
						{{r.content}}
					{{# }  }}
				</td>
				<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="7"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "equipment/queryEquipmentVersionList.web",
					defaultTableId : 'equipmentList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'equipmentListBody',
					formName : 'equipmentListForm',
					isTable : false,
					laytplTemplet : 'equipmentListTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>