<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@page import="com.ymx.common.utils.PageUtil" %>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.upload.plugin.js"></script>
<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>

<link rel="stylesheet" href="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/themes/base/jquery.ui.all.css">
<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.core.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.widget.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.position.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.menu.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.autocomplete.js"></script>
<script type="text/javascript">
	(function($) {
		$(function() {
			// 保存
			$('#icon-save').click(function() {
				if($("#url").val()==""){
					com.ymx.layui.public.core.layer.msg('请选择版本文件!');
				}else{
					formSubmit({
						"formId":"equipmentForm" ,
						"url":"equipment/saveOrUpdate.web" ,
						"backListUrl":"equipment/list.htm" ,
						"leftMenuId":'${leftMenuId}'
					});
				}
			});
			// 返回
			$("#icon-back").click(function() {
				windowLocaltionHref("equipment/list.htm", "&leftMenuId=${leftMenuId}")
			});

			// 上传图片
			$.layuiUpload({
				elem: '#uploadPic',
				showElem: 'picShow',
				showTextElem: 'picText',
				data: {type: 'equipment'},
				doneSingleUpload: function(res, index, upload) {
					if(res.rec === 'SUC') {
						$("#url").val(res.reModel);
						$("#url_Text").html(res.reModel);
					}
				}
			});
        })
	})(jQuery)
</script>
</head>
<body>
<div class="admin-main" id="equipmentSaveOrUpdate">
	<form action="" class="layui-form" id="equipmentForm" name="equipmentForm">
		<%-- 隐藏属性 --%>
		<input type="hidden" name="id" id="id" value="${info.id}"/>
		<input type="hidden" name="url" id="url" value="${info.url}" />
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="编号" /></label>
			<div class="layui-input-block">
				<input type="text" name="versionNo" id="versionNo" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					   value="${info.versionNo}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入编号" />" class="layui-input" />
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="版本号" /></label>
			<div class="layui-input-block">
				<input type="text" name="versionNum" id="versionNum" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					   value="${info.versionNum}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入版本号" />" class="layui-input" />
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="版本文件" /></label>
			<div class="layui-input-inline">
				<div class="layui-upload">
					<div id="url_Text">${info.url}</div>
					<c:if test="${operator != 'detail'}">
						<button type="button" class="layui-btn" id="uploadPic"><s:message code="上传文件" /></button>
					</c:if>
					<div class="layui-upload-list">
						<p id="picText"></p>
					</div>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="更新说明" /></label>
			<div class="layui-input-block"><input type="hidden" id="inverterId" name="inverterId" />
				<input type="text" name="content" id="content"  <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					   value="${info.content}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入更新说明" />" class="layui-input" />
			</div>
		</div>
		<c:if test="${operator == 'detail'}">
			<div class="layui-form-item">
				<label class="layui-form-label"><s:message code="创建时间" /></label>
				<div class="layui-input-block">
					<input type="text" name="createTimeCh" id="createTimeCh" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
						   value="${info.createTimeCh}" maxlength="30" autocomplete="off" class="layui-input" />
				</div>
			</div>
		</c:if>
		<%--操作按钮 --%>
		<div class="layui-form-item">
			<div class="layui-input-block">
				<c:if test="${operator != 'detail'}"><a id="icon-save" class="layui-btn" lay-submit lay-filter="equipmentForm"><s:message code="保存" /></a></c:if>
				<a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a>
			</div>
		</div>
	</form>
</div>
<script type="text/javascript">
	(function() {
		DataList.create({ isLaytpl: false, isTable: false, isPager: false,
			listener:function()	{
				<%--$.selectOption({--%>
					<%--url : 'dic/queryDicList.web' ,--%>
					<%--selectElem : 'groupType' ,--%>
					<%--queData:{"dicId":"groupType"},--%>
					<%--selectId : 'dicValueId' ,--%>
					<%--selectName : 'dicValueLabel' ,--%>
					<%--selected : '${info.groupType}',--%>
					<%--isFormRenderSelect: true--%>
				<%--});--%>
			}
		});
	}());
</script>
</body>
</html>


