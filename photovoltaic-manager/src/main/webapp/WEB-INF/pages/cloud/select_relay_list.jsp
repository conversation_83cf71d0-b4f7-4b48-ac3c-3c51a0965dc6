<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
        // 确认
        $("#selectRelayComfirm").click(function () {
            var url = $('#selectRelayComfirm').attr("url");
            // 列表数据id  是多个以逗分隔值
            var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
            var length = dataList.checkboxData.length;
            var realData = { "cloudId":'${cloudId}', "powerStationId":'${powerStationId}',"id":ids ,"operationFlag":'${operationFlag}'};
            if (length > 0 ){
                layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index ){
                    doPost( url ,  null , realData , function(data){
                        if(data.rec == 'SUC') {
                            com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
                            dataList.checkboxData = [];
                            dataList.laytplShow();
                            layer.closeAll();
                        }
                    },function(data){ });
                });
            } else {
                layer.msg('<s:message code="请选择需要操作的数据"/>');
            }
        });
        // 移除
        $("#moveRelayComfirm").click(function () {
            var url = $('#moveRelayComfirm').attr("url");
            // 列表数据id  是多个以逗分隔值
            var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
            var length = dataList.checkboxData.length;
            var realData = {  "id":ids ,"operationFlag":'${operationFlag}' };
            if (length > 0 ){
                layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index ){
                    doPost( url ,  null , realData , function(data){
                        if(data.rec == 'SUC') {
                            com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
                            dataList.checkboxData = [];
                            dataList.laytplShow();
                            layer.closeAll();
                        }
                    },function(data){ });
                });
            } else {
                layer.msg('<s:message code="请选择需要操作的数据"/>');
            }
        });
        // 返回
			$("#icon-back").click(function() {
				windowLocaltionHref("cloudter/list.htm", "&leftMenuId=${leftMenuId}")
			});
    });
	</script>
</head>
<body>
	<div class="admin-main" id="selectOrMoveRelayList">
		<%-- 操作功能 --%>
		<div>
			<div class="layui-btn-group">
				<c:if test="${operationFlag eq 'selectRelay'}">
					<button class="layui-btn" id="selectRelayComfirm" name="selectRelayComfirm" url="relay/selectOrMoveCloud.web">
						<i class="layui-icon">&#xe640;</i><s:message code="确认" />
					</button>
				</c:if>
				<c:if test="${operationFlag eq 'viewRelay'}">
					<button class="layui-btn" id="moveRelayComfirm" name="moveRelayComfirm" url="relay/selectOrMoveCloud.web">
						<i class="layui-icon">&#xe640;</i><s:message code="移除" />
					</button>
				</c:if>
				 <%-- <a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a> --%>
			</div>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="relayList" lay-filter="relayList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="relayListChooseAll" id="relayListChooseAll" lay-skin="primary" /></th>
					<%-- <th><s:message code="编号" /></th> --%>
					<th><s:message code="中继器名称" /></th>
					<th><s:message code="中继器标识" /></th>
					<th><s:message code="硬件版本号" /></th>
					<th><s:message code="软件版本号" /></th>
				</tr>
			</thead>
			<tbody id="relayListBody"></tbody>
		</table>
		<script type="text/html" id="relayListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="relayList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="relayList_check_{{r.id}}" id="relayList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				
				<td data-field="relayName_{{r.relayName}}">{{r.relayName}}</td>
				<td data-field="relayId_{{r.relayId}}">{{r.relayId}}</td>
				<td data-field="hardVersion_{{r.hardVersion}}">{{r.hardVersion}}</td>
				<td data-field="softVersion_{{r.softVersion}}">{{r.softVersion}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="7"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "relay/queryRelayListForCloud.web",
					defaultTableId : 'relayList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'relayListBody',
					formName : 'relayListForm',
                    laytplPutextra : {"cloudId":'${cloudId}' , "operationFlag":'${operationFlag}'},
					isTable : false,
					laytplTemplet : 'relayListTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>