<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@page import="com.ymx.common.utils.PageUtil"%>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
        // 新增页面
        $("#cloudter-add").click(function () {
            var url = $('#cloudter-add').attr("url");
            windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
        });
        // 编辑
        $("#cloudter-edit").click(function () {
            var url = $('#cloudter-edit').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 查看
        $("#cloudter-detail").click(function () {
            var url = $('#cloudter-detail').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 删除
        $("#cloudter-delete").click(function () {
            var url = $('#cloudter-delete').attr("url");
            operationDb( url , dataList ,  { "isDelete" : 2 , "leftMenuId": '${leftMenuId}'} );
        });
		// 选择组件
		$("#cloudter-component").click(function () {
			var url = $('#cloudter-component').attr("url");
			var length = dataList.checkboxData.length;
			if(length != 1){
				com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
				return;
			}
			var id = dataList.checkboxData[0].id;
			layer.open({
				title: '<s:message code="请选择组串"/>',
				type: 2,
				area: ['100%', '100%'],
				content: ['cloudter/componentPageList.htm?id=' + id + "&cloudIdFlag=addComponent&leftMenuId=${leftMenuId}", 'no']
			});
		});
		// 查看组件
		$("#cloudter-component-info").click(function () {
			var url = $('#cloudter-component-Info').attr("url");
			var length = dataList.checkboxData.length;
			if(length != 1){
				com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
				return;
			}
			var id = dataList.checkboxData[0].id;
			//	layer.close(index);
			var index =layer.open({
				title: '<s:message code="请选择组串"/>',
				type: 2,
				area: ['100%', '100%'],
				content: ['cloudter/componentPageList.htm?id=' + id + "&cloudIdFlag=selectComponent&leftMenuId=${leftMenuId}", 'no']
			});
		//	alert(index);
		  
		});
		//下载模板
		 $("#cloudter-downfile").click(function () {
            var url = $('#cloudter-downfile').attr("url");
         
            windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
        });
		// 导入
        $('#cloudter-import').click(function(){
			var url = $('#cloudter-import').attr("url");
			doPost(url , 'text' ,  {} , function(data){
				 cloudUpload = $.layerOpenCustom({
			        title: '<s:message code="云终端"/>' , //不显示标题栏
			        area: '600px;' ,
			        id: 'setterSave' ,//设定一个id，防止重复弹出
			        content: data
				});
			},function(data){
				
			}); 
		});
		
		              // 查看中继器
                    $("#cloudter-relay-info").click(function () {
                			var url = $('#cloudter-relay-info').attr("url");
                			var length = dataList.checkboxData.length;
                			if(length != 1){
                				com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
                				return;
                			}
                			var cloudId = dataList.checkboxData[0].imei;
                			layer.open({
                				title: '<s:message code="查看中继器"/>',
                				type: 2,
                				area: ['100%', '100%'],
                				content: ['cloudter/relayPageList.htm?cloudId=' + cloudId + "&operationFlag=viewRelay&leftMenuId=${leftMenuId}", 'no']
                			});
                	});
					
					
					  // 选择中继器
            		$("#cloudter-relay").click(function () {
            			var url = $('#cloudter-relay').attr("url");
            			var length = dataList.checkboxData.length;
            			if(length != 1){
            				com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
            				return;
            			}
            			var cloudId = dataList.checkboxData[0].imei;
            			var powerStationId = dataList.checkboxData[0].powerStationId;

            			layer.open({
            				title: '<s:message code="请选择中继器"/>',
            				type: 2,
            				area: ['100%', '100%'],
            				content: ['cloudter/relayPageList.htm?cloudId=' + cloudId +"&powerStationId="+powerStationId+"&operationFlag=selectRelay&leftMenuId=${leftMenuId}", 'no']
            			});
            		});


            		 // 查询版本
                     $("#version-query").click(function () {
					  if(checkIsSingle())
			          {
                     var queryType=1;
                       layer.open({
                                     title: '查询版本',
                                     offset: 'auto',
			                         //area : ['380px', '170px'],
                                     btn: ['查询自身版本', '查询所属组件版本','取消'],
                                     btnAlign: 'c',
                                     btn1: function (index, layero) {
                                         queryType=1;
                                          versionQuery(queryType);
                                          layer.close(index);
                                         },
                                      btn2: function (index, layero) {
                                          queryType=2;
                                           versionQuery(queryType);
                                          layer.close(index);
                                       },
                                       btn3: function (index, layero) {
                                           layer.close(index);
                                                  },
                                 });
								 
			           }
                     });
					
		
    });


    function versionQuery(queryType)
    {
       var url = $('#version-query').attr("url");
       var imei = getStrAttrVal({data :dataList.checkboxData , "attrName":"imei"}) ;
       operationDb( url , dataList ,  { "isDelete" : 2 , "leftMenuId": '${leftMenuId}',
        "imei": imei,"queryId": imei,"queryType":queryType,"type":1 } );
    }
	
	            // 检查操作了几条数据
		       function checkIsSingle() {
                       var length = dataList.checkboxData.length;
                        if(length != 1){
                         com.ymx.layui.public.core.layer.msg('<s:message code="一次只能操作一条数据"/>!');
                        return false;
                        }
                        return true;
                  }
    		    
    
	</script>
</head>
<body>
	<div class="admin-main" id="cloud_list">
		<%-- <s:message code="查询条件" />区域 --%>
		<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="cloudListForm" id="cloudListForm">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="录入时间" /></label>
							<div class="layui-input-inline" >
								<input type="text" name="createTimeStart" id="createTimeStart" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'createTimeEnd\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" placeholder="<s:message code="录入开始时间" />" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
							<div class="layui-form-mid">-</div>
							<div class="layui-input-inline">
								<input type="text" name="createTimeEnd" id="createTimeEnd" placeholder="<s:message code="录入结束时间" />" onclick="WdatePicker({minDate:'#F{$dp.$D(\'createTimeStart\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="名称" /></label>
							<div class="layui-input-inline">
								<input type="text" name="cloudName" id="cloudName" placeholder="<s:message code="请输入名称" />" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="序列号" /></label>
							<div class="layui-input-inline">
								<input type="text" name="serialNo" id="serialNo" placeholder="<s:message code="请输入序列号" />" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="生产商" /> &nbsp; &nbsp; </label>
							<div class="layui-input-inline">
								<input type="text" name="producers" id="producers" placeholder="<s:message code="生产商" />"    autocomplete="off" class="layui-input"/>
								<%--<select id="producers" name="producers" lay-verify="" >--%>
									<%--<option value=""><s:message code="请选择" /></option>--%>
								<%--</select>--%>
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="型号" /></label>
							<div class="layui-input-inline">
								<input type="text" name="model" id="model" placeholder="<s:message code="型号" />"  autocomplete="off" class="layui-input"/>
								<%--<select id="model" name="model" lay-verify="" >--%>
									<%--<option value=""><s:message code="请选择" /></option>--%>
								<%--</select>--%>
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="电站名称" /></label>
							<div class="layui-input-inline">
								<input type="text" name="powerStationName" id="powerStationName" placeholder="<s:message code="电站名称" />"  autocomplete="off" class="layui-input"/>
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		</blockquote>
		<%-- 操作功能 --%>
		<div>
			<jsp:include page="/functionBuilder.htm"></jsp:include>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="cloudList" lay-filter="cloudList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="cloudListChooseAll" id="cloudListChooseAll" lay-skin="primary" /></th>
					<%-- <th><s:message code="编号" /></th> --%>
					<th><s:message code="名称" /></th>
					<%-- <th><s:message code="序列号" /></th>--%>
					<th><s:message code="采集器标识" /></th>
					<%--<th><s:message code="型号" /></th>--%>
					<th><s:message code="电站名称" /></th>
					<th><s:message code="软件版本号" /></th>
					<th><s:message code="所在分区" /></th>
					<th><s:message code="录入时间" /></th>
					<th><s:message code="更新时间" /></th>
					<th  style="display: none"><s:message code="电站id" /></th>
				</tr>
			</thead>
			<tbody id="cloudListBody"></tbody>
		</table>
		<script type="text/html" id="cloudListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="cloudList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="cloudList_check_{{r.id}}" id="cloudList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				
				<td data-field="cloudName_{{r.cloudName}}">{{r.cloudName}}</td>
				<%--<td data-field="serialNo_{{r.serialNo}}">{{r.serialNo}}</td>--%>
				<td data-field="imei_{{r.imei}}">{{r.imei}}</td>
				<%--<td data-field="model_{{r.model}}">{{r.model}}</td>--%>

				<td data-field="powerStationName_{{r.powerStationName}}">{{r.powerStationName}}</td>
			   <td data-field="softVersion_{{r.softVersion}}">
			   	{{# if(!isNull(r.softVersion)){ }}
               					{{r.softVersion}}
               					{{# }  }}
			   </td>
			   <td data-field="bootPartition_{{r.bootPartition}}">
			    {{# if(!isNull(r.bootPartition)){ }}
                 {{r.bootPartition}}
                  {{# }  }}
			   </td>
			   <td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
			    <td data-field="updateTime_{{r.updateTime}}">
               			 	{{# if(!isNull(r.updateTime)){ }}
                                 {{r.updateTime}}
                               {{# }  }}
               	</td>
			 </td>
				<td style="display: none" data-field="powerStationId_{{r.powerStationId}}">{{r.powerStationId}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="6"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "cloudter/queryCloudTerminalList.web",
					defaultTableId : 'cloudList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'cloudListBody',
					formName : 'cloudListForm',
					isTable : false,
					laytplTemplet : 'cloudListTemplet'
				});
//				DataList.create({ isLaytpl: false, isTable: false, isPager: false ,
//					listener:function()	{
//						$.selectOption({
//							url : 'dic/queryDicList.web' ,
//							selectElem : 'model' ,
//							queData:{"dicId":"model"},
//							selectId : 'dicValueId' ,
//							selectName : 'dicValueLabel' ,
//							selected : '',
//							isFormRenderSelect: true
//						});
//						$.selectOption({
//							url : 'dic/queryDicList.web' ,
//							selectElem : 'producers' ,
//							queData:{"dicId":"producer"},
//							selectId : 'dicValueId' ,
//							selectName : 'dicValueLabel' ,
//							selected : '',
//							isFormRenderSelect: true
//						});
//					}
//				});
			}())
		</script>
	</div>
</body>
</html>