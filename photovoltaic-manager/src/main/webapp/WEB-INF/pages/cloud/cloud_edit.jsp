<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@page import="com.ymx.common.utils.PageUtil" %>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.upload.plugin.js"></script>
<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
	(function($) {
		$(function() {
			// 保存
			$('#icon-save').click(function() {
				formSubmit({
					"formId":"cloudTerminalForm" ,
					"url":"cloudter/saveOrUpdate.web" ,
					"backListUrl":"cloudter/list.htm" ,
					"leftMenuId":'${leftMenuId}'
				});
			});
			// 返回
			$("#icon-back").click(function() {
				windowLocaltionHref("cloudter/list.htm", "&leftMenuId=${leftMenuId}")
			});
		})
	})(jQuery)
</script>
</head>
<body>
<div class="admin-main" id="cloudSaveOrUpdate">
	<form action="" class="layui-form" id="cloudTerminalForm" name="cloudTerminalForm">
		<%-- 隐藏属性 --%>
		<input type="hidden" name="id" id="id" value="${info.id}"/>
		<input type="hidden" name="picture" id="picture"/>
		<div class="layui-form-item">
			<%-- <div class="layui-inline">
				<label class="layui-form-label"><s:message code="编号"/></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="cloudNo" id="cloudNo" desc="编号" lay-verify="isEmpty" maxlength="30"
					       value="${info.cloudNo}" autocomplete="off" class="layui-input"/>
				</div>
			</div> --%>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="名称"/></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="cloudName" id="cloudName" desc="<s:message code="名称"/>" lay-verify="isEmpty"
						   value="${info.cloudName}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="序列号"/></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="serialNo" id="serialNo" desc="<s:message code="序列号"/>" maxlength="11" lay-verify="isEmpty"
					       value="${info.serialNo}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="型号" /></label>
				<div class="layui-input-inline">
					<%--<select id="model" name="model" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>>--%>
						<%--<option value=""><s:message code="请选择" /></option>--%>
					<%--</select>--%>
					<input type="text" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="model" id="model" desc="<s:message code="型号" />"
					       maxlength="50" lay-verify="isEmpty" value="${info.model}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="生产商" /></label>
				<div class="layui-input-inline">
					<%--<select id="producers" name="producers" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>>--%>
						<%--<option value=""><s:message code="请选择" /></option>--%>
					<%--</select>--%>
					<input type="text"  <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="producers" id="producers"
					       desc="<s:message code="生产商" />" maxlength="50" lay-verify="isEmpty"  value="${info.producers}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="采集器标识"/></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="imei" id="imei" desc="<s:message code="采集器标识"/>"
					       value="${info.imei}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="创建人"/></label>
				<div class="layui-input-inline">
					<input type="text" disabled="disabled" name="createUserName" id="createUserName" desc="<s:message code="创建人"/>" value="${info.createUserName}" autocomplete="off"
					       class="layui-input"/>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
        			<div class="layui-inline">
        				<label class="layui-form-label"><s:message code="软件版本号"/></label>
        				<div class="layui-input-inline">
        				<input type="text" disabled="disabled" name="softVersion" id="softVersion" desc="<s:message code="软件版本号"/>" value="${info.softVersion}" autocomplete="off"
                                					       class="layui-input"/>
        				</div>
        			</div>

        			<div class="layui-inline">
        				<label class="layui-form-label"><s:message code="分区"/></label>
        				<div class="layui-input-inline">
        					<input type="text" disabled="disabled" name="bootPartition" id="bootPartition" desc="<s:message code="创建人"/>" value="${info.bootPartition}" autocomplete="off"
        					       class="layui-input"/>
        				</div>
        			</div>
        </div>

        		<div class="layui-form-item">
                			<div class="layui-inline">
                				<label class="layui-form-label"><s:message code="mcu"/></label>
                				<div class="layui-input-inline">
                					<input type="text" disabled="disabled" name="mcu" id="mcu" desc="<s:message code="mcu"/>" value="${info.mcu}" autocomplete="off"
                                                					       class="layui-input"/>

                				</div>
                			</div>
                			<div class="layui-inline">
                				<label class="layui-form-label"><s:message code="bomId"/></label>
                				<div class="layui-input-inline">
                					<input type="text" disabled="disabled" name="bomId" id="bomId" desc="<s:message code="bomId"/>" value="${info.bomId}" autocomplete="off"
                					       class="layui-input"/>
                				</div>
                			</div>
                		</div>

              	<div class="layui-form-item">
                	<div class="layui-inline">
                		<label class="layui-form-label"><s:message code="硬件版本号"/></label>
                		<div class="layui-input-inline">
                			<input type="text" disabled="disabled" name="hardVersion" id="hardVersion" desc="<s:message code="hardVersion"/>" value="${info.hardVersion}" autocomplete="off"
                                          					       class="layui-input"/>

                		</div>
                	</div>
                	<div class="layui-inline">
                		<label class="layui-form-label"><s:message code="更新时间"/></label>
                		<div class="layui-input-inline">
                			<input type="text" disabled="disabled" name="updateTime" id="updateTime" desc="<s:message code="updateTime"/>" value="${info.updateTime}" autocomplete="off"
                			       class="layui-input"/>
                		</div>
                	</div>
                </div>

		<div class="layui-form-item">
			<c:if test="${operator == 'detail'}">
				<div class="layui-inline">
					<label class="layui-form-label"><s:message code="创建时间" /></label>
					<div class="layui-input-inline">
						<input type="text"
						       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="createTimeCh" id="createTimeCh" desc="<s:message code="创建时间" />" lay-verify="isEmpty"
						       value="${info.createTimeCh}" autocomplete="off" class="layui-input"/>
					</div>
				</div>
			</c:if>
			<c:if test="${operator == 'detail'}">
				<div class="layui-inline">
					<label class="layui-form-label"><s:message code="电站名称"/></label>
					<div class="layui-input-inline">
						<input type="text"
						       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="powerStationName" id="powerStationName"  lay-verify="isEmpty"
						       value="${info.powerStationName}" autocomplete="off" class="layui-input"/>
					</div>
				</div>
			</c:if>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="描述"/></label>
				<div class="layui-input-inline">
					<textarea id="describetion" name="describetion"  <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> style="resize: none;" cols="50" rows="5" >${info.describetion}</textarea>
				</div>
			</div>
		</div>

		<%--操作按钮 --%>
		<div class="layui-form-item">
			<div class="layui-input-block">
				<c:if test="${operator != 'detail'}"><a id="icon-save" class="layui-btn" lay-submit lay-filter="cloudTerminalForm"><s:message code="保存"/></a></c:if>
				<a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回"/></a>
			</div>
		</div>
	</form>
</div>
<script type="text/javascript">
	(function() {
		DataList.create({ isLaytpl: false, isTable: false, isPager: false ,
			listener:function()	{
				<%--$.selectOption({--%>
					<%--url : 'dic/queryDicList.web' ,--%>
					<%--selectElem : 'model' ,--%>
					<%--queData:{"dicId":"model"},--%>
					<%--selectId : 'dicValueId' ,--%>
					<%--selectName : 'dicValueLabel' ,--%>
					<%--selected : '${info.model}',--%>
					<%--isFormRenderSelect: true--%>
				<%--});--%>
				<%--$.selectOption({--%>
					<%--url : 'dic/queryDicList.web' ,--%>
					<%--selectElem : 'producers' ,--%>
					<%--queData:{"dicId":"producer"},--%>
					<%--selectId : 'dicValueId' ,--%>
					<%--selectName : 'dicValueLabel' ,--%>
					<%--selected : '${info.producers}',--%>
					<%--isFormRenderSelect: true--%>
				<%--});--%>
			}
		});
	}());
</script>
</body>
</html>


