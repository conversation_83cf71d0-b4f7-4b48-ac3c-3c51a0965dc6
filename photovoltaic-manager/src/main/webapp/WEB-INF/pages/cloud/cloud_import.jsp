<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/jquery-1.9.1.min.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/jquery.form.js"></script>
  <script type="text/javascript">  
            //ajax 方式<th><s:message code="上传文件" />操作  
             $(document).ready(function(){  
                $('#btn').click(function(){  
                    if(checkData()){  
                        $('#form1').ajaxSubmit({    
                            url:'cloudter/cloudUpload.htm',  
                            dataType: 'text',  
                            success: resutlMsg,  
                            error: errorMsg  
                        });   
                        function resutlMsg(msg){  
                             alert('<s:message code="导入"/>excel<s:message code="成功"/>！');  
                               com.ymx.layui.public.core.layer.close(cloudUpload._layer_index);
                            $("#upfile").val("");  
                        }  
                        function errorMsg(){   
                            alert('<s:message code="导入"/>excel<s:message code="出错"/>！');      
                        }  
                    }  
                });  
             });  
               
             //JS校验form表单信息  
             function checkData(){  
                var fileDir = $("#upfile").val();  
                var suffix = fileDir.substr(fileDir.lastIndexOf("."));  
                if("" == fileDir){  
                    alert('<s:message code="选择需要导入的"/>Excel<s:message code="文件"/>！');  
                    return false;  
                }  
                if(".xls" != suffix && ".xlsx" != suffix ){  
                    alert('<s:message code="选择"/>Excel<s:message code="格式的文件导入"/>！');  
                    return false;  
                }  
                return true;  
             }  
    </script> 


<div id="cloudUpload" style="margin: 10px;">
      <form method="POST"  enctype="multipart/form-data" id="form1" >  
        <table>  
         <tr>  
            <td><s:message code="上传文件" />: </td>  
            <td> <input id="upfile" type="file" name="upfile"></td>  
         </tr>  
        <tr>  
             <td></td>  
            <td></br><input type="button" class="layui-btn"  value=<s:message code="提交" /> id="btn" name="btn" ></td>
         </tr>  
        </table>    
    </form>  	 
</div>