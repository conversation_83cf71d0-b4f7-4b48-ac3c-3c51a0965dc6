<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
<jsp:include page="/include.htm" />
<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
<title>用户权限设置</title>
<script language="javascript">
	//行全选
	function chooseAllInfo(obj){
		if(obj.checked){
			$('input[name^="r_' + $(obj).attr("id") +'"]').prop("checked",true);
			$('input[name="m_'+ $(obj).attr('id') + '"]').prop("checked",true);
		} else {
			$('input[name^="r_' + $(obj).attr("id") + '"]').prop("checked" , false);
			$('input[name="m_' + $(obj).attr('id') + '"]').prop("checked" , false);
		}
	}
</script >
</head>
<script language="javascript">
	(function($){
		$(function(){
			//全选
			$('#icon-redo').click(function(){
				$(':checkbox').prop('checked',true);
			});
			
			//取消全选
			$('#icon-undo').click(function(){
				$(':checkbox').prop('checked',false);
			});
	
			//保存
			$('#icon-save').click(function(){
				$('#roleMenu').submit();
			});
			
			//返回
			$('#icon-back').click(function(){
				window.location.href = "role/search.htm?leftMenuId=${leftMenuId}";
			});
	
		});
	})(jQuery);
</script>
<body>
<div class="admin-main" id="role_menu_list">
	<div>
		<div class="layui-btn-group">
		    <button class="layui-btn" id="icon-save"><s:message code="保存"/></button>
		    <button class="layui-btn" id="icon-redo"><s:message code="全选"/></button>
		    <button class="layui-btn" id="icon-undo"><s:message code="全不选"/></button>
		    <button class="layui-btn" id="icon-back"><s:message code="返回"/></button>
		</div>
		<span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font color="red"><s:message code="角色名称"/> &nbsp;:&nbsp;${roleName}</font></span>
	</div>
	<form  id="roleMenu" name="roleMenu" action="role/setterRole.htm?leftMenuId=${leftMenuId}" method="post">
	     <input type="hidden" name="roleId" value="${roleId}"/>
		 <table class="layui-table" lay-filter="menuRoleData" lay-even>
			  <thead>
			   <tr>
				   <th><s:message code="菜单名称"/></th>
				   <th><s:message code="菜单图标"/></th>
				   <th><s:message code="全选"/></th>
				   <th><s:message code="访问权"/></th>
				   <th><s:message code="操作权"/></th>
				   <th><s:message code="菜单链接"/></th>
			   </tr>
			</thead>
			<tbody id="dataList"></tbody>
		</table>
		<!-- 列表模板展示 -->
		<script id="menuRoleDataTemplet" type="text/html">
		{{# layui.each( d , function( index , r ) { }}
		<%-- 顶级菜单 --%>
		<tr>
    		<td>{{r.menuName}}</td>
    		<td><i class="fa {{r.menuIcon}}"></i></td>
			<td>&nbsp;</td>
    		<td>&nbsp;</td>
			<td><font color="#cccccc"><s:message code="顶级菜单无功能"/></font></td>
			<td>{{r.menuLink}}</td>
		</tr>
		<%-- 二级菜单 --%>
		{{#  if(!isObjEmpty(r.children) )  {  }}
			{{#  layui.each( r.children , function( index , child ) { }}
				
				<tr >
					<td><span style="padding-left:30px;">&nbsp;</span>{{child.menuName}}</td>
					<td><i class="fa {{child.menuIcon}}"></i></td>
					<td><input type="checkbox" class="chooseall" id="{{child.menuId}}" name="{{child.menuId}}" onClick="chooseAllInfo(this);" /></td>
    		        <td>	
	                		{{#  if(child.hasViewPower) { }} 
			           			<input type="checkbox" class="chooseall" name="m_{{child.menuId}}"  id="m_{{child.menuId}}" value="{{child.menuId}}" checked="checked" />
		           			{{#  } else {  }}
			            		<input type="checkbox" class="chooseall" name="m_{{child.menuId}}"   id="m_{{child.menuId}}"  value="{{child.menuId}}"   />
		           			{{#   } }}
					</td>
                     <%-- 操作权限 --%>
					<td>
						{{#  if(!isObjEmpty(child.functions) )  {  }}
							<%-- 功能菜单 --%>
							{{#  layui.each( child.functions , function(index , funs ) { }}
								{{# 	if ( funs.checked ) { }}
										<input type="checkbox" checked="checked" id="r_{{funs.menuId}}_{{funs.funId}}"  name="r_{{funs.menuId}}_{{funs.funId}}" value="{{funs.funId}}" id="{{funs.funId}}"/><label>{{funs.funDesc}}</label>&nbsp;&nbsp;
									{{#  } else { }}
										<input type="checkbox" id="r_{{funs.menuId}}_{{funs.funId}}"  name="r_{{funs.menuId}}_{{funs.funId}}" value="{{funs.funId}}" id="{{funs.funId}}"/><label>{{funs.funDesc}}</label>&nbsp;&nbsp;
								{{# } }}
							{{# }); }}
							<%-- 功能菜单结束 --%>
						{{# } }}
					</td>
					<td>{{child.menuLink}}</td>
				</tr>
			{{#  }) ;  }}
		{{# } }}
		<%-- 二级菜单结束 --%>

		{{#  });  }}	 <%-- 顶级菜单结束 --%>
		
		<%-- 列表无数据 --%>
		{{# if(d.length === 0){ }}
		             <s:message code="暂无数据"/>!
		{{# } }}
		</script>
	</form>
</div>
</body>
<script>
	layui.use( 'laytpl' ,  function () {
	    var laytpl = layui.laytpl ;
	    doPost( "menu/listWithFunctions.web" , null , {"roleId" : '${roleId}'  } , function(data){
	    	if(!isNull(data)){
				if (data.rec == 'SUC') {
					var rows = data.reModel;
					var getTpl = $("#menuRoleDataTemplet").html();
					var view = $("#dataList");
					laytpl(getTpl).render( rows , function(html){
						view.html(html);
					});
	            } else {
	            }
			}else{
			   console.log("<s:message code="数据解析异常"/>");
			}
		}, function(data){
		});
	});
 </script>
</html>