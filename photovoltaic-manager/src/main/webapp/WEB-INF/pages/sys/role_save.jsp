<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<div id="role_form" style="margin: 10px;">
    <form id="roleForm" name="roleForm" class="layui-form" action="">
    	  <input type="hidden" id="roleId" name="roleId" value="${role.roleId}"/>
		  <div class="layui-form-item">
		    <label class="layui-form-label"><s:message code="角色名称" /></label>
		    <div class="layui-input-block">
		      <input type="text" name="roleName" id="roleName" value="${role.roleName}" lay-verify="title" autocomplete="off" placeholder="<s:message code="请输入角名名称" />" class="layui-input" />
		    </div>
		  </div>
		  
		  <div class="layui-form-item">
		    <label class="layui-form-label"><s:message code="角色描述"/></label>
		    <div class="layui-input-block">
		      <input type="text" name="roleRemark" id="roleRemark" value="${role.roleRemark}" lay-verify="required" placeholder="<s:message code="请输入角色描述" />" autocomplete="off" class="layui-input" />
		    </div>
		  </div>
	</form>
	<div class="layui-form-item">
		  <div class="layui-input-block">
			  <button class="layui-btn" onclick="roleSubmit();"><s:message code="保存"/></button>
		  </div>
	  </div>
</div>
