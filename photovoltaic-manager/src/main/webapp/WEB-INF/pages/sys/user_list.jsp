<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.Read"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%-- <% String deptEnabled = Read.getmsg('system.dept.enabled"); %>   --%>  
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<title>用户列表</title>
	<script type="text/javascript">
	$(function(){	
		 //增加
		$('#icon-add').click(function() {
			var url = $('#icon-add').attr("url") ;
			var param = "";
			window.location.href = BASE_PATH +  url + "?leftMenuId=${leftMenuId}&operator=add";
		});
		//编辑
		$('#icon-edit').click(function(){
			//单条编辑
			var length = obj1.checkboxData.length;
			if(length == 1) {
				window.location.href = BASE_PATH +  "user/tosave.htm?operator=edit&leftMenuId=${leftMenuId}&userId=" + obj1.checkboxData[0].userId + "&deptId=" + obj1.checkboxData[0].deptId  ;
			} else {
				com.ymx.layui.public.core.layer.msg('<s:message code="请选择单条记录进行编辑"/>!');
			}
		});
		
		//删除
		$('#icon-remove').click(function(){
		   var length = obj1.checkboxData.length;
		   if (length > 0  ){
			   var userIds = getStrAttrVal({data :obj1.checkboxData , "attrName":"userId"})  ;
			   layer.confirm('<s:message code="确认要删除此项数据吗"/>?', {icon: 3, title:'<s:message code="提示"/>'}, function(index){
				   doPost( $('#icon-remove').attr("url"), 'text' , {"leftMenuId" : '${leftMenuId}' , "userIds" : userIds } , function(data){
					   if(data == 'true') {
						   com.ymx.layui.public.core.layer.msg('<s:message code="删除成功"/>');
						   obj1.checkboxData = [];
						   //window.location.href = 'user/search.htm?leftMenuId=${leftMenuId}';
						   obj1.laytplShow();
		   			   }
				   },function(data){
				   });
			   });
		   } else {
			   com.ymx.layui.public.core.layer.msg('<s:message code="请选择需要删除的记录"/>!');
		   }
		});
		
		//初始化密码
		$('#icon-reload').click(function(){
		   var length = obj1.checkboxData.length;
		   if (length > 0  ){
			   var userIds = getStrAttrVal({data :obj1.checkboxData , "attrName":"userId"})  ;
			   console.log(JSON.stringify(obj1.checkboxData));
			   console.log(userIds);
			   layer.confirm('<s:message code="确认要将所选择的数据重新初始化密码吗"/>?', {icon: 3, title:'<s:message code="提示"/>'}, function(index){
				   doPost( $('#icon-reload').attr("url"), 'text' , {"leftMenuId" : '${leftMenuId}' , "userIds" : userIds } , function(data){
					   if(data == 'true') {
						   com.ymx.layui.public.core.layer.msg('<s:message code="密码初始化成功"/>!');
						   obj1.checkboxData = [];
						   obj1.laytplShow();
						   //window.location.href = 'user/search.htm?leftMenuId=${leftMenuId}';
		   			   }
				   },function(data){
				   });
			   });
		   } else {
			   com.ymx.layui.public.core.layer.msg('<s:message code="请选择需要密码初始化的数据"/>!');
		   }
		});
		
	})
	</script>
</head>
<body>
<div class="admin-main" id="userList" >
	<%-- 操作功能 --%>
	<div >
        <jsp:include page="/functionBuilder.htm"></jsp:include>
	</div>
	<%-- 列表数据展示区 --%>
	<table class="layui-table" id="userList" lay-filter="userList" lay-even>
		<thead>
			<th><input type="checkbox" name="userListChooseAll" id="userListChooseAll" lay-skin="primary" /></th>
			<th><s:message code="用户名" /></th>
			<th><s:message code="名称" /></th>
			<th><s:message code="手机号" /></th>
			<%--<th><s:message code="镇街社区" /></th>--%>
			<%--<th><s:message code="村小区" /></th>--%>
			<th><s:message code="所属部门" /></th>
			<th><s:message code="角色" /></th>
		</thead>
		<tbody id="userListData"></tbody>
	</table>
	<%-- 分页显示 --%>
	<div align="center" id="page"></div>
	<%-- 模板引擎 --%>
	<script type="text/html" id="userListTemplet">
	{{# layui.each( d , function( index , r ) { }}
	<tr id="userList_tr_{{r.userId}}" data-obj="{'userId':'{{r.userId}}' , 'userName':'{{r.userName}}' }">
		<td data-field="userId_{{r.userId}}"><input type="checkbox" name="userList_check_{{r.userId}}" id="userList_check_{{r.userId}}" lay-skin="primary" value="{{r.userId}}" /></td>
		<td>{{r.userId}}</td>
		<td data-field="userName_{{r.userName}}">{{r.userName}}</td>
		<td data-field="userPhone_{{r.userPhone}}">{{r.userPhone}}</td>
		<%--<td data-field="town_{{r.town}}">--%>
			<%--{{# if(!isNull(r.town)){ }}--%>
				<%--{{r.town}}--%>
			<%--{{# } else { }}--%>
			<%--{{# }  }}--%>
		<%--</td>--%>
		<%--<td data-field="village_{{r.village}}">--%>
			<%--{{# if(!isNull(r.village)){ }}--%>
				<%--{{r.village}}--%>
			<%--{{# } else { }}--%>
			<%--{{# }  }}--%>
		<%--</td>--%>
		<td style="display:none;" data-field="deptId_{{r.deptId}}">&nbsp;</td>
		<td data-field="deptName_{{r.deptName}}">
		{{# if(!isNull(r.deptName)){ }}
		     {{r.deptName}}
		{{# } else { }}
		{{# }  }}
		</td>
		<td data-field="roleNames_{{r.roleNames}}">
			{{# if(!isNull(r.roleNames)){ }}
		     	{{r.roleNames}}
			{{# } else { }}
			{{# }  }}
		</td>
	</tr>
	{{#  });  }}	
	<%-- 列表无数据 --%>
	{{# if(d.length === 0){ }}
		<tr >
			<td colspan="5"><s:message code="暂无数据"/>!</td>
		</tr>   
	{{# } }}

	</script>
	<script type="text/javascript">
	(function(){
		 obj1 = DataList.create({
		    url : "user/list.web",
			defaultTableId : 'userList',
			isLaytpl : true,
			laytplAppendHtml : 'userListData',
			isTable : false,
			laytplTemplet : 'userListTemplet'
		});
	}());
	</script>
</div>
</body>
</html>