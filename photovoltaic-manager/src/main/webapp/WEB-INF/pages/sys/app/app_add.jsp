<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm" />
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.upload.plugin.js?t=1"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
        (function($){
            $(function(){
                //保存
                $('#icon-save').click(function() {
                    com.ymx.layui.public.core.form.on('submit(memberForm)', function(dataObj){
                        var obj = formSerialize("appForm");
                        doPost("appCtr/operate.web" , null , obj , function(data) {
                            if (data.rec == 'SUC'){
                                windowLocaltionHref( "appCtr/view.htm" , "&leftMenuId=${leftMenuId}" );
                            } else {
                                com.ymx.layui.public.core.layer.msg( data.errMsg);
							}
                        } , function(data){});
                        //防止跳转
                        return false;
                    });
                });
                //返回
                $("#icon-back").click(function(){
                    windowLocaltionHref("appCtr/view.htm" , "&leftMenuId=${leftMenuId}" )
                });

                /* 上传APK */
                $.layuiUpload({
                    elem:'#uploadPic',
                    showTextElem :'picText' ,
                    data:{type:'apk'} ,
                    doneSingleUpload : function(res, index, upload){
	                    $("#uploadPic").removeAttr("disabled");
                        if(res.rec == 'SUC'){
                            $("#apk").text(res.reModel) ;
	                        $("#url").val(res.reModel);
	                        $("#apkurl").val(res.reModel);
                        }
                    }
                });
            })
        })(jQuery)

	</script>
</head>
<body>
<div class="admin-main" id="memberSaveOrUpdate">
	<form action="" class="layui-form" id="appForm" name="appForm">
		<%-- 隐藏属性 --%>
		<input type="hidden" name="id" id="id" value="${info.id}" />
			<input type="hidden" name="url" id="url"  />

		<div class="layui-form-item">
			<div class="layui-form-item">
				<label class="layui-form-label"><s:message code="版本号" /></label>
				<div class="layui-input-block">
					<input type="text" <c:if test="${not empty info.id}">disabled="disabled"</c:if>  name="versionNum" id="versionNum" desc="版本号" lay-verify="isEmpty" maxlength="11" value="${info.versionNum}" autocomplete="off" class="layui-input" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><s:message code="链接地址" /></label>
				<div class="layui-input-block">
					<input type="text" disabled="disabled" id="apkurl" desc="链接地址" lay-verify="isEmpty" value="${info.url}" autocomplete="off" class="layui-input" />
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><s:message code="更新说明" /></label>
				<div class="layui-input-block">
					<input type="text" <c:if test="${not empty info.id}">disabled="disabled"</c:if>  name="content" id="content" desc="更新说明" maxlength="11" lay-verify="isEmpty" value="${info.content}" autocomplete="off" class="layui-input" />
				</div>
			</div>
		</div>
		<c:if test="${empty info.id}">
			<div class="layui-form-item">
				<label class="layui-form-label">apk<s:message code="上传" /></label>
				<div class="layui-input-inline">
					<div class="layui-upload">
						<button type="button"  class="layui-btn" id="uploadPic"><s:message code="上传" />apk</button>
						<div class="layui-upload-list">
							<a id="apk" ></a>
							<p id="picText"></p>
						</div>
					</div>
				</div>
			</div>
		</c:if>

		<%--操作按钮 --%>
		<div class="layui-form-item">
			<div class="layui-input-block">
				<c:if test="${empty info.id}"><a id="icon-save" class="layui-btn" lay-submit lay-filter="memberForm"><s:message code="保存" /></a></c:if>
				<a id="icon-back" class="layui-btn" style="margin: 10px;" ><s:message code="返回" /></a>
			</div>
		</div>
	</form>
</div>
<script type="text/javascript">
    (function(){
        dataList = DataList.create({
            isLaytpl : false,
            isTable : false,
            isPager : false
        });
    }());
</script>
</body>
</html>

