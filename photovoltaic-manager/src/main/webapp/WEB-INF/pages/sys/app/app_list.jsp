<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<title>视频管理列表</title>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		$(function(){
			//查看
			$('#icon_detail').click(function(){
				var length = dataList.checkboxData.length;
				if(length != 1){
					com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
					return;
				}else{
					windowLocaltionHref("appCtr/viewRequest.htm?view=detail&id="+dataList.checkboxData[0].id , "&leftMenuId=${leftMenuId}");
				}
			});

			//新增
			$("#icon_add").click(function () {
				var url = $('#icon_add').attr("url");
				windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
			});
 // 
			//删除
			$("#icon_delete").click(function()
			{
			    /* var id = getArrayAttrVal({data:dataList.checkboxData});
				var length = dataList.checkboxData.length;
                if(length == 0)
				{
					com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
					return;
				}
				else
				{
				     doPost("appCtr/delete.web" , 'json' ,  {"id":id } , function(data)
					 {
    					if(data.rec == 'SUC' )
						{
                                             com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>!');
							setTimeout(function(){
								windowLocaltionHref("appCtr/view.htm" , "&leftMenuId=${leftMenuId}");
							},2000);
						}
					 					 
					 },function(){
						alert('<s:message code="未知错误"/>，<s:message code="请及时排查"/>！");
						})

				} */
				 operationDb( 'appCtr/delete.web' , dataList ,  { "isDelete" : 2 , "leftMenuId": '${leftMenuId}'} );

			});
		});
	</script>
</head>
<body>
<div class="admin-main" id="member_detail_list">
	<%-- 操作功能 --%>
	<div>
		<jsp:include page="/functionBuilder.htm"></jsp:include>
	</div>
	<%-- 数据列表展示 --%>
	<table class="layui-table" id="appDataList" lay-filter="appDataList" lay-even>
		<thead>
		<tr>
			<th><input type="checkbox" name="appDataListChooseAll" id="appDataListChooseAll" lay-skin="primary" /></th>
			<th><s:message code="版本号"/></th>
			<th><s:message code="链接"/></th>
			<th><s:message code="说明"/></th>
			<th><s:message code="创建时间" /></th>
		</tr>
		</thead>
		<tbody id="app"></tbody>
	</table>
	<script type="text/html" id="appDataListTemplet">
		{{# layui.each( d , function( index , r ) { }}
		<tr id="appDataList_tr_{{r.id}}">
			<td data-field="id_{{r.id}}"><input type="checkbox" name="appDataList_check_{{r.id}}" id="appDataList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
			<td data-field="versionNum">{{r.versionNum}}</td>
			<td data-field="url">{{r.url}}</td>
			<td data-field="content">{{r.content}}</td>
			<td data-field="createTimeCh">{{r.createTimeCh}}</td>
		</tr>
		{{#  });  }}
		<%-- 列表无数据 --%>
		{{# if(d.length === 0){ }}
		<tr >
			<td colspan="5"><s:message code="暂无数据"/>!</td>
		</tr>
		{{# } }}
	</script>
	<%-- 分页显示 --%>
	<div align="center" id="page"></div>
	<script>
		//初始化
		(function() {
			dataList = DataList.create({
				url : "appCtr/appList.web",
				defaultTableId : 'appDataList',
				isPager : true,
				isLaytpl : true,
				laytplAppendHtml : 'app',
				formName : 'appForm',
				isTable : false,
				laytplTemplet : 'appDataListTemplet'
			});
		}())
	</script>
</div>
</body>
</html>