<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
<title>角色信息列表</title>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
<script type="text/javascript">
   $(function(){
	    //增加
		$('#icon-add').click(function(){
			doPost("role/tosave.htm" , 'text' ,  {"leftMenuId": '${leftMenuId}' , "roleId":"" } , function(data){
				roleLayer = $.layerOpenCustom({
			        title: '<s:message code="新增菜单功能"/>' , //不显示标题栏
			        area: '800px;' ,
			        id: 'roleLayerAdd' ,//设定一个id，防止重复弹出
			        content: data
				},function(data){
					
				});
			});
		});
		//编辑
		$('#icon-edit').click(function(){
			var length = dataList.checkboxData.length;
			//单条编辑
			if( length == 1 ) {
				doPost("role/tosave.htm" , 'text' ,  dataList.checkboxData[0] , function(data){
					roleLayer = $.layerOpenCustom({
				        title: '<s:message code="编辑参数设置"/>' , //不显示标题栏
				        area: '800px;' ,
				        id: 'roleLayerEdit' ,//设定一个id，防止重复弹出
				        content: data
					});
				},function(data){
					
				});
			} else {
				com.ymx.layui.public.core.layer.msg('<s:message code="请选择单条记录进行编辑"/>!');
			}
		});
		
		//删除
		$('#icon-remove').click(function(){
			var length = dataList.checkboxData.length;
			var datas = [];
			$.each( dataList.checkboxData ,function(i,n) {
				datas.push(n.roleId);
			});
			if(length > 0){
				layer.confirm('<s:message code="确认要删除此项数据吗"/>?', {icon: 3, title:'<s:message code="提示"/>'}, function(index){
					layer.close(index);
					doPost("role/batchDelete.htm" , 'text' ,  { "ids" : datas } , function(data){
						com.ymx.layui.public.core.layer.msg('<s:message code="删除成功"/>!');
						//刷新列表
		                com.ymx.layui.public.core.table.reload('roleTable', {} );
					},function(data){
						
					});
				});
			} else {
				com.ymx.layui.public.core.layer.msg('<s:message code="请选择需要删除的记录"/>!');
			}
		});
		
		//分配权限
		 $('#icon-role').click(function(){
	    	   var length = dataList.checkboxData.length;
	    	   if(length == 1){
	    		   var row = dataList.checkboxData[0];
	    		   window.location.href = "role/toSetterRole.htm?leftMenuId=${leftMenuId}&roleId="+row.roleId+'&roleName='+encodeURI(encodeURI(row.roleName));
	    	   } else {
	    		   com.ymx.layui.public.core.layer.msg('<s:message code="请选择单条角色进行分配权限"/>!');
	    	   }
	     });

    });
   
    //保存
	function roleSubmit(){
		var obj  = formSerialize("roleForm");
         doPost("role/save.htm" , 'text' ,  obj , function(flag){
           if(flag == 'yes'){
              com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
              //关闭弹出层
              com.ymx.layui.public.core.layer.closeAll();
              //刷新列表
              com.ymx.layui.public.core.table.reload('roleTable', {} );
              //清空
              dataList.checkboxData = [];
           } else {
           	   com.ymx.layui.public.core.layer.msg('<s:message code="名称已经存在"/>，<s:message code="请检查"/>' );
           }
        },function(data){
			
		});
	}
</script>
</head>
<body>
	<div class="admin-main" id="roleList">
		<%-- 操作功能 --%>
		<div>
	        <jsp:include page="/functionBuilder.htm"></jsp:include>
		</div>
		<%-- 数据列表展示 --%>
		<table id="roleTable" lay-filter="roleTable"></table>
		<%-- 分页显示 
		<div align="center" id="page"></div>
		--%>
	   <script>
	   (function(){
			dataList = DataList.create({
				url:"role/list.web" ,
				defaultTableId : 'roleTable',
				isPager: false ,
				id : 'roleTable' ,
				cols: [[  {checkbox: true},
			            {field:'roleName', title:'<s:message code="角色名称"/>', width: 800},
						{field:'roleRemark', title:'<s:message code="角色描述"/>', width: 800}
				]]
			});
		}())
	    //初始化
		/*com.ymx.layui.public.core.layuiCustom.init();
		com.ymx.layui.public.core.layuiCustom.set({
			url:"role/list.web" ,
			defaultTableId : 'roleTable',
			isPager: false ,
			cols: [[  {checkbox: true},
		            {field:'roleName', title:'角色名称', width: 600},
					{field:'roleRemark', title:'角色描述', width: 640}
			]]
		});*/  
	   </script>
	</div>
</body>
</html>