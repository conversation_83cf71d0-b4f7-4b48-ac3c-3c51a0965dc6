<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<div id="setterSave" style="margin: 10px;">
      <form id="saveform" method="post" class="layui-form">
	    <div class="layui-form-item">
	    <label class="layui-form-label"><s:message code="设置名称" /></label>
	    <div class="layui-input-block">
	      <input type="text" name="setterName"  id="setterName" value="${bean.setterName}" lay-verify="required" placeholder="<s:message code="设置名称"/>"  class="layui-input">
	    </div>
	  </div>
	  <div class="layui-form-item layui-form-text">
	    <label class="layui-form-label"><s:message code="设置值"/></label>
	    <div class="layui-input-block">
	      <textarea  name="setterValue"  id="setterValue" placeholder="<s:message code="设置值"/>" class="layui-textarea">${bean.setterValue}</textarea>
	    </div>
	  </div>
	  <div class="layui-form-item layui-form-text">
	    <label class="layui-form-label"><s:message code="设置说明"/></label>
	    <div class="layui-input-block">
	      <textarea  name="setterRemark" id="setterRemark" placeholder="<s:message code="设置说明"/>" class="layui-textarea">${bean.setterRemark}</textarea>
	    </div>
	  </div>
	  <input type="hidden" name="id" id="setterId" value="${bean.id}"/>
	  <input type="hidden" name="action" value="${action}">
	  </form>
	  <div class="layui-form-item">
		  <div class="layui-input-block">
			  <button class="layui-btn" onclick="setterSubmit();"><s:message code="保存"/></button>
		  </div>
	  </div>
</div>