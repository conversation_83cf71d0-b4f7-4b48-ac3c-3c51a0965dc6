<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script type="text/javascript">
	/**
	 * 重写detail方法 案例
	com.ymx.layui.public.core.layuiCustom.detail = function(data){
		alert('我要查看了' + JSON.stringify(data));
	}
	*/
	$(function(){
	    //新增
		$('#icon-add').click(function(){
			var url = $('#icon-add').attr("url");
			doPost(url , 'text' ,  {} , function(data){
				 setterlayuer = $.layerOpenCustom({
			        title: '<s:message code="新增参数设置" />' , //不显示标题栏
			        area: '800px;' ,
			        id: 'setterSave' ,//设定一个id，防止重复弹出
			        content: data
				});
			},function(data){
				
			});
		});
		
		//删除
		$('#icon-remove').click(function(){
			var length = dataList.checkboxData.length;
			var ids = backStringData(dataList.checkboxData );
			var url = $('#icon-remove').attr("url") ;
			if(length > 0){
				layer.confirm('<s:message code="确认要删除此项数据吗" />?', {icon: 3, title:'<s:message code="提示"/>'}, function(index){
					doPost(url , 'text' ,  { "ids" : ids} , function(data){
						com.ymx.layui.public.core.layer.msg('<s:message code="删除成功" />!');
						//刷新列表
						dataList.checkboxData = [];
		                com.ymx.layui.public.core.table.reload('setterListData', {} );
					},function(data){
						
					});
				});
			} else {
				com.ymx.layui.public.core.layer.msg('<s:message code="请选择需要删除的记录" />!');
			}
		});
		
		//编辑
		$('#icon-edit').click(function(){
			var url = $('#icon-edit').attr("url");
			var length = dataList.checkboxData.length;
			//单条编辑
			if(length == 1){
				doPost(url , 'text' ,  dataList.checkboxData[0] , function(data){
					 setterlayuer = $.layerOpenCustom({
				        title: '<s:message code="编辑参数设置" />' , //不显示标题栏
				        area: '600px;' ,
				        id: 'setterUpdate' ,//设定一个id，防止重复弹出
				        content: data
					},function(data){
						
					});
				});
			} else {
				com.ymx.layui.public.core.layer.msg('<s:message code="请选择单条记录进行编辑" />!');
			}
		});
    });
    
    //新增或编辑
 	function setterSubmit(){
        var id = document.getElementById("setterId").value;
	    var name = document.getElementById("setterName").value;
	    doPost("setter/checkNameExist.htm" , 'text' ,  {"id": id , "name" : name } , function(data){
	          if(data == 'false'){
	           	  var obj  = formSerialize("saveform");
	              doPost("setter/save.htm" , 'text' ,  obj , function(flag){
	                if(flag == 'true'){
	                   com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
	                   //关闭弹出层
	                   com.ymx.layui.public.core.layer.close(setterlayuer._layer_index);
	                   //<s:message code="查询条件" /> var objform  = formSerialize("formname");
	                   //刷新列表
	                   dataList.checkboxData = [];
	                   com.ymx.layui.public.core.table.reload('setterListData', {} );
	                } else {
	                	com.ymx.layui.public.core.layer.msg('<s:message code="设置项名称已经存在" />，<s:message code="请检查" />' );
	                }
	             },function(data){
	 				
	 			});
	          } else {
	              com.ymx.layui.public.core.layer.msg('<s:message code="设置项名称已经存在" />，<s:message code="请检查" />');
	          }
	    });
    }
    

	</script>
</head>
<body>
   <div  class="admin-main">
	<%-- <s:message code="查询条件" />区域 
	 <blockquote class="layui-elem-quote">
    <div style="margin: 10px">
		<form class="layui-form" action="" name="formname" id="formname">
		  <fieldset class="admin-fieldset">
		      <legend><s:message code="查询条件" /></legend>
			  <div class="layui-form-item">
				    <div class="layui-inline">
				      <label class="layui-form-label">设置<s:message code="名称" /></label>
				      <div class="layui-input-inline">
				        <input type="text" name="number" placeholder="请输入设置名称" class="layui-input">
				      </div>
				    </div>
				    <div class="layui-inline">
				      <div class="layui-input-block">
					       <button class="layui-btn" lay-submit lay-filter="sreach">查询</button>
					       <button type="reset" class="layui-btn"><s:message code="重置" /></button>
				      </div>
				    </div>
			   </div>
		  </fieldset>
		</form>
	</div> --%>
	<%--</blockquote>
	--%>
	<%-- 操作功能 --%>
	<div>
        <jsp:include page="/functionBuilder.htm"></jsp:include>
	</div>
	<%-- 数据列表展示 --%>
	<table id="setterListData" lay-filter="setterListData"></table>
	<%-- 分页显示 --%>
	<div align="center" id="page"></div>
	<%-- 列表操作模板  
	<script type="text/html" id="operationFun">
  	    <a class="layui-btn layui-btn-mini layui-btn-normal" lay-event="detail">查看</a>
	</script>
	--%>
   <script>
	(function(){
		dataList = DataList.create({
			url:"setter/list.web" ,
			defaultTableId : 'setterListData',
			id : 'setterListData' ,
			cols: [[  {checkbox: true},
		            {field:'setterName', title:'<s:message code="设置名称"/>', width: 350},
					{field:'setterValue', title:'<s:message code="设置值"/>', width: 360},
					{field:'setterRemark', title:'<s:message code="设置说明"/>',width: 350} //,
					//{field:'title', title: '操作', width: 200, templet: '#operationFun'} //这里的templet值是模板元素的选择器
			]]
		});
	}())
   </script>
   </div>
</body>
</html>