<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.Read"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>  
  
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	
	<link rel="stylesheet" href="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/themes/base/jquery.ui.all.css">
	<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.core.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.widget.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.position.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.menu.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/easyui/autocomplete/jquery.ui.autocomplete.js"></script>
	
	
	<title>用户新增/修改</title>
</head>
<script type="text/javascript">
(function($){
	$(function(){
		//保存
		$('#icon-save').click(function() {
			com.ymx.layui.public.core.form.on('submit(userForm)', function(data) {
				var obj = formSerialize("userForm");
				var operator = obj.operator;
				if(operator == 'add'){
					doPost("user/checkUserNameExist.htm" , 'text' , obj , function(data){
						if(data == 'true'){ 
							com.ymx.layui.public.core.layer.msg('用户名已经存在，请更换!');
						} else {
							saveForm(obj);
						}
					},function(data){});
				} else {
					saveForm(obj);
				}
			    //防止跳转
			    return false;
		    });
		});
		
		function saveForm(obj){
			doPost("user/save.htm" , 'text' , obj , function(data) {
				window.location.href = BASE_PATH + 'user/search.htm?leftMenuId=${leftMenuId}';
			},function(data){});
		}
		
		//返回
		$('#icon-back').click(function(){
			  window.location.href = BASE_PATH + 'user/search.htm?leftMenuId=${leftMenuId}';
		});
		
	   //部门自动补全
	   $("#deptName").autocomplete({
			   source:function(request,response){  
				      var deptName = $("#deptName").val();
			          doPost("dept/autoDept.web" , null ,{ "deptName": deptName } ,function(datas){
			        	  if(datas.rec == 'SUC'){
			           		  var data  = datas.reModel;
			          	      response($.map( data , function(item){  
			                      var name = item.deptName;  
			                      var code = item.deptId;  
			                      return {  
			                          label:item.deptName,
			                          value:item.deptName,
			                          code:item.deptId
			                      }  
			                  })); 
		                 }else{
		                    alert(datas.msg);
		                 }
			          },function(data){});
			   },  
		       delay: 500,
		       select : function(event, ui) {  
		           $("#deptId").val(ui.item.code);  
		       }  
	   });  
	   //职务自动补全
	   $("#dutyName").autocomplete({
	       source:function(request,response){  
	    	  var dutyName = $("#dutyName").val();
	          doPost("duty/autoDuty.web" , null , { "dutyName": dutyName },function(datas){
	          		if(datas.rec == 'SUC'){
		           		  var data  = datas.reModel;
		          	      response($.map(data,function(item){  
		                      var name = item.dutyName;  
		                      var code = item.dutyId;  
		                      return {  
		                          label:item.dutyName,
		                          value:item.dutyName,
		                          code:item.dutyId
		                      }  
		                  })); 
	                 }else{
	                    alert(datas.msg);
	                 }
	          },function(data){});
	       },  
	       delay: 500,
	       select : function(event, ui) {  
	           $("#dutyId").val(ui.item.code);  
	       }  
	   });  
	   
	   //角色选择
	   $('#roleNames').click(function(){
			 userRoleIcoLayer = $.layerOpenCustom({
			        title: '<s:message code="请选择角色"/>' , //不显示标题栏
			        area: ['800px', '400px'] ,
			        id: 'userRoleIcoLayer' ,//设定一个id，防止重复弹出
			        content: $("#userRoleDIV")
			});
		});
	   
	});
})(jQuery);
</script>
<body>
<%-- 角色选择框 --%>
<div style="display: none;" class="admin-main" id="userRoleDIV" >
    <form id="userRoleForm" name="userRoleForm" method="post" class="layui-form layui-form-pane">
        <table width="100%" height="100%">
		   <tbody>
    	       <c:forEach var="obj" items="${userRoles}" varStatus="status">
			      <c:if test="${status.count eq 1 || (status.count - 1) % 5 eq 0}">      
			         <tr>      
			      </c:if>      
			       <td>      
			         <input type="checkbox" name="roleId" lay-filter="roleIdFilter" title="${obj.roleName}" value="${obj.roleId}" lay-skin="primary" <c:if test="${obj.operationStatus == 1 }">checked="checked"</c:if> />
			       </td>        
			      <c:if test="${status.count % 5 eq 0 || status.count eq 5}">      
			         </tr>      
			      </c:if>      
    	       </c:forEach>
    		</tbody>
		</table>
    </form>
</div>

<div class="admin-main" id="userSave" >
    <form id="userForm" name="userForm" method="post" class="layui-form layui-form-pane">
    	<input type="hidden" name="deptId" id="deptId" value="${user.deptId}">
    	<input type="hidden" name="dutyId" id="dutyId" value="${user.dutyId}">
    	<input type="hidden" name="operator" id="operator" value="${operator}" />
    	<div class="layui-form-item">
		    <label class="layui-form-label"><s:message code="登录账号" /></label>
		    <div class="layui-input-block">
		      <input type="text" name="userId" id="userId" value="${user.userId}" <c:if test="${not empty user.userId }">readonly="readonly"</c:if>  <c:if test="${empty user.userId }">lay-verify="isEmpty"</c:if> autocomplete="off" placeholder="<s:message code="请输入登录账号" />" class="layui-input" />
		    </div>
		</div>
    	<div class="layui-form-item">
		    <label class="layui-form-label"><s:message code="用户名称" /></label>
		    <div class="layui-input-block">
		      <input type="text" name="userName" id="userName" value="${user.userName}" lay-verify="isEmpty"  autocomplete="off" placeholder="<s:message code="请输入用户名称" />" class="layui-input" />
		    </div>
		</div>
		
		<div class="layui-form-item">
		    <label class="layui-form-label"><s:message code="用户手机" /></label>
		    <div class="layui-input-block">
		      <input type="text" name="userPhone" id="userPhone" value="${user.userPhone}" lay-verify="isEmpty" autocomplete="off" placeholder="<s:message code="请输入用户手机" />" class="layui-input" />
		    </div>
		</div>
		
		<div class="layui-form-item">
		   <label class="layui-form-label"><s:message code="在职状态" /></label>
		    <div class="layui-input-block">
		    	<select  id="isOnJob" name="isOnJob" >
					<option value="0" <c:if test="${user.isOnJob=='0'}">selected</c:if>><s:message code="在职" /></option>
					<option value="1" <c:if test="${user.isOnJob=='1'}">selected</c:if>><s:message code="已离职" /></option>
				</select>
		    </div>
		</div>
		
		<div class="layui-form-item">
		    <label class="layui-form-label"><s:message code="所属部门" /></label>
		    <div class="layui-input-block">
		    	<input type="text" placeholder="<s:message code="输入关键字选择所在部门" />" name="deptName" id="deptName" value="${user.deptName}" class="layui-input" />
		    </div>
		</div>
		
		<div class="layui-form-item">
		   <label class="layui-form-label"><s:message code="职务" /></label>
		    <div class="layui-input-block">
		    	<input type="text" placeholder="<s:message code="输入关键字选择职务" />" name="dutyName" id="dutyName" value="${user.dutyName}" class="layui-input" />
		    </div>
		</div>
		
		<div class="layui-form-item">
		    <label class="layui-form-label"><s:message code="角色" /></label>
		    <div class="layui-input-block"><input type="hidden"  name="roleIds" id="roleIds" />
		    	<input id="roleNames" name="roleNames" placeholder="<s:message code="请选择角色" />" value="${user.roleNames}" class="layui-input" readonly="readonly" />
		    </div>
		</div>
		
		<div class="layui-form-item">
		    <div class="layui-input-block">
		       <a id="icon-save" class="layui-btn"  lay-submit lay-filter="userForm"><s:message code="保存" /></a>
		       <a id="icon-back" class="layui-btn" ><s:message code="返回" /></a>
		    </div>
		 </div>
    </form>
</div>
<script type="text/javascript">
(function(){
	 obj1 = DataList.create({
		isPager: false ,
		isLaytpl : false ,
		isTable : false ,
		listener:function()	{										//form表单监听
			/*$.selectOption({
				selectElem : 'roleIds' ,
				url :'role/list.web',
				selectId : 'roleId',
				selectName:'roleName' ,
				isFormRenderSelect :true 
			});*/
		   com.ymx.layui.public.core.form.on('checkbox(roleIdFilter)', function(data){
			   var roleNames = "";
			   var roleIds = "";
			   $("input[name='roleId']").each(function(i){
				   if($(this).is(":checked")){
					   roleNames +=  $(this).attr("title") + ",";
					   roleIds +=  $(this).val() + ",";
				   }
			   });
			   if(null != roleNames && roleNames.trim() != '' && roleNames.length > 0 ){
				   roleNames = roleNames.substring(0, roleNames.length - 1 );
			   }
			   if(null != roleIds && roleIds.trim() != '' && roleIds.length > 0 ){
				   roleIds = roleIds.substring(0, roleIds.length - 1 );
			   }
			   $("#roleNames").val(roleNames);
			   $("#roleIds").val(roleIds);
		   }); 
	    }
	});
}());
</script>
</body>
</html>