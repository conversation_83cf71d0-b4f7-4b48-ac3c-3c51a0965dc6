<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@page import="com.ymx.common.utils.PageUtil" %>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.upload.plugin.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		(function($) {
			$(function() {
				// 保存
				$('#icon-save').click(function() {
                    formSubmit({
						"formId":"memberForm" ,
						"url":"memberCtr/saveOrUpdate.web" ,
						"backListUrl":"memberCtr/list.htm" ,
						"leftMenuId":'${leftMenuId}'
                    });
				});
				// 返回
				$("#icon-back").click(function() {
					windowLocaltionHref("memberCtr/list.htm", "&leftMenuId=${leftMenuId}")
				});

				// 上传图片
				$.layuiUpload({
					elem: '#uploadPic',
					showElem: 'picShow',
					showTextElem: 'picText',
					data: {type: 'head'},
					doneSingleUpload: function(res, index, upload) {
						if(res.rec === 'SUC') {
							$("#picture").val(res.reModel);
						}
					}
				});
			})
		})(jQuery)
	</script>
</head>
<body>
<div class="admin-main" id="memberSaveOrUpdate">
	<form action="" class="layui-form" id="memberForm" name="memberForm">
		<%-- 隐藏属性 --%>
		<input type="hidden" name="id" id="id" value="${info.id}"/>
		<input type="hidden" name="picture" id="picture"/>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="登录账号" /></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="phone" id="phone" desc="登录账号" lay-verify="isEmpty" maxlength="30"
					       value="${info.phone}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="账号昵称" /></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="nickName" id="nickName" desc="账号昵称" lay-verify="isEmpty" value="${info.nickName}"
					       autocomplete="off" class="layui-input"/>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="手机号" /></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="contactNumber" id="contactNumber" desc="手机号" maxlength="11" lay-verify="isEmpty"
					       value="${info.contactNumber}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="名" /></label>
				<div class="layui-input-inline">
					<input type="text"
						   <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="name" id="name" desc="真实名" lay-verify="isEmpty" value="${info.name}"
						   autocomplete="off" class="layui-input"/>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="姓" /></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="xing" id="xing" desc="真实姓" lay-verify="isEmpty" value="${info.xing}"
					       autocomplete="off" class="layui-input"/>
				</div>
			</div>

			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="邮箱" /></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="email" id="email" desc="邮箱" lay-verify="isEmpty|email" value="${info.email}"
					       autocomplete="off" class="layui-input"/>
				</div>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="人员类型" /></label>
				<div class="layui-input-inline">
					<select lay-filter="type" id="type" name="type" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>>
					</select>
				</div>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="性别" /></label>
				<div class="layui-input-inline">
					<select lay-filter="sex" id="sex" name="sex" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>>
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="创建人" /></label>
				<div class="layui-input-inline">
					<input type="text" disabled="disabled" name="createUserName" id="createUserName" desc="<s:message code="创建人" />" value="${info.createUserName}" autocomplete="off"
					       class="layui-input"/>
				</div>
			</div>
			<c:if test="${operator == 'detail'}">
				<div class="layui-inline">
					<label class="layui-form-label"><s:message code="创建时间" /></label>
					<div class="layui-input-inline">
						<input type="text"
						       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="createTimeCh" id="createTimeCh" desc="<s:message code="创建时间" />" lay-verify="isEmpty"
						       value="${info.createTimeCh}" autocomplete="off" class="layui-input"/>
					</div>
				</div>
			</c:if>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="人员图片" /></label>
			<div class="layui-input-inline">
				<div class="layui-upload">
					<c:if test="${operator != 'detail'}">
						<button type="button" class="layui-btn" id="uploadPic"><s:message code="上传图片" /></button>
					</c:if>
					<div class="layui-upload-list">
						<img class="layui-upload-img" id="picShow" width="100" height="100"
						     <c:if test="${not empty info.picture}">src="${info.picture}"</c:if> />
						<p id="picText"></p>
					</div>
				</div>
			</div>
		</div>
		<%--操作按钮 --%>
		<div class="layui-form-item">
			<div class="layui-input-block">
				<c:if test="${operator != 'detail'}"><a id="icon-save" class="layui-btn" lay-submit lay-filter="memberForm"><s:message code="保存" /></a></c:if>
				<a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a>
			</div>
		</div>
	</form>
</div>
<script type="text/javascript">
	(function() {
		DataList.create({ isLaytpl: false, isTable: false, isPager: false,
            listener:function () {
                $.selectOption({
                    url: 'dic/queryDicList.web',
                    queData: {"dicId": "sex"},
                    selectElem: 'sex',
                    selectId: 'dicValueId',
                    selectName: 'dicValueLabel',
                    isFormRenderSelect: true,
                    selected: '${info.sex}'
                });
                $.selectOption({
                    url: 'dic/queryDicList.web',
                    queData: {"dicId": "memberType"},
                    selectElem: 'type',
                    selectId: 'dicValueId',
                    selectName: 'dicValueLabel',
                    isFormRenderSelect: true,
                    selected: '${info.type}'
                });
            }
		});
	}());
</script>
</body>
</html>


