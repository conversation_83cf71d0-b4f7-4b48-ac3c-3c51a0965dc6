<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script type="text/javascript">
	$(function(){
		$("#member-icon-add").click(function () {
            //新增
			var url = $('#member-icon-add').attr("url");
            windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
        });

        $("#member-icon-edit").click(function () {
            //编辑
            var url = $('#member-icon-edit').attr("url");
            var length = dataList.checkboxData.length;
            if(length != 1){
                com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
                return;
            }
            var type = dataList.checkboxData[0].type;
            if( 1 == type){
                com.ymx.layui.public.core.layer.msg('<s:message code="此用户不允许修改"/>!');
                return;
			}
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });

        $("#member-icon-detail").click(function () {
            //查看
            var url = $('#member-icon-detail').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });

        $("#member-icon-lock").click(function () {
            //锁户
            var url = $('#member-icon-lock').attr("url");
            operationDb( url , dataList ,  {"status" : 3 , "leftMenuId": '${leftMenuId}' } );
        });
        $("#member-icon-unlock").click(function () {
            //解锁
            var url = $('#member-icon-unlock').attr("url");
            operationDb( url , dataList ,  {"status" : 1 , "leftMenuId": '${leftMenuId}'} );
        });
        $("#member-icon-repasswd").click(function () {
            //<s:message code="重置" />密码
            var url = $('#member-icon-repasswd').attr("url");
            operationDb( url , dataList ,  {"leftMenuId": '${leftMenuId}'} );
        });
        $("#member-icon-login").click(function () {
            //设置后台登录
            var url = $('#member-icon-login').attr("url");
            var ids = getStrAttrVal({data :dataList.checkboxData , "attrName":"id"})  ;
           $.ajax({
              url:url+'?id='+ids,
              data:'',
              type:'POST',
              dataType:'json',
              async:false,
              success:function(data){
                  alert(data.message);
              }
              
          }); 
        });
    });
	</script>
</head>
<body>
	<div class="admin-main" id="member_detail_list">
		<%-- <s:message code="查询条件" />区域 --%>
		<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="memberList" id="memberList">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label"><s:message code="用户账号"/></label>
							<div class="layui-input-inline">
								<input type="text" name="phone" placeholder="<s:message code="请输入账号"/>" class="layui-input" style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label"><s:message code="真实姓名"/></label>
							<div class="layui-input-inline">
								<input type="text" name="name" placeholder="<s:message code="请输入用户姓名"/>"
									class="layui-input" style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		</blockquote>
		<%-- 操作功能 --%>
		<div>
			<jsp:include page="/functionBuilder.htm"></jsp:include>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="memberListData"
			lay-filter="memberListData" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="memberListDataChooseAll" id="memberListDataChooseAll" lay-skin="primary" /></th>
					<th><s:message code="登录账号"/></th>
					<th><s:message code="用户昵称"/></th>
					<th><s:message code="名"/></th>
					<th><s:message code="姓"/></th>
					<th><s:message code="用户类型"/></th>
					<th><s:message code="用户性别"/></th>
					<th><s:message code="用户状态"/></th>
					<th><s:message code="注册时间"/></th>
					<th><s:message code="是否设置登录后台"/></th>
					<th><s:message code="电站管理"/></th>
				</tr>
			</thead>
			<tbody id="member"></tbody>
		</table>
		<script type="text/html" id="memberListDataTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="memberListData_tr_{{r.mId}}">
				<td data-field="mId_{{r.mId}}"><input type="checkbox" name="memberListData_check_{{r.mId}}" id="memberListData_check_{{r.mId}}" lay-skin="primary" value="{{r.mId}}" /></td>
				<td data-field="phone_{{r.phone}}">{{r.phone}}</td>
				<td style="display: none;" data-field="id_{{r.id}}">{{r.id}}</td>
				<td style="display: none;" data-field="type_{{r.type}}">{{r.type}}</td>
				<td data-field="nickName_{{r.nickName}}">{{r.nickName}}</td>
				<td data-field="name_{{r.name}}">
					{{# if(!isNull(r.name)){ }}
					{{r.name}}
					{{# } else { }}
					{{# }  }}
				</td>
				<td data-field="xing_{{r.xing}}">
					{{# if(!isNull(r.xing)){ }}
		     			{{r.xing}}
					{{# } else { }}
					{{# }  }}
				</td>
				<td data-field="membertype_{{r.membertype}}">{{r.membertype}}</td>
				<td data-field="sex_{{r.sexen}}">{{r.sexen}}</td>
				<td data-field="status_{{r.status}}">
				{{r.memberstatus}}	
				</td>
				<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
                <td data-field="typeCh_{{r.islogin}}">
					{{r.memberlogin}}	
				</td>
				<td data-field="t_{{}}"><a href="powerstation/list.htm?memberId={{r.mId}}&type={{r.type}}"><s:message code="查看电站"/></a></td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="10"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "memberCtr/queryMemberList.web",
					defaultTableId : 'memberListData',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'member',
					formName : 'memberList',
					isTable : false,
					laytplTemplet : 'memberListDataTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>