<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@page import="com.ymx.common.utils.PageUtil" %>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/select.link.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.upload.plugin.js"></script>
<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>css/zeroModal.css"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/zeroModal.min.js"></script>
<style type="text/css"> 
#drag{position:absolute;top:100px;left:100px;width:500px;height:360px;background:#e9e9e9;border:1px solid #444;border-radius:5px;box-shadow:0 1px 3px 2px #666;display: none;}
#drag .title{position:relative;height:27px;margin:5px;}
#drag .title h2{font-size:14px;height:27px;line-height:24px;border-bottom:1px solid #A1B4B0;}
#drag .title div{position:absolute;height:19px;top:2px;right:0;}
#drag .title a,a.open{float:left;width:28px;height:19px;display:block;margin-left:5px;}
a.open{position:absolute;top:10px;left:50%;margin-left:-10px;background-position:0 0;}
a.open:hover{background-position:0 -29px;}

#drag .title a.close{background-position:-89px 0;}
#drag .title a.close:hover{background-position:-89px -29px;}
#drag .content{overflow:auto;margin:0 5px;}
#drag .resizeBR{position:absolute;width:14px;height:14px;right:0;bottom:0;overflow:hidden;cursor:nw-resize;}
#drag .resizeL,#drag .resizeT,#drag .resizeR,#drag .resizeB,#drag .resizeLT,#drag .resizeTR,#drag .resizeLB{position:absolute;background:#000;overflow:hidden;opacity:0;filter:alpha(opacity=0);}
#drag .resizeL,#drag .resizeR{top:0;width:5px;height:100%;cursor:w-resize;}
#drag .resizeR{right:0;}
#drag .resizeT,#drag .resizeB{width:100%;height:5px;cursor:n-resize;}
#drag .resizeT{top:0;}
#drag .resizeB{bottom:0;}
#drag .resizeLT,#drag .resizeTR,#drag .resizeLB{width:8px;height:8px;background:#FF0;}
#drag .resizeLT{top:0;left:0;cursor:nw-resize;}
#drag .resizeTR{top:0;right:0;cursor:ne-resize;}
#drag .resizeLB{left:0;bottom:0;cursor:ne-resize;}
</style>
<script type="text/javascript">
	(function($) {
		$(function() {
			// 保存
			$('#icon-save').click(function() {
				formSubmit({
					"formId":"powerStationForm" ,
					"url":"powerstation/saveOrUpdate.web" ,
					"backListUrl":"powerstation/list.htm" ,
					"leftMenuId":'${leftMenuId}'
				});
			});
			// 返回
			$("#icon-back").click(function() {
				windowLocaltionHref("powerstation/list.htm", "&leftMenuId=${leftMenuId}")
			});
		})
	})(jQuery)
	
	
</script>

<script type="text/javascript">
   // 获取id, class, tagName
var get = {
byId: function(id) {
return typeof id === "string" ? document.getElementById(id) : id;
},
byClass: function(sClass, oParent) {
var aClass = [];
var reClass = new RegExp("(^| )" + sClass + "( |$)");
var aElem = this.byTagName("*", oParent);
for (var i = 0; i < aElem.length; i++) reClass.test(aElem[i].className) && aClass.push(aElem[i]);
return aClass;
},
byTagName: function(elem, obj) {
return (obj || document).getElementsByTagName(elem)
}
}
//窗口拖拽


function drag(oDrag,oTitle){
var posX=posY=0;
var oMin = get.byClass("min",oDrag)[0];
var oMax = get.byClass("max",oDrag)[0];
var oRevert = get.byClass("revert",oDrag)[0];
var oClose = get.byClass("close",oDrag)[0];
var RevertWidth=500;
var RevertHeight=360;
oTitle.onmousedown=function(event){
oTitle.style.cursor = "move";
var event = event || window.event;
var disX=event.clientX-oDrag.offsetLeft;
var disY=event.clientY-oDrag.offsetTop;
//鼠标移动，窗口随之移动     onmousemove在有物体移动是才执行alert事件；
document.onmousemove=function(event){
var event = event || window.event;
maxW=document.documentElement.clientWidth-oDrag.offsetWidth;
maxH=document.documentElement.clientHeight-oDrag.offsetHeight;
posX=event.clientX-disX;
posY=event.clientY-disY;
if(posX<0){
posX=0;
}else if(posX>maxW){
posX=maxW;
}
if(posY<0){
posY=0;
}else if(posY>maxH){
posY=maxH;
}
oDrag.style.left=posX+'px';
oDrag.style.top=posY+'px';
}
//鼠标松开，窗口将不再移动
document.onmouseup=function(){
document.onmousemove=null;
document.onmouseup=null;
}
}

            //点击关闭按钮
			oClose.onclick=function(){
			oDrag.style.display='none';
			this.onclick=null;
			}
}

			function dakai()
			{
			     var guojia = $("#countriesId").find("option:selected").text();
				 var sheng = $("#province").find("option:selected").text();
				 var shi = $("#cityId").find("option:selected").text();
				 var jd = $("#streetName").val();
				 if(document.getElementById("countriesId").selectedIndex!=0 && document.getElementById("province").selectedIndex!=0 && document.getElementById("cityId").selectedIndex!=0)
				 {
				      document.getElementById('aa').value=guojia+sheng+shi+jd;
				 }
				
				 var oDrag = document.getElementById("drag");
				 document.getElementById("drag").style.display='block';
				 var oTitle = get.byClass("title", oDrag)[0];
				 drag(oDrag, oTitle);
			}
            function loadMapScenario() {
                var map = new Microsoft.Maps.Map(document.getElementById('myMap'), {
                    /* No need to set credentials if already passed in URL */
                    zoom: 8 });
               
                
            }
			function find()
			{
			
			 var zhi =  document.getElementById('aa').value;
			// alert(zhi);
			 var map = new Microsoft.Maps.Map(document.getElementById('myMap'), {
                    /* No need to set credentials if already passed in URL */
                    zoom: 8 });
			        Microsoft.Maps.loadModule('Microsoft.Maps.Search', function () {
                    var searchManager = new Microsoft.Maps.Search.SearchManager(map);
                    var requestOptions = {
                        bounds: map.getBounds(),
                        where: zhi,
                        callback: function (answer, userData) {
                            map.setView({ bounds: answer.results[0].bestView });
                            map.entities.push(new Microsoft.Maps.Pushpin(answer.results[0].location));
                            if(answer.results[0].location != null)
                            {
                                var loc= answer.results[0].location;
                                var str=loc.toString().replace("MapLocation","").replace("[","").replace("(","").replace(")","").replace("]","");
								var arr=str.split(",");
								$("#longitude").val(arr[1]);
								$("#latitude").val(arr[0]);
                            }
							
                        }
                    };
                    searchManager.geocode(requestOptions);
                });
			}
</script>
</head>
<body>
<div class="admin-main" id="staionSaveOrUpdate">
	<form action="" class="layui-form" id="powerStationForm" name="powerStationForm">
		<%-- 隐藏属性 --%>
		<input type="hidden" name="id" id="id" value="${info.id}"/>
		<input type="hidden" name="picture" id="picture"/>

		<%-- <div class="layui-form-item">
			<label class="layui-form-label"><s:message code="系统编号" /></label>
			<div class="layui-input-block">
				<input type="text" name="systemNo" id="systemNo" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					   value="${info.systemNo}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入系统编号" />" class="layui-input" />
			</div>
		</div> --%>

		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="电站名称" /></label>
			<div class="layui-input-block">
				<input type="text" name="systemName" id="systemName" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					   value="${info.systemName}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入电站名称" />" class="layui-input" />
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="电站功率" />(KW)</label>
			<div class="layui-input-block">
				<div>
					<input type="text" name="power" id="power" onkeyup="this.value=this.value.replace(/\D/g,'')" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					       value="${info.power}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入电站功率" />(KW)" class="layui-input" />
				</div>
			</div>
		</div>

			<div class="layui-form-item">
                			<label class="layui-form-label"><s:message code="电站类型" /></label>
                			<div class="layui-input-block">
                				<select id="type" name="type" lay-verify="isEmpty" value="${info.type}">
                					<option value=""><s:message code="请选择" /></option>
                					<option value="1" ><s:message code="V08" /></option>
                					<option value="2"><s:message code="PLC" /></option>
                				</select>
                			</div>
                	</div>

		<div class="layui-form-item">
        			<label class="layui-form-label"><s:message code="布局模式" /></label>
        			<div class="layui-input-block">
        				<select id="layoutType" name="layoutType" lay-verify="isEmpty" value="${info.layoutType}">
        					<option value=""><s:message code="请选择" /></option>
        					<option value="1" ><s:message code="组件模式" /></option>
        					<option value="2"><s:message code="组串模式" /></option>
        				</select>
        			</div>
        	</div>

		<%--<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="电站级别" /></label>
			<div class="layui-input-block">
				<input type="text" name="grade" id="grade" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
				       value="${info.grade}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入电站级别" />" class="layui-input" />
			</div>
		</div>--%>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="国家" /></label>
			<div class="layui-input-block">
				<select id="countriesId" name="countriesId" >
					<option value="0"><s:message code="请选择" /></option>
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="省地区" /></label>
			<div class="layui-input-block">
				<select id="province" name="province" >
					<option value="0"><s:message code="请选择" /></option>
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="城市" /></label>
			<div class="layui-input-block">
				<select id="cityId" name="cityId" >
					<option value="0"><s:message code="请选择" /></option>
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="街道" /></label>
			<div class="layui-input-block">
				<input type="text" name="streetName" id="streetName" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					   value="${info.streetName}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入街道" />" class="layui-input" />
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="选择经纬度" /></label>
			<div class="layui-input-block">
				 <input type="button" value="<s:message code="选择" />" class="layui-btn" id="dialog_show" onclick="dakai()"/>
			</div>
		</div>
       <div class="layui-form-item">
			<label class="layui-form-label"><s:message code="经度" /></label>
			<div class="layui-input-block">
				<input type="text" name="longitude" id="longitude" 
					   value="${info.longitude}" maxlength="50" autocomplete="off"  class="layui-input" />
			</div>
		</div>
		 <div class="layui-form-item">
			<label class="layui-form-label"><s:message code="纬度" /></label>
			<div class="layui-input-block">
				<input type="text" name="latitude" id="latitude"  
					   value="${info.latitude}" maxlength="50" autocomplete="off" class="layui-input" />
			</div>
		</div>
        <div class="layui-form-item">
			<label class="layui-form-label"><s:message code="日出时间" /></label>
			<div class="layui-input-block">
				<input type="text" name="sunuptimech" id="sunuptimech" onclick="WdatePicker({dateFmt: 'HH:mm' })" desc="<s:message code="日出时间" />" lay-verify="isEmpty"
				placeholder="<s:message code="日出时间格式" />：06:00"	 value="${info.sunuptimech}" autocomplete="off" class="layui-input"/>
			</div>
		</div>
		 <div class="layui-form-item">
			<label class="layui-form-label"><s:message code="日落时间" /></label>
			<div class="layui-input-block">
				<input type="text" name="sundowntimech" id="sundowntimech" onclick="WdatePicker({dateFmt: 'HH:mm' })" desc="<s:message code="日落时间" />" lay-verify="isEmpty"
				placeholder="<s:message code="日落时间格式" />：18:00"		   value="${info.sundowntimech}" autocomplete="off" class="layui-input"/>
			</div>
		</div>

	    <div class="layui-form-item">
    			<label class="layui-form-label"><s:message code="采集时间间隔" /></label>
    			<div class="layui-input-block">
    				<input type="text" name="collectGap" id="collectGap" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
    					   value="${info.collectGap}" maxlength="30" autocomplete="off" placeholder="<s:message code="单位为秒" />" class="layui-input" />
    			</div>
    	</div>

		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="邮编" /></label>
			<div class="layui-input-block">
				<input type="text" name="zipCode" id="zipCode" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					   value="${info.zipCode}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入邮编" />" class="layui-input" />
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="创建人" /></label>
			<div class="layui-input-block">
				<input type="text" name="createUserName" id="createUserName" lay-verify="isEmpty" disabled="disabled"
					   value="${info.createUserName}" maxlength="30" autocomplete="off" placeholder="<s:message code="创建人" />" class="layui-input" />
			</div>
		</div>

		<c:if test="${operator == 'detail'}">
			<div class="layui-form-item">
				<label class="layui-form-label"><s:message code="创建时间" /></label>
				<div class="layui-input-block">
					<input type="text"
						   <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="createTimeCh" id="createTimeCh" desc="<s:message code="创建时间" />" lay-verify="isEmpty"
						   value="${info.createTimeCh}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
		</c:if>

		<c:if test="${operator == 'detail'}">
			<div class="layui-form-item">
				<label class="layui-form-label"><s:message code="云终端" /></label>
				<div class="layui-input-block">
					<input type="text"
						   <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>  value="${info.cloudTerminalNum}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
		</c:if>

		<c:if test="${operator == 'detail'}">
			<div class="layui-form-item">
				<label class="layui-form-label"><s:message code="逆变器" /></label>
				<div class="layui-input-block">
					<input type="text"
						   <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>  value="${info.inverterNum}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
		</c:if>
		<c:if test="${operator == 'detail'}">
			<div class="layui-form-item">
				<label class="layui-form-label"><s:message code="组件" /></label>
				<div class="layui-input-block">
					<input type="text"
						   <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>  value="${info.componentNum}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
		</c:if>
		<%--操作按钮 --%>
		<div class="layui-form-item">
			<div class="layui-input-block">
				<c:if test="${operator != 'detail'}"><a id="icon-save" class="layui-btn" lay-submit lay-filter="powerStationForm"><s:message code="保存" /></a></c:if>
				<a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a>
			</div>
		</div>
		<div id="drag">
		    <div class="title">
		        <div>
		            <a class="close" href="javascript:;" title="关闭">关闭</a>
		        </div>
		    </div>
		    <div class="resizeL"></div>
		    <div class="resizeT"></div>
		    <div class="resizeR"></div>
		    <div class="resizeB"></div>
		    <div class="resizeLT"></div>
		    <div class="resizeTR"></div>
		    <div class="resizeBR"></div>
		    <div class="resizeLB"></div>
		    <div class="content">
			  地址:  <input id="aa"  width="150px" height="30px"/> <button  style="width:50px; height:30px;" type="button" onclick="find()">查询</button>
		        <div id='myMap' style='width:490px;height:280px;'></div>
		    </div>
			 <script type='text/javascript' src='https://cn.bing.com/api/maps/mapcontrol?key=AuSqp8yFdgmqAlwip_2oCe9zgBFrFIOQ3nXIibLG-U0IjjW0myJqUQ1G1Eiklmtn&callback=loadMapScenario' async defer></script>
		</div>
	</form>
</div>
<script type="text/javascript">
	// 初始化
	(function() {
		DataList.create({ isLaytpl: false, isTable: false, isPager: false,
            listener: function () {
                SelectLink.create({selectItems:[
                    {
                        url : 'region/queryRegion.web' ,
                        selectElem : 'countriesId' ,
                        queData:{"level":"2"},
                        selectId : 'id' ,
                        selectName : 'name' ,
						selected : '${info.countriesId}',
                        isFormRenderSelect: true
                    },
                    {
                        url : 'region/queryRegion.web' ,
                        selectElem : 'province' ,
                        queData:{"level":"3"},
                        selectId : 'id' ,
                        selectName : 'name' ,
                        parentElem : 'pid',
                        selected : '${info.province}',
                        isFormRenderSelect: true
                    },
                    {
                        url : 'region/queryRegion.web' ,
                        selectElem : 'cityId' ,
                        queData:{"level":"4"},
                        selectId : 'id' ,
                        selectName : 'name' ,
                        parentElem : 'pid',
                        selected : '${info.cityId}',
                        isFormRenderSelect: true
                    }
                ]});
            }
		});

		$('#layoutType').val(${info.layoutType});
        $('#type').val(${info.type});

	}());
</script>
<script type='text/javascript' src='https://cn.bing.com/api/maps/mapcontrol?key=AuSqp8yFdgmqAlwip_2oCe9zgBFrFIOQ3nXIibLG-U0IjjW0myJqUQ1G1Eiklmtn&callback=loadMapScenario&mkt=zh-cn' async defer></script>
</body>
</html>


