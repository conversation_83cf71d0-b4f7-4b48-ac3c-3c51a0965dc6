<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
        // 移除
        $("#stationComponentDelete").click(function () {
            var url = $("#stationComponentDelete").attr("url");
            // 列表数据id  是多个以逗分隔值
            var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
            var length = dataList.checkboxData.length;
            var realData = { "id":'${powerStationId}' , "equipmentType":3 , "equipmentId":ids };
            if (length > 0 ){
                layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index ){
                    doPost( url ,  null , realData , function(data){
                        if(data.rec == 'SUC') {
                            com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
                            dataList.checkboxData = [];
                            dataList.laytplShow();
                        }
                    },function(data){ });
                });
            } else {
                layer.msg('<s:message code="请选择需要操作的数据"/>');
            }
        });
        // 返回
			$("#icon-back").click(function() {
				windowLocaltionHref("powerstation/list.htm", "&leftMenuId=${leftMenuId}")
			});
    });
	</script>
</head>
<body>
	<div class="admin-main" id="component_list">
		<%-- 操作功能 --%>
		<div>
			<div class="layui-btn-group">
				<button class="layui-btn" id="stationComponentDelete" name="stationComponentDelete" url="powerstation/stationDeleteEquipment.web">
					<i class="layui-icon">&#xe640;</i><s:message code="移除" />
				</button>
				<%-- <a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a> --%>
			</div>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="componentList" lay-filter="componentList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="componentListChooseAll" id="componentListChooseAll" lay-skin="primary" /></th>
					<%-- <th><s:message code="编号" /></th> --%>
					<th><s:message code="生产商" /></th>
					<th><s:message code="出厂前序列号" /></th>
					<th><s:message code="型号" /></th>
					<th><s:message code="组件坐标" /></th>
					<th><s:message code="所属组串" /></th>
					<th><s:message code="所属电站" /></th>
					<th><s:message code="状态" /></th>
					<th><s:message code="录入人" /></th>
					<th><s:message code="录入时间" /></th>
				</tr>
			</thead>
			<tbody id="componentListBody"></tbody>
		</table>
		<script type="text/html" id="componentListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="componentList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="componentList_check_{{r.id}}" id="componentList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				
				<td data-field="producers_{{r.producers}}">{{r.producers}}</td>
				<td data-field="serialNo_{{r.serialNo}}">{{r.serialNo}}</td>
				<td data-field="model_{{r.model}}">{{r.model}}</td>
				<td data-field="chipId_{{r.chipId}}">{{r.chipId}}</td>
				<td data-field="belongsGroupName_{{r.belongsGroupName}}">{{r.belongsGroupName}}</td>
				<td data-field="powerStationName_{{r.powerStationName}}">{{r.powerStationName}}</td>
				<td data-field="status_{{r.status}}">
					{{# if(!isNull(r.status) && r.status == 1 ){ }}
						<s:message code="打开"/>
					{{# }  }}
					{{# if(!isNull(r.status) && r.status == 2 ){ }}
						<s:message code="关闭"/>
					{{# }  }}
				</td>
				<td data-field="createUserName_{{r.createUserName}}">
					{{# if(!isNull(r.createUserName)){ }}
						{{r.createUserName}}
					{{# }  }}
				</td>
				<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="10"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "component/queryComponentList.web",
					defaultTableId : 'componentList',
					isPager : true,
					isLaytpl : true,
                    laytplPutextra: {"powerStationId":'${powerStationId}' },
					laytplAppendHtml : 'componentListBody',
					formName : 'componentListForm',
					isTable : false,
					laytplTemplet : 'componentListTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>