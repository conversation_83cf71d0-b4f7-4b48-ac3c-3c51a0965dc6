<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
        // 删除
		$("#stationInverterDelete").click(function () {
			var url = $("#stationInverterDelete").attr("url");
			// 列表数据id  是多个以逗分隔值
			var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
			var length = dataList.checkboxData.length;
			var realData = { "id":'${powerStationId}' , "equipmentType":2 , "equipmentId":ids };
			if (length > 0 ){
				layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index ){
					doPost( url ,  null , realData , function(data){
						if(data.rec == 'SUC') {
							com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
							dataList.checkboxData = [];
							dataList.laytplShow();
						}
					},function(data){ });
				});
			} else {
				layer.msg('<s:message code="请选择需要操作的数据"/>');
			}
		});
		// 返回
			$("#icon-back").click(function() {
				windowLocaltionHref("powerstation/list.htm", "&leftMenuId=${leftMenuId}")
			});
    });
	</script>
</head>
<body>
	<div class="admin-main" id="inverter_list">
		<%-- 操作功能 --%>
		<div>
			<div class="layui-btn-group">
				<button class="layui-btn" id="stationInverterDelete" name="stationInverterDelete" url="powerstation/stationDeleteEquipment.web">
					<i class="layui-icon">&#xe640;</i><s:message code="移除" />
				</button>
			<%-- 	<a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a> --%>
			</div>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="inverterList" lay-filter="inverterList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="inverterListChooseAll" id="inverterListChooseAll" lay-skin="primary" /></th>
					<%-- <th><s:message code="编号" /></th> --%>
					<th><s:message code="名称" /></th>
					<th><s:message code="生产商" /></th>
					<th><s:message code="型号" /></th>
					<th><s:message code="控制器" /></th>
					<th><s:message code="所属电站" /></th>
					<th><s:message code="录入人" /></th>
					<th><s:message code="录入时间" /></th>
				</tr>
			</thead>
			<tbody id="inverterListBody"></tbody>
		</table>
		<script type="text/html" id="inverterListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="inverterList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="inverterList_check_{{r.id}}" id="inverterList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				
				<td data-field="inverterName_{{r.inverterName}}">{{r.inverterName}}</td>
				<td data-field="producers_{{r.producers}}">{{r.producers}}</td>
				<td data-field="model_{{r.model}}">{{r.model}}</td>
				<td data-field="controller_{{r.controller}}">{{r.controllerCh}}</td>
				<td data-field="powerStationName_{{r.powerStationName}}">{{r.powerStationName}}</td>
				<td data-field="createUserName_{{r.createUserName}}">
					{{# if(!isNull(r.createUserName)){ }}
						{{r.createUserName}}
					{{# }  }}
				</td>
				<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="9"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "inverter/queryInverterList.web",
					defaultTableId : 'inverterList',
					isPager : true,
					isLaytpl : true,
                    laytplPutextra: {"powerStationId":'${powerStationId}' },
					laytplAppendHtml : 'inverterListBody',
					formName : 'inverterListForm',
					isTable : false,
					laytplTemplet : 'inverterListTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>