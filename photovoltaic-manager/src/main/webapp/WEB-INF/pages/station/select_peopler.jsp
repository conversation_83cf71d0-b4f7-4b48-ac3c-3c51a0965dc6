<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script type="text/javascript">
	$(function(){
	     /* // 返回
				$("#icon-back").click(function() {
					windowLocaltionHref("powerstation/list.htm", "&leftMenuId=${leftMenuId}")
				}); */
        // 确认
       $("#selectCloudComfirm").click(function (){
            var url = $("#selectCloudComfirm").attr("url");
            var length = dataList.checkboxData.length;
            if(length != 1){
	                com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
	                return;
	            }
	             var id = dataList.checkboxData[0].id;
	              var type = dataList.checkboxData[0].type;
	              if(type != 1)
	              {
	                 com.ymx.layui.public.core.layer.msg('<s:message code="只能选择普通用户"/>!');
	                 return false;
	              }
	             $.ajax({
             
              url:url+'?memberId='+id+'&powerStationId=${powerStationId}',
              data:'',
              type:'POST',
              dataType:'json',
              async:false,
              success:function(data){
                    alert(data.message);
                    dataList.checkboxData = [];
                    dataList.laytplShow();
                    layer.closeAll();
              }
            }); 
        });
        // 返回
			$("#icon-back").click(function() {
				windowLocaltionHref("powerstation/list.htm", "&leftMenuId=${leftMenuId}")
			});
    });
	</script>
</head>
<body>
	<div class="admin-main" id="member_detail_list">
		<%-- <s:message code="查询条件" />区域 --%>
		<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="memberList" id="memberList">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label"><s:message code="用户账号"/></label>
							<div class="layui-input-inline">
								<input type="text" name="phone" placeholder="<s:message code="请输入账号"/>" class="layui-input" style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label"><s:message code="真实姓名"/></label>
							<div class="layui-input-inline">
								<input type="text" name="name" placeholder="<s:message code="请输入用户姓名"/>"
									class="layui-input" style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		</blockquote>
		<%-- 操作功能 --%>
		<div>
			
			<div class="layui-btn-group">
				<button class="layui-btn" id="selectCloudComfirm" name="selectCloudComfirm" url="powerstation/selectMemberInsert.web">
					<i class="layui-icon">&#xe640;</i><s:message code="确认" />
				</button>
				<%-- <a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a> --%>
				<%-- &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a> --%>
			</div>
		
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="memberListData"
			lay-filter="memberListData" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="memberListDataChooseAll" id="memberListDataChooseAll" lay-skin="primary" /></th>
					<th><s:message code="登录账号"/></th>
					
					<th><s:message code="名"/></th>
					<th><s:message code="姓"/></th>
					<th><s:message code="用户类型"/></th>
					
					<th><s:message code="用户状态"/></th>
					<th><s:message code="注册时间"/></th>
					
				</tr>
			</thead>
			<tbody id="member"></tbody>
		</table>
		<script type="text/html" id="memberListDataTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="memberListData_tr_{{r.mId}}">
				<td data-field="mId_{{r.mId}}"><input type="checkbox" name="memberListData_check_{{r.mId}}" id="memberListData_check_{{r.mId}}" lay-skin="primary" value="{{r.mId}}" /></td>
				<td data-field="phone_{{r.phone}}">{{r.phone}}</td>
				<td style="display: none;" data-field="id_{{r.id}}">{{r.id}}</td>
				<td style="display: none;" data-field="type_{{r.type}}">{{r.type}}</td>
				
				<td data-field="name_{{r.name}}">
					{{# if(!isNull(r.name)){ }}
					{{r.name}}
					{{# } else { }}
					{{# }  }}
				</td>
				<td data-field="xing_{{r.xing}}">
					{{# if(!isNull(r.xing)){ }}
		     			{{r.xing}}
					{{# } else { }}
					{{# }  }}
				</td>
				<td data-field="typeCh_{{r.type}}">
					{{# if(!isNull(r.type) && r.type == 1){ }}
						普通用户
					{{# } }}
					{{# if(!isNull(r.type) && r.type == 2){ }}
						运维人员
					{{# } }}
					{{# if(!isNull(r.type) && r.type == 3){ }}
						超级管理员
					{{# } }}
				</td>
				
				<td data-field="status_{{r.status}}">
					{{# if(!isNull(r.status) && r.status == 1){ }}
						正常
					{{# } }}
					{{# if(!isNull(r.status) && r.status == 2){ }}
						删除
					{{# } }}
					{{# if(!isNull(r.status) && r.status == 3){ }}
						锁定
					{{# } }}
				</td>
				<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
               
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="7"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "memberCtr/queryMemberList.web?membertype=1",
					defaultTableId : 'memberListData',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'member',
					formName : 'memberList',
					isTable : false,
					laytplTemplet : 'memberListDataTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>