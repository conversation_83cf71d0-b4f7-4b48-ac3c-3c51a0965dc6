<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@page import="com.ymx.common.utils.PageUtil"%>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.link.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">

	function checkIsSingle() {
               var length = dataList.checkboxData.length;
                if(length != 1){
                 com.ymx.layui.public.core.layer.msg('<s:message code="一次只能操作一条数据"/>!');
                return false;
                }
                return true;
            }


	$(function(){
        // 新增页面
        $("#powerstation-add").click(function () {
            var url = $('#powerstation-add').attr("url");
            windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
        });
        // 编辑
        $("#powerstation-edit").click(function () {
            var url = $('#powerstation-edit').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 查看
        $("#powerstation-detail").click(function () {
            var url = $('#powerstation-detail').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
		// 删除
		$("#powerstation-delete").click(function () {
			var url = $('#powerstation-delete').attr("url");
			operationDb( url , dataList ,  { "isDelete" : 2 , "leftMenuId": '${leftMenuId}'} );
		});
        // 选择云终端
        $("#powerstation-cloud").click(function () {
            var url = $('#powerstation-cloud').attr("url");
            var length = dataList.checkboxData.length;
            if(length != 1){
                com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
                return;
            }
            var id = dataList.checkboxData[0].id;
            var status =  dataList.checkboxData[0].status;
            var mkt = $('#mkt').val();
            if(status ==2)
            {
              if(mkt.indexOf("en") == -1)
              {
                alert("电站已完成调试，不能添加采集器!");
              }
              else
              {
                 alert("The power station has been commissioned and the collector cannot be added!");
              }
              
              return false;
            }
            else
            {
               layer.open({
				title: '<s:message code="请选择采集器"/>',
                type: 2,
                area: ['100%', '100%'],
                content: ['powerstation/stationCloudList.htm?id=' + id + "&flag=selectCloudTerminal&leftMenuId=${leftMenuId}", 'no']
            });
            }
        });
        // 选择逆变器
        $("#powerstation-inverter").click(function () {
            var url = $('#powerstation-inverter').attr("url");
            var length = dataList.checkboxData.length;
            if(length != 1){
                com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
                return;
            }
            var id = dataList.checkboxData[0].id;
            var status =  dataList.checkboxData[0].status;
             var mkt = $('#mkt').val();
            if(status ==2)
            {
              if(mkt.indexOf("en") == -1)
              {
                alert("电站已完成调试，不能添加逆变器!");
              }
                else
                {
                alert("The power station has been commissioned and the inverter cannot be added!");
                }
            }
            else
            {
            layer.open({
                title: '<s:message code="请选择逆变器"/>',
                type: 2,
                area: ['100%', '100%'],
                content: ['powerstation/stationInverterList.htm?id=' + id + "&flag=selectInverter&leftMenuId=${leftMenuId}", 'no']
            });}
        });
        // 调试
        $("#powerstation-try").click(function () {

            if(checkIsSingle())
            {
               var url = $('#powerstation-try').attr("url");
                        var ids = getStrAttrVal({data :dataList.checkboxData , "attrName":"id"});
                        var status = getStrAttrVal({data :dataList.checkboxData , "attrName":"status"});

                        $.ajax({
                          url:url+'?id='+ids+'&status='+status,
                          data:'',
                          type:'POST',
                          dataType:'json',
                          async:false,
                          success:function(data){
                              alert(data.message);
                          }
                      });
            }


        });
        // 查看设备
        $("#powerstation-equipment").click(function () {
            var length = dataList.checkboxData.length;
            if(length != 1){
                com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
                return;
            }
            var powerStationId = dataList.checkboxData[0].id;
            layer.tab({
                area: ['100%', '100%'],
                tab: [{
                    title: '<s:message code="采集器"/>',
                    content: '<iframe id="cloudterminal" name="cloudterminal" frameborder="0" height="560"  scrolling="no" width="100%" src="powerstation/stationCloudList.htm?id='+ powerStationId +'"></iframe>'
                }, {
                    title: '<s:message code="逆变器"/>',
                    content: '<iframe id="inverterIframe" name="inverterIframe" frameborder="0" height="560"  scrolling="no" width="100%" src="powerstation/stationInverterList.htm?id='+ powerStationId +'"></iframe>'
                }, {
                    title: '<s:message code="组件"/>',
                    content: '<iframe id="componentIframe" name="componentIframe" frameborder="0" height="560"  scrolling="no" width="100%" src="powerstation/stationComponentList.htm?id='+ powerStationId +'"></iframe>'
                }]
            });
        });
        // 选择业主
      
			$("#powerstation-select-peopler").click(function ()
			 {
	            var url = $('#powerstation-select-peopler').attr("url");
	            var length = dataList.checkboxData.length;
	            if(length != 1){
	                com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
	                return;
	            }
	            var id = dataList.checkboxData[0].id;
	            layer.open({
					title: '<s:message code="请选择业主"/>',
	                type: 2,
	                area: ['100%', '100%'],
	                content: [url+'?id=' + id + "&flag=selectCloudTerminal&leftMenuId=${leftMenuId}", 'no']
	            });
        });

		
    });
	</script>
</head>
<body>
<input type="hidden" name="mkt" id="mkt" value="${mkt }"/>
	<div class="admin-main" id="station_list">
		<%-- <s:message code="查询条件" />区域 --%>
		<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="stationListForm" id="stationListForm">
				<%--<input type="hidden" name="memberId" id="memberId" value="${memberId }"/>--%>
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="创建时间" /></label>
							<div class="layui-input-inline" >
								<input type="text" name="createTimeStart" id="createTimeStart" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'createTimeEnd\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" placeholder="<s:message code="开始时间"/>" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
							<div class="layui-form-mid">-</div>
							<div class="layui-input-inline">
								<input type="text" name="createTimeEnd" id="createTimeEnd" placeholder="<s:message code="结束时间"/>" onclick="WdatePicker({minDate:'#F{$dp.$D(\'createTimeStart\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select">&nbsp; &nbsp; <s:message code="电站名称" /></label>
							<div class="layui-input-inline">
								<input type="text" name="systemName" id="systemName" placeholder="<s:message code="请输入电站名称"/>" class="layui-input"style="width: 100%;">
							</div>
						</div>
						 <div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="系统编号" /></label>
							<div class="layui-input-inline">
								<input type="text" name="systemNo" id="systemNo" placeholder="<s:message code="请输入系统编号"/>" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<c:if test="${memberId == ''}">
							<div class="layui-inline">
								<label class="layui-form-label-select"><s:message code="创建人" /></label>
								<div class="layui-input-inline">
									<input type="text" name="createUserName" id="createUserName" placeholder="<s:message code="创建人"/>" class="layui-input"style="width: 100%;">
								</div>
							</div>
						</c:if>
						<c:if test="${memberId != ''}">
							<div class="layui-inline">
								<c:if test="${memberType == '2'}">
									<label class="layui-form-label-select"><s:message code="创建人" /></label>
									<div class="layui-input-inline">
										<input type="text" value="${userName}" disabled="disabled" placeholder="<s:message code="创建人"/>" class="layui-input"style="width: 100%;">
									</div>
								</c:if>
								<c:if test="${memberType == '1'}">
									<label class="layui-form-label-select"><s:message code="业主姓名" /></label>
									<div class="layui-input-inline">
										<input type="text" value="${userName}" disabled="disabled" placeholder="<s:message code="运维人员"/>" class="layui-input"style="width: 100%;">
									</div>
								</c:if>
							</div>
						</c:if>

						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="国家" /></label>
							<div class="layui-input-inline">
								<select id="countriesId" name="countriesId" >
									<option value=""><s:message code="请选择" /></option>
								</select>
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="省地区" /> &nbsp; &nbsp; </label>
							<div class="layui-input-inline">
								<select id="province" name="province" >
									<option value=""><s:message code="请选择" /></option>
								</select>
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="城市" /></label>
							<div class="layui-input-inline">
								<select id="cityId" name="cityId" >
									<option value=""><s:message code="请选择" /></option>
								</select>
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="街道" /> &nbsp; &nbsp; </label>
							<div class="layui-input-inline">
								<input type="text" name="streetName" id="streetName" placeholder="<s:message code="街道"/>" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="状态" /></label>
							<div class="layui-input-inline">
								<select id="status" name="status" >
									<option value="0"><s:message code="请选择"/></option>
								</select>
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="电站级别" /></label>
							<div class="layui-input-inline">
								<select id="grade" name="grade" >
									<option value=""><s:message code="请选择"/></option>
									<option value="1"><s:message code="微型"/></option>
									<option value="2"><s:message code="小型"/></option>
									<option value="3"><s:message code="中型"/></option>
									<option value="4"><s:message code="大型"/></option>
									<option value="5"><s:message code="特大型"/></option>
								</select>
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
								<c:if test="${memberId != ''}">
									<a class="layui-btn" href="<%=PageUtil.getBasePath(request) %>memberCtr/list.htm"><s:message code="返回会员列表" /></a>
								</c:if>
								<c:if test="${memberType == '3'}">
									<a class="layui-btn" href="<%=PageUtil.getBasePath(request) %>powerMapView/list.htm"><s:message code="返回电站地图" /></a>
								</c:if>
								<c:if test="${memberType == '4'}">
									<a class="layui-btn" href="<%=PageUtil.getBasePath(request) %>index/portal.htm"><s:message code="返回电站地图" /></a>
								</c:if>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		</blockquote>
		<%-- 操作功能 --%>
		<div>
			<jsp:include page="/functionBuilder.htm"></jsp:include>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="stationList" lay-filter="stationList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="stationListChooseAll" id="stationListChooseAll" lay-skin="primary" /></th>
					 <th><s:message code="系统编号" /></th>
					<th><s:message code="电站名称" /></th>
					<th><s:message code="国家" /></th>
					<th><s:message code="省地区" /></th>
					<th><s:message code="城市" /></th>
					<th><s:message code="详细地址" /></th>
					<th><s:message code="邮编" /></th>
					<th><s:message code="状态" /></th>
					<th><s:message code="业主姓名" /></th>
					<th><s:message code="电站级别" /></th>
					<th><s:message code="创建人" /></th>
					<th><s:message code="创建时间" /></th>
				</tr>
			</thead>
			<tbody id="stationBody"></tbody>
		</table>
		<script type="text/html" id="stationListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="stationList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="stationList_check_{{r.id}}" id="stationList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				<td data-field="systemNo_{{r.systemNo}}">{{r.systemNo}}</td>
				<td data-field="systemName_{{r.systemName}}">{{r.systemName}}</td>
                <td data-field="countries_{{r.countries}}">{{r.countries}}</td>
                <td data-field="province_{{r.province}}">{{r.provinceName}}</td>
				<td data-field="cityName_{{r.cityName}}">{{r.cityName}}</td>
				<td data-field="streetName_{{r.streetName}}">
					{{# if(!isNull(r.streetName)){ }}
					{{r.streetName.substring(0,10)}}
					{{# }  }}
				</td>
				<td data-field="zipCode_{{r.zipCode}}">{{r.zipCode}}</td>
				<td data-field="status_{{r.status}}">
					{{# if(!isNull(r.status) && r.status == 1 ){ }}
						<s:message code="待调试"/>
					{{# }  }}
					{{# if(!isNull(r.status) && r.status == 2 ){ }}
						<s:message code="已调试"/>
					{{# }  }}
				</td>
                <td data-field="memberName_{{r.memberName}}">{{r.memberName}}</td>
				<td data-field="grade_{{r.grade}}">
					{{# if(!isNull(r.grade) && r.grade == 1 ){ }}
						<s:message code="微型"/>
					{{# }  }}
					{{# if(!isNull(r.grade) && r.grade == 2 ){ }}
						<s:message code="小型"/>
					{{# }  }}
					{{# if(!isNull(r.grade) && r.grade == 3 ){ }}
						<s:message code="中型"/>
					{{# }  }}
					{{# if(!isNull(r.grade) && r.grade == 4 ){ }}
						<s:message code="大型"/>
					{{# }  }}
					{{# if(!isNull(r.grade) && r.grade == 5 ){ }}
						<s:message code="特大型"/>
					{{# }  }}
				</td>
				<td data-field="createUserName_{{r.createUserName}}">
					{{# if(!isNull(r.createUserName)){ }}
						{{r.createUserName}}
					{{# }  }}
				</td>
				<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<c:if test="${memberId == ''}">
						<td colspan="13"><s:message code="暂无数据"/>!</td>
					</c:if>
					<c:if test="${memberId != ''}">
						<td colspan="13"><s:message code="未找到管理的数据"/>!</td>
					</c:if>
				</tr>   
			{{# } }}

		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "powerstation/queryPowerStationList.web?memberId=${memberId}&id=${id}&m=1",
					defaultTableId : 'stationList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'stationBody',
					formName : 'stationListForm',
					isTable : false,
					laytplTemplet : 'stationListTemplet',
                    listener :function(){
                        $.selectOption({
                            url : 'dic/queryDicList.web' ,
                            selectElem : 'status' ,
                            queData:{"dicId":"stationStatus"},
                            selectId : 'dicValueId' ,
                            selectName : 'dicValueLabel' ,
                            isFormRenderSelect: true
                        });
	                    SelectLink.create({selectItems:[
		                    {
			                    url : 'region/queryRegion.web' ,
			                    selectElem : 'countriesId' ,
			                    queData:{"level":"2"},
			                    selectId : 'id' ,
			                    selectName : 'name' ,
			                    selected : '${info.countriesId}',
			                    isFormRenderSelect: true
		                    },
		                    {
			                    url : 'region/queryRegion.web' ,
			                    selectElem : 'province' ,
			                    queData:{"level":"3"},
			                    selectId : 'id' ,
			                    selectName : 'name' ,
			                    parentElem : 'pid',
			                    selected : '${info.province}',
			                    isFormRenderSelect: true
		                    },
		                    {
			                    url : 'region/queryRegion.web' ,
			                    selectElem : 'cityId' ,
			                    queData:{"level":"4"},
			                    selectId : 'id' ,
			                    selectName : 'name' ,
			                    parentElem : 'pid',
			                    selected : '${info.cityId}',
			                    isFormRenderSelect: true
		                    }
	                    ]});
                    }
				});
			}())
		</script>
	</div>
</body>
</html>