<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
 </head>
<body>
	<div class="admin-main" id="component_list">
		<%-- <s:message code="查询条件" />区域 --%>
		<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="componentListForm" id="componentListForm">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="反馈时间" /></label>
							<div class="layui-input-inline" >
								<input type="text" name="createTimeStart" id="createTimeStart" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'createTimeEnd\')}'})" placeholder="<s:message code="反馈开始时间" />" autocomplete="off" class="layui-input">
							</div>
							<div class="layui-form-mid">-</div>
							<div class="layui-input-inline">
								<input type="text" name="createTimeEnd" id="createTimeEnd" placeholder="<s:message code="反馈结束时间" />" onclick="WdatePicker({minDate:'#F{$dp.$D(\'createTimeStart\')}'})" autocomplete="off" class="layui-input">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="联系方式" /></label>
							<div class="layui-input-inline">
								<input type="text" name="information" id="information" placeholder="<s:message code="请输入联系方式" />" class="layui-input">
							</div>
						</div>
						
						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		</blockquote>
		<%-- 操作功能 --%>
		<div>
			<jsp:include page="/functionBuilder.htm"></jsp:include>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="componentList" lay-filter="componentList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="componentListChooseAll" id="componentListChooseAll" lay-skin="primary" /></th>
					<th><s:message code="联系方式" /></th>
					<th><s:message code="反馈内容" /></th>
					<th><s:message code="录入时间" /></th>
				</tr>
			</thead>
			<tbody id="componentListBody"></tbody>
		</table>
		<script type="text/html" id="componentListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="componentList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="componentList_check_{{r.id}}" id="componentList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				<td data-field="componentNo_{{r.information}}">{{r.information}}</td>
				<td data-field="producers_{{r.content}}">{{r.content}}</td>
				<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="10"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "feedBackView/queryFeedbackList.web",
					defaultTableId : 'feedbackList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'componentListBody',
					formName : 'componentListForm',
					isTable : false,
					laytplTemplet : 'componentListTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>