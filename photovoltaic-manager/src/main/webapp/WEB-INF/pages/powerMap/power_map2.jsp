<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@page import="com.ymx.common.utils.PageUtil"%>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.link.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
      <link href="<%=PageUtil.getBasePath(request) %>css/map.css" rel="stylesheet">
	  <script type="text/javascript">
	  function resetdata()
	  {
//	    document.getElementById("quyu").selectedIndex=0;
	    document.getElementById("guojia").selectedIndex=0;
		  document.getElementById("sheng").selectedIndex=0;
	    document.getElementById("shi").selectedIndex=0;    
	    document.getElementById("systemName").value="";  
	    document.getElementById("dizhi").value="";
	  }
               function finddata()
			   {
			        var shengId=$("#sheng").find('option:selected').val();
			        var guojiaId=$("#guojia").find('option:selected').val();
			        var shiId=$("#shi").find('option:selected').val();
			        var dizhi =  $("#dizhi").val();
			        var username =  $("#username").val();
			        var systemName =  $("#systemName").val();
			        var grade =  $("#grade").val();
			        $.ajax({
						url: "powerMapView/findlist.htm?name="+username+"&systemName="+systemName+"&shengId="+shengId+"&guojiaId="+guojiaId+"&shiId="+shiId+"&dizhi="+dizhi+"&grade="+grade,
						type: "post",
						dataType: "json",
						async : false,
						success: function(data)
						{
							if(data!=null){
								var longitude,latitude;
								var count=0;
								$.each(data,function(i,item)
								{
									if(item!=null && item.length>0){
										$.each(item,function(j,val)
										{
//											longitude=val.longitude;//经度
//											latitude=val.latitude;//纬度
											if(val.latitude!=null && val.latitude<=90){
												longitude=val.latitude;
												latitude=val.longitude;
												count++;
											}

										});
										if(count>0){
	//										alert(longitude+"=经度====纬度="+latitude);
											var map = new Microsoft.Maps.Map(document.getElementById('myMap'), {
												center: new Microsoft.Maps.Location(longitude,latitude),zoom:4 });
	//										if(count!=1){
	//											map.setOptions({
	//												maxZoom: 6,
	//												minZoom: 4
	//											});
	//										}
											$.each(item,function(j,val)
											{
												if(val.latitude!=null && val.latitude<=90){
													var dzid=val.id;
													var infoboxTemplate ='<div class="region-list active postition-7 online-node" onclick=highlight(dzid)><div class="area-box"><span class="dot"></span><span class="pulse delay-11"></span><span class="pulse delay-10"></span><span class="pulse delay-09"></span></div></div></div>';
													var p = new Microsoft.Maps.Location(val.latitude,val.longitude);
													var pushpinid="pushpin_"+val.id;
													var infoboxid="infobox_"+val.id;
													window['pushpinid'] = new Microsoft.Maps.Pushpin(p, null);
													window['infoboxid'] = new Microsoft.Maps.Infobox(p, {title: val.systemName,
														description: val.systemName, visible: true   });
													window['infoboxid'].setMap(map);
													Microsoft.Maps.Events.addHandler(window['pushpinid'], 'click', function () { highlight(dzid); });
													function highlight(id) {
														alert(id);
														<%--windowLocaltionHref("powerstation/list.htm","&leftMenuId=${leftMenuId}&type=3&id="+id);--%>
														<%--windowLocaltionHref("systemView/findmoveView.htm?ymd="+ymd+"&powerStationId="+dzid , "&leftMenuId=${leftMenuId}");--%>
													}
													map.entities.push(window['pushpinid']);
													window['pushpinid'].setOptions({ enableHoverStyle: true, enableClickedStyle: true });
												}
											});
										}else{
											var map = new Microsoft.Maps.Map(document.getElementById('myMap'), {});
										}
									}else{
										var map = new Microsoft.Maps.Map(document.getElementById('myMap'), {});
									}
								});

							}else{
								var map = new Microsoft.Maps.Map(document.getElementById('myMap'), {});
							}
					   }
					});
			}  
		</script>	     
	
</head>
<body onload="finddata()">
<input type="hidden" name="username" id="username"/>
<%--value="${userKey}"--%>
	<div class="admin-main" id="station_list_map2">
		<%-- <s:message code="查询条件" />区域 --%>
		<div class="layui-form">
			<blockquote class="layui-elem-quote">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" />222</legend>
					<div class="layui-form-item">

						<%-- <div class="layui-inline">
							<label class="layui-form-label"><s:message code="用户名" /></label>
							<div class="layui-input-inline">
								<input type="text" name="systemName" id="systemName"  class="layui-input"style="width: 100%;">
							</div>
						</div> --%>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="电站名称" /></label>
							<div class="layui-input-inline">
								<input type="text" name="systemName" id="systemName"  class="layui-input"style="width: 100%;">
							</div>
						</div>
						<%-- <div class="layui-inline">
							<label class="layui-form-label"><s:message code="查询时间" /></label>
							<div class="layui-input-inline" >
								<input type="text" name="createTimeBegin" id="createTimeBegin" onclick="WdatePicker()" placeholder="<s:message code="查询时间" />" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
							
						</div> --%>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="电站级别" /></label>
							<div class="layui-input-inline">
								<select id="grade" name="grade" style="height:38px;display:true;">
									<option value=""><s:message code="请选择"/></option>
									<option value="1"><s:message code="微型"/></option>
									<option value="2"><s:message code="小型"/></option>
									<option value="3"><s:message code="中型"/></option>
									<option value="4"><s:message code="大型"/></option>
									<option value="5"><s:message code="特大型"/></option>
								</select>
							</div>
						</div>
						<%--<div class="layui-inline" style="display: none">--%>
							<%--<label class="layui-form-label-select"><s:message code="区域" /></label>--%>
							<%--<div class="layui-input-inline" style="height:38px;">--%>
							   <%--<select id="quyu" style="height:38px;display:true;" onChange=getguojia()>--%>
								   <%--<option value=''><s:message code="请选择" /></option>--%>
								   <%--<c:forEach var="quyu" items="${powerMaplt}" varStatus="s">--%>
									  <%--<option value="${quyu.id }">${quyu.name }</option>--%>
								  <%--</c:forEach>	--%>
								<%--</select>--%>
							<%--</div>--%>
						<%--</div>--%>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="国家" /></label>
							<div class="layui-input-inline">
								<select id="guojia" name="guojia" >
									<option value=""><s:message code="请选择" /></option>
								</select>
							</div>
							<%--<label class="layui-form-label-select"><s:message code="国家" /></label>--%>
							<%--<div class="layui-input-inline" style="height:38px;">--%>
							   <%--<select id="guojia" style="height:38px;display:true;" onChange=getsf()>--%>
								   <%--<option value=''><s:message code="请选择" /></option>--%>
								  <%--&lt;%&ndash;  <c:forEach var="guojia" items="${powerMaplt}" varStatus="s">--%>
									  <%--<option value="${guojia.id }">${guojia.name }${guojia.nameen}</option>--%>
								  <%--</c:forEach>	 &ndash;%&gt;--%>
								<%--</select>--%>
							<%--</div>--%>
						</div>
						</br>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="省" /></label>
							<div class="layui-input-inline" style="height:38px;">
								<select id="sheng" name="sheng" >
									<option value=""><s:message code="请选择" /></option>
								</select>
							   <%--<select id="sheng" style="height:38px;display:true;" onChange=getshi()>--%>
								   <%--<option value=''><s:message code="请选择" /></option>--%>
								<%--</select>--%>
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="市" /></label>
							<div class="layui-input-inline" >
								<select id="shi" name="shi" >
									<option value=""><s:message code="请选择" /></option>
								</select>
							   <%--<select id="shi" style="height:38px;display:true;" >--%>
								   <%--<option value=''><s:message code="请选择" /></option>--%>
								<%--</select>--%>
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="详细地址" /></label>
							<div class="layui-input-inline">
								<input type="text" name="dizhi" id="dizhi"  class="layui-input"style="width: 100%;">
							</div>
						</div>

						<div class="layui-inline">
							<div class="layui-input-block">
								<%--<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>--%>
								<button class="layui-btn" onClick=finddata()><s:message code="查询"/></button>
								<button type="reset" class="layui-btn" onClick=resetdata()><s:message code="重置" /></button>
							</div>
						</div>

					</div>
				</fieldset>
			</blockquote>
		</div>
     <!--     <button  width="90px" height="120px" value="danji" onClick='danji()'>kfkfkkfksks</button> -->
        <div id='printoutPanel'></div>
        <div id='myMap' style='width: 100vw; height: 100vh;'></div>
            <script type='text/javascript'>
            function loadMapScenario() 
            {
//                   var map = new Microsoft.Maps.Map(document.getElementById('myMap'), {});
            }
        </script>
        <script type='text/javascript' src='https://cn.bing.com/api/maps/mapcontrol?key=AuSqp8yFdgmqAlwip_2oCe9zgBFrFIOQ3nXIibLG-U0IjjW0myJqUQ1G1Eiklmtn&callback=loadMapScenario&mkt=${mapmkt}' async defer></script>
	</div>

<%--<div class="layui-tab" lay-filter="demo" lay-allowclose="true">--%>
	<%--<ul class="layui-tab-title">--%>
		<%--<li lay-id="1111">用户管理</li>--%>
		<%--<li lay-id="2222">商品管理</li>--%>
	<%--</ul>--%>
	<%--<div class="layui-tab-content">--%>
		<%--<div class="layui-tab-item layui-show">内容1</div>--%>
		<%--<div class="layui-tab-item">内容2</div>--%>
	<%--</div>--%>
<%--</div>--%>
<%--<div class="site-demo-button" style="margin-bottom: 0;">--%>
	<%--<button class="layui-btn site-demo-active" data-type="tabAdd">新增Tab项</button>--%>
	<%--<button class="layui-btn site-demo-active" data-type="tabDelete">删除：商品管理</button>--%>
	<%--<button class="layui-btn site-demo-active" data-type="tabChange">切换到：用户管理</button>--%>
<%--</div>--%>

<script type="text/javascript">
	// 初始化
	(function() {
		DataList.create({ isLaytpl: false, isTable: false, isPager: false,
			listener: function () {
				SelectLink.create({selectItems:[
					{
						url : 'region/queryRegion.web' ,
						selectElem : 'guojia' ,
						queData:{"level":"2"},
						selectId : 'id' ,
						selectName : 'name' ,
						selected : '',
						isFormRenderSelect: true
					},
					{
						url : 'region/queryRegion.web' ,
						selectElem : 'sheng' ,
						queData:{"level":"3"},
						selectId : 'id' ,
						selectName : 'name' ,
						parentElem : 'pid',
						selected : '',
						isFormRenderSelect: true
					},
					{
						url : 'region/queryRegion.web' ,
						selectElem : 'shi' ,
						queData:{"level":"4"},
						selectId : 'id' ,
						selectName : 'name' ,
						parentElem : 'pid',
						selected : '',
						isFormRenderSelect: true
					}
				]});
			}
		});
	}());


//	layui.use('element', function(){
//		var $ = layui.jquery,element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
//		//触发事件
//		var active = {
//			tabAdd: function(){
//				//新增一个Tab项
//				element.tabAdd('demo', {
//					title: '新选项'+ (Math.random()*1000|0) //用于演示
//					,content: '内容'+ (Math.random()*1000|0)
//					,id: 88 //实际使用一般是规定好的id，这里以时间戳模拟下
//				});
//				alert(444);
//				element.tabAdd('kitTab', {
//					title: '新选项'+ (Math.random()*1000|0) //用于演示
//					,content: '内容'+ (Math.random()*1000|0)
//					,id: 888 //实际使用一般是规定好的id，这里以时间戳模拟下
//				});
//				alert(555);
//				element.tabChange('demo', '88'); //切换到：用户管理
//			}
//			,tabDelete: function(othis){
//				//删除指定Tab项
////				element.tabDelete('demo', '44'); //删除：“商品管理”
//				element.tabDelete('kitTab', '70'); //删除：“商品管理”
//				othis.addClass('layui-btn-disabled');
//			}
//			,tabChange: function(){//切换到指定Tab项
//				element.tabChange('demo', '22'); //切换到：用户管理
//			}
//		};
//		$('.site-demo-active').on('click', function(){
//			alert(11);
//			var othis = $(this), type = othis.data('type');
//			active[type] ? active[type].call(this, othis) : '';
//		});
//	});
</script>
</body>
</html>