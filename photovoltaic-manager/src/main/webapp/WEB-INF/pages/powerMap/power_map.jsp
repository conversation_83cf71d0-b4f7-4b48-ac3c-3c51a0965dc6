<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@page import="com.ymx.common.utils.PageUtil"%>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.link.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<link rel="stylesheet" href="http://libs.baidu.com/bootstrap/3.2.0/css/bootstrap.min.css">
	<link href="<%=PageUtil.getBasePath(request) %>css/map.css" rel="stylesheet">
	<script src="https://cdn.bootcss.com/echarts/3.5.3/echarts.min.js"></script>
	<script type="text/javascript">
			function myBrowser(){
			var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
			var isOpera = userAgent.indexOf("Opera") > -1;
			if (isOpera) {
				return "Opera"
			}; //判断是否Opera浏览器
			if (userAgent.indexOf("Firefox") > -1) {
				return "FF";
			} //判断是否Firefox浏览器
			if (userAgent.indexOf("Chrome") > -1){
				return "Chrome";
			}
			if (userAgent.indexOf("Safari") > -1) {
				return "Safari";
			} //判断是否Safari浏览器
			if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
				return "IE";
			}; //判断是否IE浏览器
			if (userAgent.indexOf("Edge") > -1) {
				return "IE";
			};
			if (userAgent.indexOf("Mozilla") > -1) {
				return "IE";
			};
		}
		function resetdata()
		{
			document.getElementById("guojia").selectedIndex=0;
		    document.getElementById("sheng").selectedIndex=0;
			document.getElementById("shi").selectedIndex=0;
			document.getElementById("systemName").value="";
			document.getElementById("dizhi").value="";
		}
       function finddata()
	   {
	   	    var mb=myBrowser();
	        var shengId=$("#sheng").find('option:selected').val();
	        var guojiaId=$("#guojia").find('option:selected').val();
	        var shiId=$("#shi").find('option:selected').val();
	        var dizhi =  $("#dizhi").val();
	        var username =  $("#username").val();
	        var systemName =  $("#systemName").val();
	        var grade =  $("#grade").val();
	        $.ajax({
				url: "powerMapView/findlist.htm?name="+username+"&systemName="+systemName+"&shengId="+shengId+"&guojiaId="+guojiaId+"&shiId="+shiId+"&dizhi="+dizhi+"&grade="+grade,
				type: "post",
				dataType: "json",
				async : false,
				success: function(data)
				{
					if(data!=null){
						var longitude,latitude;
						var count=0;
						$.each(data,function(i,item)
						{
							if(item!=null && item.length>0){
								$.each(item,function(j,val)
								{
									if(val.latitude!=null && val.latitude<=90){
										longitude=val.latitude;//经度
										latitude=val.longitude;//纬度
										count++;
									}
								});
								if(count>0){//document.getElementById('myMap')
									var map = new Microsoft.Maps.Map('#myMap', {});
									if ("FF" == mb || "IE" == mb) {
										map = new Microsoft.Maps.Map('#myMap', {
											center: new Microsoft.Maps.Location(longitude,latitude)
											,mapTypeId: Microsoft.Maps.MapTypeId.birdseye
											,credentials: 'AuSqp8yFdgmqAlwip_2oCe9zgBFrFIOQ3nXIibLG-U0IjjW0myJqUQ1G1Eiklmtn&callback=loadMapScenario'
										});
										map.setOptions({
											maxZoom: 20
											,minZoom: 1
										});
									}else{
										map = new Microsoft.Maps.Map('#myMap', {
											center: new Microsoft.Maps.Location(longitude,latitude)
											,mapTypeId: Microsoft.Maps.MapTypeId.birdseye
											,credentials: 'AuSqp8yFdgmqAlwip_2oCe9zgBFrFIOQ3nXIibLG-U0IjjW0myJqUQ1G1Eiklmtn&callback=loadMapScenario'
											,zoom:2//Firefox ， IE  不支持属性
										});
									}
									$.each(item,function(j,val)
									{
										if(val.latitude!=null && val.latitude<=90){
											var dzid=val.id;
											var infoboxTemplate ='<div class="region-list active postition-7 online-node" onclick=highlight(dzid)><div class="area-box"><span class="dot"></span><span class="pulse delay-11"></span><span class="pulse delay-10"></span><span class="pulse delay-09"></span></div></div></div>';
											var p = new Microsoft.Maps.Location(val.latitude,val.longitude);
											var pushpinid="pushpin_"+val.id;
											var infoboxid="infobox_"+val.id;
											window['pushpinid'] = new Microsoft.Maps.Pushpin(p, null);
											window['infoboxid'] = new Microsoft.Maps.Infobox(p, {title: val.systemName,
												description: val.systemName, visible: true   });
											window['infoboxid'].setMap(map);
											Microsoft.Maps.Events.addHandler(window['pushpinid'], 'click', function () { highlight(dzid); });
											function highlight(id) {
												getDivInfo(id);
												<%--windowLocaltionHref("powerstation/list.htm","&leftMenuId=${leftMenuId}&type=3&id="+id);--%>
												<%--windowLocaltionHref("systemView/findmoveView.htm?ymd="+ymd+"&powerStationId="+dzid , "&leftMenuId=${leftMenuId}");--%>
											}
//											map.entities.push(pin);
											map.entities.push(window['pushpinid']);
											window['pushpinid'].setOptions({ enableHoverStyle: true, enableClickedStyle: true });
										}
									});
								}else{
									var map = new Microsoft.Maps.Map('#myMap', {});
								}
							}else{
								var map = new Microsoft.Maps.Map('#myMap', {});
							}
						});
					}else{
						var map = new Microsoft.Maps.Map('#myMap', {});
					}
			    }
			});
		}
		function searchMap2(){//NavBar_MapTypeButtonContainer maximise  max  maximize_window  NavBar_MapTypeButtonContainerWrapper
			var img="";
			if("${type}" !="1"){
				img=" &nbsp; <div style='cursor:pointer;background:#726DAD;float:right'> <img onclick='searchOpne()' src='<%=PageUtil.getBasePath(request) %>images/maximise.png' width='40' height='40'></div>";
			}
			$(".NavBar_MapTypeButtonContainer").before("<div class='divSearch' style='cursor:pointer;background:#1092AD;height:40px;width:85px;' onclick='searchI()'><div style='float:left;line-height:40px;width:41px;' >查询<i  id='searchI' class='fa fa-search'></i></div>"+img+"</div>");
		}
        timeSearchMap();
        function timeSearchMap(){
	        setTimeout(function(){//.find(".divSearch")
	            var nm=$(".divSearch");
	           if(nm==null || nm.html()==null){
		           searchMap2();
	           }
		        timeSearchMap();
	        },5000);
        }
		function searchI(){//fa-times  fa-search-plus
			if($("#divSearch").css('display')=='none'){
				$("#divSearch").show();
				$("#searchI").addClass(' fa-times');
				$("#searchI").removeClass('fa-search');
			}else{
				$("#divSearch").hide();
				$("#searchI").addClass(' fa-search');
				$("#searchI").removeClass('fa-times');
			}
		}
		function searchOpne(){//kit-side-fold fa fa-navicon
			window.open ('<%=PageUtil.getBasePath(request) %>index/portal.htm?type=1','newwindow','width='
				+(window.screen.availWidth-10)+',height='+(window.screen.availHeight-70)+
				',top=0,left=0,toolbar=no,menubar=no,scrollbars=no,fullscreen=yes, resizable=no,location=no, status=no')
		}
		function getDivInfo(id){
			$("#inverterInfo").show();
			$("#componentInfo").show();
			$("#inverterInfo").html("");
			$("#componentInfo").html("");
			var ul=$("<ul class='list-group'>");
			var li1=$("<ul class='list-group-item'>");
			var li2=$("<ul class='list-group-item'>");
			var li3=$("<ul class='list-group-item'>");
			var li4=$("<ul class='list-group-item'>");
			var li5=$("<ul class='list-group-item'>");
			var li6=$("<ul class='list-group-item'>");
			var li7=$("<ul class='list-group-item'>");
			$(li1).append("<s:message code='电站'/>:");
			$(li2).append("<s:message code='煤'/>：");
			$(li3).append("<s:message code='二氧化碳'/>:");
			$(li4).append("<s:message code='二氧化硫'/>:");
			$(li5).append("<s:message code='氮氧化物'/>:");
			$(li6).append("<s:message code='碳粉尘'/>:");
			$(li7).append("<s:message code='发电量'/>:");
			$.ajax({
				url: "powerMapView/getPowerStationInfo.web?powerStationId="+id,
				type: "post",
				dataType: "json",
				async : false,
				success: function(data)
				{
					if(data.code==0){
						$(li1).append(data.reModel.powerStation.systemName);
						$(li2).append(data.reModel.coal);
						$(li3).append(data.reModel.dioxide);
						$(li4).append(data.reModel.sulfur);
						$(li5).append(data.reModel.nitrogen);
						$(li6).append(data.reModel.dust);
						$(li7).append(data.reModel.kwh+" Kwh");
						$(ul).append(li1);
						$(ul).append(li7);
						$(ul).append(li2);
						$(ul).append(li3);
						$(ul).append(li4);
						$(ul).append(li5);
						$(ul).append(li6);
						$("#myMap").append($("#componentInfo").append($(ul).html()));
						$("#myMap").append($("#inverterInfo").append("<div id='main'   style='width: 100%; height: 240px;background-color: #fff;margin-top:280px'></div>"));
						var myChart = echarts.init(document.getElementById('main'),'macarons');//https://echarts.baidu.com/examples/#chart-type-pie
						$("#inverterInfo").dblclick(function(){//click
							window.location.href="systemView/reportForm.htm?pid="+data.reModel.powerStation.id;
						});
						var option = {
							title : {
								text: ''+data.reModel.powerStation.systemName,
								subtext: '<s:message code="总发电量"/>：'+data.reModel.kwh+" Kwh \n <s:message code='当前功率'/>："+data.reModel.power+"W    ",
								x:'center'
							},
							tooltip : {
								trigger: 'item',
								formatter: "{a} <br/>{b} : {c} ({d}%)"
							},
							series : [
								{
									name:'<s:message code="排放量"/>',
									type:'pie',
									radius : '30%',
									center: ['55%', '50%'],
									label: {
										normal: {
											formatter: '{b}({d}%)\n{c} ', //formatter: '{a} \n{b} : {c} \n({d}%)',
											borderWidth: 1
										}
									},
									data:[
										{value:data.reModel.coal, name:'<s:message code="煤"/>'},
										{value:data.reModel.dioxide, name:'<s:message code="二氧化碳"/>'},
										{value:data.reModel.sulfur, name:'<s:message code="二氧化硫"/>'},
										{value:data.reModel.nitrogen, name:'<s:message code="氮氧化物"/>'},
										{value:data.reModel.dust, name:'<s:message code="碳粉尘"/>'}
									]
								}
							]
						};
						myChart.setOption(option);// 使用刚指定的配置项和数据显示图表。fff
						$("#main").css({"background-color":"#fff","opacity":0.75});//$("#main").css({"opacity":0.75});
					}
				}
			});
		}
	</script>
</head>
<style>
	legend
	{
		font-size: 14px;
		margin-bottom: 16px;
	}
	blockquote{
		font-size: 14px;
	}
	.layui-elem-quote{
		line-height: 10px;
	}
</style>
<%--onload="finddata()"  GetMap--%>
<body onload="finddata();">
<input type="hidden" name="username" id="username"/>
<div id="componentInfo" style="display:none;width: 180px;float:left;z-index:200;"></div>
<div id="inverterInfo" style="display:none;width: 290px;float:right;z-index:201;"></div>
<div class="admin-main" id="station_list_map2">
	<div class="layui-form" id="divSearch" style="display: none;">
		<blockquote class="layui-elem-quote">
			<fieldset class="admin-fieldset">
				<legend><s:message code="查询条件" /></legend>
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label-select"><s:message code="电站名称" /></label>
						<div class="layui-input-inline">
							<input type="text" name="systemName" id="systemName"  class="layui-input"style="width: 100%;">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label-select"><s:message code="电站级别" /></label>
						<div class="layui-input-inline">
							<select id="grade" name="grade" style="height:38px;display:true;">
								<option value=""><s:message code="请选择"/></option>
								<option value="1"><s:message code="微型"/></option>
								<option value="2"><s:message code="小型"/></option>
								<option value="3"><s:message code="中型"/></option>
								<option value="4"><s:message code="大型"/></option>
								<option value="5"><s:message code="特大型"/></option>
							</select>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label-select"><s:message code="国家" /></label>
						<div class="layui-input-inline">
							<select id="guojia" name="guojia" >
								<option value=""><s:message code="请选择" /></option>
							</select>
						</div>
					</div>
					</br>
					<div class="layui-inline">
						<label class="layui-form-label-select"><s:message code="省" /></label>
						<div class="layui-input-inline" style="height:38px;">
							<select id="sheng" name="sheng" >
								<option value=""><s:message code="请选择" /></option>
							</select>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label-select"><s:message code="市" /></label>
						<div class="layui-input-inline" >
							<select id="shi" name="shi" >
								<option value=""><s:message code="请选择" /></option>
							</select>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label-select"><s:message code="详细地址" /></label>
						<div class="layui-input-inline">
							<input type="text" name="dizhi" id="dizhi"  class="layui-input"style="width: 100%;">
						</div>
					</div>
					<div class="layui-inline">
						<div class="layui-input-block">
							<%--<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>--%>
							<button class="layui-btn" onClick=finddata()><s:message code="查询"/></button>
							<button type="reset" class="layui-btn" onClick=resetdata()><s:message code="重置" /></button>
						</div>
					</div>
				</div>
			</fieldset>
		</blockquote>
	</div>
    <div id='printoutPanel'></div>
    <div id='myMap' style='width: 98vw; height: 95.7vh;'></div>
    <script type='text/javascript' src='https://cn.bing.com/api/maps/mapcontrol?key=AuSqp8yFdgmqAlwip_2oCe9zgBFrFIOQ3nXIibLG-U0IjjW0myJqUQ1G1Eiklmtn&callback=loadMapScenario&mkt=${mapmkt}' async defer></script>
</div>
<script type="text/javascript">
	(function() {// 初始化
		DataList.create({ isLaytpl: false, isTable: false, isPager: false,
			listener: function () {
				SelectLink.create({selectItems:[
					{
						url : 'region/queryRegion.web' ,
						selectElem : 'guojia' ,
						queData:{"level":"2"},
						selectId : 'id' ,
						selectName : 'name' ,
						selected : '',
						isFormRenderSelect: true
					},
					{
						url : 'region/queryRegion.web' ,
						selectElem : 'sheng' ,
						queData:{"level":"3"},
						selectId : 'id' ,
						selectName : 'name' ,
						parentElem : 'pid',
						selected : '',
						isFormRenderSelect: true
					},
					{
						url : 'region/queryRegion.web' ,
						selectElem : 'shi' ,
						queData:{"level":"4"},
						selectId : 'id' ,
						selectName : 'name' ,
						parentElem : 'pid',
						selected : '',
						isFormRenderSelect: true
					}
				]});
			}
		});
	}());
</script>
</body>
</html>