<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
        // 新增页面
        $("#inverter-add").click(function () {
            var url = $('#inverter-add').attr("url");
            windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
        });
        // 编辑
        $("#inverter-edit").click(function () {
            var url = $('#inverter-edit').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 查看
        $("#inverter-detail").click(function () {
            var url = $('#inverter-detail').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 选择组串
        $("#inverter-selectComponentGroup").click(function () {
            var url = $('#inverter-selectComponentGroup').attr("url");
            var length = dataList.checkboxData.length;
            if(length != 1){
                com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
                return;
            }
            var id = dataList.checkboxData[0].id;
            layer.open({
                title: '<s:message code="请选择组串" />',
                type: 2,
                area: ['100%', '100%'],
                content: [url + '?id=' + id + "&inverterFlag=selectComponentGroup", 'no']
            });
        });
        // 查看组串
        $("#inverter-componentGroupDetail").click(function () {
            var url = $("#inverter-componentGroupDetail").attr("url");
            var length = dataList.checkboxData.length;
            if(length != 1){
                com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
                return;
            }
            var id = dataList.checkboxData[0].id;
	        layer.tab({
		        area: ['100%', '100%'],
		        tab: [{
			        title: '<s:message code="采集器"/>',
			        content: '<iframe id="cloudterminal" name="cloudterminal" frameborder="0" height="560"  scrolling="no" width="100%" src="inverter/selectCloudList.htm?id='+ id +'"></iframe>'
		        }, {
			        title: '<s:message code="查看组串"/>',
			        content: '<iframe id="componentIframe" name="componentIframe" frameborder="0" height="560"  scrolling="no" width="100%" src="'+url+'?id='+ id +'&inverterFlag=detailComponentGroup"></iframe>'
		        }]
	        });
        });
		// 选择采集器
		$("#inverter-selectCloud").click(function () {
			var url = $('#inverter-selectCloud').attr("url");
			var length = dataList.checkboxData.length;
			if(length != 1){
				com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
				return;
			}
			var id = dataList.checkboxData[0].id;
			layer.open({
				title: '<s:message code="请选择采集器" />',
				type: 2,
				area: ['100%', '100%'],
				content: [url + '?id=' + id + "&selectInverter=selectInverter&leftMenuId=${leftMenuId}", 'no']
			});
		});
		// 查看采集器
		<%--$("#inverter-cloudDetail").click(function () {--%>
			<%--var url = $("#inverter-cloudDetail").attr("url");--%>
			<%--var length = dataList.checkboxData.length;--%>
			<%--if(length != 1){--%>
				<%--com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');--%>
				<%--return;--%>
			<%--}--%>
			<%--var id = dataList.checkboxData[0].id;--%>
			<%--layer.open({--%>
				<%--title: '<s:message code="查看采集器" />',--%>
				<%--type: 2,--%>
				<%--area: ['100%', '100%'],--%>
				<%--content: [url + '?id=' + id + "&inverterFlag=detailComponentGroup", 'no']--%>
			<%--});--%>
		<%--});--%>
         //下载模板
		 $("#inverter-downfile").click(function () {
            var url = $('#inverter-downfile').attr("url");
         
            windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
        });
        // 删除
        $("#inverter-delete").click(function () {
            var url = $('#inverter-delete').attr("url");
            operationDb( url , dataList ,  { "isDelete" : 2, "leftMenuId": '${leftMenuId}'} );
        });
        // 导入
        $('#inverter-import').click(function(){
			var url = $('#inverter-import').attr("url");
			doPost(url , 'text' ,  {} , function(data){
				 inverterUpload = $.layerOpenCustom({
			        title: '<s:message code="导入逆变器" />' , //不显示标题栏
			        area: '600px;' ,
			        id: 'setterSave' ,//设定一个id，防止重复弹出
			        content: data
				});
			},function(data){
				
			}); 
		});
    });
	</script>
</head>
<body>
	<div class="admin-main" id="inverter_list">
		<%-- <s:message code="查询条件" />区域 --%>
		<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="inverterListForm" id="inverterListForm">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="录入时间" /></label>
							<div class="layui-input-inline" >
								<input type="text" name="createTimeStart" id="createTimeStart" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'createTimeEnd\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" placeholder="<s:message code="录入开始时间" />" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
							<div class="layui-form-mid">-</div>
							<div class="layui-input-inline">
								<input type="text" name="createTimeEnd" id="createTimeEnd" placeholder="<s:message code="录入结束时间" />" onclick="WdatePicker({minDate:'#F{$dp.$D(\'createTimeStart\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="生产商" /></label>
							<div class="layui-input-inline">
								<input type="text" name="producers" id="producers" placeholder="<s:message code="生产商" />"    autocomplete="off" class="layui-input"/>
								<%--<select id="producers" name="producers" lay-verify="" >--%>
									<%--<option value=""><s:message code="请选择" /></option>--%>
								<%--</select>--%>
							</div>
						</div>
						<%-- <div class="layui-inline">
							<label class="layui-form-label"><s:message code="编号" /></label>
							<div class="layui-input-inline">
								<input type="text" name="inverterNo" id="inverterNo" placeholder="<s:message code="请输入编号" />" class="layui-input" style="width: 100%;">
							</div>
						</div> --%>
						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		</blockquote>
		<%-- 操作功能 --%>
		<div>
			<jsp:include page="/functionBuilder.htm"></jsp:include>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="inverterList" lay-filter="inverterList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="inverterListChooseAll" id="inverterListChooseAll" lay-skin="primary" /></th>
					<%-- <th><s:message code="编号" /></th> --%>
					<th><s:message code="名称" /></th>
					<th><s:message code="逆变器标识" /></th>
					<th><s:message code="生产商" /></th>
					<th><s:message code="型号" /></th>
					<th><s:message code="控制器" /></th>
					<th><s:message code="额定功率" /></th>
					<th><s:message code="电站名称" /></th>
					<th><s:message code="录入人" /></th>
					<th><s:message code="录入时间" /></th>
				</tr>
			</thead>
			<tbody id="inverterListBody"></tbody>
		</table>
		<script type="text/html" id="inverterListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="inverterList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="inverterList_check_{{r.id}}" id="inverterList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				
				<td data-field="inverterName_{{r.inverterName}}">{{r.inverterName}}</td>
				<td data-field="chipId_{{r.chipId}}">
					{{# if(!isNull(r.chipId)){ }}
					{{r.chipId}}
					{{# }  }}
				</td>
				<td data-field="producers_{{r.producers}}">{{r.producers}}</td>
				<td data-field="model_{{r.model}}">{{r.model}}</td>
				<td data-field="controller_{{r.controller}}">{{r.controllerCh}}</td>
				<td data-field="power_{{r.power}}">{{r.power}}</td>
				<td data-field="powerStationName_{{r.powerStationName}}">{{r.powerStationName}}</td>
				<td data-field="createUserName_{{r.createUserName}}">
					{{# if(!isNull(r.createUserName)){ }}
						{{r.createUserName}}
					{{# }  }}
				</td>
				<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="9"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "inverter/queryInverterList.web",
					defaultTableId : 'inverterList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'inverterListBody',
					formName : 'inverterListForm',
					isTable : false,
					laytplTemplet : 'inverterListTemplet'
				});
//				$.selectOption({
//					url : 'dic/queryDicList.web' ,
//					selectElem : 'producers' ,
//					queData:{"dicId":"producer"},
//					selectId : 'dicValueId' ,
//					selectName : 'dicValueLabel' ,
//					selected : '',
//					isFormRenderSelect: true
//				});
			}())
		</script>
	</div>
</body>
</html>