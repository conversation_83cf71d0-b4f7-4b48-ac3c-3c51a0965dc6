<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
        // 确认
        $("#selectComponentGroupComfirm").click(function () {
            var url = $('#selectComponentGroupComfirm').attr("url");
            // 列表数据id  是多个以逗分隔值
            var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
            var length = dataList.checkboxData.length;
            var realData = { "id":ids , "inverterFlag":'${inverterFlag}' , "inverterId":'${inverterId}'};
            if (length > 0 ){
                layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index ){
                    doPost( url ,  null , realData , function(data){
                        if(data.rec == 'SUC') {
                            com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
                            dataList.checkboxData = [];
                            dataList.laytplShow();
                            layer.closeAll();
                        }
                    },function(data){ });
                });
            } else {
                layer.msg('<s:message code="请选择需要操作的数据"/>');
            }
        });
        // 移除
        $("#moveComponentGroupComfirm").click(function () {
            var url = $('#moveComponentGroupComfirm').attr("url");
            // 列表数据id  是多个以逗分隔值
            var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
            var length = dataList.checkboxData.length;
            var realData = {  "id":ids ,"inverterFlag":'${inverterFlag}' };
            if (length > 0 ){
                layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index ){
                    doPost( url ,  null , realData , function(data){
                        if(data.rec == 'SUC') {
                            com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
                            dataList.checkboxData = [];
                            dataList.laytplShow();
                            layer.closeAll();
                        }
                    },function(data){ });
                });
            } else {
                layer.msg('<s:message code="请选择需要操作的数据"/>');
            }
        });
    });
	</script>
</head>
<body>
	<div class="admin-main" id="selectOrMoveComponentGroupList">
		<%-- 操作功能 --%>
		<div>
			<c:if test="${inverterFlag eq 'selectComponentGroup'}">
				<button class="layui-btn" id="selectComponentGroupComfirm" name="selectComponentGroupComfirm" url="componentGroup/selectOrMoveComponentGroup.web">
					<i class="layui-icon">&#xe640;</i><s:message code="确定" />
				</button>
			</c:if>
			<c:if test="${inverterFlag eq 'detailComponentGroup'}">
				<button class="layui-btn" id="moveComponentGroupComfirm" name="moveComponentGroupComfirm" url="componentGroup/selectOrMoveComponentGroup.web">
					<i class="layui-icon">&#xe640;</i><s:message code="移除" />
				</button>
			</c:if>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="componentGroupList" lay-filter="componentGroupList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="componentGroupListChooseAll" id="componentGroupListChooseAll" lay-skin="primary" /></th>
					<%-- <th><s:message code="编号" /></th> --%>
					<th><s:message code="组串名称" /></th>
					<th><s:message code="逆变器" /></th>
					<th><s:message code="类型" /></th>
					<th><s:message code="录入人" /></th>
					<th><s:message code="录入时间" /></th>
				</tr>
			</thead>
			<tbody id="componentGroupListBody"></tbody>
		</table>
		<script type="text/html" id="componentGroupListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="componentGroupList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="componentGroupList_check_{{r.id}}" id="componentGroupList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				
				<td data-field="groupName_{{r.groupName}}">{{r.groupName}}</td>
				<td data-field="inverterName_{{r.inverterName}}">{{r.inverterName}}</td>
				<td data-field="groupType_{{r.groupType}}">
					{{# if(!isNull(r.groupType) && 1 == r.groupType ){ }}
						<s:message code="独立"/>
					{{# }  }}
					{{# if(!isNull(r.groupType) && 2 == r.groupType ){ }}
					    <s:message code="并联"/>
					{{# }  }}
				</td>
				<td data-field="createUserName_{{r.createUserName}}">
					{{# if(!isNull(r.createUserName)){ }}
						{{r.createUserName}}
					{{# }  }}
				</td>
				<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="7"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "componentGroup/queryComponentGroupList.web",
					defaultTableId : 'componentGroupList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'componentGroupListBody',
					formName : 'componentGroupListForm',
                    laytplPutextra : {"inverterFlag":'${inverterFlag}' , "inverterId":'${inverterId}'},
					isTable : false,
					laytplTemplet : 'componentGroupListTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>