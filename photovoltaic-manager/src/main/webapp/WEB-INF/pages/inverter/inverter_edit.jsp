<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@page import="com.ymx.common.utils.PageUtil" %>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.upload.plugin.js"></script>
<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
		 $(function() {
				$("#icon-save").click(function()
				{
				   var num = $("#inverterNo").val();
				   var model = $("#model").val();
				   var serialNo = $("#serialNo").val();
				   var url = "inverter/validateinverter.web";
					<%--$.ajax({--%>
				      <%--url:url+'?id=${info.id}&num='+num+'&model='+model+'&serialNo='+serialNo,--%>
				      <%--data:'',--%>
				      <%--type:'POST',--%>
				      <%--dataType:'json',--%>
				      <%--async:false,--%>
				      <%--success:function(data){--%>
				          <%--if(data.message == 'success')--%>
				          <%--{--%>
				             <%--formSubmit({--%>
								<%--"formId":"inverterForm" ,--%>
								<%--"url":"inverter/saveOrUpdate.web" ,--%>
								<%--"backListUrl":"inverter/list.htm" ,--%>
								<%--"leftMenuId":'${leftMenuId}'--%>
							<%--});--%>
				          <%--}--%>
				          <%--else--%>
				          <%--{--%>
				              <%--alert(data.message);--%>
				          <%--}--%>
				      <%--}--%>
				     <%--});--%>
					formSubmit({
						"formId":"inverterForm" ,
						"url":"inverter/saveOrUpdate.web" ,
						"backListUrl":"inverter/list.htm" ,
						"leftMenuId":'${leftMenuId}'
					});
				});
				$("#icon-back").click(function() {// 返回
					windowLocaltionHref("inverter/list.htm", "&leftMenuId=${leftMenuId}")
				});
			});
</script>
</head>
<body>
<div class="admin-main" id="inverterSaveOrUpdate">
	<form action="" class="layui-form" id="inverterForm" name="inverterForm" >
		<%-- 隐藏属性 --%>
		<input type="hidden" name="id" id="id" value="${info.id}"/>
		<input type="hidden" name="picture" id="picture"/>
		<div class="layui-form-item">
			<%-- <div class="layui-inline">
				<label class="layui-form-label"><s:message code="编号" /></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="inverterNo" id="inverterNo" desc="编号" lay-verify="isEmpty" maxlength="30"
					       value="${info.inverterNo}" autocomplete="off" class="layui-input"/>
				</div>
			</div> --%>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="名称" /></label>
				<div class="layui-input-inline">
					<input type="text"
						   <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="inverterName" id="inverterName" desc="<s:message code="名称" />" lay-verify="isEmpty" value="${info.inverterName}"
						   autocomplete="off" class="layui-input"/>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="生产商" /></label>
				<div class="layui-input-inline">
					<%--<select id="producers" name="producers" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>>--%>
						<%--<option value=""><s:message code="请选择" /></option>--%>
					<%--</select>--%>
					<input type="text" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="producers" id="producers" maxlength="50"
					       desc="<s:message code="生产商" />"  lay-verify="isEmpty" value="${info.producers}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="额定功率" /></label>
				<div class="layui-input-inline">
					<input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="power" id="power"  maxlength="11" lay-verify="isEmpty"
					       value="${info.power}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="型号" /></label>
				<div class="layui-input-inline">
					<%--<select id="model" name="model" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>>--%>
						<%--<option value=""><s:message code="请选择" /></option>--%>
					<%--</select>--%>
					<input type="text"  <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="model" id="model" desc="<s:message code="型号" />"
					       maxlength="50" lay-verify="isEmpty" value="${info.model}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="序列号" /></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="serialNo" id="serialNo" desc="序列号" maxlength="11" lay-verify="isEmpty"
					       value="${info.serialNo}" autocomplete="off" class="layui-input"/>
				</div>
			</div>

			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="控制器" /></label>
				<div class="layui-input-inline">
					<select id="controller" name="controller" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>>
						<option value=""><s:message code="请选择" /></option>
					</select>
				</div>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="逆变器标识" /></label>
				<div class="layui-input-inline">
					<input type="text" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="chipId" id="chipId" desc="<s:message code="逆变器标识" />" value="${info.chipId}" autocomplete="off"
					       lay-verify="isEmpty" class="layui-input"/>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="创建人" /></label>
				<div class="layui-input-inline">
					<input type="text" disabled="disabled" name="createUserName" id="createUserName" desc="<s:message code="创建人" />" value="${info.createUserName}" autocomplete="off"
					       class="layui-input"/>
				</div>
			</div>
			<c:if test="${operator == 'detail'}">
				<div class="layui-inline">
					<label class="layui-form-label"><s:message code="创建时间" /></label>
					<div class="layui-input-inline">
						<input type="text"
						   <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="createTimeCh" id="createTimeCh" desc="<s:message code="创建时间" />" lay-verify="isEmpty"
						   value="${info.createTimeCh}" autocomplete="off" class="layui-input"/>
					</div>
				</div>
			</c:if>
		</div>
		<%--操作按钮 --%>
		<div class="layui-form-item">
			<div class="layui-input-block">
				<c:if test="${operator != 'detail'}"><a id="icon-save" class="layui-btn" lay-submit lay-filter="inverterForm"><s:message code="保存" /></a></c:if>
				<a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a>
			</div>
		</div>
	</form>
</div>
<script type="text/javascript">
	(function() {
		DataList.create({ isLaytpl: false, isTable: false, isPager: false ,
            listener:function()	{
                $.selectOption({
                    url : 'dic/queryDicList.web' ,
                    selectElem : 'controller' ,
                    queData:{"dicId":"controller"},
                    selectId : 'dicValueId' ,
                    selectName : 'dicValueLabel' ,
                    selected : '${info.controller}',
                    isFormRenderSelect: true
                });
	            <%--$.selectOption({--%>
		            <%--url : 'dic/queryDicList.web' ,--%>
		            <%--selectElem : 'model' ,--%>
		            <%--queData:{"dicId":"model"},--%>
		            <%--selectId : 'dicValueId' ,--%>
		            <%--selectName : 'dicValueLabel' ,--%>
		            <%--selected : '${info.model}',--%>
		            <%--isFormRenderSelect: true--%>
	            <%--});--%>
	            <%--$.selectOption({--%>
		            <%--url : 'dic/queryDicList.web' ,--%>
		            <%--selectElem : 'producers' ,--%>
		            <%--queData:{"dicId":"producer"},--%>
		            <%--selectId : 'dicValueId' ,--%>
		            <%--selectName : 'dicValueLabel' ,--%>
		            <%--selected : '${info.producers}',--%>
		            <%--isFormRenderSelect: true--%>
	            <%--});--%>

            }
		});
	}());
</script>
</body>
</html>


