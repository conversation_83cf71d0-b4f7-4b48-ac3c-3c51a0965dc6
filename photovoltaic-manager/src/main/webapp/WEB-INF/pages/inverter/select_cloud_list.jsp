<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@page import="com.ymx.common.utils.PageUtil"%><%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		$(function(){
			// 确认
			$("#selectCloudComfirm").click(function () {
				var url = $("#selectCloudComfirm").attr("url");
				// 列表数据id  是多个以逗分隔值
				var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
				var length = dataList.checkboxData.length;
				var realData = { "inverterId":'${id}' , "id":ids };
				if (length > 0 )
				{
					layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index )
					{
						doPost( url ,  null , realData , function(data)
						{
							if(data.rec == 'SUC')
							{
								com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
								dataList.checkboxData = [];
								dataList.laytplShow();
								layer.closeAll();
							}
						},function(data){ });
					});
				}
				else {
					layer.msg('<s:message code="请选择需要操作的数据"/>');
				}
			});
			// 删除
			$("#moveCloudComfirm").click(function () {
				var url = $("#moveCloudComfirm").attr("url");
				// 列表数据id  是多个以逗分隔值
				var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
				var length = dataList.checkboxData.length;
				var realData = { "inverterId":'' , "id":ids };
				if (length > 0 )
				{
					layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index )
					{
						doPost( url ,  null , realData , function(data)
						{
							if(data.rec == 'SUC')
							{
								com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
								dataList.checkboxData = [];
								dataList.laytplShow();
								layer.closeAll();
							}
						},function(data){ });
					});
				}
				else {
					layer.msg('<s:message code="请选择需要操作的数据"/>');
				}
			});
			// 返回
			$("#icon-back").click(function() {
				windowLocaltionHref("inverter/list.htm", "&leftMenuId=164FF0A56050F26F943F0A3CF1AB2B75");//${leftMenuId}
			});
		});
	</script>
</head>
<body topmargin="0" leftmargin="0" rightmargin="0" bottomMargin="0" style="height:100%;">
<div class="admin-main" id="selectCloudComfirmList">
	<%-- 操作功能 --%>
	<div>
		<div class="layui-btn-group">
			<c:if test="${selectInverter  eq 'selectInverter'}">
				<button class="layui-btn" id="selectCloudComfirm" name="selectCloudComfirm" url="inverter/selectOrMoveCloudTerminal.web">
					<i class="layui-icon">&#xe640;</i><s:message code="确认" />
				</button>
			</c:if>
			<c:if test="${selectInverter  != 'selectInverter'}">
				<button class="layui-btn" id="moveCloudComfirm" name="selectCloudComfirm" url="inverter/selectOrMoveCloudTerminal.web">
					<i class="layui-icon">&#xe640;</i><s:message code="移除" />
				</button>
			</c:if>
			<%--<a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a>--%>
			<%--<a href="javascript:;" class="layui-btn" onClick="javascript:history.back(-1);">返回</a>--%>
		</div>
	</div>
	<%-- 数据列表展示 --%>
	<table class="layui-table" id="cloudList" lay-filter="cloudList" lay-even>
		<thead>
		<tr>
			<th><input type="checkbox" name="cloudListChooseAll" id="cloudListChooseAll" lay-skin="primary" /></th>
			<%-- <th><s:message code="编号" /></th> --%>
			<th><s:message code="名称" /></th>
			<th><s:message code="序列号" /></th>
			<th><s:message code="所属电站" /></th>
			<th><s:message code="创建时间" /></th>
		</tr>
		</thead>
		<tbody id="cloudListBody"></tbody>
	</table>
	<script type="text/html" id="cloudListTemplet">
		{{# layui.each( d , function( index , r ) { }}
		<tr id="cloudList_tr_{{r.id}}">
			<td data-field="id_{{r.id}}"><input type="checkbox" name="cloudList_check_{{r.id}}" id="cloudList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>

			<td data-field="cloudName_{{r.cloudName}}">{{r.cloudName}}</td>
			<td data-field="serialNo_{{r.serialNo}}">{{r.serialNo}}</td>
			<td data-field="powerStationName_{{r.powerStationName}}">{{r.powerStationName}}</td>
			<td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
		</tr>
		{{#  });  }}
		<%-- 列表无数据 --%>
		{{# if(d.length === 0){ }}
		<tr >
			<td colspan="6"><s:message code="暂无数据"/>!</td>
		</tr>
		{{# } }}
	</script>
	<%-- 分页显示 --%>
	<div align="center" id="page"></div>
	<script>
		//初始化
		(function() {
			dataList = DataList.create({
				url : "cloudter/queryCloudTerminalList.web",
				defaultTableId : 'cloudList',
				isPager : true,
				isLaytpl : true,
				laytplPutextra: {"selectInverter":'${selectInverter}','inverterId':'${inverterId}'},
				laytplAppendHtml : 'cloudListBody',
				formName : 'cloudListForm',
				isTable : false,
				laytplTemplet : 'cloudListTemplet'
			});
		}())
	</script>
</div>
</body>
</html>