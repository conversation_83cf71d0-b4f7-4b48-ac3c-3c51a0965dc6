<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">

	function checkIsSingle() {
           var length = dataList.checkboxData.length;
            if(length != 1){
             com.ymx.layui.public.core.layer.msg('<s:message code="一次只能操作一条数据"/>!');
            return false;
            }
            return true;
        }

	$(function(){

	         // 刷新按钮
             $("#queryTask-refresh").click(function () {
                        var url = $('#queryTask-refresh').attr("url");
                        windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
              });

               // 删除
              $("#queryTask-delete").click(function () {
                  if(checkIsSingle())
                  {
                    var url = $('#queryTask-delete').attr("url");
                    var taskId = getStrAttrVal({data :dataList.checkboxData , "attrName":"taskId"});
                    operationDb(url , dataList , { "isDelete" : 2 , "leftMenuId": '${leftMenuId}',"taskId":taskId});
                  }
              });

                 // 重试
              $("#queryTask-retry").click(function () {
                  if(checkIsSingle())
                  {
                  var status = getStrAttrVal({data :dataList.checkboxData , "attrName":"status"});
                  if(status ==0 || status == -1 )
                   {
                    com.ymx.layui.public.core.layer.msg('<s:message code="任务没有完成，无需重试"/>!');
                     return;
                   }
                   var url = $('#queryTask-retry').attr("url");
                   var taskId = getStrAttrVal({data :dataList.checkboxData , "attrName":"taskId"})  ;
                   operationDb(url , dataList , { "isDelete" : 2 , "leftMenuId": '${leftMenuId}',"taskId":taskId});
                  }

              });

               $("#queryTask-result").click(function ()
               {
                 var queryType = getStrAttrVal({data :dataList.checkboxData , "attrName":"queryType"}) ;
                 var type = getStrAttrVal({data :dataList.checkboxData , "attrName":"type"}) ;
                 var queryId = getStrAttrVal({data :dataList.checkboxData , "attrName":"queryId"}) ;
                 var imei = getStrAttrVal({data :dataList.checkboxData , "attrName":"imei"}) ;
                 var taskId = getStrAttrVal({data :dataList.checkboxData , "attrName":"taskId"}) ;

                  var url = $('#queryTask-result').attr("url");
                  url=url+"?queryType="+queryType+"&type="+type+"&queryId="+queryId+"&imei="+imei+"&taskId="+taskId;
                  goToUrl(dataList , {"url":url, "leftMenuId":'${leftMenuId}' });
                });

                 $("#queryTask-end").click(function () {
                    if(checkIsSingle())
                    {
                     var status = getStrAttrVal({data :dataList.checkboxData , "attrName":"status"}) ;
                     if(status==1)
                     {
                      com.ymx.layui.public.core.layer.msg('<s:message code="任务已结束，无需再次结束"/>!');
                      return;
                     }

                     if(status==-1)
                     {
                          com.ymx.layui.public.core.layer.msg('<s:message code="任务正处查询状态，请稍后再结束"/>!');
                          return;
                     }
                      var url = $('#queryTask-end').attr("url");
                      var taskId = getStrAttrVal({data :dataList.checkboxData , "attrName":"taskId"})  ;
                      operationDb(url , dataList , { "isDelete" : 2 , "leftMenuId": '${leftMenuId}',"taskId":taskId});
                    }
                   });

    });
	</script>
</head>
<body>
	<div class="admin-main" id="queryTask_list">

         <%-- 操作功能 --%>
        <div>
         <jsp:include page="/functionBuilder.htm"></jsp:include>
        </div>

		<%-- 数据列表展示 --%>
		<table class="layui-table" id="queryTaskList" lay-filter="queryTaskList" lay-even>
			<thead>
				<tr>
				<th><input type="checkbox" name="queryTaskListChooseAll" id="queryTaskListChooseAll" lay-skin="primary" /></th>
					<th><s:message code="任务标识" /></th>
					<th><s:message code="查询标识" /></th>
				    <th><s:message code="查询标识类型" /></th>
					<th><s:message code="查询类型" /></th>
					<th><s:message code="采集器imei" /></th>
					<th><s:message code="状态" /></th>
					<th><s:message code="任务开始时间" /></th>
					<th><s:message code="更新时间" /></th>

				</tr>
			</thead>
			<tbody id="queryTaskListBody"></tbody>
		</table>
		<script type="text/html" id="queryTaskListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="queryTaskList_tr_{{r.taskId}}">
			   <td data-field="index_{{index}}"><input type="checkbox" name="queryTaskList_check_{{r.taskId}}" id="queryTaskList_check_{{r.taskId}}" lay-skin="primary" value="{{r.taskId}}" /></td>
			   <td data-field="taskId_{{r.taskId}}">{{r.taskId}}</td>
			   <td data-field="queryId_{{r.queryId}}">{{r.queryId}}</td>
				 <td data-field="type_{{r.type}}">
				 {{# if(1 == r.type  ){ }}
                   <s:message code="采集器"/>
                   {{# }  }}
                 {{# if(2 == r.type  ){ }}
                   <s:message code="中继器"/>
                   {{# }  }}
                  {{# if(3 == r.type  ){ }}
                    <s:message code="优化器"/>
                    {{# }  }}
                   {{# if(4 == r.type  ){ }}
                  <s:message code="组串"/>
                    {{# }  }}
				</td>
				<td data-field="queryType_{{r.queryType}}">
               	 {{# if(1 == r.queryType  ){ }}
                         <s:message code="查询自身"/>
                  {{# }  }}
                  {{# if(2 == r.queryType  ){ }}
                     <s:message code="查询所属组件"/>
                  {{# }  }}
               	</td>
                <td data-field="imei_{{r.imei}}">{{r.imei}}</td>
               	<td data-field="status_{{r.status}}">
                  {{# if(0 == r.status  ){ }}
                   <s:message code="未开始"/>
                  {{# }  }}
                   {{# if(1 == r.status  ){ }}
                     <s:message code="查询结束"/>
                  {{# }  }}
                  {{# if(-1 == r.status  ){ }}
                  <s:message code="查询中"/>
                  {{# }  }}
                  {{# if(2 == r.status  ){ }}
                   <s:message code="采集器没有响应，任务停止"/>
                   {{# }  }}
                 </td>

			    <td data-field="beginTime{{r.beginTime}}">
			    {{# if(!isNull(r.beginTime)){ }}
			    {{r.beginTime}}
                {{# }  }}
                 <td data-field="updateTime_{{r.updateTime}}">
                 {{# if(!isNull(r.updateTime)){ }}
                 {{r.updateTime}}
                  {{# }  }}
                 </td>

			    </td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="10"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>

		<script>
			//初始化
			(function () {
				dataList = DataList.create({
					url : "versionQueryTask/queryTask.web",
					defaultTableId : 'queryTaskList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'queryTaskListBody',
					formName : 'queryTaskListForm',
					isTable : false,
					laytplTemplet : 'queryTaskListTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>