<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){

         // 返回
  	     $("#icon-back").click(function() {
				windowLocaltionHref("versionQueryTask/list.htm", "&leftMenuId=${leftMenuId}")
		});

			 // 刷新按钮
         $("#icon-refresh").click(function () {
                      var taskId=$("#taskId").val();
           				dataList = DataList.create({
           					url : "versionQueryTask/queryTaskResult.web",
           					defaultTableId : 'queryTaskList',
           					isPager : true,
           					isLaytpl : true,
           					laytplAppendHtml : 'queryTaskListBody',
           					formName : 'queryTaskListForm',
           					isTable : false,
           				    laytplPutextra: {"taskId":taskId},
           					laytplTemplet : 'queryTaskListTemplet'
           				});
         });

    });
	</script>
</head>
<body>
	<div class="admin-main" id="queryTask_list">


		<%-- <div>
			<jsp:include page="/functionBuilder.htm"></jsp:include>
		</div> --%>
	    <input type="hidden" name="queryType" id="queryType" value="${sessionScope.queryType}"/>
	    <input type="hidden" name="type" id="type" value="${sessionScope.type}"/>
	    <input type="hidden" name="queryId" id="queryId" value="${sessionScope.queryId}"/>
	    <input type="hidden" name="imei" id="imei" value="${sessionScope.imei}"/>
	    <input type="hidden" name="taskId" id="taskId" value="${sessionScope.taskId}"/>

		 <div>
                     <div>
                	<a id="icon-back" class="layui-btn" style="margin: 0px;"><s:message code="返回" /></a>
                	 <a id="icon-refresh" class="layui-btn"  lay-filter="left"><s:message code="刷新" /></a>
                	 </div>
         </div>

		<%-- 数据列表展示 --%>
		<table class="layui-table" id="queryTaskList" lay-filter="queryTaskList" lay-even>
			<thead>
				<tr>
					<th><s:message code="任务标识" /></th>
					<th><s:message code="组件标识" /></th>
				    <th><s:message code="固件版本号" /></th>
					<th><s:message code="分区" /></th>
					<th><s:message code="更新时间" /></th>
				</tr>
			</thead>
			<tbody id="queryTaskListBody"></tbody>
		</table>
		<script type="text/html" id="queryTaskListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			 <tr id="queryTaskList_tr_{{r.taskId}}">
			 <td data-field="taskId_{{r.taskId}}">{{r.taskId}}</td>
				<td data-field="componentId_{{r.componentId}}">{{r.componentId}}</td>
				 <td data-field="softVersion_{{r.softVersion}}">
				{{# if(!isNull(r.softVersion)){ }}
				{{r.softVersion}}
				{{# }  }}
				</td>
				<td data-field="bootPartition_{{r.bootPartition}}">
				{{# if(!isNull(r.bootPartition)){ }}
				{{r.bootPartition}}
				{{# }  }}
				</td>
				<td data-field="updateTime_{{r.updateTime}}">
			    {{# if(!isNull(r.updateTime)){ }}
				{{r.updateTime}}
				{{# }  }}
				</td>
			</tr>
			{{#  });  }}
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="10"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>

		<script>
			//初始化
			(function () {
			var queryType=$("#queryType").val();
			var type=$("#type").val();
			var queryId=$("#queryId").val();
			var imei=$("#imei").val();
			var taskId=$("#taskId").val();

				dataList = DataList.create({
					url : "versionQueryTask/queryTaskResult.web",
					defaultTableId : 'queryTaskList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'queryTaskListBody',
					formName : 'queryTaskListForm',
					isTable : false,
				    laytplPutextra: {"queryType":queryType,"type":type,"queryId":queryId,
				    "imei":imei,"taskId":taskId},
					laytplTemplet : 'queryTaskListTemplet'
				});
			}())
		</script>
	</div>
</body>
</html>