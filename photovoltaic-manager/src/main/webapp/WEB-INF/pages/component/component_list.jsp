<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>

	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>

	<script type="text/javascript">
	$(function(){
	    // 新增页面
		$("#component-add").click(function () {
            var url = $('#component-add').attr("url");
            windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
        });
		// 编辑
        $("#component-edit").click(function () {
            var url = $('#component-edit').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 查看
        $("#component-detail").click(function () {
            var url = $('#component-detail').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 删除
        $("#component-delete").click(function () {
            var url = $('#component-delete').attr("url");
            operationDb( url , dataList ,  { "isDelete" : 2 , "leftMenuId": '${leftMenuId}'} );
        });
        //下载模板
		 $("#component-downfile").click(function () {
            var url = $('#component-downfile').attr("url");
         
            windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
        });
        // 导入
        $('#component_import').click(function(){
			var url = $('#component_import').attr("url");
			doPost(url , 'text' ,  {} , function(data){
				 componentUpload = $.layerOpenCustom({
			        title: '<s:message code="导入组件" />' , //不显示标题栏
			        area: '600px;' ,
			        id: 'setterSave' ,//设定一个id，防止重复弹出
			        content: data
				});
			},function(data){
				
			}); 
		});
               
			   // 检查操作了几条数据
		       function checkIsSingle() {
                       var length = dataList.checkboxData.length;
                        if(length != 1){
                         com.ymx.layui.public.core.layer.msg('<s:message code="一次只能操作一条数据"/>!');
                        return false;
                        }
                        return true;
                  }

                             // 查询版本
                           $("#version-query").click(function () {
                     
					        if(checkIsSingle())
                           {
                             layer.open({
                                           title: '查询版本',
                                           offset: 'auto',
      			                         //area : ['380px', '170px'],
                                           btn: ['查询自身版本','取消'],
                                           btnAlign: 'c',
                                           btn1: function (index, layero) {
                                              var queryType=1;
                                                versionQuery(queryType);
                                                layer.close(index);
                                               },
                                             btn2: function (index, layero) {
                                                 layer.close(index);
                                                        },
                                       });
						   }
                           });

    });

      function versionQuery(queryType)
            {
               var url = $('#version-query').attr("url");
               var imei = getStrAttrVal({data :dataList.checkboxData , "attrName":"imei"}) ;
               var chipId = getStrAttrVal({data :dataList.checkboxData , "attrName":"chipId"}) ;
               operationDb( url , dataList ,  { "isDelete" : 2 , "leftMenuId": '${leftMenuId}',
                "imei": imei,"queryId": chipId,"queryType":queryType,"type":3 } );
            }

	</script>
</head>
<body>
	<div class="admin-main" id="component_list">
		<%-- <s:message code="查询条件" />区域 --%>
		<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="componentListForm" id="componentListForm">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="录入时间" /></label>
							<div class="layui-input-inline" >
								<input type="text" name="createTimeStart" id="createTimeStart" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'createTimeEnd\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" placeholder="<s:message code="录入开始时间" />" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
							<div class="layui-form-mid">-</div>
							<div class="layui-input-inline">
								<input type="text" name="createTimeEnd" id="createTimeEnd" placeholder="<s:message code="录入结束时间" />" onclick="WdatePicker({minDate:'#F{$dp.$D(\'createTimeStart\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="电站名称" /></label>
							<div class="layui-input-inline">
								<input type="text" name="powerStationName" id="powerStationName" placeholder="<s:message code="电站名称" />" autocomplete="off" class="layui-input"/>
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="生产商" /></label>
							<div class="layui-input-inline">
								<%--<select id="producers" name="producers" lay-verify="" >--%>
									<%--<option value=""><s:message code="请选择" /></option>--%>
								<%--</select>--%>
								<input type="text" name="producers" id="producers" placeholder="<s:message code="生产商" />"    autocomplete="off" class="layui-input"/>
							</div>
						</div>

						<div class="layui-inline">
							<label class="layui-form-label-select"> &nbsp; &nbsp; &nbsp; &nbsp;<s:message code="型号" /> </label>
							<div class="layui-input-inline">
								<%--<select id="model" name="model" lay-verify="" >--%>
									<%--<option value=""><s:message code="请选择" /></option>--%>
								<%--</select>--%>
								<input type="text" name="model" id="model" placeholder="<s:message code="型号" />"    autocomplete="off" class="layui-input"/>
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="组件坐标" /> </label>
							<div class="layui-input-inline">
								<%--<select id="model" name="model" lay-verify="" >--%>
								<%--<option value=""><s:message code="请选择" /></option>--%>
								<%--</select>--%>
								<input type="text" name="chipId" id="chipId" placeholder="<s:message code="组件坐标" />"    autocomplete="off" class="layui-input"/>
							</div>
						</div>

						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		</blockquote>
		<%-- 操作功能 --%>
		<div>
			<jsp:include page="/functionBuilder.htm"></jsp:include>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="componentList" lay-filter="componentList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="componentListChooseAll" id="componentListChooseAll" lay-skin="primary" /></th>
					<th><s:message code="组件坐标" /></th>
					<th><s:message code="所属组串" /></th>
					<th><s:message code="电站名称" /></th>
				    <th><s:message code="采集器标识" /></th>
					<th><s:message code="软件版本号" /></th>
                    <th><s:message code="分区" /></th>
                    <th><s:message code="录入时间" /></th>
                     <th><s:message code="更新时间" /></th>
				</tr>
			</thead>
			<tbody id="componentListBody"></tbody>
		</table>
		<script type="text/html" id="componentListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="componentList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="componentList_check_{{r.id}}" id="componentList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				<td data-field="chipId_{{r.chipId}}">{{r.chipId}}</td>
				<td data-field="belongsGroupName_{{r.belongsGroupName}}">{{r.belongsGroupName}}</td>
				<td data-field="powerStationName_{{r.powerStationName}}">{{r.powerStationName}}</td>
				<td data-field="imei_{{r.imei}}">
				{{# if(!isNull(r.imei)){ }}
				{{r.imei}}
				{{# }  }}
				</td>
				<td data-field="softVersion_{{r.softVersion}}">
                {{# if(!isNull(r.softVersion)){ }}
                {{r.softVersion}}
                {{# }  }}
                </td>
                <td data-field="bootPartition_{{r.bootPartition}}">
                {{# if(!isNull(r.bootPartition)){ }}
                {{r.bootPartition}}
                 {{# }  }}
                 </td>
                <td data-field="createTimeCh_{{r.createTimeCh}}">{{r.createTimeCh}}</td>
                <td data-field="versionUpdateTime_{{r.versionUpdateTime}}">
                                	{{# if(!isNull(r.versionUpdateTime)){ }}
                                         {{r.versionUpdateTime}}
                                     {{# }  }}

                 </td>
			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="10"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "component/queryComponentList.web",
					defaultTableId : 'componentList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'componentListBody',
					formName : 'componentListForm',
					isTable : false,
					laytplTemplet : 'componentListTemplet'
				});
//				DataList.create({ isLaytpl: false, isTable: false, isPager: false ,
//					listener:function()	{
						<%--$.selectOption({--%>
							<%--url : 'dic/queryDicList.web' ,--%>
							<%--selectElem : 'model' ,--%>
							<%--queData:{"dicId":"model"},--%>
							<%--selectId : 'dicValueId' ,--%>
							<%--selectName : 'dicValueLabel' ,--%>
							<%--selected : '${info.model}',--%>
							<%--isFormRenderSelect: true--%>
						<%--});--%>
						<%--$.selectOption({--%>
							<%--url : 'dic/queryDicList.web' ,--%>
							<%--selectElem : 'producers' ,--%>
							<%--queData:{"dicId":"producer"},--%>
							<%--selectId : 'dicValueId' ,--%>
							<%--selectName : 'dicValueLabel' ,--%>
							<%--selected : '${info.producers}',--%>
							<%--isFormRenderSelect: true--%>
						<%--});--%>
//					}
//				});
			}())
		</script>
	</div>
</body>
</html>