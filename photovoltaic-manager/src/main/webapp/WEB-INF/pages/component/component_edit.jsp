<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@page import="com.ymx.common.utils.PageUtil" %>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
<jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.upload.plugin.js"></script>
<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
	(function($) {
		$(function() {
			// 保存
			/* $('#icon-save').click(function() {
				formSubmit({
					"formId":"componentForm" ,
					"url":"component/saveOrUpdate.web" ,
					"backListUrl":"component/list.htm" ,
					"leftMenuId":'${leftMenuId}'
				});
			}); */
			$("#icon-save").click(function() 
			{
			   var num = $("#componentNo").val();
			   var chipId = $("#chipId").val();
			   var serialNo = $("#serialNo").val();
			   var model = $("#model").val();
			   var url = "component/validatecomponent.web";
				$.ajax({
					url:url+'?id=${info.id}&num='+num+'&model='+model+'&serialNo='+serialNo+'&chipId='+chipId,
					data:'',
					type:'POST',
					dataType:'json',
					async:false,
					success:function(data){
						if(data.message == 'success')
						{
							 formSubmit({
								"formId":"componentForm" ,
								"url":"component/saveOrUpdate.web" ,
								"backListUrl":"component/list.htm" ,
								"leftMenuId":'${leftMenuId}'
							 });
						}
						else
						{
						  com.ymx.layui.public.core.layer.msg(data.message);
						}
					}
         		 }); 
			});
			// 返回
			$("#icon-back").click(function() {
				windowLocaltionHref("component/list.htm", "&leftMenuId=${leftMenuId}")
			});
		})
	})(jQuery)
</script>
</head>
<body>
<div class="admin-main" id="componentSaveOrUpdate">
	<form action="" class="layui-form" id="componentForm" name="componentForm">
		<%-- 隐藏属性 --%>
		<input type="hidden" name="id" id="id" value="${info.id}"/>
		<input type="hidden" name="picture" id="picture"/>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="编号" /></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="componentNo" id="componentNo" desc="<s:message code="编号" />" lay-verify="isEmpty" maxlength="30"
					       value="${info.componentNo}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="生产商" /></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="producers" id="producers" desc="<s:message code="生产商" />" maxlength="50" lay-verify="isEmpty"
					       value="${info.producers}" autocomplete="off" class="layui-input"/>
					<%--<select id="producers" name="producers" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>>--%>
						<%--<option value=""><s:message code="请选择" /></option>--%>
					<%--</select>--%>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="出厂前序列号" /></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="serialNo" id="serialNo" desc="<s:message code="出厂前序列号" />" maxlength="11" lay-verify="isEmpty"
					       value="${info.serialNo}" autocomplete="off" class="layui-input"/>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="型号" /></label>
				<div class="layui-input-inline">
					<input type="text"
					       <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="model" id="model" desc="<s:message code="型号" />" maxlength="50" lay-verify="isEmpty"
					       value="${info.model}" autocomplete="off" class="layui-input"/>
					<%--<select id="model" name="model" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>>--%>
						<%--<option value=""><s:message code="请选择" /></option>--%>
					<%--</select>--%>
				</div>
			</div>

			<c:if test="${operator == 'detail'}">
				<div class="layui-inline">
					<label class="layui-form-label"><s:message code="采集器标识" /></label>
					<div class="layui-input-inline">
						<input type="text" disabled="disabled"  name="imei" id="imei" desc="<s:message code="采集器标识" />"
							   value="${info.imei}" autocomplete="off" class="layui-input"/>
					</div>
				</div>
			</c:if>
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="组件坐标" /></label>
				<div class="layui-input-inline">
					<input type="text"
						   <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="chipId" id="chipId" desc="<s:message code="组件坐标" />" maxlength="10" lay-verify="isEmpty"
						   value="${info.chipId}" autocomplete="off" class="layui-input"/>
				</div>
			</div>

		</div>

     <c:if test="${operator == 'detail'}">

		<div class="layui-form-item">
          	<div class="layui-inline">
          		<label class="layui-form-label"><s:message code="硬件版本号" /></label>
          		<div class="layui-input-inline">
          			<input type="text" disabled="disabled" name="hardVersion" id="hardVersion" desc="<s:message code="硬件版本号" />" value="${info.hardVersion}" autocomplete="off"
          			       class="layui-input"/>
          		</div>
          	</div>
          		<div class="layui-inline">
          			<label class="layui-form-label"><s:message code="软件版本号" /></label>
          			<div class="layui-input-inline">
          				<input type="text"
          				  disabled="disabled" name="softVersion" id="softVersion" desc="<s:message code="软件版本号" />" lay-verify="isEmpty"
          				   value="${info.softVersion}" autocomplete="off" class="layui-input"/>
          			</div>
          		</div>
          </div>

          <div class="layui-form-item">
            <div class="layui-inline">
            	<label class="layui-form-label"><s:message code="mcu" /></label>
            	<div class="layui-input-inline">
            		<input type="text" disabled="disabled" name="mcu" id="mcu" desc="<s:message code="mcu" />" value="${info.mcu}" autocomplete="off"
            		       class="layui-input"/>
            	</div>
            </div>
            	<div class="layui-inline">
            		<label class="layui-form-label"><s:message code="bomId" /></label>
            		<div class="layui-input-inline">
            			<input type="text"
            			  disabled="disabled" name="bomId" id="bomId" desc="<s:message code="bomId" />" lay-verify="isEmpty"
            			   value="${info.bomId}" autocomplete="off" class="layui-input"/>
           	</div>
                  </div>
          </div>

        <div class="layui-form-item">
           <div class="layui-inline">
           	<label class="layui-form-label"><s:message code="分区" /></label>
           	<div class="layui-input-inline">
           		<input type="text" disabled="disabled" name="bootPartition" id="bootPartition" desc="<s:message code="分区" />" value="${info.bootPartition}" autocomplete="off"
           		       class="layui-input"/>
           	</div>
           </div>
           	<div class="layui-inline">
           		<label class="layui-form-label"><s:message code="更新时间" /></label>
           		<div class="layui-input-inline">
           			<input type="text"
           			  disabled="disabled" name="versionUpdateTime" id="versionUpdateTime" desc="<s:message code="更新时间" />" lay-verify="isEmpty"
           			   value="${info.versionUpdateTime}" autocomplete="off" class="layui-input"/>
           	</div>
                 </div>
        </div>
        </c:if>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><s:message code="创建人" /></label>
				<div class="layui-input-inline">
					<input type="text" disabled="disabled" name="createUserName" id="createUserName" desc="<s:message code="创建人" />" value="${info.createUserName}" autocomplete="off"
					       class="layui-input"/>
				</div>
			</div>
			<c:if test="${operator == 'detail'}">
				<div class="layui-inline">
					<label class="layui-form-label"><s:message code="创建时间" /></label>
					<div class="layui-input-inline">
						<input type="text"
						   <c:if test="${operator == 'detail'}">disabled="disabled"</c:if> name="createTimeCh" id="createTimeCh" desc="<s:message code="创建时间" />" lay-verify="isEmpty"
						   value="${info.createTimeCh}" autocomplete="off" class="layui-input"/>
					</div>
				</div>
			</c:if>
		</div>



		<%--操作按钮 --%>
		<div class="layui-form-item">
			<div class="layui-input-block">
				<c:if test="${operator != 'detail'}"><a id="icon-save" class="layui-btn" lay-submit lay-filter="componentForm"><s:message code="保存" /></a></c:if>
				<a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a>
			</div>
		</div>
	</form>
</div>
<script type="text/javascript">
	(function() {
		DataList.create({ isLaytpl: false, isTable: false, isPager: false ,
			listener:function()	{
				<%--$.selectOption({--%>
					<%--url : 'dic/queryDicList.web' ,--%>
					<%--selectElem : 'model' ,--%>
					<%--queData:{"dicId":"model"},--%>
					<%--selectId : 'dicValueId' ,--%>
					<%--selectName : 'dicValueLabel' ,--%>
					<%--selected : '${info.model}',--%>
					<%--isFormRenderSelect: true--%>
				<%--});--%>
				<%--$.selectOption({--%>
					<%--url : 'dic/queryDicList.web' ,--%>
					<%--selectElem : 'producers' ,--%>
					<%--queData:{"dicId":"producer"},--%>
					<%--selectId : 'dicValueId' ,--%>
					<%--selectName : 'dicValueLabel' ,--%>
					<%--selected : '${info.producers}',--%>
					<%--isFormRenderSelect: true--%>
				<%--});--%>
			}
		});
	}());
</script>
</body>
</html>


