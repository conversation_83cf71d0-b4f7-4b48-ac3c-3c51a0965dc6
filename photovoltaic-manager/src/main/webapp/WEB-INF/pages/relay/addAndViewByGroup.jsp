<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
        // 选择组串确认
        $("#selectComponentGroupComfirm").click(function () {
            var url = $('#selectComponentGroupComfirm').attr("url");
            // 列表数据id  是多个以逗分隔值
            var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
            var length = dataList.checkboxData.length;
            var realData = { "relayId":'${relayId}'  , "id":ids};
            if (length > 0 ){
                layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index ){
                    doPost( url ,  null , realData , function(data){
                        if(data.rec == 'SUC') {
                            com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
                            dataList.checkboxData = [];
                            dataList.laytplShow();
                            layer.closeAll();
                            componentDataList.laytplShow();

                        }
                    },function(data){ });
                });
            } else {
                layer.msg('<s:message code="请选择需要操作的数据"/>');
            }
        });


        // 组串移除
        $("#moveComponentGroupComfirm").click(function () {
            var url = $('#moveComponentGroupComfirm').attr("url");
            // 列表数据id  是多个以逗分隔值
            var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
            var length = dataList.checkboxData.length;
            // 如果移除组串的话 就不传组串id
            var realData = { "id":ids };
            if (length > 0 ){
                layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index ){
                    doPost( url ,  null , realData , function(data){
                        if(data.rec == 'SUC') {
                            com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
                            dataList.checkboxData = [];
                            dataList.laytplShow();
                            layer.closeAll();
                            componentDataList.laytplShow();
                        }
                    },function(data){ });
                });
            } else {
                layer.msg('<s:message code="请选择需要操作的数据"/>');
            }
        });
		
		
		 // 按组件选择
        $("#byComponent").click(function () {
            var url = $('#byComponent').attr("url");
			
		    layer.open({
                title: false,
                type: 2,
				closeBtn: 0,
                area: ['100%', '100%'],
                content: [url + '?relayId=' + '${relayId}'+ "&operationFlag=${operationFlag}&leftMenuId=${leftMenuId}", 'yes']
            });
			

        });
		
		// 返回
		$("#icon-back").click(function() {
			windowLocaltionHref("relay/list.htm", "&leftMenuId=${leftMenuId}")
		});


    });

	</script>
</head>
<body>
	<div class="admin-main" id="selectOrMoveComponentGroupList">
	
	<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="searchGroupForm" id="searchGroupForm">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="组串名称" /></label>
							<div class="layui-input-inline">
								<input type="text" name="groupName" id="groupName" placeholder="<s:message code="组串名称" />"  autocomplete="off" class="layui-input"/>
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		  </blockquote>
		  
    		<%-- 操作功能 --%>
    		<div>
    			<div class="layui-btn-group">
    				<c:if test="${operationFlag eq 'select'}">
    					<button class="layui-btn" id="selectComponentGroupComfirm" name="selectComponentGroupComfirm" url="relay/changeComponentGroup.web">
    						<i class="layui-icon">&#xe640;</i><s:message code="确认" />
    					</button>
    				</c:if>
    				<c:if test="${operationFlag eq 'view'}">
    					<button class="layui-btn" id="moveComponentGroupComfirm" name="moveComponentGroupComfirm" url="relay/changeComponentGroup.web">
    						<i class="layui-icon">&#xe640;</i><s:message code="移除" />
    					</button>
    				</c:if>
					<button class="layui-btn" id="byComponent" name="byComponent" url="relay/addAndViewByComponent.htm">
    						<i class="layui-icon">&#xe640;</i><s:message code="按组件选择" />
    				</button>
    				 <a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a>
    			</div>
    		</div>
    		<%-- 数据列表展示 --%>
    		<table class="layui-table" id="componentGroupList" lay-filter="componentGroupList" lay-even>
    			<thead>
    				<tr>
    					<th><input type="checkbox" name="componentGroupListChooseAll" id="componentGroupListChooseAll" lay-skin="primary" /></th>
    					<%-- <th><s:message code="编号" /></th> --%>
    					<th><s:message code="组串名称" /></th>
    					<th><s:message code="组件个数" /></th>
    					<th><s:message code="单个组件功率" /></th>
    					<th><s:message code="采集器标识" /></th>
    					<th><s:message code="电站名称" /></th>
    				</tr>
    			</thead>
    			<tbody id="componentGroupListBody"></tbody>
    		</table>
    		<script type="text/html" id="componentGroupListTemplet">
    			{{# layui.each( d , function( index , r ) { }}
    			<tr id="componentGroupList_tr_{{r.id}}">
    				<td data-field="id_{{r.id}}"><input type="checkbox" name="componentGroupList_check_{{r.id}}" id="componentGroupList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>

    				<td data-field="groupName_{{r.groupName}}">{{r.groupName}}</td>
    				<td data-field="groupNum_{{r.groupNum}}">
    				{{# if(!isNull(r.groupNum)){ }}
    				{{r.groupNum}}
    				{{# }  }}
    				</td>
    			    <td data-field="power_{{r.power}}">
                    {{# if(!isNull(r.power)){ }}
                    {{r.power}}
                    {{# }  }}
                    </td>
                    <td data-field="imei_{{r.imei}}">
                     {{# if(!isNull(r.imei)){ }}
                     {{r.imei}}
                     {{# }  }}
                    </td>
                    <td data-field="powerStationName_{{r.powerStationName}}">
                    {{# if(!isNull(r.powerStationName)){ }}
                    {{r.powerStationName}}
                    {{# }  }}
                    </td>
    			</tr>
    			{{#  });  }}
    			<%-- 列表无数据 --%>
    			{{# if(d.length === 0){ }}
    				<tr >
    					<td colspan="7"><s:message code="暂无数据"/>!</td>
    				</tr>
    			{{# } }}
    		</script>
    		<%-- 分页显示 --%>
    		<div align="center" id="page"></div>

    		<script>
    			//初始化
    			(function() {
    				dataList = DataList.create({
    					url : "relay/queryComponentGroupList.web",
    					defaultTableId : 'componentGroupList',
    					isPager : true,
    					isLaytpl : true,
    					laytplAppendHtml : 'componentGroupListBody',
    					formName : 'searchGroupForm',
                        laytplPutextra : {"relayId":'${relayId}',"operationFlag":'${operationFlag}'},
    					isTable : false,
    					laytplTemplet : 'componentGroupListTemplet'
    				});
    			}())
    		</script>
    	</div>


</body>
</html>