<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="com.ymx.common.utils.PageUtil" %>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
<jsp:include page="/include.htm"/>
<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.upload.plugin.js"></script>
<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>

<script type="text/javascript">

layui.use(['form', 'layedit', 'laydate'], function(){
  var form = layui.form
  ,layer = layui.layer
  ,layedit = layui.layedit
  ,laydate = layui.laydate;

  //自定义验证规则

  
  form.on('submit(top)', function(data){

	   var obj = formSerialize("relayForm");
        doPost("relay/saveOrUpdate.web" , null, obj, function(data) {
            if(data.rec === 'SUC') {
				windowLocaltionHref("relay/list.htm", "&leftMenuId=${leftMenuId}");
            } else {
                layer.msg(data.errMsg);
            }
        }, function(data) { });
    console.log(data);
    return false;
  });
  

  	$("#icon-back").click(function() {
				windowLocaltionHref("relay/list.htm", "&leftMenuId=${leftMenuId}")
			});
});
	
</script>

</head>
<body>
<br/>
<br/>
<form class="layui-form" id="relayForm" >
		<input type="hidden" name="id" id="id" value="${info.id}"/>

	  	<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="中继名称" /></label>
			<div class="layui-input-block">
				<input type="text" name="relayName" id="relayName" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					   value="${info.relayName}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入中继名称" />" class="layui-input" />
			</div>
		</div>

		<div class="layui-form-item">
                			<label class="layui-form-label"><s:message code="中继标识" /></label>
                			<div class="layui-input-block">
                				<input type="text" name="relayId" id="relayId" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
                					   value="${info.relayId}" maxlength="30" autocomplete="off" placeholder="<s:message code="请输入中继标识" />" class="layui-input" />
                			</div>
       </div>

	  <c:if test="${operator == 'detail'}">
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="硬件版本号" /></label>
			<div class="layui-input-block">
				<input type="text" name="hardVersion" onkeyup="this.value=this.value.replace(/\D/g,'')" id="power" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
				       value="${info.hardVersion}" maxlength="11" autocomplete="off" placeholder="<s:message code="" />" class="layui-input" />
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="软件版本号" /></label>
			<div class="layui-input-block">
				<input type="text" name="softVersion" id="softVersion"  <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
				       value="${info.softVersion}" autocomplete="off" placeholder="<s:message code="" />" class="layui-input" />
			</div>
		</div>

         <div class="layui-form-item">
        			<label class="layui-form-label"><s:message code="分区" /></label>
        			<div class="layui-input-block">
        						<input type="text" name="bootPartition" id="bootPartition"  disabled="disabled"
                            				       value="${info.bootPartition}" autocomplete="off" placeholder="<s:message code="" />" class="layui-input" />
        			</div>
         </div>

         <div class="layui-form-item">
        			<label class="layui-form-label"><s:message code="mcu" /></label>
        			<div class="layui-input-block">
        				<input type="text" name="mcu" id="mcu"  disabled="disabled"
        				       value="${info.mcu}" autocomplete="off" placeholder="<s:message code="" />" class="layui-input" />
        			</div>
        </div>

        <div class="layui-form-item">
                			<label class="layui-form-label"><s:message code="bomId" /></label>
                			<div class="layui-input-block">
                				<input type="text" name="bomId" id="bomId"  disabled="disabled"
                				       value="${info.bomId}" autocomplete="off" placeholder="<s:message code="" />" class="layui-input" />
                			</div>
        </div>

        <div class="layui-form-item">
                			<label class="layui-form-label"><s:message code="更新时间" /></label>
                			<div class="layui-input-block">
                				<input type="text" name="updateTime" id="updateTime"  disabled="disabled"
                				       value="${info.updateTime}" autocomplete="off" placeholder="<s:message code="" />" class="layui-input" />
                			</div>
        </div>


		<%--<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="组件数量" /></label>
			<div class="layui-input-block"><input type="hidden" id="componentNum" name="componentNum" />
				<input type="text" name="componentNum" id="componentNum" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
					   value="${info.componentNum}" maxlength="30" autocomplete="off" placeholder="<s:message code="" />" class="layui-input" />
			</div>
		</div>--%>
		<div class="layui-form-item">
        			<label class="layui-form-label"><s:message code="电站id" /></label>
        			<div class="layui-input-block"><input type="hidden" id="powerStationId" name="powerStationId" />
        				<input type="text" name="powerStationId" id="powerStationId" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
        					   value="${info.powerStationId}" maxlength="30" autocomplete="off" placeholder="<s:message code="" />" class="layui-input" />
        			</div>
        </div>
        <div class="layui-form-item">
                			<label class="layui-form-label"><s:message code="采集器id" /></label>
                			<div class="layui-input-block"><input type="hidden" id="cloudId" name="cloudId" />
                				<input type="text" name="cloudId" id="cloudId" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
                					   value="${info.cloudId}" maxlength="30" autocomplete="off" placeholder="<s:message code="" />" class="layui-input" />
                			</div>
         </div>

		<%--<div class="layui-form-item">
			<label class="layui-form-label"><s:message code="创建人" /></label>
			<div class="layui-input-block">
				<input type="text" name="createUserName" id="createUserName" lay-verify="isEmpty" disabled="disabled" <c:if test="${operator == 'detail'}"></c:if>
					   value="${info.createUserName}" maxlength="30" autocomplete="off"   class="layui-input" />
			</div>
		</div>--%>
			<div class="layui-form-item">
				<label class="layui-form-label"><s:message code="创建时间" /></label>
				<div class="layui-input-block">
					<input type="text" name="createTime" id="createTime" lay-verify="isEmpty" <c:if test="${operator == 'detail'}">disabled="disabled"</c:if>
						   value="${info.createTime}" maxlength="30" autocomplete="off"   class="layui-input" />
				</div>
			</div>
		</c:if>
		
  <div class="layui-form-item">
     <div class="layui-input-block">
	<c:if test="${operator != 'detail'}"><a id="icon-save" class="layui-btn" lay-submit lay-filter="top"><s:message code="保存" /></a></c:if>
    <!--<button class="layui-btn" lay-submit lay-filter="top">立即提交</button>-->
	<a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a>
	 </div>
  </div>
</form>



</body>
</html>


