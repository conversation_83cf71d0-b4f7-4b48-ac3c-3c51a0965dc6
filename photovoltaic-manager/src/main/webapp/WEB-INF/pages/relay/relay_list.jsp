<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@page import="com.ymx.common.utils.PageUtil"%>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
	    // 新增页面
		$("#relay-add").click(function () {
            var url = $('#relay-add').attr("url");
            windowLocaltionHref(url , "&leftMenuId=${leftMenuId}");
        });
		// 编辑
        $("#relay-edit").click(function () {
            var url = $('#relay-edit').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });
        // 查看
        $("#relay-detail").click(function () {
            var url = $('#relay-detail').attr("url");
            goToUrl(dataList , {"url":url , "leftMenuId":'${leftMenuId}' });
        });

        // 请选择组件
        $("#relay-componentSelect").click(function () {
            var url = $('#relay-componentSelect').attr("url");
            var length = dataList.checkboxData.length;
            if(length != 1){
                com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
                return;
            }
            var relayId = dataList.checkboxData[0].relayId;

            layer.open({
                title: false,
                type: 2,
				closeBtn: 0,
                area: ['100%', '100%'],
                content: [url + '?relayId=' + relayId+ '&operationFlag=select&leftMenuId=${leftMenuId}', 'yes']
            });
        });
        // 查看组件
        $("#relay-componentView").click(function () {

            var url = $('#relay-componentView').attr("url");
            var length = dataList.checkboxData.length;
            if(length != 1){
                com.ymx.layui.public.core.layer.msg('<s:message code="请正确选择操作项"/>!');
                return;
            }
            var relayId = dataList.checkboxData[0].relayId;

            layer.open({
                title: false,
                type: 2,
				closeBtn: 0,
                area: ['100%', '100%'],
                content: [url + '?relayId=' + relayId + "&operationFlag=view&leftMenuId=${leftMenuId}", 'yes']
            });
        });
        // 删除
        $("#relay-delete").click(function () {
            var url = $('#relay-delete').attr("url");
            operationDb( url , dataList ,  { "isDelete" : 2 , "leftMenuId": '${leftMenuId}'} );
        });


         // 查询版本
          $("#version-query").click(function () {
			  if(checkIsSingle())
			  {
				     var queryType=1;
                    layer.open({
                          title: '查询版本',
                          offset: 'auto',
                       //area : ['380px', '170px'],
                          btn: ['查询自身版本', '查询所属组件版本','取消'],
                          btnAlign: 'c',
                          btn1: function (index, layero) {
                               queryType=1;
                               versionQuery(queryType);
                               layer.close(index);
                              },
                           btn2: function (index, layero) {
                                queryType=2;
                                versionQuery(queryType);
                               layer.close(index);
                            },
                            btn3: function (index, layero) {
                                layer.close(index);
                                       },
                      });
			  }
       
          });
		  
		  
    });

     function versionQuery(queryType)
        {
           var url = $('#version-query').attr("url");
           var imei = getStrAttrVal({data :dataList.checkboxData , "attrName":"cloudId"}) ;
           var relayId = getStrAttrVal({data :dataList.checkboxData , "attrName":"relayId"}) ;
           operationDb( url , dataList ,  { "isDelete" : 2 , "leftMenuId": '${leftMenuId}',
            "imei": imei,"queryId": relayId,"queryType":queryType,"type":2 } );
        }
		
		       // 检查操作了几条数据
		       function checkIsSingle() {
                       var length = dataList.checkboxData.length;
                        if(length != 1){
                         com.ymx.layui.public.core.layer.msg('<s:message code="一次只能操作一条数据"/>!');
                        return false;
                        }
                        return true;
                  }


	</script>
</head>
<body>
	<div class="admin-main" id="relay_list">
		<%-- <s:message code="查询条件" />区域 --%>
		<%--<blockquote class="layui-elem-quote"> --%>
			<form class="layui-form" action="" name="relayListForm" id="relayListForm">
				<!--<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="录入时间" /></label>
							<div class="layui-input-inline" >
								<input type="text" name="createTimeBegin" id="createTimeBegin" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'createTimeEnd\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" placeholder="<s:message code="录入开始时间" />" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
							<div class="layui-form-mid">-</div>
							<div class="layui-input-inline">
								<input type="text" name="createTimeEnd" id="createTimeEnd" placeholder="<s:message code="录入结束时间" />" onclick="WdatePicker({minDate:'#F{$dp.$D(\'createTimeBegin\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" autocomplete="off" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="中继器名称" /></label>
							<div class="layui-input-inline">
								<input type="text" name="groupName" id="groupName" placeholder="<s:message code="请输入中继器名称" />" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="电站名称" /></label>
							<div class="layui-input-inline">
								<input type="text" name="powerStationName" id="powerStationName" placeholder="<s:message code="电站名称" />"  autocomplete="off" class="layui-input"/>
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-input-block">
								<button class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>-->
			</form>
		
		<%--</blockquote> --%>
		<%-- 操作功能 --%>
		<div>
			<jsp:include page="/functionBuilder.htm"></jsp:include>
		</div>
		<%-- 数据列表展示 --%>
		<table class="layui-table" id="relayList" lay-filter="relayList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="relayListChooseAll" id="relayListChooseAll" lay-skin="primary" /></th>
					<%-- <th><s:message code="编号" /></th> --%>
					<th><s:message code="中继器名称" /></th>
					<th><s:message code="中继器标识" /></th>
					<th><s:message code="采集器" /></th>
					<th><s:message code="电站名称" /></th>
                    <th><s:message code="软件版本号" /></th>
                    <th><s:message code="分区" /></th>
					<th><s:message code="录入时间" /></th>
					<th><s:message code="更新时间" /></th>
			   <%-- <th><s:message code="组件数量" /></th>--%>


				</tr>
			</thead>
			<tbody id="relayListBody"></tbody>
		</table>
		<script type="text/html" id="relayListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="relayList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="relayList_check_{{r.id}}" id="relayList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>
				<td data-field="relayName_{{r.relayName}}">{{r.relayName}}</td>
				<td data-field="relayId_{{r.relayId}}">{{r.relayId}}</td>
				<td data-field="cloudId_{{r.cloudId}}">
				    {{# if(!isNull(r.cloudId)){ }}
				    {{r.cloudId}}
				    {{# }  }}
				</td>
			  <td data-field="powerStationName{{r.powerStationName}}">
			    {{# if(!isNull(r.powerStationName)){ }}
			    {{r.powerStationName}}
			    {{# }  }}
			    </td>
                <td data-field="softVersion_{{r.softVersion}}">
                {{# if(!isNull(r.softVersion)){ }}
                  {{r.softVersion}}
                    {{# }  }}
                </td>
                <td data-field="bootPartition{{r.bootPartition}}">
                  {{# if(!isNull(r.bootPartition)){ }}
                  {{r.bootPartition}}
                    {{# }  }}
                </td>
                <td data-field="createTime_{{r.createTime}}">{{r.createTime}}</td>
                <td data-field="updateTime{{r.updateTime}}">
                                  {{# if(!isNull(r.updateTime)){ }}
                                  {{r.updateTime}}
                                  {{# }  }}
                </td>
				<%--  <td data-field="componentNum_{{r.componentNum}}">
				{{# if(!isNull(r.componentNum)){ }}
				{{r.componentNum}}
				 {{# }  }}
				</td>--%>

			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="10"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "relay/queryRelayList.web",
					defaultTableId : 'relayList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'relayListBody',
					formName : 'relayListForm',
					isTable : false,
					laytplTemplet : 'relayListTemplet'
				});

			}())
		</script>
	</div>
</body>
</html>