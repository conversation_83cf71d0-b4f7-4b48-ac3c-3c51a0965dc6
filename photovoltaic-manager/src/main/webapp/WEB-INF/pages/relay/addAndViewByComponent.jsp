<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<!DOCTYPE html>
<html>
<head>
	<jsp:include page="/include.htm"/>
	<script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js?t=adfasdfasdfasdfasd"></script>
	<script src="<%=PageUtil.getBasePath(request) %>js/common.core.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/DataList.${mkt}.js"></script>
	<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
	<script src="<%=PageUtil.getBasePath(request)%>plugins/${tabmkt}/datePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	$(function(){
      
                // 选择组件确认
                $("#selectComponentComfirm").click(function () {
                    var url = $('#selectComponentComfirm').attr("url");
                    // 列表数据id  是多个以逗分隔值
                    var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
                    var length = dataList.checkboxData.length;
                    var realData = { "relayId":'${relayId}'  , "id":ids};
                    if (length > 0 ){
                        layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index ){
                            doPost( url ,  null , realData , function(data){
                                if(data.rec == 'SUC') {
                                    com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
                                    dataList.checkboxData = [];
                                    dataList.laytplShow();
                                    layer.closeAll();
                                    dataList.laytplShow();

                                }
                            },function(data){ });
                        });
                    } else {
                        layer.msg('<s:message code="请选择需要操作的数据"/>');
                    }
                });

    
                // 组件移除
                $("#moveComponentComfirm").click(function () {
                    var url = $('#moveComponentComfirm').attr("url");
                    // 列表数据id  是多个以逗分隔值
                    var ids = getStrAttrVal({data : dataList.checkboxData , "attrName":"id"}) ;
                    var length = dataList.checkboxData.length;
                    // 如果移除组串的话 就不传组串id
                    var realData = { "id":ids };
                    if (length > 0 ){
                        layer.confirm('<s:message code="确认要操作所选择的数据吗"/>?', {icon: 3 , title:'<s:message code="提示"/>'}, function( index ){
                            doPost( url ,  null , realData , function(data){
                                if(data.rec == 'SUC') {
                                    com.ymx.layui.public.core.layer.msg('<s:message code="操作成功"/>');
                                    dataList.checkboxData = [];
                                    dataList.laytplShow();
                                    layer.closeAll();
                                    dataList.laytplShow();
                                }
                            },function(data){ });
                        });
                    } else {
                        layer.msg('<s:message code="请选择需要操作的数据"/>');
                    }
                });
				
				
		 // 按组串选择
        $("#byGroup").click(function () {
            var url = $('#byGroup').attr("url");
			
		    layer.open({
                title: false,
                type: 2,
				closeBtn: 0,
                area: ['100%', '100%'],
                content: [url + '?relayId=' + '${relayId}'+ "&operationFlag=${operationFlag}&leftMenuId=${leftMenuId}", 'yes']
            });
			

        });
		
		// 返回
		$("#icon-back").click(function() {
			windowLocaltionHref("relay/list.htm", "&leftMenuId=${leftMenuId}");
		});



    });


	</script>
</head>
<body>

	<div class="admin-main" id="selectComponentGroupList">
	
		<blockquote class="layui-elem-quote">
			<form class="layui-form" action="" name="selectComponentListForm" id="selectComponentListForm">
				<fieldset class="admin-fieldset">
					<legend><s:message code="查询条件" /></legend>
					<div class="layui-form-item">
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="组件坐标" /></label>
							<div class="layui-input-inline">
								<input type="text" name="chipId" id="chipId" placeholder="<s:message code="请输入组件坐标" />" class="layui-input"style="width: 100%;">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label-select"><s:message code="组串名称" /></label>
							<div class="layui-input-inline">
								<input type="text" name="groupName" id="groupName" placeholder="<s:message code="请输入组串名称" />"  autocomplete="off" class="layui-input"/>
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-input-block">
								<button  class="layui-btn" lay-submit lay-filter="sreach"><s:message code="查询"/></button>
								<button type="reset" class="layui-btn"><s:message code="重置" /></button>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		  </blockquote>
			  
		<%-- 操作功能 --%>
		<div>
			<div class="layui-btn-group">
				<c:if test="${operationFlag eq 'select'}">
					<button class="layui-btn" id="selectComponentComfirm" name="selectComponentComfirm" url="relay/changeComponent.web">
						<i class="layui-icon">&#xe640;</i><s:message code="确认" />
					</button>
				</c:if>
				<c:if test="${operationFlag eq 'view'}">
					<button class="layui-btn" id="moveComponentComfirm" name="moveComponentComfirm" url="relay/changeComponent.web">
						<i class="layui-icon">&#xe640;</i><s:message code="移除" />
					</button>
				</c:if>
				
	   <%--  			   <button class="layui-btn" id="byGroup" name="addByGroup" url="relay/addAndViewByGroup.htm">
    				<i class="layui-icon">&#xe640;</i><s:message code="按组串选择" />
    		   </button> --%>
			   
    		   <a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="返回" /></a>
			</div>
		   
		</div>
		

		<%-- 数据列表展示 --%>
		<table class="layui-table" id="selectComponentList" lay-filter="selectComponentList" lay-even>
			<thead>
				<tr>
					<th><input type="checkbox" name="selectComponentListChooseAll" id="selectComponentListChooseAll" lay-skin="primary" /></th>
					<%-- <th><s:message code="编号" /></th> --%>
					<th><s:message code="组件坐标" /></th>
					<th><s:message code="组串名称" /></th>
					<th><s:message code="采集器标识" /></th>
					<th><s:message code="电站名称" /></th>
				</tr>
			</thead>
			<tbody id="selectComponentListBody"></tbody>
		</table>
		<script type="text/html" id="selectComponentListTemplet">
			{{# layui.each( d , function( index , r ) { }}
			<tr id="selectComponentList_tr_{{r.id}}">
				<td data-field="id_{{r.id}}"><input type="checkbox" name="selectComponentList_check_{{r.id}}" id="selectComponentList_check_{{r.id}}" lay-skin="primary" value="{{r.id}}" /></td>

				<td data-field="chipId_{{r.chipId}}">{{r.chipId}}</td>
				<td data-field="belongsGroupName_{{r.belongsGroupName}}">
                    {{# if(!isNull(r.belongsGroupName)){ }}
                    	{{r.belongsGroupName}}
                    	{{# }  }}
                </td>
				<td data-field="imei_{{r.imei}}">
                   {{# if(!isNull(r.imei)){ }}
                         {{r.imei}}
                    {{# }  }}
                 </td>
                 <td data-field="powerStationName_{{r.powerStationName}}">
                 {{# if(!isNull(r.powerStationName)){ }}
                    {{r.powerStationName}}
                  {{# }  }}
                 </td>

			</tr>
			{{#  });  }}	
			<%-- 列表无数据 --%>
			{{# if(d.length === 0){ }}
				<tr >
					<td colspan="10"><s:message code="暂无数据"/>!</td>
				</tr>   
			{{# } }}
		</script>
		<%-- 分页显示 --%>
		<div align="center" id="page"></div>
		<script>
			//初始化
			(function() {
				dataList = DataList.create({
					url : "relay/queryComponentList.web",
					defaultTableId : 'selectComponentList',
					isPager : true,
					isLaytpl : true,
					laytplAppendHtml : 'selectComponentListBody',
					formName : 'selectComponentListForm',
                    laytplPutextra : {"relayId":'${relayId}',"operationFlag":'${operationFlag}'},
					isTable : false,
					laytplTemplet : 'selectComponentListTemplet'
				});
			}())
		</script>
	</div>


</body>
</html>