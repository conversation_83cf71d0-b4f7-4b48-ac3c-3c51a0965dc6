<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@page import="com.ymx.common.utils.Read"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>      
<base href="<%=PageUtil.getBasePath(request) %>" />

   <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
        "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script type="text/javascript" charset="utf-8" src="<%=PageUtil.getBasePath(request) %>js/utf8-jsp/ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="<%=PageUtil.getBasePath(request) %>js/utf8-jsp/ueditor.all.min.js"> </script>
    <!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
    <!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
    <script type="text/javascript" charset="utf-8" src="<%=PageUtil.getBasePath(request) %>js/utf8-jsp/lang/zh-cn/zh-cn.js"></script>

    <style type="text/css">
        div{
            width:100%;
        }
    </style>
</head>
<body>
<div>
 ${editorValue }

</body>
</html>