<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.ymx.common.utils.PageUtil"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="s" %>
<%@page import="com.ymx.common.utils.Read"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>      
<base href="<%=PageUtil.getBasePath(request) %>" />

   <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
        "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <jsp:include page="/include.htm"/><script src="<%=PageUtil.getBasePath(request) %>common/plugins/layui/layui.${mkt}.js"></script>
    <script src="<%=PageUtil.getBasePath(request) %>common/layui.core.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>js/common.core.en.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.layer.plugin.custom.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/DataList.en.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/select.option.js"></script>
<script src="<%=PageUtil.getBasePath(request) %>common/layui.upload.plugin.js"></script>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script type="text/javascript" charset="utf-8" src="<%=PageUtil.getBasePath(request) %>js/utf8-jsp/ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="<%=PageUtil.getBasePath(request) %>js/utf8-jsp/ueditor.all.min.js"> </script>
    <!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
    <!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
    <script type="text/javascript" charset="utf-8" src="<%=PageUtil.getBasePath(request) %>js/utf8-jsp/lang/zh-cn/zh-cn.js"></script>

    <style type="text/css">
        div{
            width:100%;
        }
    </style>
    
    <script type="text/javascript">
	(function($) {
		$(function() {
			// 保存
			/*  $('#icon-save').click(function() {
				formSubmit({
					"formId":"cfile" ,
					"url":"helpFileView/createfile.htm" ,
					"backListUrl":"inverter/list.htm" ,
					"leftMenuId":'${leftMenuId}'
				});
			});  */
			/*  $("#icon-save").click(function () {
                  formSubmit({
                      "formId":"loginCodeForm" ,
                      "url":"helpFileView/createfile.htm" ,
                      "backType": 2 ,
                      callBack : function (data) {
                          layer.msg('<s:message code="操作成功"/>');
                          dataList.checkboxData = [];
                          dataList.laytplShow();
                          layer.closeAll();
                      }
                  });
              }); */
			// 返回
			$("#icon-back").click(function() {
				windowLocaltionHref("helpFileView/findfile.htm", "&leftMenuId=${leftMenuId}")
			});
		})
	})(jQuery)
</script>
</head>
<body>
<div>
  <form action="helpFileView/createfile.htm" id="cfile" method="post" > 
    
     <script id="editor" type="text/plain" style="width:99%;height:400px;"></script>
	 <div class="layui-inline" style="padding-top:10px;text-align:center;">
		<%-- <div class="layui-input-block">
		    <button class="layui-btn" type="submit"><s:message code="保存"/></button>
		    <button  class="layui-btn"><s:message code="查看页面" /></button>
		</div> --%>
		<div class="layui-form-item">
			<div class="layui-input-block">
				 <button class="layui-btn" type="submit"><s:message code="保存"/></button>
				<a id="icon-back" class="layui-btn" style="margin: 10px;"><s:message code="查看页面" /></a>
			</div>
		</div>
	</div>
</form> 
<script type="text/javascript">

    //实例化编辑器
    //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
    var ue = UE.getEditor('editor');
</script>
</body>
</html>