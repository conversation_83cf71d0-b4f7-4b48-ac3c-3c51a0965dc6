<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/mvc 	http://www.springframework.org/schema/mvc/spring-mvc-4.3.xsd
		http://www.springframework.org/schema/context 	http://www.springframework.org/schema/context/spring-context.xsd">
		
	<!-- 注解扫描包 -->
	<context:component-scan base-package="com.ymx.manager,com.ymx.common.log"/>
	
	<!-- 避免IE执行AJAX时,返回JSON出现下载文件   -->
	<bean id="mappingJacksonHttpMessageConverter"
		class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
		<property name="supportedMediaTypes">
			<list>
				<value>text/plain;charset=UTF-8</value>
			</list>
		</property>
	</bean>

	<!-- 启动Spring MVC的注解功能，完成请求和注解POJO的映射 -->
	<bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter">
		<property name="messageConverters">
			<list>
				<ref bean="mappingJacksonHttpMessageConverter" /><!-- json转换器 -->
			</list>
		</property>
	</bean>

	<bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter">
		<property name="messageConverters">
			<list>
				<bean
					class="org.springframework.http.converter.StringHttpMessageConverter">
					<property name="supportedMediaTypes">
						<list>
							<value>text/plain;charset=UTF-8</value>
						</list>
					</property>
				</bean>
			</list>
		</property>
	</bean>

     <!-- 定义HTML文件的位置 -->  
     <bean id="htmlviewResolver"    
        class="org.springframework.web.servlet.view.InternalResourceViewResolver">   
        <property name="viewClass" value="com.ymx.manager.security.utils.HtmlResourceView"/>
        <property name="order" value="0" />  
        <property name="prefix" value="/WEB-INF/pages/html/"/>  
        <property name="suffix" value=".html" />    
        <property name="contentType" value="text/html;charset=UTF-8"/>
    </bean>  
    <!-- 定义JSP文件的位置 -->  
    <bean id="jspViewResolver" class="org.springframework.web.servlet.view.InternalResourceViewResolver">  
        <property name="order" value="1" />  
        <property name="prefix" value="/WEB-INF/pages/"/>  
        <property name="suffix" value=".jsp"/>  
    </bean>  


	<!-- 静态文件 -->
	<mvc:resources location="/common/" mapping="/common/**" cache-period="31556926"/>
	<mvc:resources location="/css/" mapping="/css/**"/>
	<mvc:resources location="/tld/" mapping="/tld/**"/>
	<mvc:resources location="/js/" mapping="/js/**"/>
	<mvc:resources location="/images/" mapping="/images/**"/>
	<mvc:resources location="/img/" mapping="/img/**"/>
	<mvc:resources location="/plugins/" mapping="/plugins/**"/>
	
	<mvc:annotation-driven/>
	
	<mvc:default-servlet-handler/>
	
	<!-- 拦截器 -->
	 <mvc:interceptors>
	    <!-- 后台拦截器 -->
		<mvc:interceptor>
			<mvc:mapping path="/**"/>
			<mvc:exclude-mapping path="/index/map.htm" />
			<mvc:exclude-mapping path="/assets/**"/>
			<mvc:exclude-mapping path="/css/**"/>
			<mvc:exclude-mapping path="/js/**"/>
			<mvc:exclude-mapping path="/common/**"/>
			<mvc:exclude-mapping path="/images/**"/>
			<mvc:exclude-mapping path="/tld/**"/>
			<mvc:exclude-mapping path="/img/**"/>
			<mvc:exclude-mapping path="/plugins/**"/>
			<mvc:exclude-mapping path="/free/**"/>
			<mvc:exclude-mapping path="/**/*.api"/>
			<mvc:exclude-mapping path="/template/**"/>
			<bean class="com.ymx.manager.security.common.SystemInterceptor" />
		</mvc:interceptor>
	</mvc:interceptors>

</beans>
