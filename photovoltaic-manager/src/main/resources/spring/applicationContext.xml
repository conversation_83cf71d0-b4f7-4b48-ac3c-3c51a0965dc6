<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans.xsd">
		
	<!-- 初始化缓存数据 -->
	<bean id="initLoader" lazy-init="false" class="com.ymx.common.base.config.SystemConfig" init-method="init" />
		
	<!-- redis配置 -->
	<bean id="redisProperties" class="com.ymx.service.third.jedis.JedisPoolUtil">
		<property name="locations">
			<list>
				<value>classpath:jedis.properties</value>
			</list>
		</property>
	</bean>

	<!--加载文件服务器配置文件 -->
	<bean id="fileProperties" class="com.ymx.common.common.config.FileConfig">
		<property name="locations">
			<list>
				<value>classpath:file.properties</value>
			</list>
		</property>
	</bean>


	<!--加载配置文件 -->
	<bean id="configProperties" class="com.ymx.common.base.config.ConfigConstants">
		<property name="locations">
			<list>
				<value>classpath:config.properties</value>
			</list>
		</property>
	</bean>

</beans>