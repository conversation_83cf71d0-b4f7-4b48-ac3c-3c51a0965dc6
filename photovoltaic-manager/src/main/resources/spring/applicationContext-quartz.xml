<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"  
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xmlns:task="http://www.springframework.org/schema/task"
    xmlns:context="http://www.springframework.org/schema/context"  
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd  
                        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
                        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.0.xsd">  
  
   <context:component-scan base-package="com.ymx.common,com.ymx.service"/>
   <!-- 开启这个配置，spring才能识别@Scheduled注解   -->
   <task:executor id="executor" pool-size="5" />  
   <task:scheduler id="scheduler" pool-size="10" />  
   <task:annotation-driven executor="executor" scheduler="scheduler" />
   
</beans>