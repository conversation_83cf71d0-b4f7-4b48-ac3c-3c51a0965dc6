<?xml version="1.0" encoding="utf-8"?>
<root>
	<sql id="sql_query_addrbook_count" description="查询通讯录记录数">
   	  <![CDATA[
   	  		select count(0) from t_fc_addrbook
   	  	   where 1=1
				and name like ?
				and department like ?
				and phone_num like ?
   	  ]]>
	</sql>

	<sql id="sql_query_addrbook_list" description="查询通讯录记录列表">
   	  <![CDATA[
   	  		select
				name,
				department,
				phone_num,
				town,
				village,
   	  			id
   	  		 from t_fc_addrbook 
   	  		 where 1=1
				and name like ?
				and department like ?
				and phone_num like ?
   	  ]]>
	</sql>

	<sql id="sql_query_notice_count" description="查询通知公告记录数">
   	  <![CDATA[
   	  		select count(0) from t_fc_notice_info
   	  	   where 1=1
				and title like ?
   	  ]]>
	</sql>

	<sql id="sql_query_notice_list" description="查询通知公告记录列表">
   	  <![CDATA[
   	  		select
				title,
				pub_time,
				author,
   	  			id
   	  		 from t_fc_notice_info 
   	  		 where 1=1
				and title like ?
			order by pub_time desc
   	  ]]>
	</sql>
	
	<sql id="sql_query_notice_count_mobile" description="查询通知公告记录数">
   	  <![CDATA[
   	  		select count(0) from t_fc_notice_info
   	  	   where 1=1
				and title like ? 
				and relUser like ?
   	  ]]>
	</sql>

	<sql id="sql_query_notice_list_mobile" description="查询通知公告记录列表">
   	  <![CDATA[
   	  		select
				title,
				pub_time,
				author,
   	  			id
   	  		 from t_fc_notice_info 
   	  		 where 1=1
				and title like ?
				and relUser like ?
			order by pub_time desc
   	  ]]>
	</sql>

	<sql id="sql_query_terminal_count" description="查询用户终端信息记录数">
   	  <![CDATA[
   	  		select count(0) from t_fc_terminal_info
   	  	   where 1=1
   	  ]]>
	</sql>

	<sql id="sql_query_terminal_list" description="查询用户终端信息记录列表">
   	  <![CDATA[
   	  		select
				phone_name,
				phone_version,
				soft_version,
				imsi,
				imei,
				login_name,
				ownship,
   	  			id
   	  		 from t_fc_terminal_info 
   	  		 where 1=1
   	  ]]>
	</sql>

	<sql id="sql_query_sign_count" description="查询签到签退信息记录数">
   	  <![CDATA[
   	  		select count(0) from t_fc_sign_in_out t
   	  		 left join t_fs_user tu on t.user_id = tu.user_id 
   	  		 left join t_fs_dept td on tu.dept_id = td.dept_id
   	  	   where 1=1
				and tu.user_name like ?
				and td.dept_name like ?
				and t.sign_time between ? and ?
				and (t.type = ? or '-1' = ?)
   	  ]]>
	</sql>

	<sql id="sql_query_sign_list" description="查询签到签退信息记录列表">
   	  <![CDATA[
   	  		select
				tu.user_name,
				td.dept_name,
				t.type,
				t.address,
				t.hand_address,
				t.sign_time,
   	  			t.id
   	  		 from t_fc_sign_in_out t
   	  		 left join t_fs_user tu on t.user_id = tu.user_id 
   	  		 left join t_fs_dept td on tu.dept_id = td.dept_id
   	  		 where 1=1
				and tu.user_name like ?
				and td.dept_name like ?
				and t.sign_time between ? and ?
				and (t.type = ? or '-1' = ?)
			order by t.sign_time desc	
   	  ]]>
	</sql>
	
	<sql id="common_tree_getDeptUserTree" description="查询所有部门人员树">
		<![CDATA[
			  select t.dept_id as nid,
			         t.dept_name as name,
			         t.p_dept_id as npid,
			         t.dept_name title,
			         'true' as isParent
			    from t_fs_dept t
			  union all
			  select t1.user_id as nid,
			         t1.user_name as name,
			         t1.dept_id as npid,
			         t1.user_name title,
			         'false' as isParent
			    from t_fs_user t1
			   where t1.dept_id in (select DISTINCT dept_id from t_fs_dept)
		]]>
	</sql>
	<sql id="queryAddrWithCondition_byName" description="">
		<![CDATA[
        select name, department, phone_num, id
          from t_fc_addrbook
         where name = ?
			and department = ?
		]]>
	</sql>
	<sql id="queryDeptByPhotoNum" description="">
		<![CDATA[
			select
				name,
				department,
				phone_num,
   	  			id
   	  		 from t_fc_addrbook where phone_num = ?
		]]>
	</sql>
	<sql id="queryDeptByName" description="">
		<![CDATA[
			select dept_id deptId,
			       dept_name deptName,
			       dept_short_name deptShortName,
			       dept_phone deptPhone,
			       dept_fax deptFax,
			       dept_address deptAddress,
			       p_dept_id pDeptId,
			       dept_leader deptLeader,
			       dept_level deptLevel,
			       dept_remark deptRemark
			  from t_fs_dept
			 where dept_name = ?
		]]>
	</sql>

	<sql id="sql_query_work_count" description="查询工作记录记录数">
   	  <![CDATA[
   	  		select count(0) from t_fc_work tw
   	  		 left join t_fs_user tu on tu.user_id = tw.recorder 
   	  		 left join t_fs_dept td on tu.dept_id = td.dept_id
   	  	   where 1=1
   	  	   	and td.dept_name like ? 
   	  	   	and tu.user_name like ? 
   	  	   	and record_time between ? and ?
   	  ]]>
	</sql>

	<sql id="sql_query_work_list" description="查询工作记录记录列表">
   	  <![CDATA[
   	  		select
				tw.meeting,
				tw.performance,
				tw.recorder,
				tw.record_time,
				tw.record_address,
				tw.record_images,
   	  			tw.id,
   	  			tu.user_name,
   	  			td.dept_name
   	  		 from t_fc_work  tw
   	  		 left join t_fs_user tu on tu.user_id = tw.recorder 
   	  		 left join t_fs_dept td on tu.dept_id = td.dept_id
   	  	   where 1=1
   	  	   	and td.dept_name like ? 
   	  	   	and tu.user_name like ? 
   	  	   	and tw.record_time between ? and ?
   	  	   	 order by record_time desc
   	  ]]>
	</sql>
	
	<sql id="sql_query_user_work_count" description="查询用户工作记录记录数">
   	  <![CDATA[
   	  		select count(0) from t_fc_work
   	  	   where 1=1 and recorder = ? 
   	  ]]>
	</sql>

	<sql id="sql_query_user_work_list" description="查询用户工作记录记录列表">
   	  <![CDATA[
   	  		select
				*
   	  		 from t_fc_work 
   	  		 where 1=1 and recorder = ? 
   	  		 order by record_time desc
   	  ]]>
	</sql>

	<sql id="sql_query_thingreport_count" description="查询事件上报记录数">
   	  <![CDATA[
   	  		select
				count(0)
   	  		 from t_fc_thing_report tr
   	  		 left join t_fs_user tu on tu.user_id = tr.reporter
   	  		 left join t_fs_dept td on td.dept_id = tu.dept_id
   	  		 where 1=1
				and td.dept_name like ?
				and tu.user_name like ?
				and tr.appeal_result like ?
				and report_time between ? and ?
   	  ]]>
	</sql>

	<sql id="sql_query_thingreport_list" description="查询事件上报记录列表">
   	  <![CDATA[
   	  		select
				tr.*,td.dept_name,tu.user_name
   	  		 from t_fc_thing_report tr
   	  		 left join t_fs_user tu on tu.user_id = tr.reporter
   	  		 left join t_fs_dept td on td.dept_id = tu.dept_id
   	  		 where 1=1
				and td.dept_name like ?
				and tu.user_name like ? 
				and tr.appeal_result like ?
				and report_time between ? and ?
				order by report_time desc
   	  ]]>
	</sql>
	
	<sql id="sql_query_company_info" description="">
   	  <![CDATA[
			select
					td.dept_name,tu.*
	   	  		 from t_fc_thing_report tr
	   	  		 left join t_fs_user tu on tu.user_id = tr.reporter
	   	  		 left join t_fs_dept td on td.dept_id = tu.dept_id
			where tr.reporter = ?
   	  ]]>
	</sql>
	
	<sql id="sql_query_user_thingreport_count" description="查询用户事件上报记录数">
   	  <![CDATA[
   	  		select count(0) from t_fc_thing_report
   	  	   where 1=1
				and reporter = ?
   	  ]]>
	</sql>

	<sql id="sql_query_user_thingreport_list" description="查询用户事件上报记录列表">
   	  <![CDATA[
   	  		select
				*
   	  		 from t_fc_thing_report 
   	  		 where 1=1
				and reporter = ?
			order by report_time desc
   	  ]]>
	</sql>
	<sql id="sql_query_peoplething_count" description="查询民生实事记录数">
   	  <![CDATA[
   	  		select count(0) from t_fc_people_info ti
   	  		 left join t_fs_user tu on ti.create_user = tu.user_id
   	  		 left join t_fs_dept td on tu.dept_id = td.dept_id
   	  	   where 1=1
   	  	   and td.dept_name like ?
   	  	   and tu.user_name like ?
   	  	   and ti.create_date between ? and ?
   	  	   and ti.town like ? 
   	  	   and ti.village like ?
   	  ]]>
	</sql>
	
	<sql id="sql_query_peoplething_list" description="查询民生实事记录列表">
   	  <![CDATA[
   	  		select
				ti.*,tu.user_name,td.dept_name,tu.user_phone
   	  		 from t_fc_people_info ti
   	  		 left join t_fs_user tu on ti.create_user = tu.user_id
   	  		 left join t_fs_dept td on tu.dept_id = td.dept_id
   	  		 where 1=1
   	  		 	and td.dept_name like ?
   	  	   		and tu.user_name like ?
   	  	   		and ti.create_date between ? and ? 
   	  	   		and ti.town like ? 
   	  	  		and ti.village like ?
   	  		 order by ti.create_date desc
   	  ]]>
	</sql>
	
	<sql id="sql_query_sign_things_reports_count" description="查询数据统计报表">
	    <![CDATA[
		    select count(distinct t.id) from t_fc_sign_in_out t				
				left join t_fs_user tu on t.user_id = tu.user_id
				left join t_fs_dept td on tu.dept_id = td.dept_id
				where t.type = 0 
				and 
				td.dept_name like ?
				and 
				tu.user_name like ?
				and t.sign_time between ? and ?
	    ]]>
	</sql>
	
	<sql id="sql_query_sign_things_reports" description="查询数据统计报表">
	    <![CDATA[
	           select t.id,t.sign_id,tu.user_name,t.sign_time,td.dept_name,tu.user_phone,tr.description,t.hand_address appealer_address
		       from t_fc_sign_in_out t
				left join t_fc_thing_report tr on t.id = tr.sign_id and tr.is_valid = '1'
				left join t_fc_work tw on t.id = tw.sign_id  and tw.is_valid = '1'
				left join t_fs_user tu on t.user_id = tu.user_id
				left join t_fs_dept td on td.dept_id = tu.dept_id
				where t.type = 0                 
				and td.dept_name like ? 
				and tu.user_name like ?  
				and t.sign_time between ? and ?
				order by t.sign_time desc 
	    ]]>
	</sql>
	
	<sql id="sql_export_sign_things_reports" description="查询数据统计导出的报表">
	    <![CDATA[
	    	 select tt.* from (
	    	select  t.id in_id,ti.id out_id,td.dept_name,t.hand_address,tu.user_name,tu.user_phone,ti.sign_time out_sign_time,ti.address out_address,ti.img_path out_img_path,tw.meeting,tw.record_images,tr.appealer_name,tr.appealer_phone,tr.appealer_address,tr.description,tr.appeal_images,t.sign_time in_sign_time,t.address in_address,t.img_path in_img_path
		       from t_fc_sign_in_out t
				left join t_fc_thing_report tr on t.id = tr.sign_id
				left join t_fc_work tw on t.id = tw.sign_id
				left join t_fs_user tu on t.user_id = tu.user_id
				left join t_fs_dept td on td.dept_id = tu.dept_id
                left join t_fc_sign_in_out ti on t.id = ti.sign_id                
				where t.type = 0 and tu.user_name like ?
				 and td.dept_name like ?
				and t.sign_time between ? and ?  
				order by t.sign_time desc ) tt
 			group by tt.in_id
	    ]]>
	</sql>
	
	<sql id="sql_query_sign_things_report" description="根据编码查询统计报表详情">
	    <![CDATA[
		      select tso.address out_address,tso.sign_time out_sign_time,tso.img_path out_img_path,t.address in_address,t.hand_address in_hand_address,t.sign_time in_sign_time,t.img_path in_image_path,tr.*,tw.*,tu.* 
		        from t_fc_sign_in_out t
				left join t_fc_thing_report tr on t.id = tr.sign_id and tr.is_valid = '1'
				left join t_fc_work tw on t.id = tw.sign_id and tw.is_valid = '1'
				left join t_fs_user tu on t.user_id = tu.user_id
                left join t_fc_sign_in_out tso on t.id  = tso.sign_id
				where t.type = 0 and t.id = ?
	    ]]>
	</sql>
	<sql id="sql_query_sms_count" description="查询短信信息记录数">
   	  <![CDATA[
   	  		select count(0) from t_fc_sms
   	  	   where 1=1
   	  ]]>
	</sql>
	
	<sql id="sql_query_sms_list" description="查询短信信息记录列表">
   	  <![CDATA[
   	  		select
				send_users,
				content,
				create_user,
				create_date,
   	  			id
   	  		 from t_fc_sms 
   	  		 where 1=1
   	  		 order by create_date desc
   	  ]]>
	</sql>
	
	<sql id="sql_update_user_position" description="更新用户当前位置">
	    <![CDATA[
	    	update t_fc_online_user set position = ?, update_date = ? where user_id = ?
	    ]]>
	</sql>
	
	<sql id="sql_insert_user_position" description="新增用户当前位置">
	    <![CDATA[
	    	insert into t_fc_online_user(user_id,position,update_date) values(?,?,?)
	    ]]>
	</sql>
	
	<sql id="sql_query_online_users" description="查询所有在线用户">
	    <![CDATA[
	    	select tfu.user_name,tfu.user_phone,tu.position
	    	from t_fc_online_user tu
	    	left join t_fs_user tfu on tu.user_id = tfu.user_id
	    	where TIMESTAMPDIFF(MINUTE,update_date,now())<=5  
	    ]]>
	</sql>
	
	<sql id="sql_query_user_areas" description="根据用户编码查询用户所在区域">
	    <![CDATA[
	    select ta.town,ta.village from t_fc_address_user tu
			left join t_fc_address ta on ta.id = tu.address_id
			where tu.user_id = ?
	     ]]>
	</sql>
	
	<sql id="sql_delete_area_users"  description="删除区域用户">
	    <![CDATA[
	    	delete from t_fc_address_user where address_id = ?
	    ]]>
	</sql>
	
		<sql id="sql_insert_area_users" description="新增区域用户">
	    <![CDATA[
	    	insert into t_fc_address_user(address_id,user_id) values(?,?)
	    ]]>
	</sql>
	
	<sql id="sql_query_user_names_by_address_id" description="查询用户信息">
	    <![CDATA[
	    select user_name,user_phone from t_fs_user where user_id in (select user_id from t_fc_address_user where address_id = ?)
	    ]]> 
	</sql>
	
	<sql id="sql_query_town_and_villages_count" description="查询所有的乡镇、社区信息">
	    <![CDATA[
	    select count(0) from t_fc_address where town like ? and village like ?
	     ]]> 
	</sql>
	
	<sql id="sql_query_town_and_villages" description="查询所有的乡镇、社区信息">
	    <![CDATA[
	    select * from t_fc_address where town like ? and village like ?
	     ]]> 
	</sql>
	
	<sql id="sql_query_thingreport_by_slsbid" description="">
		<![CDATA[
			select * from t_fc_thing_report where slsb_id = ?
		]]> 
	</sql>
	<sql id="sql_delete_thingreport_by_slsbid" description="">
		<![CDATA[
			delete from t_fc_thing_report where slsb_id = ?
		]]> 
	</sql>
	
	<sql id="sql_insert_notice_users" description="保存公告的相关用户">
	    <![CDATA[
	    	insert into t_fc_notice_user(id,user_id) values(?,?)
	    ]]> 
	</sql>
	
	<sql id="sql_delete_notice_users" description="删除相关的公告">
	    <![CDATA[
	    	delete from t_fc_notice_user where user_id = ?
	    ]]> 
	</sql>
	
	<sql id="sql_query_user_notices" description="查询用户未读的公告">
	    	<![CDATA[
	    		select id from t_fc_notice_user where user_id = ?
	    	]]> 
	</sql>
	
	<sql id="sql_update_thing_report_is_valid" description="更新事件上报状态">
	    <![CDATA[
	    	update t_fc_thing_report set is_valid = '0' where sign_id = ? 
	    ]]> 
	</sql>
	
	<sql id="sql_update_work_is_valid" description="更新工作记录状态">
	    <![CDATA[
	    	update t_fc_work set is_valid = '0' where sign_id = ? 
	    ]]> 
	</sql>
	
	<sql id="sql_query_indeximage_count" description="查询首页图片记录数">
   	  <![CDATA[
   	  		select count(0) from t_fc_index_image
   	  	   where 1=1
   	  ]]>
	</sql>
	
	<sql id="sql_query_indeximage_list" description="查询首页图片记录列表">
   	  <![CDATA[
   	  		select
				create_date,
				create_user,
   	  			id
   	  		 from t_fc_index_image 
   	  		 where 1=1
   	  ]]>
	</sql>
	
	<sql id="sql_query_address_count" description="查询联络人管理记录数">
   	  <![CDATA[
   	  		select count(0) from t_fc_address
   	  	   where 1=1
   	  ]]>
	</sql>
	
	<sql id="sql_query_address_list" description="查询联络人管理记录列表">
   	  <![CDATA[
   	  		select
				town,
				village,
   	  			id
   	  		 from t_fc_address 
   	  		 where 1=1
   	  ]]>
	</sql>
	
	<sql id="sql_query_goodperson_count" description="查询汶南好人的集合数量">
	    <![CDATA[
	    	select count(1) from t_fc_good_person 
	    	where 1=1
	    	and type=1 
	    	and title like ?
	    ]]>
	</sql>
	
	<sql id="sql_query_goodperson_list" description="查询汶南好人的集合">
	    <![CDATA[
	    	select
				title,
				create_time,
				create_user,
				id
   	  		 from t_fc_good_person 
   	  		 where 1=1
   	  		 and type=1
			 and title like ?
			 order by order_time desc
	    ]]>
	</sql>
	
	<sql id="sql_query_goodperson_count_mobile" description="查询汶南好人的集合数量">
	    <![CDATA[
	    	select count(1) from t_fc_good_person 
	    	where 1=1
	    	and type=1 
	    	and title like ?
	    ]]>
	</sql>
	
	<sql id="sql_query_goodperson_list_mobile" description="查询汶南好人的集合">
	    <![CDATA[
	    	select bean.* from t_fc_good_person bean where 1=1 order by bean.order_time desc limit 1
	    ]]>
	</sql>
	
	<sql id="sql_query_fourvirtues_count" description="查询四德榜的集合数量">
	    <![CDATA[
	    	select count(1) from t_fc_four_virtues 
	    	where 1=1
	    	and type=1 
	    	and title like ?
	    ]]>
	</sql>
	
	<sql id="sql_query_fourvirtues_list" description="查询四德榜的集合">
	    <![CDATA[
	    	select
				title,
				create_time,
				create_user,
				id
   	  		 from t_fc_four_virtues 
   	  		 where 1=1
   	  		 and type=1
			 and title like ?
			 order by order_time desc
	    ]]>
	</sql>
	
	<sql id="sql_query_fourvirtues_list_mobile" description="查询四德榜的集合">
	    <![CDATA[
	    	select bean.* from t_fc_four_virtues bean where 1=1 order by bean.order_time desc limit 1
	    ]]>
	</sql>
	
	<sql id="sql_query_goodthings_count" description="查询凡人好事的集合数量">
	    <![CDATA[
	    	select count(1) from t_fc_good_things 
	    	where 1=1
	    	and type=1 
	    	and title like ?
	    ]]>
	</sql>
	
	<sql id="sql_query_goodthings_list" description="查询凡人好事的集合">
	    <![CDATA[
	    	select
				title,
				create_time,
				create_user,
				id
   	  		 from t_fc_good_things 
   	  		 where 1=1
   	  		 and type=1
			 and title like ?
			 order by order_time desc
	    ]]>
	</sql>
	
	<sql id="sql_query_goodthings_list_query" description="查询凡人好事的集合">
	    <![CDATA[
	    	select bean.* from t_fc_good_things bean where 1=1 order by bean.order_time desc limit 1
	    ]]>
	</sql>
	
	<sql id="sql_query_volunteerservice_count" description="查询志愿服务的集合数量">
	    <![CDATA[
	    	select count(1) from t_fc_volunteer_service 
	    	where 1=1
	    	and type=1 
	    	and title like ?
	    ]]>
	</sql>
	
	<sql id="sql_query_volunteerservice_list" description="查询志愿服务的集合">
	    <![CDATA[
	    	select
				title,
				create_time,
				create_user,
				id
   	  		 from t_fc_volunteer_service 
   	  		 where 1=1
   	  		 and type=1
			 and title like ?
			 order by order_time desc
	    ]]>
	</sql>
	
	<sql id="sql_query_volunteerservice_list_mobile" description="查询志愿服务的集合">
	    <![CDATA[
	    	select bean.* from t_fc_volunteer_service bean where 1=1 order by bean.order_time desc limit 1
	    ]]>
	</sql>
	
	
	
	<sql id="sql_query_beautifulcountry_count" description="查询美丽乡村的集合数量">
	    <![CDATA[
	    	select count(1) from t_fc_beautiful_country 
	    	where 1=1
	    	and type=1 
	    	and title like ?
	    ]]>
	</sql>
	
	<sql id="sql_query_beautifulcountry_list" description="查询美丽乡村集合">
	    <![CDATA[
	    	select
				title,
				create_time,
				create_user,
				id
   	  		 from t_fc_beautiful_country 
   	  		 where 1=1
   	  		 and type=1
			 and title like ?
			 order by order_time desc
	    ]]>
	</sql>
	
	<sql id="sql_query_beautifulcountry_list_mobile" description="查询手机端美丽乡村集合">
	    <![CDATA[
	    	select
				title,
				create_time,
				create_user,
				label,
				image_path,
				id
   	  		 from t_fc_beautiful_country 
   	  		 where 1=1
   	  		 and type=1
			 and title like ?
			 order by create_time desc
	    ]]>
	</sql>
	
	<sql id="sql_query_classroom_count" description="查询青云山大讲堂的集合数量">
	    <![CDATA[
	    	select count(1) from t_fc_class_room 
	    	where 1=1
	    	and type=1 
	    	and title like ?
	    ]]>
	</sql>
	
	<sql id="sql_query_classroom_list" description="查询青云山大讲堂集合">
	    <![CDATA[
	    	select
				title,
				create_time,
				create_user,
				id
   	  		 from t_fc_class_room 
   	  		 where 1=1
   	  		 and type=1
			 and title like ?
			 order by order_time desc
	    ]]>
	</sql>
	
	<sql id="sql_query_classroom_list_mobile" description="查询青云山大讲堂集合">
	    <![CDATA[
	    	select
				title,
				create_time,
				create_user,
				id
   	  		 from t_fc_class_room 
   	  		 where 1=1
   	  		 and type=1
			 and title like ?
			 order by create_time desc
	    ]]>
	</sql>
	
	<sql id="sql_query_impresssouth_count" description="查询印象汶南的集合数量">
	    <![CDATA[
	    	select count(1) from t_fc_impress_south 
	    	where 1=1
	    	and type=1 
	    	and title like ?
	    ]]>
	</sql>
	
	<sql id="sql_query_impresssouth_list" description="查询印象汶南集合">
	    <![CDATA[
	    	select
				title,
				create_time,
				create_user,
				id
   	  		 from t_fc_impress_south 
   	  		 where 1=1
   	  		 and type=1
			 and title like ?
			 order by order_time desc
	    ]]>
	</sql>
	
	<sql id="sql_query_impresssouth_list_mobile" description="查询印象汶南集合">
	    <![CDATA[
	    	select
				title,
				create_time,
				create_user,
				id
   	  		 from t_fc_impress_south 
   	  		 where 1=1
   	  		 and type=1
			 and title like ?
			 order by create_time desc
	    ]]>
	</sql>
</root>