package com.ymx.manager.security.common;

import com.ymx.common.base.entity.Function;
import com.ymx.common.security.cache.CacheFactory;
import com.ymx.common.utils.CommonUtil;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;




/**
 * 菜单功能创建
 * 
 */
public class FunctionBuilder {

    /**
     * 创建操作按钮权限菜单
     * @return 操作按钮权限菜单字符串
     */
    public static String build(HttpServletRequest request) {
        String menuId = SessionHelper.getRequest().getParameter( "leftMenuId" );
        String userId = SessionHelper.getUserId();
        return build( userId, menuId );
    }

    /**
     * 根据指定的操作功能编码构建菜单
     * 
     * @param funs 操作功能编码，使用,号隔开
     * @return 操作按钮字符串
     */
    public static String build( String funs ) {
        String menuId = SessionHelper.getRequest().getParameter( "leftMenuId" );
        String userId = SessionHelper.getUserId();
        return build( funs, userId, menuId );
    }

    private static String build( String funsStr, String userId, String menuId ) {
        funsStr += ",";
        String key = userId + "_" + menuId;
        List<Function> funs = CacheFactory.getInstance().getUserFunMap().get( key );
        StringBuffer result = new StringBuffer("<div style=\"margin-bottom:5px\">\n" );
        if ( null != funs ) {
            for ( Function function : funs ) {
                if ( funsStr.indexOf( function.getFunId() + "," ) != -1 )  {
                    result.append( "<a href=\"javascript:;\" id=\"" )
                            .append( function.getFunId() )
                            .append("\" class=\"easyui-linkbutton\" data-options=\"iconCls:'" )
                            .append( function.getFunId() ).append( "',plain:true\">" ).append( function.getFunName() ).append( "</a>\n" );
                }
            }
        } else {
            result.append( "<div style=\"padding:5px;\"><font color=\"#cccccc\">无任何操作权限</font></div>\n" );
        }
        result.append( "</div>" );
        return result.toString();
    }

    /**
     * 列表菜单功能展示
     * @company 江苏岳创信息
     * @date 2017-9-19
     * @param userId 登录用户id
     * @param menuId 点击左侧菜单id
     * @return
     * @return String
     */
    private static String build( String userId , String menuId ) {
        String key = userId + "_" + menuId;
        List<Function> funs = CacheFactory.getInstance().getUserFunMap().get( key );
        StringBuffer result = new StringBuffer( "<div class=\"layui-btn-group\">\n" );
        if ( null != funs )  {
            for ( Function function : funs ){
                result.append( "<button class=\"layui-btn\" id=\"" + function.getFunName() + "\" name=\""+ function.getFunName() +"\"  url=\"" + function.getFunLink() + "\">");
                if (StringUtils.hasLength(function.getFunIcon())  && CommonUtil.isEmpty(function.getFunIcon())){
                    result.append("\n<i class=\"layui-icon\">" + function.getFunIcon() + "</i>");
                }
                result.append(function.getFunDesc() );
                result.append("</button>" ).append( "\n" );
            }
        } else{
            result.append( "<div style=\"padding:5px;\"><font color=\"#cccccc\">暂无任何操作权限</font></div>\n" );
        }
        result.append( "</div>" );
        return result.toString();
    }


}
