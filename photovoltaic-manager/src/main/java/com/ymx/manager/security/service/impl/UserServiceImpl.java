package com.ymx.manager.security.service.impl;

import com.ymx.common.base.dao.IDao;
import com.ymx.common.base.dao.IUserDao;
import com.ymx.common.base.entity.User;
import com.ymx.common.base.entity.UserRole;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageInfo;
import com.ymx.common.common.result.PageView;
import com.ymx.common.utils.*;
import com.ymx.manager.security.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户管理业务层接口实现
 * 
 */

@Service
public class UserServiceImpl implements IUserService {
	
	@Autowired
	private IUserDao userDao;
	
	@Autowired
	private IDao dao;

	/**
	 * 用户登录
	 * @param userId   用户名
	 * @param password  登录密码
	 * @return 登录的用户，如果错误，则返回null
	 */
	public User login(String userId, String password) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("userId", userId);
		paramMap.put("password", password);
		User user = userDao.userLogin(paramMap);
		return user;
	}
	
	/**
	 * 
	 * @desc
	 * @date 2017-10-24
	 * @UserServiceImpl.java
	 * @param userId
	 * @return
	 */
	@Override
	public boolean checkUserNameExist(String userId) {
		String sql = SqlFactory.getInstance().getSql("sql_query_user_exist");
		int result = dao.findForInt(sql, new Object[] {userId});
		return result == 1 ;
	}
	
	/**
	 * 获取分页数据
	 * @param user 		查询条件
	 * @param pageView  分页实体
	 * @return 数据对象
	 */
	public CallResult list(User user, PageView pageView) {
		CallResult  callResult = CallResult.newInstance();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		QueryParamMapUtil.setMapPropreties(paramMap, user, true);
		int total = userDao.searchUsersCount(paramMap);
		PageInfo pageInfo = CommonPage.getPageInfo(total, pageView, "");
		
		paramMap.put("page", pageInfo);
		List<User> list = userDao.searchUsers(paramMap);
		//清除map
		paramMap.clear();
		callResult.setPageInfo(pageInfo);
		paramMap.put("data", list);
		callResult.setReModel(paramMap);
		return callResult;
	}
	
	/**
	 * 根据用户编码查询用户
	 * @param userId 用户编码
	 * @return 用户信息
	 */
	public User query(String userId) {
		User user = userDao.queryUser(userId);
		return user;
	}
	public User queryUserById(String id) {
		User user = userDao.queryUserById(id);
		return user;
	}

	/**
	 * 删除用户
	 * 
	 * @param userIds
	 *            用户编码，多个之间使用,号隔开
	 * @return 删除是否成功
	 */
	public boolean delete(String userIds) {
		int result = userDao.deleteUsers(SqlXml.getInParamList(userIds));
		return result > 0;
	}
	/**
	 * 初始化用户密码
	 * @param userIds  用户编码，多个之间使用,号隔开
	 * @return 删除是否成功
	 */
	public boolean resetPswd(String userIds) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("userids", SqlXml.getInParamList(userIds));
		map.put("password", MD5.password(Read.getMsg("system.default.user.password")));
		int result = userDao.defaultUserPassword(map);
		return result > 0;
	}
	/**
	 * 新增操作
	 * 
	 * @param user
	 *            用户信息
	 * @return 新增是否成功
	 */
	public boolean insert(User user) {
		int result = userDao.insertUser(user);
		// 更新用户的角色信息
		updateUserRole(user);
		return result == 1;
	}
	/**
	 * 更新用户角色信息
	 * 
	 * @param user
	 *            用户
	 */
	private void updateUserRole(User user) {
		String userId = user.getUserId();
		if(null != user && CommonUtil.isEmpty(user.getRoleIds())){
			String[] roleIds = user.getRoleIds().split(",");
			List<UserRole> uroles = new ArrayList<UserRole>();
			UserRole urole = null;
			if (roleIds != null) {
				for (int i = 0; i < roleIds.length; i++) {
					urole = new UserRole();
					urole.setUserId(userId);
					urole.setRoleId(roleIds[i]);
					uroles.add(urole);
				}
			}
			saveUserRole(userId, uroles);
		}
	}
	/**
	 * 修改操作
	 * @param user  用户信息
	 * @return 修改是否成功
	 */
	public boolean update(User user) {
		int result = userDao.updateUser(user);
		// 更新用户的角色信息
		updateUserRole(user);
		return result == 1;
	}
	/**
	 * 查询用户角色
	 * @param userId  用户编码
	 * @return 用户角色
	 */
	public List<UserRole> queryUserRole(String userId) {
		// 首先查询当前用户所拥有的角色信息
		String sql = SqlFactory.getInstance().getSql("sql_query_user_roles");
		List<UserRole> userRoles = userDao.queryUserRoles(userId);
		Map<String, String> roleMap = new HashMap<String, String>();
		for (UserRole user : userRoles) {
			roleMap.put(user.getRoleId(), user.getUserId());
		}
		// 然后查询所有的角色信息列表
		String roleSql = SqlFactory.getInstance().getSql("sql_search_roles");
		List<UserRole> allUserRoles = userDao.searchRoles();
		for (UserRole ur : allUserRoles) {
			ur.setUserId(roleMap.get(ur.getRoleId()));
		}
		return allUserRoles;
	}
	/**
	 * 需要保存的角色信息
	 * @param userId  用户编码
	 * @param role    角色信息List
	 * @return 保存是否成功
	 */
	public boolean saveUserRole(String userId, List<UserRole> role) {
		// 首先进行用户下的角色删除
		userDao.deleteRoleUsers(userId);
		// 将用户的角色信息插入到用户角色表中
		int result = 0;
		if (role.size() > 0) {
			result = userDao.insertUserRoles(role);
		}
		return result >= 0;
	}
	/**
	 * 查询指定职务下的用户信息
	 * @param dutyId 职务编码
	 * @return 用户信息List
	 */
	public List<User> queryDutyUsers(String dutyId) {
		List<User> result = userDao.queryDutyUsers(dutyId);
		return result;
	}
	/**
	 * 修改用户密码
	 * @param userId 用户编码
	 * @param password 密码
	 * @return 修改是否成功
	 */
	public boolean changePassword(String userId, String password) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("userId", userId);
		paramMap.put("password", password);
		int result = userDao.updatePassword(paramMap);
		return result == 1;
	}
	public List<UserRole> queryUserRoleByID(String userId) {
		// 首先查询当前用户所拥有的角色信息
		String sql = SqlFactory.getInstance().getSql("sql_query_user_roles");
		List<UserRole> userRoles = userDao.queryUserRoles(userId);
		
		return userRoles;
	}

	@Override
	public String queryUserIdBylocal(String userId, String password) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("userId", userId);
		paramMap.put("password", password);
		String createUserId = userDao.queryUserLocal(paramMap);
		return createUserId;
	}
}
