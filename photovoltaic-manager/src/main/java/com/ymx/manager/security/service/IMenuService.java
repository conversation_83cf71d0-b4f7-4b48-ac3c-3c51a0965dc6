package com.ymx.manager.security.service;

import com.ymx.common.base.entity.Function;
import com.ymx.common.base.entity.LayuiMenu;
import com.ymx.common.base.entity.Menu;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;




/**
 * 菜单管理业务层接口
 */
public interface IMenuService {

    /**
     * 查询菜单信息
     * @return
     */
    List<Menu> list(HttpServletRequest request);
    
    /**
     * layui　查询菜单列表
     * @company 江苏岳创信息
     * @file IMenuService.java
     * @date 2017-9-19
     * @return LayuiMenu
     */
    LayuiMenu listMenus(HttpServletRequest request);

    /**
     * 获取包括操作功能的菜单列表
     * @param roleId  角色编码
     * @return 权限菜单列表
     */
    List<Menu> listWithFunctions( String roleId ,HttpServletRequest request);

    /**
     * 根据用户编码查询用户的菜单
     * @param userId
     *            用户编码
     * @return 用户所具有的菜单权限
     */
    List<Menu> searchUserMenus( String userId,HttpSession session,HttpServletRequest request );

    /**
     * 查询用户的菜单权限
     * @param userId  用户编码
     * @return 菜单权限对象
     */
    Map<String, List<Function>> queryUserMenuFunctions(String userId ,HttpServletRequest request);

    /**
     * 查询菜单信息
     * @param menuId  菜单编码
     * @return 菜单信息
     */
    Menu queryMenu( String menuId,HttpServletRequest request );
    
    /**
     * 查询菜单功能信息
     * @param funId   菜单编码
     * @return        菜单信息
     */
    Function queryFunctionById( String funId ,HttpServletRequest request);

    /**
     * 查询顶级菜单
     * @return
     */
    List<Menu> queryTopMenus(HttpServletRequest request);

    /**
     * 根据上级菜单，查询子菜单
     * @param pMenuId
     * @return
     */
    List<Menu> queryChildrenMenus( String pMenuId,HttpServletRequest request );

    /**
     * 增加菜单
     * @param menu 菜单信息
     * @return 增加是否成功
     */
    boolean add( Menu menu );

    /**
     * 更新菜单
     * @param menu 菜单信息
     * @return 更新是否成功
     */
    boolean update( Menu menu );

    /**
     * 检测菜单名称是否已经存在
     * @param menuName  菜单名称
     * @param pMenuId  上级编码
     * @param menuId  当前菜单编码（修改时进行判断）
     */
    boolean checkMenuNameExist( String menuName, String pMenuId, String menuId );

    /**
     * 删除菜单
     * @param menuId  菜单编码
     * @return 删除是否成功
     */
    boolean delete( String menuId );

    /**
     * 查询菜单的所有功能
     * @param menuId  菜单编码
     * @return 菜单功能列表
     */
    List<Function> queryMenuFunctions( String menuId ,HttpServletRequest request);

    /**
     * 保存菜单操作功能
     * @param menuId 菜单编码
     * @param funs 操作功能
     */
    void saveMenuFunctions( String menuId, List<Function> funs );

    /**
     * 进行菜单拍下
     * 
     * @param menu
     *            需要排序的菜单
     */
    void order( List<Menu> menu );

    /**
     * 新增菜单下的功能
     * @param function
     * @return
     */
	boolean addFunction(Function function);

	/**
	 *编辑菜单下的功能
	 * @param function
	 * @return
	 */
	boolean updateFunction(Function function);
	
	/**
	 * 判断菜单功能名称是否已存在
	 * @param function
	 * @param operator
	 * @return
	 */
	boolean checkFunctionNameExist(Function function , String operator) ;

	/**
	 * 批量删除
	 * @param ids
	 * @return
	 */
	boolean batchDelete(String[] ids);

	int queryFunctionOrderMax(String menuId);

}
