package com.ymx.manager.security.web;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ymx.common.base.entity.SearchCondition;
import com.ymx.common.base.entity.Setter;
import com.ymx.common.common.result.PageView;
import com.ymx.common.utils.IDUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.ymx.common.common.result.CallResult;
import com.ymx.common.security.cache.CacheFactory;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.service.ISetterService;
import com.ymx.manager.security.utils.JsonUtil;


/**
 *系统设置对应的controller控制类
 */
@Controller
@RequestMapping("setter")
public class SetterController {
    
        @Autowired
        private ISetterService setterService;
        
        @InitBinder
        protected void initBinder(WebDataBinder binder) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
        }

		/**
		  * 跳转到系统设置列表页面
		  *@param request 请求
		  *@return 转到跳转后的list页面
		  */
        @RequestMapping("forwardList.htm")
        public ModelAndView forwardList(HttpServletRequest request) {
            //request.setAttribute( "functionStr", FunctionBuilder.build() );
//        	String mkt = "";
//       	 String datamkt = "";
//            Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//            if(ob.toString().contains("en"))
//            {
//         	   mkt="en";
//         	   datamkt = "en";
//            }
//            else
//            {
//         	   mkt="cn";
//         	   datamkt="cn";
//            }
//            request.setAttribute( "datamkt", datamkt );
//            request.setAttribute( "mkt", mkt );
            return new ModelAndView("sys/setter_list");
        }
        
        /**
         * 获取列表数据
         * @company 江苏岳创信息
         * @desc 
         * @file SetterController.java
         * @date 2017-9-21
         * @param pageView
         * @param request
         * @param response
         * @return void
         */
        @RequestMapping("list.web")
        @ResponseBody
        public CallResult list(PageView pageView , HttpServletRequest request, HttpServletResponse response) {
        	List<SearchCondition> conditions = new ArrayList<SearchCondition>();
            return setterService.list(  pageView , conditions ) ;
        }
        
        @RequestMapping("listAll.htm")
        public void listAll(HttpServletRequest request, HttpServletResponse response)
        {
            JsonUtil.list2JsonForDate(response, setterService.listAll());
        }
        
        @RequestMapping("checkNameExist.htm")
        public void checkNameExist(String id,String name, HttpServletResponse response) {
            JsonUtil.boolOut( response, setterService.checkNameExist( id, name ) );
        }
        
        @RequestMapping("toSave.htm")
        @ResponseBody
        public ModelAndView toSave(HttpServletRequest request,String id)  {
            String action = "add";
            Setter setter = new Setter();
            if(null != id && !"".equals(id))  {
                Object obj = setterService.queryById( id );
                if(obj instanceof Setter)  {
                    setter = (Setter) obj;
                }
                action = "edit";
            }
            else  {
                setter.setId(IDUtil.getUUIDStr());
            }
            request.setAttribute( "bean", setter );
            request.setAttribute("action", action);
//            String mkt = "";
//       	    String datamkt = "";
//            Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//            if(ob.toString().contains("en"))
//            {
//         	   mkt="en";
//         	   datamkt = "en";
//            }
//            else
//            {
//         	   mkt="cn";
//         	   datamkt="cn";
//            }
//            request.setAttribute( "datamkt", datamkt );
//            request.setAttribute( "mkt", mkt );
            return new ModelAndView("sys/setter_save");
        }
        
        @RequestMapping("save.htm")
        public void save(Setter setter, HttpServletResponse response, HttpServletRequest request, String action)  {
            boolean result = false;
            if("add".equals(action)) {
                setter.setCreateUser( SessionHelper.getUserId() );
                setter.setCreateDate( new Date() );
                result = setterService.insert( setter );
            } else  {
                setter.setUpdateDate( new Date() );
                setter.setUpdateUser( SessionHelper.getUserId() );
                result = setterService.updateById( setter );
            }
            if(result)
            {
                List<Object> setters = setterService.listAll();
                Setter set = null;
                Map<String,String> setterMap = new HashMap<String,String>();
                for ( Object object : setters )  {
                    set = (Setter)object;
                    setterMap.put( set.getSetterName(), set.getSetterValue() );
                }
                CacheFactory.getInstance().setSetterMap( setterMap );
            }
            JsonUtil.boolOut( response, result );
        }
        
        @RequestMapping("delete.htm")
        public void delete(String id,HttpServletResponse response) {
            boolean result = setterService.deleteById( id );
            JsonUtil.boolOut( response, result );
        }
        
        /**
         * 批量删除
         * @param id
         * @param response
         */
        @RequestMapping("batchDelete.htm")
        public void batchDelete ( HttpServletRequest reqeust , HttpServletResponse response) {
        	String [] ids = reqeust.getParameterValues("ids[]");
        	boolean result = setterService.batchDelete( ids );
            JsonUtil.boolOut( response, result );
        }
        
        @RequestMapping("view.htm")
        public ModelAndView view(String id,HttpServletRequest request)  {
            Object setter = setterService.queryById(id);
            request.setAttribute("bean", setter);
//            String mkt = "";
//       	    String datamkt = "";
//            Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//            if(ob.toString().contains("en"))
//            {
//         	   mkt="en";
//         	   datamkt = "en";
//            }
//            else
//            {
//         	   mkt="cn";
//         	   datamkt="cn";
//            }
//            request.setAttribute( "datamkt", datamkt );
//            request.setAttribute( "mkt", mkt );
            return new ModelAndView("sys/setter_view");
        }
    
}
