
package com.ymx.manager.security.web;

import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.common.base.entity.Menu;
import com.ymx.common.base.entity.User;
import com.ymx.common.base.entity.UserRole;
import com.ymx.common.common.result.CallResult;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.station.model.PowerMapModel;
import com.ymx.service.photovoltaic.station.service.PowerMapService;
import com.ymx.common.utils.CheckMobile;
import com.ymx.common.utils.MD5;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import com.ymx.common.security.cache.CacheFactory;
import com.ymx.manager.security.service.ICacheService;
import com.ymx.manager.security.service.IUserService;
import com.ymx.manager.security.utils.JsonUtil;


/**
 * 用户登录Controller
 * @version  [版本号, 2017-09-23]
 * @see  [相关类/方法]
 * @since  [产品/模块版本]
 */
@Controller
@RequestMapping("")
public class LoginController {
	@Resource
	private PowerMapService powerMapService;
	SessionLocaleResolver localeResolver;
	
    @Autowired
    private IUserService userService;

    @Autowired
    private ICacheService cacheService;
   /* @Resource
	private LogService logService;*/
    //记录用户登录日志信息
    @Autowired
    private ILoginLoggerService loginLoggerService;

    /**
     * 系统登录页面
     * @return 跳转到系统登录页面
     */
    @RequestMapping( value = "/free/index.htm" )
    public ModelAndView index(HttpServletRequest request) {
        String userAgent = request.getHeader( "USER-AGENT" ).toLowerCase();  
        if(CheckMobile.check( userAgent )) {
            return new ModelAndView("mobile_login");
        } else {
            return new ModelAndView( CacheFactory.getInstance().getSetter( "login.page" ) );
        }
    }
    
    /**
     * 工作台
     * @return 跳转到工作台
     */
    @RequestMapping( value = "/index/portal.htm")
    public ModelAndView protal(HttpServletRequest request,String type) {
    	 List<PowerMapModel> powerMaplt = powerMapService.queryGuojiaListByPowId();
         request.setAttribute( "powerMaplt", powerMaplt );
//    	String mkt="";
//    	String layuimkt="";
//	    String datamkt = "";
//	    String mapmkt = "";
        Object ob = request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt = "en-us";
//     	  layuimkt="en";
//        }
//        else
//        {
//     	   mkt = "zh-cn";
//     	  layuimkt="cn";
//        }
//	    if(ob.toString().contains("en"))
//	    {
//		    mkt="en";
//		    datamkt = "en";
//		    mapmkt = "en-us";
//	    }
//	    else
//	    {
//		    mkt="cn";
//		    datamkt="cn";
//		    mapmkt = "zh-cn";
//	    }
//	    request.setAttribute( "datamkt", datamkt );
//	    request.setAttribute( "mapmkt", mapmkt);
//	    request.setAttribute( "mkt", mkt );

//	    TranslateLanguage.initRequestLanguage(request,ob);

        Object username =  request.getSession().getAttribute("userName");
        request.setAttribute( "username", username );
	    request.setAttribute( "type", type );
//        request.setAttribute( "layuimkt", layuimkt );
	    return new ModelAndView( "powerMap/power_map" );
//    	return new ModelAndView( "welcome" );//暂时废掉
    }
	/**
	 * @param request
	 * @return
	 */
	//@CrossOrigin(origins = "http://192.168.10.22:8080")注解不生效
	@RequestMapping(value={"/free/check.api" , "/free/check.web"})
	@ResponseBody
	public CallResult check(HttpServletRequest request,HttpSession ss,HttpServletResponse response) {
		response.setHeader("Access-Control-Allow-Credentials", "true");
		response.setHeader("Access-Control-Expose-Headers", "Set-Cookie");
		response.setHeader("Access-Control-Allow-Origin","http://192.168.10.21:8080");
		String userId = request.getParameter( "userId" );
		String password = request.getParameter( "password" );
		String language = request.getParameter( "isbn" );
		User result = userService.login( userId, MD5.password( password ) );
		if ( null == result ){
			CallResult callResult=CallResult.newInstance();
			callResult.setErrNo("1");
			return callResult;
		}else{
			HttpSession session = request.getSession( true );
			String typeLanguage="cn";
			if(null != language)
			{
				if(language.equals("中文") || language.equals(""))
				{
					Locale loc = new Locale("zh","CN");
					ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
					request.getSession().setAttribute( "mkt", "cn" );
					request.getSession().setAttribute( "tabmkt", "cn" );
					typeLanguage="cn";
					request.getSession().setAttribute( "mapmkt", "zh-cn" );
				}else if(language.equals("عربي ،")){
					Locale loc = new Locale("ar","AR");
					ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
					request.getSession().setAttribute( "mkt", "ar" );
					request.getSession().setAttribute( "tabmkt", "en" );
					typeLanguage="ar";
					request.getSession().setAttribute( "mapmkt", "en-us" );
				}else if(language.equals("日本語")){
					Locale loc = new Locale("ja","JA");
					ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
					request.getSession().setAttribute( "mkt", "ja" );
					request.getSession().setAttribute( "tabmkt", "en" );
					typeLanguage="ja";
					request.getSession().setAttribute( "mapmkt", "en-us" );
				}else if(language.equals("Français")){
					Locale loc = new Locale("fr","FR");
					ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
					request.getSession().setAttribute( "mkt", "fr" );
					request.getSession().setAttribute( "tabmkt", "en" );
					typeLanguage="fr";
					request.getSession().setAttribute( "mapmkt", "en-us" );
				}else if(language.equals("Español")){
					Locale loc = new Locale("es","ES");
					ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
					request.getSession().setAttribute( "mkt", "es" );
					request.getSession().setAttribute( "tabmkt", "en" );
					typeLanguage="es";
					request.getSession().setAttribute( "mapmkt", "en-us" );
				}else
				{
					Locale loc = new Locale("en","US");
					ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
					request.getSession().setAttribute( "mkt", "en" );
					//          		session.setAttribute("tabmkt", "en");
					request.getSession().setAttribute( "tabmkt", "en" );
					typeLanguage="en";
					request.getSession().setAttribute( "mapmkt", "en-us" );
				}
			}else{
				Locale loc = new Locale("zh","CN");
				ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
				request.getSession().setAttribute( "mkt", "cn" );
				request.getSession().setAttribute( "tabmkt", "cn" );
				request.getSession().setAttribute( "mapmkt", "zh-cn" );
			}
			List<UserRole> ltuserrole =  userService.queryUserRoleByID(userId);
			String names = "";
			if(ltuserrole.size()>0)
			{
				for(int i=0;i<ltuserrole.size();i++)
				{
					UserRole ur =  ltuserrole.get(i);
					String rolename = ur.getRoleName();
					names+=","+rolename;
				}
			}
			session.setAttribute("userId", userId);
			session.setAttribute("userKey", result.getId());
			session.setAttribute("userName", result.getUserName());
			session.setAttribute("currentUser", result);
			session.setAttribute("deptId", result.getDeptId());
			session.setAttribute("deptName", result.getDeptName());
			session.setAttribute("deptLevel", result.getDeptLevel());
			session.setAttribute("checkrole", names);
			SessionHelper.setSessionUser(result);
			//记录系统用户登录信息
			LoginLogger logger = new LoginLogger();
			String content = "登录后台";
			logger.setId(UUID.randomUUID().toString());
			logger.setUserId(SessionHelper.getUserId());
			logger.setLastLoginTime(new Date());
			logger.setLoginIp(getIpAddr(request));
			logger.setContent(content);
			loginLoggerService.insert(logger);
			// 设置用户菜单缓存
			cacheService.setUserMenuCache(SessionHelper.getUserId(), session, request);
			// 设置用户操作功能缓存
			cacheService.setUserFunctionCache(SessionHelper.getUserId(), request);
			String userAgent = request.getHeader("USER-AGENT").toLowerCase();
			//				if (CheckMobile.check(userAgent)) {
			//					return new ModelAndView("redirect:" + CacheFactory.getInstance().getSetter("mobile.index.page"));
			//				} else {
			//					return new ModelAndView("redirect:/main.htm");
			//				}
			return  CallResult.newInstance();
		}
	}


	/**
     * 用户登录
     * @param request 请求
     * @param response 响应
     * @return 重定向到系统管理首页
     */
    @RequestMapping( value = "/free/login.htm" )
    public ModelAndView login( HttpServletRequest request,   HttpServletResponse response,HttpSession ss ){
        String userId = request.getParameter( "userId" );
        String password = request.getParameter( "password" );
        String language = request.getParameter( "isbn" );
        HttpSession session = request.getSession( true );
        String typeLanguage="cn";
        if(null != language)
        {
        	if(language.equals("中文") || language.equals(""))
            {
            	Locale loc = new Locale("zh","CN");
          		ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
          		request.getSession().setAttribute( "mkt", "cn" );
          		request.getSession().setAttribute( "tabmkt", "cn" );
	            typeLanguage="cn";
	            request.getSession().setAttribute( "mapmkt", "zh-cn" );
            }else if(language.equals("عربي ،")){
		        Locale loc = new Locale("ar","AR");
		        ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
		        request.getSession().setAttribute( "mkt", "ar" );
		        request.getSession().setAttribute( "tabmkt", "en" );
		        typeLanguage="ar";
		        request.getSession().setAttribute( "mapmkt", "en-us" );
	        }else if(language.equals("日本語")){
		        Locale loc = new Locale("ja","JA");
		        ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
		        request.getSession().setAttribute( "mkt", "ja" );
		        request.getSession().setAttribute( "tabmkt", "en" );
		        typeLanguage="ja";
		        request.getSession().setAttribute( "mapmkt", "en-us" );
	        }else if(language.equals("Français")){
		        Locale loc = new Locale("fr","FR");
		        ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
		        request.getSession().setAttribute( "mkt", "fr" );
		        request.getSession().setAttribute( "tabmkt", "en" );
		        typeLanguage="fr";
		        request.getSession().setAttribute( "mapmkt", "en-us" );
	        }else if(language.equals("Español")){
		        Locale loc = new Locale("es","ES");
		        ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
		        request.getSession().setAttribute( "mkt", "es" );
		        request.getSession().setAttribute( "tabmkt", "en" );
		        typeLanguage="es";
		        request.getSession().setAttribute( "mapmkt", "en-us" );
	        }else
            {
            	Locale loc = new Locale("en","US");
          		ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
          		request.getSession().setAttribute( "mkt", "en" );
//          		session.setAttribute("tabmkt", "en");
	            request.getSession().setAttribute( "tabmkt", "en" );
	            typeLanguage="en";
	            request.getSession().setAttribute( "mapmkt", "en-us" );
            }
        }else{
	        Locale loc = new Locale("zh","CN");
	        ss.setAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME, loc);
	        request.getSession().setAttribute( "mkt", "cn" );
	        request.getSession().setAttribute( "tabmkt", "cn" );
	        request.getSession().setAttribute( "mapmkt", "zh-cn" );
        }
        User result = userService.login( userId, MD5.password( password ) );
        List<UserRole> ltuserrole =  userService.queryUserRoleByID(userId);
        String names = "";
        if(ltuserrole.size()>0)
        {
        	for(int i=0;i<ltuserrole.size();i++)
        	{
        		UserRole ur =  ltuserrole.get(i);
        		String rolename = ur.getRoleName();
        		names+=","+rolename;
        	}
        }
        if ( null == result ) 
        {// 登录失败,跳转到登录页面
            request.setAttribute( "userId", userId );
	        request.setAttribute( "error", TranslateLanguage.getResultLanguage("用户名或者密码错误!",typeLanguage));
            String userAgent = request.getHeader( "USER-AGENT" ).toLowerCase();  
            if(CheckMobile.check( userAgent )) {
                return new ModelAndView("mobile_login");
            }  else {
                return new ModelAndView( CacheFactory.getInstance().getSetter( "login.page" ) );
            }
        } 
        else    
        {    
            session.setAttribute( "userId", userId );
            session.setAttribute("userKey" , result.getId());
            session.setAttribute( "userName", result.getUserName() );
            session.setAttribute( "currentUser", result );
            session.setAttribute("deptId", result.getDeptId());
            session.setAttribute("deptName", result.getDeptName());
            session.setAttribute("deptLevel", result.getDeptLevel());
            session.setAttribute("checkrole", names);
            SessionHelper.setSessionUser(result);
            //记录系统用户登录信息
            LoginLogger logger = new LoginLogger();
            String content = "登录后台";
            logger.setId( UUID.randomUUID().toString() );
            logger.setUserId( SessionHelper.getUserId() );
            logger.setLastLoginTime( new Date() );
            logger.setLoginIp( getIpAddr(request) );
            logger.setContent(content);
          //  logger.setOperation_id(UUID.randomUUID().toString());
            loginLoggerService.insert( logger );
         /*  LoggerModel lm = new LoggerModel();
            lm.setId(UUID.randomUUID().toString());
            lm.setContent("啦啦啦啦啦");
            logService.addMember(lm);*/
           // boolean updateResult = loginLoggerService.updateById( logger );
           // if(!updateResult) {
              
           // }
            // 设置用户菜单缓存
            cacheService.setUserMenuCache( SessionHelper.getUserId(), session,request);

            // 设置用户操作功能缓存
            cacheService.setUserFunctionCache( SessionHelper.getUserId(),request );
            String userAgent = request.getHeader( "USER-AGENT" ).toLowerCase();  
            if(CheckMobile.check( userAgent )) {
                return new ModelAndView("redirect:"+CacheFactory.getInstance().getSetter( "mobile.index.page" ));
            }
            else   {
                return new ModelAndView( "redirect:/main.htm" );
            }
        }
    }
    
    private String getIpAddr(HttpServletRequest request) { 
        String ip = request.getHeader("x-forwarded-for"); 
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) { 
            ip = request.getHeader("Proxy-Client-IP"); 
        } 
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) { 
            ip = request.getHeader("WL-Proxy-Client-IP"); 
        } 
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) { 
            ip = request.getRemoteAddr(); 
        } 
        return ip; 
    }

    @RequestMapping( value = "/main.htm" )
    public ModelAndView main( HttpServletRequest request ) {
        // 从缓存中获取用户可访问的菜单权限
        List<Menu> menus = CacheFactory.getInstance().getUserMenu(SessionHelper.getUserId());

        String menuString = JsonUtil.list2Json( menus );
        request.setAttribute( "menuString", menuString );
        String mainStyle = CacheFactory.getInstance().getSetter( "main.style" );
        if(null == mainStyle || "".equals( mainStyle ))  {
            mainStyle = "main";
        }  else  {
            request.setAttribute( "menus", menus );
        }
        return new ModelAndView( mainStyle );
    }

    public static void main( String[] args ) {
        System.out.println(MD5.password("123456"));
    }
    /**
     * 工作台
     * @return 跳转到工作台
     * , produces = "text/html; charset=utf-8"
     */
    @RequestMapping( value = "/index/initmap.htm" )
    public ModelAndView initmap(String mid) 
    { 
    	ModelAndView  mv = new ModelAndView( "initmap" );
    	mv.getModel().put("mid", mid);
    	 return mv;
    }
    @RequestMapping( value = "/index/map.htm" )
    public ModelAndView protalmap(HttpServletRequest request) 
    { 
    	
    	String mid =  request.getParameter("mid");
    	ModelAndView  mv = new ModelAndView( "bingmap" );
    	mv.getModel().put("mid", mid);
    	/*ModelAndView  mv = new ModelAndView( "welcomehtm" );
    	
    	String mkt =  request.getParameter("mkt");
    	if(mkt.toString().contains("en"))
        {
     	   mkt = "en-us";
        }
        else
        {
     	   mkt = "zh-cn";
        }
    	PageUtil pu = new PageUtil();
        String initurl = pu.getBasePath(request).toString();
       // request.setAttribute( "initurl", initurl );*/
        request.setAttribute( "mkt", "en" );
      //  mv.addObject("initurl", initurl );
        System.out.println("bing================================================");
        return mv;
    }
    @RequestMapping( value = "/index/baidumap.htm" )
    public ModelAndView protalbaidumap(HttpServletRequest request) 
    { 
    	String mid =  request.getParameter("mid");
    	ModelAndView  mv = new ModelAndView("baidumap");
    	mv.getModel().put("mid", mid);
    	/*ModelAndView  mv = new ModelAndView( "welcomehtm" );
    	
    	String mkt =  request.getParameter("mkt");
    	if(mkt.toString().contains("en"))
        {
     	   mkt = "en-us";
        }
        else
        {
     	   mkt = "zh-cn";
        }
    	PageUtil pu = new PageUtil();
        String initurl = pu.getBasePath(request).toString();
       // request.setAttribute( "initurl", initurl );
        request.setAttribute( "mkt", mkt );
        mv.addObject("initurl", initurl );*/
    	System.out.println("baidu================================================");
        return mv;
    }
   /* @RequestMapping( value = "/index/changeCoordinate.htm" )
    public ModelAndView changeCoordinate(String powerStationId,String countriesId,String province,String cityId,String streetName) 
    { 
    	ModelAndView  mv = new ModelAndView( "changeCoordinate" );
    	mv.getModel().put("powerStationId", powerStationId);
    	mv.getModel().put("countriesId", countriesId);
    	mv.getModel().put("province", province);
    	mv.getModel().put("cityId", cityId);
    	mv.getModel().put("streetName", streetName);
    	 return mv;
    }*/
}
