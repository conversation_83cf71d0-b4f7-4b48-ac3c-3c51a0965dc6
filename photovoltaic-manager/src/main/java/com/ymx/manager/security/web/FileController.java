package com.ymx.manager.security.web;

import com.ymx.common.base.service.FileHandleService;
import com.ymx.common.base.service.FileTransService;
import com.ymx.common.common.constant.ErrCode;
import com.ymx.common.common.result.CallResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Decoder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/file")
/**
 * 
 * Copyright © 2018 意美旭科技
 * @Description:文件处理控制层
 * @date: 2018-04-26 下午7:29:23
 */
public class FileController {

	private static final Logger logger = LoggerFactory.getLogger(FileController.class);

	@Resource
	private FileHandleService fileHandleService;
	@Resource
	private FileTransService fileTransService;

	@RequestMapping(value={ "/upload.web"})
	@ResponseBody
	/**
	 * @Description: 文件上传
	 * @date: 2017-09-28 下午7:29:23
	 * @param: file 上传图片
	 * @param: type 模块号，如头像、资讯
	 */
	public CallResult upload(@RequestParam("file") MultipartFile file, String type) {
		// 获取上传路径结果
		CallResult result = CallResult.newInstance();
		// 上传文件结果
		CallResult transResult;
		// 上传文件不存在
		if (file == null || file.getSize() == 0) {
			logger.error(ErrCode.UPLOAD_PIC_FAL_TEXT);
			result.setErr(ErrCode.UPLOAD_PIC_NULL, ErrCode.UPLOAD_PIC_NULL_TEXT);
			return result;
		}
		// 获取文件名
		String originalFilename = file.getOriginalFilename();
		logger.info(originalFilename);
		// 获取文件上传路径
		result = fileHandleService.uploadPath(type,originalFilename,file.getSize());
		if (!result.isSuccess()) {
			return result;
		}
		String destPath = (String) result.getReModel();
		InputStream inputStream = null;
		// 上传文件
		try {
			inputStream = file.getInputStream();
			transResult = fileTransService.uploadFile(destPath, inputStream);
		} catch (IOException e) {
			logger.error("IOException",e);
			result.setErr(ErrCode.IO_EXCEPTION, ErrCode.IO_EXCEPTION_TEXT);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.setErr(ErrCode.IO_EXCEPTION, ErrCode.IO_EXCEPTION_TEXT);
			return result;
		}finally {
			try {
				inputStream.close();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		// 上传成功返回文件名称
		if (transResult.isSuccess()) {
			File tempFile = new File((String) result.getReModel());
			String fileName = tempFile.getName();
			result.setReModel(fileName);
			//真实文件名
			return result;
		}
		// 上传失败返回错误内容
		else {
			return transResult;
		}
	}

	@RequestMapping(value={ "/batchUpload.web"})
	@ResponseBody
	/**
	 * @Description: 文件上传
	 * @param: file 上传图片
	 * @param: type 模块号，如头像、资讯
	 */
	public CallResult batchUpload(@RequestParam("file") MultipartFile[] file, String type) {
		// 获取上传路径结果
		CallResult result = CallResult.newInstance();
		// 上传文件结果
		CallResult transResult;
		// 上传文件不存在
		if (null == file || 0 == file.length) {
			logger.error(ErrCode.UPLOAD_PIC_FAL_TEXT);
			result.setErr(ErrCode.UPLOAD_PIC_NULL, ErrCode.UPLOAD_PIC_NULL_TEXT);
			return result;
		}
		// 上传后的文件名称
		List<String> list = new ArrayList<>();
		// 上传前的真实文件名称
		List<String> realFileNameList = new ArrayList<>();
		for (MultipartFile fileObj : file) {
			// 获取文件名
			String originalFilename = fileObj.getOriginalFilename();
			logger.info(originalFilename);
			realFileNameList.add(originalFilename);
			// 获取文件上传路径
			result = fileHandleService.uploadPath(type, originalFilename, fileObj.getSize());
			if (!result.isSuccess()) {
				return result;
			}
			String destPath = (String) result.getReModel();
			InputStream inputStream = null;
			// 上传文件
			try {
				inputStream = fileObj.getInputStream();
				transResult = fileTransService.uploadFile(destPath, inputStream);
			} catch (IOException e) {
				logger.error("IOException",e);
				result.setErr(ErrCode.IO_EXCEPTION, ErrCode.IO_EXCEPTION_TEXT);
				return result;
			} catch (Exception e) {
				e.printStackTrace();
				result.setErr(ErrCode.IO_EXCEPTION, ErrCode.IO_EXCEPTION_TEXT);
				return result;
			} finally {
				try {
					inputStream.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			// 上传成功返回文件名称
			if (transResult.isSuccess()) {
				File tempFile = new File((String) result.getReModel());
				//真实文件名
				String fileName = tempFile.getName();
				list.add(fileName);
			}
		}
		Map<String ,Object> map = new HashMap<>();
		if (!list.isEmpty() && list.size() > 0) {
			map.put("uploadFileName" , StringUtils.collectionToDelimitedString(list, ","));
			map.put("realFileName" , StringUtils.collectionToDelimitedString(realFileNameList, ","));
			result.setReModel(map);
		}
		return result;
	}

	/**
	 * 获取绝对路径
	 * @company 江苏岳创信息
	 * @desc 
	 * @file FileController.java
	 * @date 2017-09-20
	 * @param type
	 * @param fileName
	 * @param response
	 * @return
	 * @return CallResult
	 */
	@RequestMapping("/getPath.web")
	@ResponseBody
	public CallResult getPath(String type , String fileName , HttpServletResponse response ){
		return fileHandleService.getPath(fileName);
	}

	/**
	 * base64上传文件
	 * @param image
	 * @param response
	 * @param request
	 * @param type
	 * @return
	 */
	@RequestMapping("/baseUpload.api")
	@ResponseBody
	public CallResult baseUpload(String image, HttpServletResponse response, HttpServletRequest request,
                                 String type){
		CallResult callResult = CallResult.newInstance();
		//对字节数组字符串进行Base64解码并生成图片  
		if (image == null || "".equals(image)){
			//图像数据为空  
			callResult.setErr(ErrCode.FILE_CREATE, ErrCode.FILE_CREATE_TEXT);
			return callResult;  
		}
		BASE64Decoder decoder = new BASE64Decoder();
		try {
			//Base64解码
			byte[] b = decoder.decodeBuffer(image);
			for(int i=0;i<b.length;++i){
				if(b[i]<0){
					//调整异常数据
					b[i]+=256;
				}
			}
			String fileName = "user_header.jpg";
			//文件路径
			callResult=fileHandleService.uploadPath(type,fileName,0);
			//生成jpg图片
			String imgFilePath = (String)callResult.getReModel();//新生成的图片
			//创建文件夹
			CallResult tt = CallResult.newInstance();
			tt = fileTransService.createFilePath(imgFilePath);
			if(tt.isSuccess()){
				OutputStream out = new FileOutputStream(imgFilePath);
				out.write(b);
				out.flush();
				out.close();
			}
		} catch (Exception e){
			callResult.setErr(ErrCode.FILE_CREATE, ErrCode.FILE_CREATE_TEXT);
		}
		//上传成功,返回文件名
		if (callResult.isSuccess()) {
			File tempFile = new File((String) callResult.getReModel());
			String fileName = tempFile.getName();
			callResult.setReModel(fileName);
			return callResult;
		}
		return callResult;
		
		
	}
}
