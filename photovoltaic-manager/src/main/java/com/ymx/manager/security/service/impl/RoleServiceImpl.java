package com.ymx.manager.security.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ymx.common.base.dao.IDao;
import com.ymx.common.base.dao.IRoleDao;
import com.ymx.common.base.entity.Role;
import com.ymx.common.utils.QueryParamMapUtil;
import com.ymx.common.utils.SqlFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ymx.manager.security.service.IRoleService;

/**
 * 角色管理业务层实现
 * 
 */
@Service
public class RoleServiceImpl implements IRoleService {

	@Autowired
	private IDao dao;
	@Autowired
	private IRoleDao roleDao;
	
	/**
	 * 查看角色
	 * @desc
	 * @date 2017-11-27
	 * @param role
	 * @return
	 */
	public List<Role> queryRoleByUser(Role role , String userId) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("userId", userId);
		QueryParamMapUtil.setMapPropreties( map , role , false ) ;
		return roleDao.queryRoleByUser(map);
	}

	/**
	 * 增加角色信息
	 * 
	 * @param role
	 *            角色信息
	 */
	public boolean addRole(Role role) {
		int result = roleDao.addRole(role);
		return result == 1;
	}
	/**
	 * 修改角色信息
	 * 
	 * @param role
	 *            角色信息
	 * @return 是否成功
	 */
	public boolean editRole(Role role) {
		int result = roleDao.editRole(role);
		return result == 1;
	}
	/**
	 * 根据菜单编码查询菜单信息
	 * 
	 * @param roleId
	 *            菜单编码
	 * @return 菜单信息
	 */
	public Role queryRole(String roleId) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("roleId", roleId);
		Role role = roleDao.queryRole(paramMap);
		return role;
	}
	
	/**
	 * 查询角色名是否存在
	 * @param roleId   角色编码
	 * @param roleName 角色名称
	 * @return 是否存在
	 */
	public boolean existRoleName(String roleId, String roleName) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("roleName", roleName);
		paramMap.put("roleId", roleId);
		int result = roleDao.queryRoleNameForExist(paramMap);
		return result > 0;
	}
	
	/**
	 * 查询角色信息
	 * 
	 * @return 角色信息列表
	 */
	public List<Role> search() {
		List<Role> list = new ArrayList<Role>();
		list = roleDao.searchRoles();
		return list;
	}
	
	
	/**
	 * 删除角色信息
	 * @param roleId 角色编码
	 * @return 删除是否成功
	 */
	public boolean deleteRole(String roleId) {
		// 删除角色
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("roleId", roleId);
		int result = roleDao.deleteRole(paramMap);
		// 删除角色所对应的菜单分配信息
		roleDao.deleteRoleMenus(paramMap);
		// 删除角色所对应分配的操作功能
		roleDao.deleteRoleMenuFunctions(paramMap);
		// 删除用户所分配的该角色
		roleDao.deleteRoleUsersRel(paramMap);
		return result == 1;
	}
	
	/**
	 * 批量删除角色信息
	 * @param ids 角色编码组
	 * @return 删除是否成功
	 */
	@Override
	public boolean batchDeleteRole(String[] ids) {
		// 删除角色
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("list", Arrays.asList(ids));
		int result = roleDao.batchDeleteRole(paramMap);
		// 删除角色所对应的菜单分配信息
		roleDao.batchDeleteRoleMenus(paramMap);
		// 删除角色所对应分配的操作功能
		roleDao.batchDeleteRoleMenuFunctions(paramMap);
		// 删除用户所分配的该角色
		roleDao.batchDeleteRoleUsersRel(paramMap);
		return result == 1;
	}
	
	
	/**
	 * 保存角色的权限分配信息
	 * @param funs     权限分配
	 * @param roleId   角色编码
	 */
	public void saveRoleFunctions(List<Map<String, String>> funs, List<Map<String, String>> menus, String roleId) {
		// 首先删除该角色的所有权限分配信息
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("roleId", roleId);
		roleDao.deleteRoleMenuFunctions(paramMap);
		// 将分配的权限赋予当前的角色
		if (funs.size() > 0) {
			roleDao.setRoleMenuFunctions(funs);
		}
		// 删除该角色的菜单权限
		roleDao.deleteRoleMenus(paramMap);
		// 对该角色进行菜单权限赋予
		if (menus.size() > 0) {
			roleDao.setRoleMenus(menus);
		}
	}
	/**
	 * 保存应用角色配置信息
	 * @param roleId 角色编码
	 * @param appId 应用编码
	 * @param flag 标志位
	 */
	public boolean saveAppRole(String roleId, String appId, String flag) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("appId", appId);
		paramMap.put("roleId", roleId);
		String sql = SqlFactory.getInstance().getSql("delete_app_role");
		if ("add".equals(flag)) {
			roleDao.insertAppRole(paramMap);
			sql = SqlFactory.getInstance().getSql("insert_app_role");
		} else {
			roleDao.deleteAppRole(paramMap);
		}
		int result = dao.update(sql, new Object[] { appId, roleId });
		return result == 1;
	}
}
