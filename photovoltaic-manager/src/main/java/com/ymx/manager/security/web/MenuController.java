
package com.ymx.manager.security.web;

import com.ymx.common.base.entity.Function;
import com.ymx.common.base.entity.LayuiMenu;
import com.ymx.common.base.entity.Menu;
import com.ymx.common.common.constant.Constants;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.utils.IDUtil;
import com.ymx.manager.security.service.ICacheService;
import com.ymx.manager.security.service.IMenuService;
import com.ymx.manager.security.utils.JsonUtil;
import net.sf.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 菜单管理
 * 
 */
@Controller
@RequestMapping( "/menu" )
public class MenuController {

    @Autowired
    private IMenuService menuService;

    @Autowired
    private ICacheService cacheService;

    /**
     * 跳转到menu列表页面
     * @param request 请求
     * @param response  返回
     * @return
     */
    @RequestMapping( value = "/search.htm" )
    public ModelAndView search( String leftMenuId , String menuId, HttpServletRequest request,   HttpServletResponse response )  {
        // 查询当前菜单所拥有的角色
        //request.setAttribute( "functionStr", FunctionBuilder.build() );
    	request.setAttribute("leftMenuId", leftMenuId);
//    	String mkt = "";
//   	 String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return new ModelAndView( "sys/menu_list" );
    }

    /**
     * 获取所有的菜单信息
     * @param response  返回响应
     */
    @RequestMapping( value = "/list.htm" )
    public void list( HttpServletResponse response,HttpServletRequest request )  {
        //List<Menu> list = menuService.list();
//    	String mkt = "";
//   	 String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        LayuiMenu list = menuService.listMenus(request);
        JsonUtil.bean2Json( response, list );
    }

    /**
     * 根据角色编码获取包括操作功能的菜单列表
     * @param response
     */
    @RequestMapping( value = "/listWithFunctions.web" )
    @ResponseBody
    public CallResult listWithFunctions( HttpServletRequest request,   HttpServletResponse response ) {
    	CallResult callResult = CallResult.newInstance();
        String roleId = request.getParameter( "roleId" );
        List<Menu> list = menuService.listWithFunctions( roleId,request );
        callResult.setReModel(list);
        callResult.setCount(list.size());
        //JsonUtil.list2Json( response, list );
        return callResult;
    }

    /**
     * 跳转到菜单编辑页面
     * @param request  请求
     * @param response 响应
     * @return 跳转到菜单编辑页面
     */
    @RequestMapping( value = "/toSave.htm" )
    public ModelAndView toSave(String leftMenuId , String menuId, HttpServletRequest request,HttpServletResponse response ) {
        String operator = request.getParameter( "operator" );
        Menu menu = null;
        if ( Constants.OPERATOR_ADD.equals( operator ) ) {
        	// 新增
            menu = new Menu();
           // menu.setMenuIcon(Constants.DEFAULT_ICON);
        } else {
        	//查询单条记录
            menu = menuService.queryMenu( menuId ,request);
        }
        List<String> icons = queryMenuIcons( request );
        List<Menu> menuList = menuService.queryTopMenus(request);
        //菜单图标
        request.setAttribute("leftMenuId", leftMenuId );
        request.setAttribute( "icons", icons );
        request.setAttribute( "menu", menu );
        request.setAttribute( "operator", operator );
        request.setAttribute( "menuList", menuList );
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return new ModelAndView( "sys/menu_edit" );
    }

    /**
     * 保存菜单信息
     * @param menu     菜单信息
     * @param operator 操作方式，add or edit
     * @param response 响应
     */
    @RequestMapping( value = "/save.htm" )
    public void save( @ModelAttribute Menu menu , String operator ,  HttpServletResponse response, HttpServletRequest request) {
        boolean result = false;
        if ( Constants.OPERATOR_ADD.equals( operator ) ) {
        	// 新增菜单操作
            menu.setMenuId( IDUtil.getUUIDStr() );
            result = menuService.add( menu );
        } else  {
            result = menuService.update( menu );
        }
        // 菜单缓存重新设置
        if ( result ) {
            cacheService.setMenuCache(request);
        }
        JsonUtil.boolOut( response, result );
    }

    /**
     * 检测菜单名称是否已经存在
     * @param menuName  菜单名称
     * @param pMenuId  上级编码
     * @param menuId  当前菜单编码（修改时进行判断）
     * @param
     */
    @RequestMapping( value = "/checkMenuNameExist.htm" )
    public void checkMenuNameExist( String menuName, String pMenuId,  String menuId, HttpServletResponse response ) {
        boolean result = menuService.checkMenuNameExist( menuName, pMenuId, "".equals( menuId ) ? "0" : menuId );
        JsonUtil.boolOut( response, result );
    }

    /**
     * 删除菜单
     * @param menuId    菜单编码
     * @param response  响应
     */
    @RequestMapping( value = "/delete.htm" )
    public void delete( String menuId, HttpServletResponse response,HttpServletRequest request ) {
        boolean result = menuService.delete( menuId );
        if ( result ) {
            cacheService.setMenuCache(request);
        }
        JsonUtil.boolOut( response, result );
    }

    /**
     * 查询顶级菜单
     * @param response  响应
     */
    @RequestMapping( value = "/queryTopMenus.htm" )
    public void queryTopMenus( HttpServletResponse response ,HttpServletRequest request) {
        List<Menu> menuList = menuService.queryTopMenus(request);
        JsonUtil.list2Json( response, menuList );
    }

    /**
     * 根据上级菜单查询子菜单信息
     * @param pMenuId   上级菜单编码
     * @param response
     */
    @RequestMapping( value = "/queryChildrenMenus.htm" )
    public void queryChildrenMenus( String pMenuId, HttpServletResponse response,HttpServletRequest request )  {
        List<Menu> menuList = menuService.queryChildrenMenus( pMenuId,request );
        JsonUtil.list2Json( response, menuList );
    }

    /**
     * 查询Icon目录下的图标
     * @param request    请求
     * @return           图标列表
     */
    private List<String> queryMenuIcons( HttpServletRequest request )  {
        String path = request.getSession().getServletContext().getRealPath("/images/menuicons" );
        File f = new File( path );
        List<String> result = new ArrayList<String>();
        if ( f.exists() && f.isDirectory() )
        {
            String[] files = f.list();
            if ( files != null )
            {
                for ( String string : files )
                {
                    result.add( string );
                }
            }
        }
        return result;
    }

    /**
     * 跳转到操作功能编辑页面
     * @param menuId 菜单编码
     * @return
     */
    @RequestMapping( value = "/toFunction.htm" )
    public ModelAndView toFunction(String leftMenuId , String menuId, String menuName,  HttpServletRequest request ) {
        request.setAttribute( "menuId", menuId );
        request.setAttribute( "menuName", menuName );
        request.setAttribute("leftMenuId", leftMenuId );
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return new ModelAndView( "sys/functions_list" );
    }

    /**
     * 获取菜单下的所有功能
     * @param menuId  菜单编码
     */
    @RequestMapping( value = "/menuFunctions.web")
    @ResponseBody
    public CallResult menuFunctions( String menuId , HttpServletResponse response ,HttpServletRequest request) {
    	CallResult callResult = CallResult.newInstance();
        List<Function> funs = menuService.queryMenuFunctions( menuId, request);
        callResult.setReModel(funs);
        callResult.setCount(funs.size());
        return callResult;
    }
    
    /**
     * 跳转到菜单操作功能新增或编辑页面
     * @param leftMenuId 
     * @param menuId     所属菜单主键
     * @param request    请求
     * @param response   响应
     * @return 跳转到菜单编辑页面
     */
    @RequestMapping( value = "/toFunSave.htm" )
    public ModelAndView toFunSave(String leftMenuId , String menuId , String funId , HttpServletRequest request,    HttpServletResponse response ) {
        String operator = request.getParameter( "operator" );
        Function function = null;
        if ( Constants.OPERATOR_ADD.equals( operator ) ) {
        	// 新增
        	function = new Function();
        	function.setMenuId(menuId);
        	function.setFunOrder(menuService.queryFunctionOrderMax(menuId));
        } else {
        	//查询单条记录
        	function = menuService.queryFunctionById( funId ,request);
        }
        //菜单图标
        request.setAttribute("leftMenuId", leftMenuId );
        request.setAttribute( "function", function );
        request.setAttribute( "operator", operator );
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return new ModelAndView( "sys/function_edit" );
    }
    
    /**
     * 保存菜单功能信息
     * @param menu     菜单信息
     * @param operator 操作方式，add or edit
     * @param response 响应
     */
    @RequestMapping( value = "/saveFun.htm" )
    public void saveFun (Function function , String operator , HttpServletResponse response ) {
        boolean result = false;
        if ( Constants.OPERATOR_ADD.equals( operator ) ) {
            function.setFunId( IDUtil.getUUIDStr() );
            result = menuService.addFunction( function );
        } else  {
            result = menuService.updateFunction( function );
        }
        if ( result ) {
        	//缓存操作功能数据
            cacheService.setFunctionCache();
        }
        JsonUtil.boolOut( response, result );
    }

    /**
     * 检测菜单名称是否已经存在
     * @param funDesc 菜单功能描述
     * @param menuId  当前菜单编码（修改时进行判断）
     * @param response响应
     */
    @RequestMapping( value = "/checkFunctionNameExist.htm" )
    public void checkFunctionNameExist( Function function, String operator , HttpServletResponse response ) {
        boolean result = menuService.checkFunctionNameExist( function , operator );
        JsonUtil.boolOut( response, result );
    }
    
    /**
     * 批量删除
     * @param id
     * @param response
     */
    @RequestMapping("/batchDelete.htm")
    public void batchDelete ( HttpServletRequest reqeust , HttpServletResponse response) {
    	String [] ids = reqeust.getParameterValues("ids[]");
    	boolean result = menuService.batchDelete( ids );
    	if ( result ) {
            cacheService.setFunctionCache();
        }
        JsonUtil.boolOut( response, result );
    }
    

    /**
     * 保存菜单的操作功能信息
     * @param funs
     * @param response
     */
    @SuppressWarnings( "unchecked" )
    @RequestMapping( value = "/saveMenuFunIcons.htm" )
    public void saveMenuFunIcons( HttpServletRequest request ,  HttpServletResponse response ) {
        String rowdata = request.getParameter( "rowdata" );
        String menuId = request.getParameter( "menuId" );
        List<Function> funs = (List<Function>)JSONArray.toCollection( JSONArray.fromObject( rowdata ), Function.class );
        menuService.saveMenuFunctions( menuId, funs );
        //缓存操作功能数据
        cacheService.setFunctionCache();
        JsonUtil.boolOut( response, true );
    }

    /**
     * 查询所有的功能图标
     * @param response
     */
    @RequestMapping( value = "/queryFunIcons.htm" )
    public void queryFunIcons( HttpServletResponse response ) {
        List<Map<String, String>> icons = new ArrayList<Map<String, String>>();
        Map<String, String> map = null;
        String[] iconArr = Constants.OPERATOR_ICONS.split( "[|]" );
        for ( String icon : iconArr ) {
            map = new HashMap<String, String>();
            map.put( "text", icon );
            map.put( "id", icon );
            map.put( "iconCls", icon );
            icons.add( map );
        }
        JsonUtil.list2Json( response, icons );
    }

    /**
     * 跳转到菜单排序
     * @return 返回
     */
    @RequestMapping( value = "/toOrder.htm" )
    public ModelAndView toOrder() {
        return new ModelAndView( "sys/menu_order" );
    }

    /**
     * 菜单排序
     * @return 返回
     */
    @SuppressWarnings( "unchecked" )
    @RequestMapping( value = "/order.htm" )
    public void order( HttpServletRequest request, HttpServletResponse response ) {
        String orderData = request.getParameter( "orderData" );
        List<Menu> menus = (List<Menu>)JSONArray.toCollection( JSONArray.fromObject( orderData ), Menu.class );
        menuService.order( menus );
        // 重设菜单缓存数据
        cacheService.setMenuCache(request);
    }

}
