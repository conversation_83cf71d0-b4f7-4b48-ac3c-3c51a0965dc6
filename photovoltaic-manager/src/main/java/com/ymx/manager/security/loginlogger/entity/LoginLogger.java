package com.ymx.manager.security.loginlogger.entity;

import com.ymx.common.base.entity.IEntity;
import com.ymx.common.security.annotation.Column;
import com.ymx.common.security.annotation.Table;

import java.util.Date;



/**
 * 登录日志 对应实体类
 */
@Table(name="t_fs_login_logger")
public class LoginLogger implements IEntity
{
    
    /**
     * 
     */
    @Column(name="id")
    private String id;
    
    /**
     * 登录帐号
     */
    @Column(name="user_id")
    private String userId;
    
    /**
     * 最后登录时间
     */
    @Column(name="last_login_time")
    private Date lastLoginTime;
    
    /**
     * 登录IP
     */
    @Column(name="login_ip")
    private String loginIp;
    
    @Column(name="content")
    private String content;
    
   
    
    
    
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	
	public String getId()
	{  
	  return id;  
	}  
	public void setId(String id)
	{  
	  this.id = id;  
	}  
	public String getUserId()
	{  
	  return userId;  
	}  
	public void setUserId(String userId)
	{  
	  this.userId = userId;  
	}  
	public Date getLastLoginTime()
	{  
	  return lastLoginTime;  
	}  
	public void setLastLoginTime(Date lastLoginTime)
	{  
	  this.lastLoginTime = lastLoginTime;  
	}  
	public String getLoginIp()
	{  
	  return loginIp;  
	}  
	public void setLoginIp(String loginIp)
	{  
	  this.loginIp = loginIp;  
	}  
	  
}
