package com.ymx.manager.security.web;

import com.ymx.common.base.entity.AppVersionModel;
import com.ymx.common.base.service.AppVersionService;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.common.utils.DateUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @date 2018/04/28
 * @since 版本管理控制器
 */
@Controller
@RequestMapping("appCtr/")
public class AppVersionController {

    private static String BASE = "sys/app/";

    @Resource
    private AppVersionService appVersionService;

    /**
     * @since       版本列表视图请求
     * @return      org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping("view.htm")
    public ModelAndView view(HttpServletRequest request ){
        ModelAndView modelAndView = new ModelAndView();
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        modelAndView.setViewName(BASE + "app_list");
        return modelAndView;
    }

	/**
	 * 版本视图请求
	 * @param view
	 * @param model
	 * @return
	 */
	@RequestMapping("viewRequest.htm")
    public ModelAndView viewRequest(HttpServletRequest request ,String view , AppVersionModel model){
	    ModelAndView modelAndView = new ModelAndView();
//	    String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
	    modelAndView.setViewName(BASE + "app_add");
		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(ob));
	    if (view.equals("detail")){
		    model = appVersionService.queryVersionDetail(model);
	    }
	    modelAndView.addObject("info" , model);
	    return modelAndView;
    }

    /**
     * @since       版本列表
     * @param request
     * @param pageView
     * @param model
     * @return      com.ymx.common.common.result.CallResult
     */
    @RequestMapping("appList.web")
    @ResponseBody
    public CallResult appList(HttpServletRequest request , PageView pageView , AppVersionModel model){
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return appVersionService.queryVersionList(pageView , model);
    }

	/**
	 * 新增
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping("operate.web")
	@ResponseBody
	public CallResult operate(HttpServletRequest request, AppVersionModel model){
		String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort();
		long timeMillis = System.currentTimeMillis();
		// 获取日期
		String date = DateUtils.timestampToDate(timeMillis, DateUtils.DATE_SMALL_STR);
		basePath+="/file/apk/"+date+"/";
		if(null == model.getId() ||  model.getId().equals("")){
			model.setUrl(basePath+model.getUrl());
		}else{
			if(model.getUrl()!=null && model.getUrl().indexOf("://")<0){
				model.setUrl(basePath+model.getUrl());
			}
		}
		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(ob));
		return appVersionService.operateVersion(model);
    }

	/**
	 * 删除
	 * @param
	 * @return
	 */
	@RequestMapping("delete.web")
	@ResponseBody
	public CallResult delete(AppVersionModel model,HttpServletRequest request){
		//String[] id = request.getParameterValues("id[]");
		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(ob));
		return appVersionService.deleteApp(model);
	}

}
