
package com.ymx.manager.security.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.ymx.common.common.result.CallResult;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.logging.Logger;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import static jdk.nashorn.internal.runtime.regexp.joni.Config.log;

@RestController
@Controller
public class LogoutController
{

    private static final org.slf4j.Logger log = LoggerFactory.getLogger(LogoutController.class);
    Logger logger = Logger.getLogger(LogoutController.class.getName());

    @RequestMapping( value = "/free/logout.htm" )
    public ModelAndView logout( HttpServletRequest request ) {
        HttpSession session = request.getSession();
        session.removeAttribute( "currentUser" );
        session.removeAttribute( "userId" );
        session.removeAttribute( "userName" );
        session.invalidate();
        return new ModelAndView( "redirect:/index.jsp" );
    }

    /**
     * 退出登录接口
     * @return
     */
    @RequestMapping("/free/logout.web")
    @ResponseBody
    public CallResult logout(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 获取当前会话（如果会话不存在，则不执行任何操作）
        HttpSession session = request.getSession(false);
        if (session != null) {
            // 从会话中移除用户信息
                /*session.setAttribute("userId", userId);
                session.setAttribute("userKey", result.getId());
                session.setAttribute("userName", result.getUserName());
                session.setAttribute("currentUser", result);
                session.setAttribute("deptId", result.getDeptId());
                session.setAttribute("deptName", result.getDeptName());
                session.setAttribute("deptLevel", result.getDeptLevel());
                session.setAttribute("checkrole", names);*/
            session.removeAttribute("userId");
            session.removeAttribute("userName");
            session.removeAttribute("currentUser");
            session.removeAttribute("userKey");
            session.removeAttribute("deptId");
            session.removeAttribute("deptName");
            session.removeAttribute("deptLevel");
            session.removeAttribute("checkrole");

            // 删除会话中的所有属性并结束会话
            session.invalidate();

            //送一个响应状态码给客户端，表明操作成功
            CallResult callResult = CallResult.newInstance();
            callResult.setRec("退出登录成功");
            callResult.setCode(0);
            return callResult;
        }

        // 如果会话不存在，则什么也不做（记录一个日志）
        log.info("登录信息不存在");
        return CallResult.newInstance();
    }


}
