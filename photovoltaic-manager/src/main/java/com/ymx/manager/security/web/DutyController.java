
package com.ymx.manager.security.web;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ymx.manager.security.common.FunctionBuilder;
import com.ymx.common.base.entity.Duty;
import com.ymx.common.utils.IDUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.ymx.common.common.result.CallResult;
import com.ymx.manager.security.service.IDutyService;
import com.ymx.manager.security.utils.JsonUtil;

import java.util.List;


/**
 * 职务管理
 */
@Controller
@RequestMapping( "/duty" )
public class DutyController {

    @Autowired
    private IDutyService dutyService;

    /**
     * 跳转到职务信息列表页面
     * @param request  请求
     * @return
     */
    @RequestMapping( value = "/search.htm" )
    public ModelAndView search( HttpServletRequest request )  {
        request.setAttribute( "functionStr", FunctionBuilder.build(request) );
//        String mkt = "";
//   	 String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return new ModelAndView( "sys/duty_list" );
    }

    /**
     * 查询职务信息列表数据
     * @param response
     */
    @RequestMapping( value = "/list.web" )
    @ResponseBody
    public CallResult list( HttpServletResponse response )  {
    	CallResult callResult = CallResult.newInstance();
        List<Duty> dutys = dutyService.list();
        callResult.setReModel(dutys);
        callResult.setCount(dutys.size());
        //JsonUtil.list2Json( response, dutys );
        return callResult;
    }

    /**
     * 跳转到新增或者编辑页面
     * @return
     */
    @RequestMapping( value = "/tosave.htm" )
    public ModelAndView tosave( String dutyId, HttpServletRequest request )  {
        Duty duty = new Duty();
        if ( null != dutyId ) {
            duty = dutyService.query( dutyId );
        }
        request.setAttribute( "duty", duty );
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return new ModelAndView( "sys/duty_save" );
    }
    
    /**
     * 职务自动补全
     * @date 2017-10-24
     * @DutyController.java
     * @@param duty
     * @@param request
     * @@return
     */
    @RequestMapping( value = "/autoDuty.web" )
    @ResponseBody
    public CallResult autoDuty(Duty duty , HttpServletRequest request) {
    	return dutyService.autoDuty(duty) ;
    }

    /**
     * 保存职务信息
     * @param duty        职务信息
     * @param response    响应
     */
    @RequestMapping( value = "/save.htm" )
    public void save( Duty duty, HttpServletResponse response )   {
        if ( dutyService.existName( duty.getDutyName(), duty.getDutyId() ) )   {
            JsonUtil.strOut( response, "exist" );
        } else  {
            boolean result = false;
            if ( "".equals( duty.getDutyId() ) )  {
                duty.setDutyId( IDUtil.getUUIDStr() );
                result = dutyService.insert( duty );
            } else {
                result = dutyService.update( duty );
            }
            JsonUtil.boolOut( response, result );
        }
    }

    /**
     * 删除职务信息
     * @param dutyIds  职务信息，使用,号隔开
     * @param response 响应
     */
    @RequestMapping( value = "/delete.htm" )
    public void delete( String dutyIds, HttpServletResponse response )  {
        boolean result = dutyService.delete( dutyIds );
        JsonUtil.boolOut( response, result );
    }
}
