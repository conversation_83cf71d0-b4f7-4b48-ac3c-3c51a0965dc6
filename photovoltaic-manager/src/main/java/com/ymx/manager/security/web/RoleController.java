package com.ymx.manager.security.web;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ymx.common.base.entity.Role;
import com.ymx.common.utils.IDUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.ymx.common.common.result.CallResult;
import com.ymx.manager.security.common.FunctionBuilder;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.manager.security.service.IRoleService;
import com.ymx.manager.security.utils.JsonUtil;


/**
 * 角色信息管理
 * 
 */
@Controller
@RequestMapping( "/role" )
public class RoleController {

    @Autowired
    private IRoleService roleService;
    @Autowired
    private ILoginLoggerService loginLoggerService;
    /**
     * 查询角色信息列表
     * 
     * @param request
     *            请求
     * @param response
     *            返回
     * @return 返回角色信息列表页面
     */
    @RequestMapping( value = "/search.htm" )
    public ModelAndView search( HttpServletRequest request,   HttpServletResponse response )  {
        // 查询当前菜单所拥有的角色
        request.setAttribute( "functionStr", FunctionBuilder.build(request) );
//        String mkt = "";
//   	 String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        insertlog("查询角色信息");
        return new ModelAndView( "sys/role_list" );
    }

    /**
     * 角色列表数据
     * @param response
     * @return
     */
    @RequestMapping( value = "/list.web" )
    @ResponseBody
    public CallResult list( HttpServletResponse response ) {
    	CallResult callResult = CallResult.newInstance();
        List<Role> list = roleService.search();
        callResult.setReModel(list);
        callResult.setCount(list.size());
        //JsonUtil.list2Json( response, list );
        return callResult;
    }

    /**
     * 跳转到增加角色信息页面
     * @return
     */
    @RequestMapping( value = "/tosave.htm" )
    public ModelAndView tosave( String roleId, HttpServletRequest request )  {
        Role role = new Role();
        if ( null != roleId ) {// 修改
            role = roleService.queryRole( roleId );
        }
        request.setAttribute( "role", role );
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return new ModelAndView( "sys/role_save" );
    }

    /**
     * 增加角色信息
     * @param request
     * @param response
     * @return
     * @throws UnsupportedEncodingException 
     */
    @RequestMapping( value = "/save.htm" )
    public void save( HttpServletRequest request, HttpServletResponse response ) throws UnsupportedEncodingException  {
        String roleId = request.getParameter( "roleId" );

        String roleName = java.net.URLDecoder.decode(request.getParameter("roleName"),"utf-8");
        if ( roleService.existRoleName( roleId, roleName ) )  {
            JsonUtil.strOut( response, "exist" );
        } else  {
            String roleRemark = request.getParameter( "roleRemark" );
            Role role = new Role();
            role.setRoleName( roleName );
            role.setRoleRemark( roleRemark );
            boolean result = false;
            if ( "".equals( roleId ) ) {
                role.setRoleId(IDUtil.getUUIDStr());
                insertlog("新增角色数据");
                result = roleService.addRole( role );
            } else  {
                role.setRoleId( roleId );
                insertlog("修改角色数据");
                result = roleService.editRole( role );
            }
            JsonUtil.strOut( response, result ? "yes" : "no" );
        }
    }

    /**
     * 删除角色信息
     * @param request      请求
     * @param response     响应
     */
    @RequestMapping( value = "/delete.htm" )
    public void delete( HttpServletRequest request, HttpServletResponse response )    {
        String roleId = request.getParameter( "roleId" );
        insertlog("删除角色数据");
        boolean result = roleService.deleteRole( roleId );
        JsonUtil.strOut( response, String.valueOf( result ) );
    }
    
    /**
     * 批量删除角色信息
     * @param request      请求
     * @param response     响应
     */
    @RequestMapping( value = "/batchDelete.htm" )
    public void batchDelete( HttpServletRequest request, HttpServletResponse response ) {
    	String [] ids = request.getParameterValues("ids[]");
    	 insertlog("批量删除角色数据");
        boolean result = roleService.batchDeleteRole( ids );
        JsonUtil.strOut( response, String.valueOf( result ) );
    }
    
    

    /**
     * 跳转到分配权限页面
     * 
     * @param request
     * @return
     * @throws UnsupportedEncodingException 
     */
    @RequestMapping( value = "/toSetterRole.htm" )
    public ModelAndView toSetterRole( HttpServletRequest request ) throws UnsupportedEncodingException
    {
        String roleId = request.getParameter( "roleId" );
        String roleName = java.net.URLDecoder.decode(request.getParameter("roleName"),"utf-8");
        request.setAttribute( "roleId", roleId );
        request.setAttribute( "roleName", roleName );
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return new ModelAndView( "sys/role_fun_setter" );
    }
    
    /**
     * 跳转到分配角色应用权的页面
     * @param request 请求
     * @return 跳转到应用权页面
     * @throws UnsupportedEncodingException 
     */
    @RequestMapping( value = "/toSetterAppRole.htm" )
    public ModelAndView toSetterAppRole( HttpServletRequest request ) throws UnsupportedEncodingException
    {
        String roleId = request.getParameter( "roleId" );
        String roleName = java.net.URLDecoder.decode(request.getParameter("roleName"),"utf-8");
        request.setAttribute( "roleId", roleId );
        request.setAttribute( "roleName", roleName );
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return new ModelAndView( "sys/role_app_setter" );
    }
    
    /**
     * 应用分配或者取消分配
     * @param request 请求
     * @param response 响应
     */
    @RequestMapping(value = "/setterAppRole.htm")
    public void setterAppRole( HttpServletRequest request ,HttpServletResponse response)
    {
    	 insertlog("角色应用分配或者取消分配");
        String roleId = request.getParameter( "roleId" );
        String flag = request.getParameter( "flag" );
        String appId = request.getParameter( "appId" );
        boolean result = roleService.saveAppRole( roleId, appId, flag );
        JsonUtil.boolOut( response, result );
    }

    /**
     * 保存权限分配信息
     * 
     * @return
     */
    @RequestMapping( value = "/setterRole.htm" )
    public ModelAndView setterRole( HttpServletRequest request )
    {
    	 insertlog("角色保存权限分配信息");
        String roleId = request.getParameter( "roleId" );
        // 获取访问权限
        Map<String, String[]> funs = request.getParameterMap();
        Iterator<String> it = funs.keySet().iterator();
        List<Map<String, String>> funList = new ArrayList<Map<String, String>>();
        String menuId = null;
        Map<String, String> fun = null;

        List<Map<String, String>> menuList = new ArrayList<Map<String, String>>();
        Map<String, String> menu = null;
        while ( it.hasNext() )
        {
            String key = it.next();
            if ( key.startsWith( "r_" ) )
            {
                fun = new HashMap<String, String>();
                menuId = key.substring( 2, key.lastIndexOf( "_" ) );
                fun.put( "1", roleId );
                fun.put( "2", menuId );
                fun.put( "3", key.substring( key.lastIndexOf( "_" ) + 1 ) );
                funList.add( fun );
            } else if ( key.startsWith( "m_" ) )
            {
                menu = new HashMap<String, String>();
                menuId = key.substring( 2 );
                menu.put( "1", roleId );
                menu.put( "2", menuId );
                menuList.add( menu );
            }
        }
        roleService.saveRoleFunctions( funList, menuList, roleId );

        return new ModelAndView( "redirect:/role/search.htm?leftMenuId="  + request.getParameter( "leftMenuId" ) );
    }
    public  void insertlog(String content)
    {
 	   LoginLogger logger = new LoginLogger();
       // String content = "查询会员数据";
        logger.setId( UUID.randomUUID().toString() );
        logger.setUserId( SessionHelper.getUserId() );
        logger.setLastLoginTime( new Date() );
       // logger.setLoginIp( getIpAddr(request) );
        logger.setContent(content);
      //  logger.setOperation_id(UUID.randomUUID().toString());
        loginLoggerService.insert( logger );
    }
}
