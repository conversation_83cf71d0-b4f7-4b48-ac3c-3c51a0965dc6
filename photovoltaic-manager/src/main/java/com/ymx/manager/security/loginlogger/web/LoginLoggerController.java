package com.ymx.manager.security.loginlogger.web;

import com.ymx.manager.security.common.FunctionBuilder;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.common.base.entity.SearchCondition;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.manager.security.utils.JsonUtil;
import com.ymx.common.utils.IDUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 *登录日志对应的controller控制类
 */
@Controller
@RequestMapping("loginlogger")
public class LoginLoggerController {
    
    @Autowired
    private ILoginLoggerService loginLoggerService;
    
    @InitBinder
    protected void initBinder(WebDataBinder binder) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

	/**
	  * 跳转到登录日志列表页面
	  *@param request 请求
	  *@return 转到跳转后的list页面
	  */
    @RequestMapping("forwardList.htm")
    public ModelAndView forwardList(HttpServletRequest request)  {
        request.setAttribute( "functionStr", FunctionBuilder.build(request) );
        String mkt = "";
   	    String datamkt = "";
        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
        if(ob.toString().contains("en"))
        {
     	   mkt="en";
     	   datamkt = "en";
        }
        else
        {
     	   mkt="cn";
     	   datamkt="cn";
        }
        request.setAttribute( "datamkt", datamkt );
        request.setAttribute( "mkt", mkt );
        return new ModelAndView("sys/loginlogger_list");
    }
    
    @RequestMapping("list.web")
    @ResponseBody
    public CallResult list(PageView pageView , HttpServletRequest request, HttpServletResponse response) {
    	List<SearchCondition> conditions = new ArrayList<SearchCondition>();
        return loginLoggerService.list( pageView );
        //JsonUtil.bean2JsonForDate( response,  pager);
    }
    
    @RequestMapping("listAll.htm")
    public void listAll(HttpServletRequest request, HttpServletResponse response)  {
        JsonUtil.list2JsonForDate(response, loginLoggerService.listAll());
    }
    
    @RequestMapping("toSave.htm")
    public ModelAndView toSave(HttpServletRequest request,String id)  {
        String action = "add";
        LoginLogger loginLogger = new LoginLogger();
        if(null != id && !"".equals(id)) {
            Object obj = loginLoggerService.queryById( id );
            if(obj instanceof LoginLogger)  {
                loginLogger = (LoginLogger) obj;
            }
            action = "edit";
        } else  {
            loginLogger.setId(IDUtil.getUUIDStr());
        }
        request.setAttribute( "bean", loginLogger );
        request.setAttribute("action", action);
        return new ModelAndView("loginlogger/loginlogger_save");
    }
    
    @RequestMapping("save.htm")
    public void save(LoginLogger loginLogger, HttpServletResponse response, HttpServletRequest request, String action)   {
        boolean result = false;
        if("add".equals(action)) {
            result = loginLoggerService.insert( loginLogger );
        } else {
            result = loginLoggerService.updateById( loginLogger );
        }
        JsonUtil.boolOut( response, result );
    }
    
    @RequestMapping("delete.htm")
    public void delete(String id,HttpServletResponse response) {
        boolean result = loginLoggerService.deleteById( id );
        JsonUtil.boolOut( response, result );
    }
    
    @RequestMapping("view.htm")
    public ModelAndView view(String id,HttpServletRequest request) {
        Object loginLogger = loginLoggerService.queryById(id);
        request.setAttribute("bean", loginLogger);
        return new ModelAndView("loginlogger/loginlogger_view");
    }
    
}
