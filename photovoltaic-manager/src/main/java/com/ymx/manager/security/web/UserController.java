
package com.ymx.manager.security.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.common.base.config.ConfigConstants;
import com.ymx.common.base.entity.User;
import com.ymx.common.base.entity.UserRole;
import com.ymx.common.common.constant.Constants;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.member.entity.MemberModel;
import com.ymx.service.photovoltaic.member.service.MemberService;
import com.ymx.common.utils.IDUtil;
import com.ymx.common.utils.MD5;
import com.ymx.common.utils.Read;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import com.ymx.common.common.result.CallResult;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.service.IDeptService;
import com.ymx.manager.security.service.IRoleService;
import com.ymx.manager.security.service.IUserService;
import com.ymx.manager.security.utils.JsonUtil;

/**
 * 用户管理控制类
 * 
 */
@Controller
@RequestMapping( "/user" )
public class UserController {

    @Autowired
    private IUserService userService;
    
	@Autowired
	private IDeptService deptService;
	
	@Autowired
	private IRoleService iRoleService;

	@Resource
	private MemberService memberService;
	@Autowired
	private ILoginLoggerService loginLoggerService;
    /**
     * 跳转到用户列表页面
     * @param request
     * @param response
     * @return
     */
    /*@RequestMapping( value = "/frame.htm" )
    public ModelAndView frame( HttpServletRequest request,  HttpServletResponse response ) {
    	String menuId = SessionHelper.getRequest().getParameter( "m" );
    	 request.setAttribute("m", menuId);
        return new ModelAndView( "sys/user_frame" );
    }*/

    /**
     * 转到用户列表页面
     * @param request   请求
     * @return
     */
    @RequestMapping( value = "/search.htm" )
    public ModelAndView search( HttpServletRequest request ) {
       /* String deptId = request.getParameter( "deptId" );
        if ( null == deptId ) {
            deptId = "0";
        }
        request.setAttribute( "deptId", deptId );
        request.setAttribute( "functionStr", FunctionBuilder.build() );*/
    
//    	String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        insertlog("查询用户信息");
        return new ModelAndView( "sys/user_list" );
    }

    /**
     * 查询用户分页数据
     * @param user      查询条件
     * @param request   请求
     * @param response  响应
     * @param pageView  分页
     */
    @RequestMapping( value = "/list.web" )
    @ResponseBody
    public CallResult list(@ModelAttribute User user, PageView pageView , HttpServletRequest request, HttpServletResponse response ) {
    	return userService.list(user, pageView);
    }
    

    /**
     * 跳转到新增/修改用户页面
     * @param userId  用户编码
     * @param deptId  部门编码
     * @param request 请求
     * @return 跳转页面
     */
    @RequestMapping( value = "/tosave.htm" )
    public ModelAndView tosave( String userId, String deptId,  HttpServletRequest request ) {
        User user = new User();
        String operator = Constants.OPERATOR_ADD;
        if ( null != userId ) {
            user = userService.query( userId );
            operator = Constants.OPERATOR_EDIT;
        } else  {
            user.setDeptId( deptId );
        }
        request.setAttribute("userRoles" , iRoleService.queryRoleByUser(null , userId) );
        request.setAttribute( "deptId", deptId == null ? "0" : deptId );
        request.setAttribute( "operator", operator );
        request.setAttribute( "user", user );
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return new ModelAndView( "sys/user_save" );
    }

    /**
     * 保存用户信息
     * @param user    用户
     * @param request 请求
     * @param response 响应
     */
    @RequestMapping( value = "/save.htm" )
    public void save( @ModelAttribute User user, HttpServletRequest request, HttpServletResponse response ) {
        String operator = request.getParameter( "operator" );

        // 新增手机端用户 (运维人员)  运维人员职务id
        String OPERATIONS_DUTY_ID = ConfigConstants.getConfig("OPERATIONS_DUTY_ID");
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    user.setLanguage(TranslateLanguage.getLanguageType(ob));
        boolean result = false;
        if ( Constants.OPERATOR_ADD.equals( operator ) )  {
            // 新增操作
            user.setCreator( SessionHelper.getUserId() );
            user.setId(IDUtil.getUUIDStr());
            user.setCreateDate( new Date() );
            user.setUserPswd( MD5.password( Read.getMsg( "system.default.user.password" ) ) );
            // Dept dept = deptService.query(user.getDeptId());

            if (OPERATIONS_DUTY_ID.equals(user.getDutyId())) {
            	insertlog("新增用户信息");
                MemberModel model = memberService.queryMemberByPhone(user.getUserId() , null);
                if (null == model) {
                	
                    result = userService.insert(user);
                    // 新增手机端用户 (运维人员)
                    result = memberService.addMemberUser(user);
                }
            } else {
                result = userService.insert(user);
            }
        } else  {
            if (OPERATIONS_DUTY_ID.equals(user.getDutyId())) {
                result = memberService.updateMemberUser(user);
            } else {
                // 删除原新增的运维人员数据

            }
            insertlog("修改用户信息");
            result = userService.update( user );
        }
        JsonUtil.strOut( response, String.valueOf( result ) );
    }
    
    /**
     * 
     * @desc
     * @date 2017-10-24
     * @UserController.java
     * @param request
     * @param response
     */
    @RequestMapping( value = "/checkUserNameExist.htm" )
    public void checkUserNameExist( HttpServletRequest request, HttpServletResponse response ){
    	 String userId = request.getParameter("userId");
    	 boolean result = false ;
    	 result = userService.checkUserNameExist(userId);
    	 JsonUtil.strOut( response, String.valueOf( result ) );
    }

    /**
     * 删除所选择的用户
     * 
     * @param userIds  用户编码，多个用户使用,号隔开
     * @param response 响应
     */
    @RequestMapping( value = "/delete.htm" )
    public void delete( String userIds, HttpServletResponse response ) {
    	 insertlog("删除用户信息");
        boolean result = userService.delete( userIds );
        JsonUtil.strOut( response, String.valueOf( result ) );
    }

    /**
     * 跳转到用户角色弹出页面
     * @return
     */
    @RequestMapping( value = "/toUserRole.htm" )
    public ModelAndView toUserRole( String userId, HttpServletRequest request ) {
        request.setAttribute( "userId", userId );
        //request.setAttribute("userRoles" , iRoleService.queryRoleByUser(null , userId) );
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return new ModelAndView( "sys/user_role_list" );
    }

    /**
     * 重置用户密码
     * @param userIds  用户编码
     * @param response 响应
     */
    @RequestMapping( value = "/resetPswd.htm" )
    public void resetPswd( String userIds, HttpServletResponse response ) {
    	 insertlog("重置用户密码");
        boolean result = userService.resetPswd( userIds );
        JsonUtil.boolOut( response, result );
    }

    /**
     * 查询用户角色信息对象
     * @param request  请求
     * @param response 响应
     */
    @RequestMapping( value = "/listUserRole.htm" )
    public void listUserRole( HttpServletRequest request, HttpServletResponse response ) {
        String userId = request.getParameter( "userId" );
        List<UserRole> list = userService.queryUserRole( userId );
        JsonUtil.list2Json( response, list );
    }

    /**
     * 保存用户角色信息
     */
    @RequestMapping( value = "/saveUserRole.htm" )
    public void saveUserRole( HttpServletRequest request, HttpServletResponse response ) {
        String userId = request.getParameter( "userId" );
        String roleIds = request.getParameter( "roleIds" );
        String[] roleArray = roleIds.split( "," );
        List<UserRole> roles = new ArrayList<UserRole>();
        UserRole ur = null;
        if ( null != roleArray )  {
            for ( String roleId : roleArray ) {
                if ( !"".equals( roleId ) ) {
                    ur = new UserRole();
                    ur.setRoleId( roleId );
                    ur.setUserId( userId );
                    roles.add( ur );
                }
            }
        }
        insertlog("保存用户角色信息");
        boolean result = userService.saveUserRole( userId, roles );
        JsonUtil.strOut( response, String.valueOf( result ) );
    }
    
    /**
     * 跳转到修改密码界面
     * @param request 请求
     * @param response 响应
     * @return 跳转到修改密码页面
     */
    @RequestMapping(value="/forwardChangePswd.web")
    @ResponseBody
    public ModelAndView forwardChangePswd( HttpServletRequest request,  HttpServletResponse response ){
    	return new ModelAndView("sys/change_password");
    }
    
    /**
     * 修改密码
     * @param request 请求
     * @param response 响应
     * @return 修改密码
     */
    @RequestMapping(value="/changePassword.htm")
    public void changePassword( HttpServletRequest request , HttpServletResponse response ) {
    	String password = request.getParameter("password");
    	String userId = SessionHelper.getUserId();
    	boolean result = userService.changePassword(userId, MD5.password(password));
    	insertlog("修改用户密码");
    	JsonUtil.boolOut(response, result);
    }
    public  void insertlog(String content)
    {
 	   LoginLogger logger = new LoginLogger();
       // String content = "查询会员数据";
        logger.setId( UUID.randomUUID().toString() );
        logger.setUserId( SessionHelper.getUserId() );
        logger.setLastLoginTime( new Date() );
       // logger.setLoginIp( getIpAddr(request) );
        logger.setContent(content);
      //  logger.setOperation_id(UUID.randomUUID().toString());
        loginLoggerService.insert( logger );
    }
}
