
package com.ymx.manager.security.common;

import com.ymx.common.base.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;


/**
 * 系统Session信息存取
 * 
 */
public class SessionHelper {

    @Autowired
    static HttpSession session;

    @Autowired
    static HttpServletRequest request;
    
    private final static String  DEFAULT_SESSION_USER_NAME = "loginUser" ;
    
    public static void setSessionUser(User user){
    	getSession().setAttribute( DEFAULT_SESSION_USER_NAME , user);
    }
    
    /**
     * 获取
     * @return
     */
    public static User getSessionUser(){
    	return (User) getSession().getAttribute( DEFAULT_SESSION_USER_NAME );
    }

    /**
     * 获取用户userid
     * @return
     */
    public static String getUserId() {
        session = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest().getSession();
        return String.valueOf( session.getAttribute( "userId" ) );
    }
    
    /**
     * 获取用户部门id
     * @return
     */
    public static String getDeptId() {
    	session = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest().getSession();
        return String.valueOf( session.getAttribute( "deptId" ) );
    }

    /**
     * 获取session
     * @return
     */
    public static HttpSession getSession()  {
        return ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest().getSession();
    }

    /**
     * 获取reqeust
     * @return
     */
    public static HttpServletRequest getRequest() {
        return ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
    }

}
