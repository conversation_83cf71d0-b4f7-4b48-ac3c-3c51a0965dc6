
package com.ymx.manager.security.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.ymx.common.base.entity.Dictionary;
import com.ymx.common.base.entity.DictionaryValue;
import com.ymx.common.common.result.PageInfo;
import com.ymx.common.common.result.PageView;
import com.ymx.common.utils.CommonPage;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.QueryParamMapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.base.dao.IDictionaryDao;
import com.ymx.manager.security.service.IDictionaryService;


/**
 * 字典信息管理业务接口实现
 */
@Service
public class DictionaryServiceImpl implements IDictionaryService {

    @Autowired
    private IDictionaryDao dictionaryDao;

    /**
     * 删除字典信息【组】
     * 
     * @param dicId
     *            字典编码
     * @return 删除是否成功
     */
    public boolean delete( String dicId )  {
        //删除字典数据
        int result = dictionaryDao.deleteDic(dicId );
        //删除字典值数据
        dictionaryDao.deleteDicValues(dicId);
        
        return result == 1;
    }

    /**
     * 新增字典信息【组】
     * @param dic  字典信息
     * @return     新增是否成功
     */
    public boolean insert( Dictionary dic )  {
    	dic.setCreateDate(new Date());
        int result = dictionaryDao.insertDic(dic);
        return result == 1;
    }

    /**
     * 查询字典信息列表 【组】
     * @return 字典信息列表
     */
    public List<Dictionary> search()   {
        List<Dictionary> list = dictionaryDao.searchDicList();
        CommonUtil.hanldList(list);
        return list;
    }

    /**
     * 更新字典信息 【组】
     * @param dic 字典信息
     * @return 更新是否成功
     */
    public boolean update( Dictionary dic )   {
        int result = dictionaryDao.updateDic(dic);
        return result == 1;
    }
    
    /**
     * 根据字典编码查询字典信息 【组】
     * @param dicId  字典编码
     * @return 字典信息
     */
    public Dictionary get( String dicId )  {
        Dictionary dic = dictionaryDao.queryDic(dicId);
        return dic;
    }

    
    /***********************************************************************************
     * 优美的黄金分隔线
     *********************************************************************************/
    
    /**
     * 批量新增
     */
    @Override
    public boolean updateDicValues( String dicId, List<DictionaryValue> vals ) {
        //先删除数据
        dictionaryDao.deleteDicValues(dicId);
        //再新增字典数据
		for (int i = 0; i < vals.size(); i++) {
            DictionaryValue val = vals.get( i );
            val.setDicId(dicId);
            val.setDicValueOrder( i+1 );
        }
        dictionaryDao.insertDicValues(vals);
        return true;
    }

    /**
     * 根据字典编码查询字典值信息
     * @param dicId 字典编码
     * @return 字典值信息列表
     */
    public List<DictionaryValue> query( String dicId )  {
        List<DictionaryValue> list = dictionaryDao.searchDictionaryValues(dicId);
        return list;
    }
    
    
    /**
     * 查询列表
     */
    @Override
    public CallResult searchDictionaryValue(DictionaryValue dictionaryValue , PageView pageView) {
    	CallResult callResult = CallResult.newInstance();
    	Map<String, Object> map = new HashMap<String , Object>();
    	//查询条件
    	QueryParamMapUtil.setMapPropreties(map, dictionaryValue, false);
    	//总记录
    	int tatal = dictionaryDao.searchDictionaryValuesPageListCount(map);
    	//分页
    	PageInfo pageInfo = CommonPage.getPageInfo(tatal, pageView, "");
    	map.put("page", pageInfo);
    	//数据
    	List<DictionaryValue> list = dictionaryDao.searchDictionaryValuesPageList(map);
    	CommonUtil.hanldList(list);
    	callResult.setReModel(list);
    	callResult.setPageInfo(pageInfo);
    	callResult.setCount(tatal);
    	return callResult;
    }
    
    /**
     * 新增字典维护
     */
    @Override
    public boolean insertDicValue(DictionaryValue dic) {
    	int rows = dictionaryDao.insertDicValue(dic);
    	return rows == 1;
    }
    
    /**
     * 新增字典维护
     */
    @Override
    public boolean updateDicValue(DictionaryValue dic) {
    	int rows = dictionaryDao.updateDicValue(dic);
    	return rows == 1;
    }
    
    @Override
    public DictionaryValue searchDicValuesByDicValueId(String dicValueId) {
    	return dictionaryDao.searchDicValuesByDicValueId(dicValueId) ;
    }
    
    /**
     * 删除字典维护
     * @desc
     * @date 2017-11-3
     * @DictionaryServiceImpl.java
     * @param ids id值
     * @return
     */
    @Override
    public boolean dicValueDelete(String[] ids) {
    	if(null != ids && ids.length > 0 ){
    		List<String> list = Arrays.asList(ids);
    		int rows  = dictionaryDao.dicValueDelete(list);
    		return true;
    	}
    	return false ;
    }
    
    /*****
     * 查询字典值单条记录
     * @desc
     * @date 2017-11-3
     * @DictionaryServiceImpl.java
     * @param dValue  字典值实体
     * @return
     */
    public DictionaryValue queryDicValueSingle(DictionaryValue dValue) {
    	Map<String, Object> map = new HashMap<String, Object>();
    	QueryParamMapUtil.setMapPropreties(map, dValue, false);
    	return dictionaryDao.queryDicValueSingle(map);
    }

	@Override
	public DictionaryValue searchDicValuesByDicValueName(String name) {
		// TODO Auto-generated method stub
		return dictionaryDao.searchDicValuesByDicValueName(name);
	}
    

}
