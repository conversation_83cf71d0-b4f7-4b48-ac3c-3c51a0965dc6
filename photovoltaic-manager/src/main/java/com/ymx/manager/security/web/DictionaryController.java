package com.ymx.manager.security.web;

import com.ymx.common.base.entity.Dictionary;
import com.ymx.common.base.entity.DictionaryValue;
import com.ymx.common.base.service.ParamService;
import com.ymx.common.common.constant.Constants;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.common.security.cache.CacheFactory;
import com.ymx.manager.security.common.FunctionBuilder;
import com.ymx.manager.security.service.IDictionaryService;
import com.ymx.manager.security.utils.JsonUtil;
import com.ymx.common.utils.IDUtil;
import net.sf.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 字典管理相关控制器转换
 */
@Controller
@RequestMapping("/dic")
public class DictionaryController {

	@Autowired
	private IDictionaryService dictionaryService;
	@Autowired
	private ParamService paramService;
	
	/**
	 * 跳转到字典管理框架页
	 * @param request 请求
	 * @return 字典管理框架页
	 */
	@RequestMapping(value = "/frame.htm")
	public ModelAndView frame(HttpServletRequest request) {
		String functionStr = FunctionBuilder.build(request);
		request.setAttribute( "functionStr", functionStr );
		return new ModelAndView("sys/dic_frame");
	}
	
	/**
	 * 获取字典分组数据列表 【组】
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/toGroupList.htm")
	public ModelAndView dicGroupList(HttpServletRequest request , HttpServletResponse response ){
//		String mkt = "";
//   	 String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
		return new ModelAndView("sys/dic_group_list");
	}
	
	/**
	 * 查询所有的字典列表信息 【组】
	 * @return 字典列表信息
	 */
	@RequestMapping(value="/dicGroupList.web")
	@ResponseBody
	public CallResult dicList(HttpServletResponse response){
		CallResult callResult = CallResult.newInstance();
		List<Dictionary> list = dictionaryService.search();
		callResult.setReModel(list);
		callResult.setCount(list.size());
		return callResult;
	}
	
	/**
	 * 跳转到保存字典信息页面 【组】
	 * @param dicId 字典编码
	 * @param request 请求
	 * @return 保存字典信息页面
	 */
	@RequestMapping(value="/toSave.htm")
	public ModelAndView toSave(String operator ,String dicId,HttpServletRequest request) {
		Dictionary dic = new Dictionary();
		if(Constants.OPERATOR_ADD.equals(operator)) {
			dic = new Dictionary();
		} else {
			dic = dictionaryService.get(dicId);
		}
//		String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
		request.setAttribute("dic", dic);
		request.setAttribute("operator", operator) ;
		return new ModelAndView("sys/dic_group_edit");
	}
	
	/**
	 * 保存字典信息 【组】
	 * @param dic 字典信息
	 * @param operator 标志位 add / edit
	 * @param response 响应
	 * 
	 */
	@RequestMapping(value="/save.htm")
	public void save( @ModelAttribute Dictionary dic,String operator,HttpServletResponse response)	{
		boolean result;
		if(Constants.OPERATOR_ADD.equals(operator)) {
			result = dictionaryService.insert(dic);
		} else {
			result = dictionaryService.update(dic);
		}
		JsonUtil.boolOut(response, result);
	}
	
	/**
	 * 删除字典信息 【组】
	 * @param dicId 字典编码
	 * @param response 响应
	 */
	@RequestMapping(value="/delete.htm")
	public void delete(String dicId,HttpServletResponse response) {
		boolean result = dictionaryService.delete(dicId);
		//更新缓存
		CacheFactory.getInstance().removeDictionary( dicId );
		JsonUtil.boolOut(response, result);
	}
	
	/****************************************************************************
	 * 
	 * 
	 *****************************************************************************/
	/**
	 * 获取数据字典JSON对象
	 * @desc
	 * @date 2017-10-30
	 * @DictionaryController.java
	 * @param dicId
	 * @param request
	 * @return
	 */
	@RequestMapping(value={"/queryDicList.api" , "/queryDicList.web"})
	@ResponseBody
	public CallResult queryDicList(String dicId , HttpServletRequest request) {
		CallResult callResult = CallResult.newInstance();
		List<DictionaryValue> list = dictionaryService.query(dicId);
		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		if(ob.toString().contains("en"))
		{
			for(DictionaryValue dictionaryValue:list){
				dictionaryValue.setDicValueLabel(dictionaryValue.getDicValueEn());
			}
		}
		callResult.setReModel(list);
		return callResult;
	}
	
	/**
	 * 根据字典编码跳转到字典值列表页面
	 * @param dicId 字典编码
	 * @return 转到字典列表页面
	 */
	@RequestMapping(value="/search.htm")
	public ModelAndView search(String dicId,HttpServletRequest request){
	    request.setAttribute( "dicId", dicId );
//	    String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
		return new ModelAndView("sys/dic_list"); 
	}
	
	/**
	 * 查询字典维护列表数据
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="/dicValList.web")
	@ResponseBody
	public CallResult searchDictionaryValue(DictionaryValue dictionaryValue , PageView pageView  , HttpServletRequest request , HttpServletResponse response ){
		return dictionaryService.searchDictionaryValue(dictionaryValue ,pageView);
	}
	
	/**
	 * 跳转到保存字典维护信息页面
	 * @param operator
	 * @param id 字典编码
	 * @param request 请求
	 * @return 保存字典信息页面
	 */
	@RequestMapping(value="/toDicValueSave.htm")
	public ModelAndView toValueSave(String operator ,String id , HttpServletRequest request) {
		DictionaryValue dic = new DictionaryValue();
		if(Constants.OPERATOR_ADD.equals(operator)) {
			dic = new DictionaryValue();
		} else {
			dic = dictionaryService.searchDicValuesByDicValueId(id);
		}
		List<Dictionary> list = dictionaryService.search();
		request.setAttribute("diclist", list);
		request.setAttribute("dic", dic);
		request.setAttribute("operator", operator) ;
//		String mkt = "";
//	   	 String datamkt = "";
//	        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//	        if(ob.toString().contains("en"))
//	        {
//	     	   mkt="en";
//	     	   datamkt = "en";
//	        }
//	        else
//	        {
//	     	   mkt="cn";
//	     	   datamkt="cn";
//	        }
//	        request.setAttribute( "datamkt", datamkt );
//	        request.setAttribute( "mkt", mkt );
		return new ModelAndView("sys/dic_save");
	}
	
	/**
	 * 保存字典维护信息
	 * @param dic 字典信息
	 * @param operator 标志位 add / edit
	 * @param response 响应
	 * 
	 */
	@RequestMapping(value="/dicValuesSave.htm")
	public void dicValuesSave( @ModelAttribute DictionaryValue dic,String operator,HttpServletResponse response)	{
		boolean result;
		if(Constants.OPERATOR_ADD.equals(operator)) {
			dic.setId(IDUtil.getUUIDStr());
			result = dictionaryService.insertDicValue(dic);
		} else {
			result = dictionaryService.updateDicValue(dic);
		}
		JsonUtil.boolOut(response, result);
	}
	
	
	/**
	 * 应用字典信息
	 * @param response 字典信息
	 * @param response 响应
	 * 
	 */
	@RequestMapping(value="/apply.htm")
	public void apply(HttpServletResponse response ) {
		CallResult callresult = paramService.applyParam();
		JsonUtil.boolOut(response, callresult.isSuccess());
	}
	
	/**
	 * 删除字典维护信息
	 * @param request
	 * @param response 响应
	 */
	@RequestMapping(value="/dicValueDelete.htm")
	public void diValueDelete(HttpServletRequest request , HttpServletResponse response) {
		String[] ids = request.getParameterValues("ids[]");
		boolean result = dictionaryService.dicValueDelete(ids);
		JsonUtil.boolOut(response, result);
	}
	
	
	
	
	
	
	
	
	
	
	
	/**
	 * 根据字典编码查询字典值信息  
	 * @param dicId 字典编码
	 * @param response 响应
	 */
	@RequestMapping(value="/listDicValues.htm")
	public void listDicValues(String dicId,HttpServletResponse response) {
	    List<DictionaryValue> list = dictionaryService.query( dicId );
	    JsonUtil.list2Json( response, list );
	}
	
	/**
	 * pc获取字典值
	 * @param response 响应
	 */
	@RequestMapping(value="/getDic.api")
	@ResponseBody
	public CallResult getDic(String type,HttpServletResponse response) {
		CallResult callResult = CallResult.newInstance();
		Map<String, Object> map = new HashMap<String, Object>();
	    List<DictionaryValue> list = dictionaryService.query( type );
	    map.put("data", list);
	    callResult.setReModel(map);
	    return callResult;
	}
	
	/**
	 * 保存字典值信息
	 * @param request 请求
	 * @param response 响应
	 */
	@SuppressWarnings("unchecked")
    @RequestMapping(value="/saveDicValues.htm")
	public void saveDicValues(HttpServletRequest request,HttpServletResponse response)
	{
	    String rowdata = request.getParameter( "rowdata" );
	    String dicId = request.getParameter( "dicId" );
	    List<DictionaryValue> list = (List<DictionaryValue>)JSONArray.toCollection( JSONArray.fromObject( rowdata ), DictionaryValue.class );
	    boolean result = dictionaryService.updateDicValues( dicId, list );
	    //更新缓存
	    CacheFactory.getInstance().putDictionary( dicId, list );
	    JsonUtil.boolOut( response, result );
	}

}
