
package com.ymx.manager.security.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ymx.manager.security.common.FunctionBuilder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * 系统普通页面跳转Controller
 */
@Controller
public class ForwardController {

	/**
	 * 公共引用
	 * @company 江苏岳创信息
	 * @desc 
	 * @file ForwardController.java
	 * @date 2017-9-19
	 * @return
	 * @return ModelAndView
	 */
    @RequestMapping( value = "/include.htm" )
    public ModelAndView include() {
        return new ModelAndView( "include" );
    }

	/**
	 * 下载apk页面
	 * @param request 请求
	 * @param response 响应
	 * @return 下载apk页面
	 */
	@RequestMapping(value="/downloadApk.web")
	@ResponseBody
	public ModelAndView downloadApk( HttpServletRequest request,  HttpServletResponse response ){
		return new ModelAndView("sys/download_apk");
	}
    
    /**
     * 菜单功能操作显示
     * @company 江苏岳创信息
     * @desc 
     * @date 2017-9-19
     * @param request
     * @return
     * @return ModelAndView
     */
    @RequestMapping(value = "/functionBuilder.htm")
    public ModelAndView functionBuilder(HttpServletRequest request ){
    	ModelAndView mv = new ModelAndView("sys/function_list");
    	mv.addObject("functionBuilder", FunctionBuilder.build(request));
    	return mv;
    }

    @RequestMapping(value = "/iconSelector.htm")
    public ModelAndView iconSelector(String type , HttpServletRequest request){
    	request.setAttribute("type", type);
    	return new ModelAndView("sys/icon");
    }
}
