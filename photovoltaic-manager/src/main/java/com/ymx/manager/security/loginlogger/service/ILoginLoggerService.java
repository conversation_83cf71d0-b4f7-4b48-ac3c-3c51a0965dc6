package com.ymx.manager.security.loginlogger.service;

import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;

import java.util.List;



/**
 * 登录日志相关的业务层接口
 * @version  [v1.0, 2017年09月25日]
 */
public interface ILoginLoggerService {
	
	/**
     * 查询分页记录数
     * @param pageView 分页实体
     * @return 查询的分页对象
     */
    CallResult list( PageView pageView );
    
    /**
     * 一次性查询表中所有数据
     * @return 表中所有数据
     */
    List<Object> listAll();

	/**
     * 根据主键查询对象
     * @param id 主键编码
     * @return 查询的对象
     */
    Object queryById( String id );
	
	/**
     * 根据主键删除对象
     * @param id 主键编码
     * @return 删除是否成功
     */
    boolean deleteById( String id );
    
    /**
     * 删除表中所有记录
     * @return 删除影响的记录数
     */
    int deleteAll();

	/**
     * 新增登录日志记录
     * @param loginLogger 登录日志对象
     * @return 新增是否成功
     */
    boolean insert( LoginLogger loginLogger );

	/**
     * 根据ID主键更新信息
     * @param loginLogger 登录日志对象
     * @return 更新是否成功
     */
    boolean updateById(  LoginLogger loginLogger  );
    
    /**
     * 根据用户登录信息
     * @param loginLogger 登录日志信息
     * @return 记录是否成功
     */
    boolean updateUserLogin(LoginLogger loginLogger);

}
