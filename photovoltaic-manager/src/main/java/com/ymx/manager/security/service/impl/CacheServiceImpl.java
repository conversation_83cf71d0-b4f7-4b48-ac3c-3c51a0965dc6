package com.ymx.manager.security.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.ymx.common.base.dao.IDao;
import com.ymx.common.base.entity.Dictionary;
import com.ymx.common.base.entity.Function;
import com.ymx.common.base.entity.Menu;
import com.ymx.common.base.entity.Setter;
import com.ymx.common.utils.SqlFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import com.ymx.common.security.cache.CacheFactory;
import com.ymx.manager.security.service.ICacheService;
import com.ymx.manager.security.service.IDictionaryService;
import com.ymx.manager.security.service.IMenuService;
import com.ymx.manager.security.service.ISetterService;

/**
 * 缓存服务接口实现
 * 
 */
@Service("cacheService")
public class CacheServiceImpl implements ICacheService {
	@Autowired
	private IDao dao;
	@Autowired
	private IMenuService menuService;
	@Autowired
	private IDictionaryService dictionaryService;
	@Autowired
	private ISetterService setterService;

	/**
	 * 设置菜单操作功能缓存数据
	 */
	public void setFunctionCache() {
		String sql = SqlFactory.getInstance().getSql("sql_search_functions");
		SqlRowSet rs = dao.find(sql);
		Map<String, List<Function>> funs = new HashMap<String, List<Function>>();
		Function fun = null;
		while (rs.next()) {
			String menuId = rs.getString("menu_id");
			fun = new Function();
			fun.setMenuId(menuId);
			fun.setFunId(rs.getString("fun_id"));
			fun.setFunName(rs.getString("fun_name"));
			fun.setFunOrder(rs.getInt("fun_order"));
			fun.setFunDesc(rs.getString("fun_desc"));
			fun.setFunIcon(	rs.getString("fun_icon"));
			if (null == funs.get(menuId)) {
				List<Function> list = new ArrayList<Function>();
				list.add(fun);
				funs.put(menuId, list);
			} else {
				funs.get(menuId).add(fun);
			}
		}
		CacheFactory.getInstance().setFunMap(funs);
	}
	/**
	 * 设置菜单缓存数据
	 */
	public void setMenuCache(HttpServletRequest request) {
		List<Menu> list = menuService.list(request);
		CacheFactory.getInstance().setMenuList(list);
		// 缓存菜单数据的时候重新缓存操作功能数据
		setFunctionCache();
	}
	/**
	 * 设置用户操作功能菜单缓存
	 * 
	 * @param userId
	 *            用户名称
	 */
	public void setUserFunctionCache(String userId,HttpServletRequest request) {
		Map<String, List<Function>> userFunMap = menuService.queryUserMenuFunctions(userId,request);
		if (CacheFactory.getInstance().getUserFunMap() != null) {
			CacheFactory.getInstance().getUserFunMap().putAll(userFunMap);
		} else {
			CacheFactory.getInstance().setUserFunMap(userFunMap);
		}
	}
	/**
	 * 设置用户菜单缓存
	 * 
	 * @param userId
	 *            用户名称
	 */
	public void setUserMenuCache(String userId, HttpSession session,HttpServletRequest request ) {
		List<Menu> list = menuService.searchUserMenus(userId,session,request);
		CacheFactory.getInstance().putUserMenu(userId, list);
	}
	/**
	 * 字典数据缓存功能
	 */
	public void setDictionaryCache() {
		List<Dictionary> list = dictionaryService.search();
		for (Dictionary dictionary : list) {
			CacheFactory.getInstance().putDictionary(dictionary.getDicId(), dictionary.getDicValue());
		}
	}
	/**
	 * 设置信息缓存功能
	 */
	public void setSetterCache() {
		List<Object> setters = setterService.listAll();
		Setter set = null;
		Map<String, String> setterMap = new HashMap<String, String>();
		for (Object object : setters) {
			set = (Setter) object;
			setterMap.put(set.getSetterName(), set.getSetterValue());
		}
		CacheFactory.getInstance().setSetterMap(setterMap);
	}
	
	
}
