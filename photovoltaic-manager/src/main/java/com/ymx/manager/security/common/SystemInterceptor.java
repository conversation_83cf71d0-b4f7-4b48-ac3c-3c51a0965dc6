package com.ymx.manager.security.common;

import com.alibaba.fastjson.JSONObject;
import com.ymx.common.base.entity.Function;
import com.ymx.common.base.entity.Menu;
import com.ymx.common.common.constant.ErrCode;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.security.cache.CacheFactory;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.PageUtil;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


/**
 * 系统Session与操作权限控制
 * 
 */
public class SystemInterceptor extends HandlerInterceptorAdapter {

	/**
	 * 请求之前处理
	 * @desc
	 * @date 2017-11-7
	 * @SystemInterceptor.java
	 * @param request  request请求
	 * @param response response 响应
	 * @param handler  处理器
	 * @return 
	 * @throws Exception
	 */
	@Override
    public boolean preHandle( HttpServletRequest request,  HttpServletResponse response, Object handler ) throws Exception {
        request.setCharacterEncoding( "UTF-8" );
        response.setCharacterEncoding( "UTF-8" );
        response.setContentType( "text/html;charset=UTF-8" );
        String uri = request.getRequestURI();
        if(uri.contains("/bingmap") || uri.contains("/findlist.htm")|| uri.contains("/baidumap")|| uri.contains("/initmap")|| uri.contains("/findfile.htm")|| uri.contains("/create_file"))
        {
        	return true;
        }
        else
        {
        	 // 进行用户是否登录过滤
            Object userId = request.getSession().getAttribute( "userId" );
            if ( userId == null ) { // 用户登录已经失效
                    // ajax请求
                    PrintWriter out = response.getWriter();
                    if ( isAjax( request )) {
                        if(isAccept(request)){
                        	 String tip = this.getTip(request);
                        	 out.print(tip);
                             out.close();
                             out.flush();
                        } else {
                        	CallResult callResult = CallResult.newInstance();
                            callResult.setReModel(ErrCode.SESSEION_TIMEOUT);
                            callResult.setErrMsg(ErrCode.SESSEION_TIMEOUT_TEXT);
                            response.getWriter().print(JSONObject.toJSONString(callResult));
                        }
                        return false;
                    } else {
                    	String tip = this.getTip(request);
                   	    out.print(tip);
                        out.close();
                        out.flush();
                        return false;
                    }
             } else {
                    // 1.判断链接中是否存在系统菜单参数leftMenuId ，如果存在，设置到Attribute中
                    String m = request.getParameter( "leftMenuId" );
                    if ( null != m ) {
                        request.setAttribute( "leftMenuId", m );
                    }
                    //非主页面
                    if ( uri.indexOf( "/main.htm" ) == -1 ) {
                        StringBuffer result = new StringBuffer( "" );
                        //菜单
                        List<Menu> menuList = CacheFactory.getInstance().getMenuList();
                        //根据用户获取对应的菜单
                        List<Menu> userMenuList = CacheFactory.getInstance().getUserMenu( SessionHelper.getUserId() );
                        //菜单功能
                        Map<String, List<Function>> funMap = CacheFactory.getInstance().getFunMap();
                        //用户所拥有菜单功能
                        Map<String, List<Function>> userFunMap = CacheFactory.getInstance().getUserFunMap();
                        //菜单
                        boolean isMenu = false;
                        //菜单功能
                        boolean isFun = false;
                        boolean hasLimit = false;
                        // 判断当前链接是否是菜单
                        for ( Menu menu : menuList ) {
                        	String menuLink = menu.getMenuLink();
                        	menuLink = menuLink == null ? "" : menuLink;
                            if ( uri.indexOf(menuLink) != -1 ) {// 是需要进行权限过滤的菜单，判断当前用户是否有菜单权限
                                isMenu = true;
                                if(null != userMenuList)  {
    	                            for ( Menu um : userMenuList )  {
    	                                if ( null == um.getMenuLink()   || uri.indexOf( um.getMenuLink() ) != -1 )  {
    	                                    hasLimit = true;
    	                                    break;
    	                                }
    	                            }
                                }
                                if ( hasLimit )  {
                                    break;
                                }
                            }
                        }
                        if ( isMenu )  {// 菜单
                            if ( !hasLimit )  {
                                // 用户没有打开的权限
                                result.append( "非法请求，您没有访问权限!" );
                            }

                        } else  {
                            Iterator<String> it = funMap.keySet().iterator();
                            while ( it.hasNext() )  {
                                String menuId = it.next();
                                //获取菜单所拥有功能
                                List<Function> funs = funMap.get( menuId );
                                for ( Function fun : funs )  {
                                    if ( uri.indexOf( fun.getFunLink() ) != -1 )  {
                                        isFun = true;
                                        break;
                                    }
                                }
                                if ( isFun ) {
                                    break;
                                }
                            }
                            
                            if ( isFun ) {// 是系统功能
                                if ( null == m ) {
                                    result.append( "非法请求，您没有权限访问!" );
                                } else  {
                                	//根据用户userId_菜单id(格式)
                                    String key = SessionHelper.getUserId() + "_"  + m;
                                    //获取用户在菜单下的操作功能
                                    List<Function> funs = userFunMap.get( key );
                                    if ( null == funs )  {
                                        result.append( "非法请求，您没有权限访问!" );
                                    } else  {
                                        for ( Function fun : funs )  {
                                            if ( uri.indexOf( fun.getFunLink() ) != -1 ) {
                                                hasLimit = true;
                                                break;
                                            }
                                        }
                                        if ( !hasLimit )  {
                                            result.append( "非法请求，您没有权限访问!" );
                                        }
                                    }
                                }
                            }
                        }

                        if ( !"".equals( result.toString() ) ) {
                            PrintWriter out = response.getWriter();
                            if ( isAjax( request ) )  {
                                out.print( "nolimit" );
                            } else  {
                                out.print( result.toString() );
                            }
                            out.close();
                            return false;
                        }
                    }
            }
        }
        return super.preHandle( request, response, handler );
    }
	
	
	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
		super.postHandle(request, response, handler, modelAndView);
	}
	
	/***
	 * 提示没有登录
	 * @desc
	 * @date 2017-11-24
	 * @return
	 */
	private String getTip( HttpServletRequest request ) {
		 StringBuilder builder = new StringBuilder();
		 builder.append( "<script type=\"text/javascript\" charset=\"UTF-8\">" );
         builder.append( "alert(\"页面过期，请重新登录!\");" );
         builder.append( "window.top.location.href=\"" );
         builder.append( PageUtil.getBasePath( request ) );
         builder.append( "\";</script>" );
		return builder.toString();
	}

	/**
	 * 是否AJAX请求
	 * @desc
	 * @date 2017-11-24
	 * @param request
	 * @return
	 */
    private boolean isAjax( HttpServletRequest request ) {
        String head = request.getHeader( "x-requested-with" );
        if ( "XMLHttpRequest".equals( head ) )  {
            return true;
        }
        return false;
    }
    
    /**
     * 
     * @desc
     * @date 2017-11-24
     * @param request
     * @return
     */
    private boolean isAccept(HttpServletRequest request ){
    	String head = request.getHeader( "Accept" );
        if (CommonUtil.isEmpty(head) && head.contains("text/plain") )  {
            return true;
        }
        return false;
    }

}
