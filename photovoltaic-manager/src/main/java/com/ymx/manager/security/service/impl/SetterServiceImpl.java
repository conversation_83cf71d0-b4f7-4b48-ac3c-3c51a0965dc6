package com.ymx.manager.security.service.impl;

import java.util.List;

import com.ymx.manager.security.utils.ConditionConvert;
import com.ymx.common.base.entity.SearchCondition;
import com.ymx.common.base.entity.Setter;
import com.ymx.common.common.result.PageInfo;
import com.ymx.common.common.result.PageView;
import com.ymx.common.utils.CommonPage;
import com.ymx.common.utils.SqlFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.security.annotation.dao.IAnnotaionDao;
import com.ymx.manager.security.service.ISetterService;


/**
 * 系统设置相关的业务层接口实现
 * 
 * @version  [v1.0, 2017年09月26日]
 */
@Service("setterService")
public class SetterServiceImpl implements ISetterService {
    
    @Autowired
    private IAnnotaionDao dao;

    /**
     * 查询分页记录数
     * @param pageView 分页实体
     * @param conditions 查询条件
     * @return
     */
    public CallResult list(PageView pageView , List<SearchCondition> conditions ) {
         CallResult callResult = dao.queryPage( getSql("sql_query_setter_count"),  getPageSql("sql_query_setter_list", pageView.getPageNo() ,pageView.getPageSize() ) , ConditionConvert.convert(conditions), Setter.class );
         PageInfo pageInfo = CommonPage.getPageInfo(callResult.getCount() , pageView, "");
         callResult.setPageInfo(pageInfo);
         return callResult;
    }
    
    /**
     * 一次性查询表中所有数据
     * @return 表中所有数据
     */
    public List<Object> listAll() {
        return dao.queryAllList(Setter.class);
    }

    /**
     * 根据主键查询对象
     * @param id 主键编码
     * @return 查询的对象
     */
    public Object queryById( String id )  {
        Setter setter = new Setter();
        setter.setId( id );
        return dao.queryByKey( Setter.class, setter );
    }

    /**
     * 根据主键删除对象
     * @param id 主键编码
     * @return 删除是否成功
     */
    public boolean deleteById( String id ) {
        Setter setter = new Setter();
        setter.setId( id );
        int result = dao.deleteByKey( setter );
        return result == 1;
    }
    
    /**
     * 指量删除
     * @param setterIds
     * @return
     */
    @Override
    public boolean batchDelete(String[] setterIds){
    	Setter setter = new Setter();
    	int result = 0 ;
    	StringBuffer idsBuffer = new StringBuffer();
    	int length = setterIds.length;
    	if(length > 0){
    		for (int i = 0 ; i  < length ; i++ ) {
        		idsBuffer.append("'").append(setterIds[i]).append("'");
        		if(i != ( length - 1 )){
        			idsBuffer.append(",");
        		}
    		}
        	result = dao.batchDelete( setter , "id" , idsBuffer.toString());
    	}
    	return result > 1;
    }
    
    /**
     * 删除表中所有记录
     * @return 删除是否成功
     */
    public int deleteAll() {
    	Setter setter = new Setter();
    	return dao.deleteAll( setter );
    }

    /**
     * 新增系统设置记录
     * @param  setter 系统设置对象
     * @return 新增是否成功
     */
    public boolean insert( Setter setter ) {
        int result = dao.insert( setter );
        return result == 1;
    }

    /**
     * 根据ID主键更新信息
     * @param  setter 系统设置对象
     * @return 更新是否成功
     */
    public boolean updateById( Setter setter ) {
        int result = dao.updateByKey( setter );
        return result == 1;
    }
    
    /**
     * 检查设置名称是否存在
     * @param id 设置编码
     * @param name 设置名称
     * @return 是否存在
     */
    public boolean checkNameExist(String id,String name) {
        int result = dao.queryCount( getSql("sql_exist_setter_name"), new Object[]{id, name} );
        return result > 0;
    }
    
    private String getSql(String sqlId) {
        return SqlFactory.getInstance( "system_sql.xml", SetterServiceImpl.class ).getSql( sqlId );
    }
    
    private String getPageSql(String sqlId, int pageNumber ,int pageSize)
    {
        return SqlFactory.getInstance( "system_sql.xml", SetterServiceImpl.class ).getPageSql( sqlId, pageNumber, pageSize );
    }

}
