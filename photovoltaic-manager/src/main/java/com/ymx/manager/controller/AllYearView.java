package com.ymx.manager.controller;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * @DESC 全年日历视图数据
 * @DATE 2018/8/17
 * @NAME AllYearView
 * @MOUDELNAME 模块
 */
public class AllYearView  {

    public  static class DateObject {
        private int year;
        private int month;

        public int getYear() {
            return year;
        }

        public void setYear(int year) {
            this.year = year;
        }

        public int getMonth() {
            return month;
        }

        public void setMonth(int month) {
            this.month = month;
        }
    }

    public static List<DateObject> getDateObjectList() {
        List<DateObject> list = new ArrayList<>();
        DateObject dateObject = null;
        for (int i = 11; i >= 1; i--) {
            dateObject = new DateObject();
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, -i);
            dateObject.setMonth(calendar.get(Calendar.MONTH));
            dateObject.setYear(calendar.get(Calendar.YEAR));
            list.add(dateObject);
        }
        dateObject = new DateObject();
        Calendar calendar = Calendar.getInstance();
        dateObject.setMonth(calendar.get(Calendar.MONTH));
        dateObject.setYear(calendar.get(Calendar.YEAR));
        list.add(dateObject);
        return list;
    }


}
