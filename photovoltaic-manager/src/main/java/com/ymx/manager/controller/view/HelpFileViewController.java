package com.ymx.manager.controller.view;

import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.station.model.RemeberFileModel;
import com.ymx.service.photovoltaic.station.service.RememberFileService;
import com.ymx.common.utils.CommonUtil;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @DESC 系统视图
 * @DATE 2018/8/17
 * @NAME SystemViewController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("helpFileView")
public class HelpFileViewController 
{
	    @Resource
        private RememberFileService rememberFileService;
		@Autowired
		private ILoginLoggerService loginLoggerService;
	    private static final String prefix = "helpfile/";
	    @RequestMapping("list.htm")
	    public String list(HttpServletRequest request)
	    {
	        return prefix + "help_file";
	    }
	    @RequestMapping("createfile.htm")
	    public String createfile(HttpServletRequest request)
	    {
	    	String editorValue =	request.getParameter("editorValue");
	    	if(CommonUtil.isEmpty(editorValue)==true)
	    	{
	    		String path = request.getContextPath();
		        String basePath = request.getScheme() + "://" + request.getServerName()   + ":" + request.getServerPort() + path + "/";
		       // .replaceAll("<img src=\"", "<img src=\"eswefwf")
		    	request.setAttribute("editorValue", editorValue.replaceFirst("<img src=\"", "<img src=\"+basePath"));
		    	RemeberFileModel rfm = new RemeberFileModel();
		    	rfm.setId(UUID.randomUUID().toString());
		    	rfm.setRemberfile(editorValue.replaceFirst("<img src=\"/photovoltaic-manager", "<img src=\""+basePath+""));
		    	List<RemeberFileModel> lt = rememberFileService.queryFile();

			    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
			    rfm.setLanguage(TranslateLanguage.getLanguageType(ob));
		    	if(lt.size()>0)
		    	{
		    		 rememberFileService.updatefile(rfm);
		    	}
		    	else
		    	{
		    		 rememberFileService.savefile(rfm);
		    	}
		    	insertlog("增加帮助文档");
	    	}
	    	
	        return prefix + "help_file";
	    }
	    @RequestMapping("findfile.htm")
	    public String findfile(HttpServletRequest request)
	    {
	    	List<RemeberFileModel> lt = rememberFileService.queryFile();
	    	if(lt.size()>0)
	    	{
	    		RemeberFileModel rf = lt.get(0);
		        request.setAttribute("editorValue" , rf.getRemberfile());
	    	}
	    	else
	    	{
	    		request.setAttribute("editorValue" , "");
	    	}
	        return prefix + "create_file";
	    }
	    public  void insertlog(String content)
	    {
	 	   LoginLogger logger = new LoginLogger();
	       // String content = "查询会员数据";
	        logger.setId( UUID.randomUUID().toString() );
	        logger.setUserId( SessionHelper.getUserId() );
	        logger.setLastLoginTime( new Date() );
	       // logger.setLoginIp( getIpAddr(request) );
	        logger.setContent(content);
	       // logger.setOperation_id(UUID.randomUUID().toString());
	        loginLoggerService.insert( logger );
	    }
	    
}
