package com.ymx.manager.controller.station;

import com.ymx.common.base.entity.Setter;
import com.ymx.common.base.entity.User;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.station.model.ComponentGroupModel;
import com.ymx.service.photovoltaic.station.service.ComponentGroupService;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.common.utils.CommonUtil;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * @DESC 组串
 * @DATE 2018/8/6
 * @NAME ComponentGroupController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("componentGroup")
public class ComponentGroupController {

    private static final String prefix = "componentGroup/";
    @Autowired
    private ILoginLoggerService loginLoggerService;

    @Resource
    private ComponentGroupService componentGroupService;

    /***
     * 跳转至组串列表页
     * @return
     */
    @RequestMapping("list.htm")
    public String list(HttpServletRequest request)
    {
//    	String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        insertlog("查询组串数据");
        return prefix + "componentGroup_list";
    }

    /***
     * 跳转查看组件或选择组件列表页
     * @param belongsGroupFlag 标识
     * @param id   组串主键
     * @return
     */
    @RequestMapping("componentPageList.htm")
    public String componentPageList(String belongsGroupFlag , String id , HttpServletRequest request){
        request.setAttribute("belongsGroupId" , id);
        request.setAttribute("belongsGroupFlag" , belongsGroupFlag);
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return prefix + "select_component_list";
    }




    /***
     * 查询组串数据
     * @param model    组件
     * @param pageView 分页实体
     * @return
     */
    @RequestMapping("queryComponentGroupList.web")
    @ResponseBody
    public CallResult queryComponentGroupList(ComponentGroupModel model , PageView pageView,HttpServletRequest request){
      
    	Object checkrole =   request.getSession().getAttribute("checkrole");//
        Object uid =   request.getSession().getAttribute("userKey");
        if(checkrole.toString().contains("运维"))
        {
        	model.setCreateUserId(uid.toString());
        }

	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
    	return componentGroupService.queryComponentGroupList(model , pageView);
    }


    /**
     * 页面请求 (新增,编辑,查看)
     * @param operator    操作标识
     * @param model      人员信息
     * @return
     */
    @RequestMapping(value="viewRequest.htm")
    public ModelAndView edit(HttpServletRequest request ,String operator , ComponentGroupModel model ){
//    	String mkt = "";
//   	 String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        ModelAndView mv = new ModelAndView(prefix + "componentGroup_edit");
        User user = SessionHelper.getSessionUser();
        mv.addObject("operator" , operator);
        model.setCreateUserId(user.getUserId());
        model.setCreateUserName(user.getUserName());
        if(CommonUtil.isEmpty(model.getId()) && StringUtils.hasLength(model.getId())){
            model = componentGroupService.queryComponentGroupById(model.getId());
        }
        mv.addObject("info" , model);
        return mv;
    }

    /**
     * 新增或修改
     * @param   model      组件
     * @return  CallResult
     */
    @RequestMapping(value="saveOrUpdate.web")
    @ResponseBody
    public CallResult saveOrUpdate( ComponentGroupModel model,HttpServletRequest request  ){
    	Object ob =   request.getSession().getAttribute("checkrole");//
        Object uid =   request.getSession().getAttribute("userKey");
        
        	model.setCreateUserId(uid.toString());

	    Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(obj));
        if (CommonUtil.isEmpty(model.getId())) {
            //修改
        	 insertlog("修改组串数据, 名称:"+model.getCloudName());
            return componentGroupService.updateComponentGroupModel(model);
        } else {
           // model.setCreateUserId(SessionHelper.getUserId());
        	 insertlog("新增组串数据, 名称:"+model.getCloudName());
            return componentGroupService.saveComponentGroupModel(model);
        }
    }

    /**
     * 删除
     * @param  model  组串
     * @return CallResult
     */
    @RequestMapping(value="deleteComponentGroups.web")
    @ResponseBody
    public CallResult deleteComponentGroups(ComponentGroupModel model,HttpServletRequest request ) {
    	 insertlog("删除组串数据，ID:"+model.getId());

	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return componentGroupService.deleteComponentGroups(model);
    }

    /**
     * 逻辑删除
     * @param  model  组串
     * @return CallResult
     */
    @RequestMapping(value="updateDeleteComponentGroupModels.web")
    @ResponseBody
    public CallResult updateDeleteComponentGroupModels(ComponentGroupModel model,HttpServletRequest request ) {
    	 insertlog("逻辑删除组串数据,ID:"+model.getId());
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return componentGroupService.updateDeleteComponentGroupModels(model);
    }


    /**
     * 逆变器中选择组串/逆变器中移除组串
     * @param model  组件
     * @return
     */
    @RequestMapping(value="selectOrMoveComponentGroup.web")
    @ResponseBody
    public CallResult selectOrMoveComponentGroup(ComponentGroupModel model ,HttpServletRequest request ) {
    	 insertlog("逆变器中选择组串/逆变器中移除组串,iD:"+model.getId());
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return componentGroupService.selectOrMoveComponentGroup(model);
    }


    /**
     * 逆变器中选择组串/逆变器中移除组串
     * @param model  组件
     * @return
     */
    @RequestMapping(value="selectOrMoveCloudComponentGroup.web")
    @ResponseBody
    public CallResult selectOrMoveCloudComponentGroup(ComponentGroupModel model  ,HttpServletRequest request)
    {
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return componentGroupService.selectOrMoveCloudComponentGroup(model);
    }
    @RequestMapping(value="validatecomponentGroup.web")
    @ResponseBody
    public CallResult validatecomponentGroup(String id,String num,String model,String serialNo,String chipId,HttpServletRequest request,HttpServletResponse response){
        Map<String ,Object> map = new HashMap<>();
       
        String message =  componentGroupService.validatecomponentGroup(id,num,model,serialNo,chipId);
        
        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
        if(ob.toString().contains("en")&& message.contains("success"))
        {
        	message = "The string number is duplicated, please re-enter";
        }

//	    String language=TranslateLanguage.getLanguageType(ob);
//		if(message.contains("success")){
//
//		}

        JSONObject json = new JSONObject();
      //将返回信息保存在JSON对象中
        json.put("message",message);
       
        //设置响应编码格式，防止乱码
        response.setContentType("text/html;charset=UTF-8");
        //将数据以json格式响应给ajax
        try {
			response.getWriter().write(json.toString());
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        return null;
    }

    @RequestMapping("componentGroupImport.htm")
    @ResponseBody
    public ModelAndView componentGroupImport(HttpServletRequest request,String id)  {
        String action = "import";
        Setter setter = new Setter();
        request.setAttribute( "bean", setter );
        request.setAttribute("action", action);

        return new ModelAndView("componentGroup/componentGroup_import");
    }

    // 导入组件数据上传接口
    @ResponseBody
    @RequestMapping(value="componentGroupUpload.htm",method={RequestMethod.GET,RequestMethod.POST})
    public String componentGroupUpload(HttpServletRequest request,HttpServletResponse response) throws Exception
    {
        return componentGroupService.importGroupByExcel(request, response);
    }


    public  void insertlog(String content)
    {
 	   LoginLogger logger = new LoginLogger();
       // String content = "查询会员数据";
        logger.setId( UUID.randomUUID().toString() );
        logger.setUserId( SessionHelper.getUserId() );
        logger.setLastLoginTime( new Date() );
       // logger.setLoginIp( getIpAddr(request) );
        logger.setContent(content);
      //  logger.setOperation_id(UUID.randomUUID().toString());
        loginLoggerService.insert( logger );
    }
}
