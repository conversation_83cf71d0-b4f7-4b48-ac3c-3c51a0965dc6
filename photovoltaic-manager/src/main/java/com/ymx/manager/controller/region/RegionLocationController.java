package com.ymx.manager.controller.region;

import com.ymx.common.common.result.CallResult;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.region.entity.RegionLocationModel;
import com.ymx.service.photovoltaic.region.service.RegionLocationService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @DESC 区域位置
 * @DATE 2018/8/15
 * @NAME RegionLocationController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("region")
public class RegionLocationController {

    @Resource
    private RegionLocationService regionLocationService;


    /**
     * 获取数据
     * @param model 区域位置实体
     * @return
     */
    @ResponseBody
    @RequestMapping("queryRegion.web")
    public CallResult queryRegion(HttpServletRequest request, RegionLocationModel model ){
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return regionLocationService.queryRegion(model);
    }


}
