package com.ymx.manager.controller.version;

import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageInfo;
import com.ymx.common.common.result.PageView;
import com.ymx.service.photovoltaic.station.mapper.VersionQueryMapper;
import com.ymx.service.photovoltaic.station.model.VersionQueryModel;
import com.ymx.service.photovoltaic.station.model.VersionQueryTaskModel;
import com.ymx.common.utils.CommonPage;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("versionQueryTask")
public class VersionQueryTaskController {


    @Resource
    private VersionQueryMapper versionQueryMapper;



    // 创建查询任务接口
    @RequestMapping("createTask.web")
    @ResponseBody
    public CallResult createTask(VersionQueryTaskModel versionQueryTaskModel, HttpServletRequest request){

        String taskId = System.currentTimeMillis() + "";
        taskId=taskId.replace("0","2");
        versionQueryTaskModel.setTaskId(taskId);
        String createUser=(String) request.getSession().getAttribute("userKey");
        versionQueryTaskModel.setCreateUser(createUser);
        // queryType  1 查询自身  2 查询所属设备  type  1 采集器  2 中继器 3 优化器 4 组串
        if(versionQueryTaskModel.getType()==4)
        {
            // 组串页面没有imei imei字段传的是cloudId 然后查询真正的imei
         String imei=versionQueryMapper.queryGroupImei(versionQueryTaskModel.getImei());
         versionQueryTaskModel.setImei(imei);
        }

        Integer result= versionQueryMapper.insertVersionQueryTask(versionQueryTaskModel);
        return CallResult.newInstance();
    }

    // 查询 查询任务接口
    @RequestMapping("queryTask.web")
    @ResponseBody
    public CallResult queryTask(HttpServletRequest request, PageView pageView){

        String createUser=(String) request.getSession().getAttribute("userKey");
        int count = versionQueryMapper.queryVersionQueryTaskCount(createUser);
        PageInfo pageInfo = CommonPage.getPageInfo(count, pageView, "");
        Map<String, Object> map = new HashMap<>();
        map.put("page",pageInfo);
        map.put("createUser",createUser);

        List<VersionQueryTaskModel> versionQueryTaskModelList=versionQueryMapper.queryVersionQueryTaskList(map);
        versionQueryTaskModelList.forEach(v->
        {
            String beginTime = v.getBeginTime();
            if (beginTime != null && beginTime.length() > 19) {
                v.setBeginTime(beginTime.substring(0, 19));
            }

            String updateTime = v.getUpdateTime();
            if (updateTime != null && updateTime.length() > 19) {
                v.setUpdateTime(updateTime.substring(0, 19));
            }

        });

        map.clear();
        map.put("data",versionQueryTaskModelList);
        CallResult callResult= CallResult.newInstance();
        callResult.setCount(count);
        callResult.setPageInfo(pageInfo);
        callResult.setReModel(map);
        return callResult;
    }

    // 返回查询任务列表页面
    @RequestMapping("list.htm")
    public String updateTaskResult(HttpServletRequest request)
    {

        return  "queryTask/queryTask_list";
    }

    // 删除 查询任务接口
    @RequestMapping("deleteTask.web")
    @ResponseBody
    public CallResult deleteTask(String taskId){
       versionQueryMapper.deleteQueryTask(taskId);
        return CallResult.newInstance();

    }

    // 重试 查询任务接口
    @RequestMapping("endTask.web")
    @ResponseBody
    public CallResult endTask(String taskId){
        Map<String,Object> map=new HashMap<>();
        map.put("taskId",taskId);
        map.put("status",1);
        versionQueryMapper.updateStatusWithOutTime(map);
        return CallResult.newInstance();
    }

    @RequestMapping("retryTask.web")
    @ResponseBody
    public CallResult retryTask(String taskId){
        versionQueryMapper.retryQueryTask(taskId);
        return CallResult.newInstance();
    }

    // 任务详情页面
    @RequestMapping("queryTaskResult.htm")
    public String queryTaskResult(HttpServletRequest request,VersionQueryTaskModel queryTaskModel)
    {
        request.getSession().setAttribute("queryType",queryTaskModel.getQueryType());
        request.getSession().setAttribute("type",queryTaskModel.getType());
        request.getSession().setAttribute("queryId",queryTaskModel.getQueryId());
        request.getSession().setAttribute("imei",queryTaskModel.getImei());
        request.getSession().setAttribute("taskId",queryTaskModel.getTaskId());

        return "queryTask/queryTaskResult";
    }

    // 查看任务详情
    @RequestMapping(value="queryTaskResult.web")
    @ResponseBody
    public CallResult queryTaskResultWeb(VersionQueryTaskModel queryTaskModel, PageView pageView) {
        List<VersionQueryModel> versionQueryModelList=null;
        Map<String, Object> map = new HashMap<>();
        CallResult callResult= CallResult.newInstance();
        String queryId=queryTaskModel.getQueryId();

        int count=0;
        PageInfo pageInfo=null;

        // queryType 1 查询自身 2 查询所属组件
        if(queryTaskModel.getQueryType()==1)
        {
            switch (queryTaskModel.getType())
            {
                // 1 采集器  2 中继器  3 优化器 4 组串
                case 1:
                versionQueryModelList=versionQueryMapper.queryCollectorVersion(queryId);
                break;
                case 2:
                versionQueryModelList=versionQueryMapper.queryRepeaterVersion(queryId);
                break;
                case 3:
                versionQueryModelList=versionQueryMapper.queryOptimizerVersion(queryId);
                break;
            }
            count=1;
            pageInfo = CommonPage.getPageInfo(count, pageView, "");
        }
        else
        {
            // 先查出总条数，然后再进行分页查询
            switch (queryTaskModel.getType())
            {
                // 1 查询中继器和优化器 2 查询优化器 4 查询优化器
                case 1:
                count=versionQueryMapper.queryRepeaterCountByImei(queryId);
                int optimizerCount=versionQueryMapper.queryOptimizerCountByImei(queryId);
                count=count+optimizerCount;
                pageInfo = CommonPage.getPageInfo(count, pageView, "");
                map.put("page",pageInfo);
                map.put("imei",queryId);
                versionQueryModelList=versionQueryMapper.queryRepeaterVersionByImei(map);
                // 如果中继器查询出来有值
                if(versionQueryModelList!=null&&versionQueryModelList.size()>0)
                {
                    List<VersionQueryModel> optimizerList=null;
                    int size=versionQueryModelList.size();
                    // 如果查询出来的中继器没有满一页，接着查询优化器，然后进行合并
                    if(size<pageInfo.getPageSize())
                    {
                        int pageSize=pageInfo.getPageSize();
                        pageInfo.setPageSize(pageSize-size);
                        map.put("page",pageInfo);
                        optimizerList=versionQueryMapper.queryOptimizerVersionByImei(map);
                        // 查询完成 在把pageSize设置成原来的
                        pageInfo.setPageSize(pageSize);
                    }
                    if(optimizerList!=null)
                    {
                        versionQueryModelList.addAll(optimizerList);
                    }
                }
                else
                {
                    versionQueryModelList=versionQueryMapper.queryOptimizerVersionByImei(map);
                }
                break;
                case 2:
                count=versionQueryMapper.queryOptimizerCountByRelayId(queryId);
                pageInfo = CommonPage.getPageInfo(count, pageView, "");
                map.put("page",pageInfo);
                map.put("relayId",queryId);
                versionQueryModelList=versionQueryMapper.queryOptimizerVersionByRelayId(map);
                break;
                case 4:
                count=versionQueryMapper.queryOptimizerCountByGroupId(queryId);
                pageInfo = CommonPage.getPageInfo(count, pageView, "");
                map.put("page",pageInfo);
                map.put("groupId",queryId);
                versionQueryModelList=versionQueryMapper.queryOptimizerVersionByGroupId(map);
                break;
            }

        }

        callResult.setCount(count);
        if (pageInfo != null) {
            callResult.setPageInfo(pageInfo);
        }
        map.clear();
        if(versionQueryModelList!=null)
        {
            versionQueryModelList.forEach(v -> v.setTaskId(queryTaskModel.getTaskId()));
        }
        map.put("data",versionQueryModelList);
        callResult.setReModel(map);
        return callResult;
    }


}
