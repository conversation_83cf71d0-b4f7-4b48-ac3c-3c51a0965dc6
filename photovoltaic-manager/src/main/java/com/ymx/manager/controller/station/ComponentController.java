package com.ymx.manager.controller.station;

import com.ymx.common.mina.StringUtil;
import com.ymx.common.utils.*;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.common.base.config.ConfigConstants;
import com.ymx.common.base.entity.Setter;
import com.ymx.common.base.entity.User;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.manager.security.service.IUserService;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.station.model.*;
import com.ymx.service.photovoltaic.station.service.ComponentCollectBatchNoService;
import com.ymx.service.photovoltaic.station.service.ComponentCollectService;
import com.ymx.service.photovoltaic.station.service.ComponentService;
//
import com.ymx.service.photovoltaic.station.model.*;
import com.ymx.service.photovoltaic.station.service.*;
import com.ymx.service.photovoltaic.warning.entity.WarningModel;
import com.ymx.service.photovoltaic.warning.service.WarningService;
import com.ymx.common.base.config.ConfigConstants;
import com.ymx.service.cache.Configure;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.common.mina.StringUtil;
import com.ymx.service.photovoltaic.station.model.*;
import com.ymx.service.photovoltaic.station.service.*;
import com.ymx.service.photovoltaic.warning.entity.WarningModel;
import com.ymx.service.photovoltaic.warning.service.WarningService;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.DataUtil;
import com.ymx.common.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
//
import com.ymx.service.photovoltaic.warning.entity.WarningModel;
import net.sf.json.JSONObject;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;
import org.slf4j.Logger;
import com.ymx.service.photovoltaic.station.service.ComponentDayService;

import static com.ymx.common.utils.DateUtils.getCreateTimeCh;

/**
 * @DESC 组件控制层
 * @DATE 2018/8/1
 * @NAME ComponentController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("component")
public class ComponentController {

	private static final Logger log = LoggerFactory.getLogger(ComponentController.class);
	/** 组件服务层 */
    @Resource
    private ComponentService componentService;
    @Autowired
    private ILoginLoggerService loginLoggerService;
    private static final String prefix = "component/";
	@Autowired
	private ComponentDayService componentDayService;

	//下面是新引入的
	@Resource
	private ComponentGroupService componentGroupService;
	@Autowired
	private ComponentCollectService componentCollectService;
	@Autowired
	private ComponentCollectBatchNoService componentCollectBatchNoService;
	/** 组件服务层 */
	@Resource
	private ConfigureService configureService;
	/** 逆变器服务层 */
	@Resource
	private InverterService inverterService;
	/** 云终端服务层 */
	@Resource
	private CloudTerminalService cloudTerminalService;
	@Resource
	private WarningService warningService;
	@Resource
	private InverterCollectService inverterCollectService;
	@Resource
	private PowerStationService powerStationService;
    @Autowired
    private com.ymx.manager.security.utils.Logger logger;

	@Autowired
	private IUserService userService;


	/**
     * 组件列表页
     * @return
     */
    @RequestMapping("list.htm")
    public String list(HttpServletRequest request)
    {
        insertlog("查询组件数据");
        return prefix + "component_list";
    }

    /***
     * 查询组件数据
     * @param model    组件
     * @param pageView 分页实体
     * @return
     */
    @RequestMapping("queryComponentList.web")
    @ResponseBody
    public CallResult queryComponentList(ComponentModel model , PageView pageView,HttpServletRequest request){
        Object checkrole = request.getSession().getAttribute("checkrole");//
        Object uid =   request.getSession().getAttribute("userKey");
        if(checkrole.toString().contains("运维"))
        {
        	model.setCreateUserId(uid.toString());
        }
	    Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(obj));
        return componentService.queryComponentList(model , pageView);
    }


    /**
     * 页面请求 (新增,编辑,查看)
     * @param operator    操作标识
     * @param model      组件信息
     * @return
     */
    @RequestMapping(value="viewRequest.htm")
    public ModelAndView edit(HttpServletRequest request ,String operator , ComponentModel model ){
        ModelAndView mv = new ModelAndView(prefix + "component_edit");
        User user = SessionHelper.getSessionUser();
        mv.addObject("operator" , operator);
        model.setCreateUserId(user.getUserId());
        model.setCreateUserName(user.getUserName());
        if(CommonUtil.isEmpty(model.getId()) && StringUtils.hasLength(model.getId())){
            model = componentService.queryComponentById(model.getId(),model.getLanguage());
        }
        mv.addObject("info" , model);
        return mv;
    }

    /**
     * 新增或修改
     * @param   model      组件
     * @return  CallResult
     */
    @RequestMapping(value="saveOrUpdate.web")
    @ResponseBody
    public CallResult saveOrUpdate( ComponentModel model,HttpServletRequest request  ){
        Object uid =   request.getSession().getAttribute("userKey");

		model.setCreateUserId(uid.toString());
	    Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(obj));
        if (CommonUtil.isEmpty(model.getId())) {
            //修改
        	insertlog("修改组件数据,Chipid:"+model.getChipId());
            return componentService.updateComponentModel(model);
        } else {
           // model.setCreateUserId(SessionHelper.getUserId());
        	insertlog("新增组件数据,Chipid"+model.getChipId());
			String id = componentService.selectByChipId(model.getChipId());
			if(null != id) {
				model.setId(id);
				return componentService.updateComponentModel(model);
			} else {
				return componentService.saveComponentModel(model);
			}
        }
    }

    /**
     * 删除
     * @param  model  组件
     * @return CallResult
     */
    @RequestMapping(value="deleteComponents.web")
    @ResponseBody
    public CallResult deleteComponents(ComponentModel model,HttpServletRequest request) {
    	insertlog("删除组件数据,id:"+model.getId());
	    Object obj = request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(obj));
        return componentService.deleteComponents(model);
    }

    /**
     * 逻辑删除
     * @param  model  组件
     * @return CallResult
     */
    @RequestMapping(value="updateDeleteComponentModels.web")
    @ResponseBody
    public CallResult updateDeleteComponentModels(ComponentModel model,HttpServletRequest request) {
    	insertlog("逻辑删除组件数据,id:"+model.getId());
	    Object obj = request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(obj));
        return componentService.updateDeleteComponentModels(model);
    }


    /**
     * 组串中选择组件/组串中移除组件
     * @param model  组件
     * @return
     */
    @RequestMapping(value="selectOrMoveComponent.web")
    @ResponseBody
    public CallResult selectOrMoveComponent(ComponentModel model ,HttpServletRequest request) {
    	insertlog("组串中选择组件/组串中移除组件,id:"+model.getId());
	    Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(obj));
        return componentService.selectOrMoveComponent(model);
    }


	/**
	 * 云终端中选择组件/云终端中移除组件
	 * @param model  组件
	 * @return
	 */
	@RequestMapping(value="selectOrMoveCloudComponent.web")
	@ResponseBody
	public CallResult selectOrMoveCloudComponent(ComponentModel model,HttpServletRequest request ) {
		insertlog("云终端中选择组件/云终端中移除组件,id:"+model.getId());
		Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(obj));
		return componentService.selectOrMoveCloudComponent(model);
	}

	 @RequestMapping("componentimport.htm")
     @ResponseBody
     public ModelAndView componentimport(HttpServletRequest request,String id)  {
         String action = "import";
         Setter setter = new Setter();
         request.setAttribute( "bean", setter );
         request.setAttribute("action", action);

         return new ModelAndView("component/component_import");
     }

	    // 导入组件数据上传接口
	    @ResponseBody
	    @RequestMapping(value="componentUpload.htm",method={RequestMethod.GET,RequestMethod.POST})
	    public String componentUpload(HttpServletRequest request,HttpServletResponse response) throws Exception
	    {
		   insertlog("导入组件数据");
		   return componentService.importComponentByExcel(request, response);
	    }

	    @RequestMapping(value="validatecomponent.web")
	    @ResponseBody
	    public CallResult validatecomponent(String id,String num,String model,String serialNo,String chipId,HttpServletRequest request,HttpServletResponse response){
	        Map<String ,Object> map = new HashMap<>();
	        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	        String message =  componentService.validatecomponent(id,num,model,serialNo,chipId,TranslateLanguage.getLanguageType(ob));
	        JSONObject json = new JSONObject();
	      //将返回信息保存在JSON对象中
	        json.put("message",message);

	        //设置响应编码格式，防止乱码
	        response.setContentType("text/html;charset=UTF-8");
	        //将数据以json格式响应给ajax
	        try {
				response.getWriter().write(json.toString());
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
	        return null;
	    }
	    public  void insertlog(String content)
	    {
	 	   LoginLogger logger = new LoginLogger();
	       // String content = "查询会员数据";
	        logger.setId( UUID.randomUUID().toString() );
	        logger.setUserId( SessionHelper.getUserId() );
	        logger.setLastLoginTime( new Date() );
	       // logger.setLoginIp( getIpAddr(request) );
	        logger.setContent(content);
	       // logger.setOperation_id(UUID.randomUUID().toString());
			loginLoggerService.insert( logger );
	    }
	    @ResponseBody
	    @RequestMapping(value="componentdownfile.htm",method={RequestMethod.GET,RequestMethod.POST})
	    public void downloadLocal(HttpServletResponse response) throws FileNotFoundException {
	        // 下载本地文件
	    	 // 获取文件路径
	        String REMOTE_DOWNFILE_URL = ConfigConstants.getConfig("REMOTE_DOWNFILE_URL");
	        String fileName = "component.xls".toString(); // 文件的默认保存名
	        // 读到流中
	        InputStream inStream = new FileInputStream(REMOTE_DOWNFILE_URL+fileName);// 文件的存放路径
	        // 设置输出的格式
	        response.reset();
	        response.setContentType("bin");
	        response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
	        // 循环取出流中的数据
	        byte[] b = new byte[100];
	        int len;
	        try {
	            while ((len = inStream.read(b)) > 0)
	                response.getOutputStream().write(b, 0, len);
	            inStream.close();
	        } catch (IOException e) {
	            e.printStackTrace();
	        }
	    }






	// 获取系统视图的接口  返回的信息包括组件的电气信息 组串的电气信息 总发电量  时间轴四个数据
	@RequestMapping(value = "queryComponentListTo.web")
	@ResponseBody
	public CallResult queryComponentListTo(@RequestParam(value = "powerStationId", required = true) String powerStationId,
										   @RequestParam(value = "date", required = true) String date,
										   @RequestParam(value = "groupId", required = false) String groupId,
										   @RequestParam(value = "sunUpTime", required = false) String sunUpTime,
										   @RequestParam(value = "sunDownTime", required = false) String sunDownTime,
										   @RequestParam(value = "imei", required = false) String imei) {
		String type = "2";
		return querySystemViewDataTo(groupId, powerStationId, date, sunUpTime, sunDownTime, type,imei);
	}

	public CallResult querySystemViewDataTo(String groupId, String powerStationId,
											String date, String sunUpTime,
											String sunDownTime, String type,String imei) {
		CallResult callResult = CallResult.newInstance();
		// 返回系统视图需要的数据
		Map<String, Object> map = getElectricData(groupId, powerStationId, date, sunUpTime, sunDownTime, type);

		// 查询分组信息
		if (groupId == null) {
			//List<GroupModel> groupModelList = componentGroupService.queryGroupInfoForApp(powerStationId);
			List<GroupModel> groupModelList = componentGroupService.queryGroupInfoForWeb(powerStationId);
			map.put("groupList", groupModelList);
		}
		List<CloudTerminalModelVo> collectorList=cloudTerminalService.queryCollectorListByPowerIdAndImei(powerStationId,imei);
		map.put("collectorList",collectorList);
		callResult.setReModel(map);
		return callResult;
	}

	// 返回系统视图需要的数据
	public Map<String, Object> getElectricData(String groupId, String powerStationId,
											   String date, String sunUpTime, String sunDownTime, String type) {

		// 先获取位置和告警信息对象 再获取电气信息对象 然后合并两个对象
		List<ComponentControlModel> componentControlList = getComPositionAndWarnStatusTo(groupId, powerStationId, date);

		Map<String, Object> queryMap = getQueryMap(powerStationId,date);
		List<ComponentViewModel> componentViewModelList = getComponentViewList(groupId,date,componentControlList,queryMap);

		Map<String, Object> returnMap =new HashMap<>();
		returnMap.put("data", componentViewModelList);

		// 设置时间轴信息
		if (type.equals("1")) {
			String collectTime = null;
			if (componentViewModelList.size() > 0) {
				collectTime = componentViewModelList.get(0).getCollectTime();
				//logger.info("first collect time:" + collectTime);
			}
			String timeAxisCreateTime = (String) queryMap.get("collectTimeStart");
			timeAxisCreateTime = collectTime != null ? collectTime : timeAxisCreateTime + ":00";

			returnMap.put("timeAxis", Collections.singletonList(timeAxisCreateTime));
			returnMap.put("timeAxisStep", ConfigUtil.getTimeStep());
			String day = timeAxisCreateTime.substring(0, 10);
			returnMap.put("timeAxisBeginTime", day + " " + sunUpTime + ":00");
			returnMap.put("timeAxisEndTime", day + " " + sunDownTime + ":00");
		}
		return returnMap;
	}

	// 查询组件位置和告警状态  考虑分组 日期不用考虑 告警都是取当天的
	private List<ComponentControlModel> getComPositionAndWarnStatus(String groupId, String powerStationId,
																	String date) {
		Map<String, Object> map = new HashMap<>();
		map.put("powerStationId", powerStationId);

		List<ComponentControlModel> componentControlList;
		// 如果组串id为空 则查询整个电站 否则查询单个组串的
		if (groupId == null) {
			componentControlList = componentService.queryComponentLocationWeb(powerStationId);
		} else {
			componentControlList = componentService.queryGroupComPositionWeb(groupId);
			// 取出equipmentIdList 查询告警状态
			List<String> chipIdList = componentControlList.stream().
					map(ComponentControlModel::getChipId).collect(Collectors.toList());
			if (chipIdList.size() > 0) {
				map.put("chipIdList", chipIdList);
			}
		}
		String nowDay = LocalDate.now().toString();
		// 只查询今天的告警 历史告警不处理
		if (date.length() > 10 && date.substring(0, 10).equals(nowDay)) {
			map.put("createTime", nowDay);
			// 查询告警的chipIdList
			List<String> chipIdList = warningService.queryTodayWarningList(map);
			// 如果chipId存在告警 就把状态置为3 1 打开 2 关闭 3 有告警
			componentControlList.forEach(comControlModel ->
			{
				if (chipIdList.contains(comControlModel.getChipId()) && comControlModel.getStatus() == 1) {
					comControlModel.setStatus(3);
				}
			});
		}

		return componentControlList;
	}

	// 查询组件位置和告警状态  考虑分组 日期不用考虑 告警都是取当天的
	private List<ComponentControlModel> getComPositionAndWarnStatusTo(String groupId, String powerStationId,
																	String date) {
		Map<String, Object> map = new HashMap<>();
		map.put("powerStationId", powerStationId);

		List<ComponentControlModel> componentControlList;
		// 如果组串id为空 则查询整个电站 否则查询单个组串的
		if (groupId == null) {
			//查询整个电站的组件
			componentControlList = componentService.queryComponentLocationWebTo(powerStationId);
		} else {
			componentControlList = componentService.queryGroupComPositionWeb(groupId);
			// 取出equipmentIdList 查询告警状态
			List<String> chipIdList = componentControlList.stream().
					map(ComponentControlModel::getChipId).collect(Collectors.toList());
			if (chipIdList.size() > 0) {
				map.put("chipIdList", chipIdList);
			}
		}
		String nowDay = LocalDate.now().toString();
		// 只查询今天的告警 历史告警不处理
		if (date.length() > 10 && date.substring(0, 10).equals(nowDay)) {
			map.put("createTime", nowDay);
			// 查询告警的chipIdList
			List<String> chipIdList = warningService.queryTodayWarningList(map);
			// 如果chipId存在告警 就把状态置为3 1 打开 2 关闭 3 有告警
			componentControlList.forEach(comControlModel ->
			{
				if (chipIdList.contains(comControlModel.getChipId()) && comControlModel.getStatus() == 1) {
					comControlModel.setStatus(3);
				}
			});
		}

		return componentControlList;
	}


	private Map<String, Object> getQueryMap(String powerStationId,String date) {
		Map<String, Object> map = new HashMap<>();
		map.put("tableName", "_" + powerStationId);
		String timeAxisCreateTime;
		int timeStep = ConfigUtil.getTimeStep();

		if (date != null && date.contains(":")) {
			// 取出最近入库的两倍组件数量  防止入库时间不一致
			DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
			LocalDateTime createTime = LocalDateTime.parse(date, dtf);
			createTime = createTime.plusMinutes(-timeStep);
			timeAxisCreateTime = createTime.withNano(0).toString().replace("T", " ");
			map.put("collectTimeStart", timeAxisCreateTime);
		} else {
			// 如果是过去的日期
			LocalTime now = LocalTime.now();
			LocalTime afterNow = now.plusMinutes(timeStep);
			timeAxisCreateTime = date + " " + now.withSecond(0).withNano(0);
			map.put("collectTimeStart", timeAxisCreateTime);
			map.put("collectTimeEnd", date + " " + afterNow.withSecond(0).withNano(0));
		}
		return map;
	}

	private List<ComponentViewModel> getComponentViewList(String groupId,String date,
														  List<ComponentControlModel> componentControlList,Map<String, Object> map) {
		if (groupId != null) {
			List<String> chipIdList = componentControlList.stream().map(ComponentControlModel::getChipId).collect(Collectors.toList());
			map.put("chipIdList", chipIdList);
		}

		List<ComponentViewModel> componentViewModelList;
		if (date != null && date.contains(":")) {
			map.put("limit", componentControlList.size() * 2);
			componentViewModelList = componentCollectService.queryLastCollectInfo(map);
			if (componentViewModelList.size() != 0) {
				// 获取每个批次的组件数量  最后是否需要batchNo 还是只需要采集时间
				Map<String, Long> collectTimeMap = Optional.of(componentViewModelList).
						orElse(new ArrayList<>()).stream().
						collect(Collectors.groupingBy(ComponentViewModel::getCollectTime, Collectors.counting()));

				collectTimeMap.forEach((k, v) -> {
					log.info("key:value = " + k + ":" + v);
				});
				String maxCollectTime = getMaxCollectTime(collectTimeMap);

				log.info("maxCollectTime: " + maxCollectTime);
				// 用这个批次号去过滤组件
				componentViewModelList = Optional.of(componentViewModelList).
						orElse(new ArrayList<>()).stream().
						filter(componentViewModel -> componentViewModel.getCollectTime().equals(maxCollectTime))
						.collect(Collectors.toList());
			}
		} else {
			componentViewModelList = componentCollectService.queryPastCollectInfo(map);
		}

		// key chipId value 为ComponentViewModel
		Map<String, ComponentViewModel> comViewModelMap = Optional.of(componentViewModelList).
				orElse(new ArrayList<>()).stream().
				collect(Collectors.toMap(ComponentViewModel::getChipId, v -> v, (a, b) -> a));

		List<ComponentViewModel> returnComViewModelList = new ArrayList<>();

		ComponentViewModel componentViewModel;
		// 循环componentControlList  合并电气信息对象
		for (ComponentControlModel componentControlModel : componentControlList) {
			componentViewModel = new ComponentViewModel(componentControlModel,
					comViewModelMap.get(componentControlModel.getChipId()));
			returnComViewModelList.add(componentViewModel);
		}

		return returnComViewModelList;

	}

	//获取最大的批次号
	private String getMaxCollectTime(Map<String, Long> collectTimeMap) {
		List<String> keyList = collectTimeMap.keySet().stream().
				sorted(Comparator.comparing(collectTimeMap::get)).collect(Collectors.toList());
		if (keyList.size() == 1) {
			return keyList.get(0);
		} else {
			String firstKey = keyList.get(0);
			String secondKey = keyList.get(1);

			long firstValue = collectTimeMap.get(firstKey);
			long secondValue = collectTimeMap.get(secondKey);

			// 哪个批次的数据全就取哪个批次的
			if (firstValue > secondValue) {
				return firstKey;
			}
			if (firstValue < secondValue) {
				return secondKey;
			}
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

			// 如果两个批次的数据量相同 取最近的批次号
			LocalDateTime firstCollectTime = LocalDateTime.parse(firstKey, formatter);
			LocalDateTime secondCollectTime = LocalDateTime.parse(secondKey, formatter);
			if (firstCollectTime.isAfter(secondCollectTime)) {
				return firstKey;
			} else {
				return secondKey;
			}
		}

	}

	//查询当日发电量排名,备用接口
	@RequestMapping(value = "groupByKwhRe.web")
	@ResponseBody
	public CallResult getPowerStationGroupByKwh(HttpServletRequest httpServletRequest) {
		HttpSession session = httpServletRequest.getSession();
		String userId = (String) session.getAttribute("userId");
		String password = (String) session.getAttribute("password");
		String createUserId = userService.queryUserIdBylocal(userId,password);
		List<String> powerStationIds = powerStationService.queryPowerStationListByCreateUserId(createUserId);
		List<PowerStationGroupByKwh> list =componentService.queryGroupByKwh(powerStationIds);
		for (PowerStationGroupByKwh powerStationGroupByKwh : list) {
			powerStationGroupByKwh.setPowerStationName(powerStationService.getSystemById(powerStationGroupByKwh.getPowerStationId()));
		}
		CallResult callResult = CallResult.newInstance();
		callResult.setReModel(list);
		return callResult;
	}


	/**
	 * 统计  当前功率，当天电量，总电流 ，
	 * @return CallResult
	 */
	@RequestMapping(value = "groupByKwh.web")
	@ResponseBody
	public CallResult queryComponentByStatistics(String day,String language,HttpServletRequest httpServletRequest) {
		//获取session,拿到createUserId,查询电站Id集合
		HttpSession session = httpServletRequest.getSession();
		String key = session.getAttribute("userKey").toString();
		List<String> powerStationIds = powerStationService.queryPowerStationListByCreateUserId(key);

		CallResult callResult = CallResult.newInstance();

		if (StringUtil.nullOrEmpty(day)) {
			// 时间为null 就获取现在的时间 day客户端默认不传
			day = LocalDate.now().toString();
		}
		if (powerStationIds == null && powerStationIds.size() == 0) {
			callResult.setErrMsg("用户信息不全");
		}

		List<PowerStationGroupByKwh> powerStationGroupByKwhs = new ArrayList<>();
		//遍历电站Id,循环查询每个电站的发电量
		for (String powerStationId : powerStationIds) {
			try {
				String powerStationName = powerStationService.getSystemById(powerStationId);
				BigDecimal lastPower = new BigDecimal(0);
				BigDecimal totalPowerSum = new BigDecimal(0);
				BigDecimal todayKwh = new BigDecimal(0);
				BigDecimal allKwh = new BigDecimal(0);
				List<ComponentHourModel> comHourModelList = getComHourModelList(powerStationId, day,false,null);
				if (comHourModelList.size() > 0) {
					comHourModelList.forEach(comHourModel ->
					{
						// batchNoPower的单位是W
						BigDecimal batchNoPower = comHourModel.getOutputCurrent().multiply(comHourModel.getOutputVoltage());
						comHourModel.setPower(batchNoPower);
					});
					// lastPower 今天最近一个批次的功率
					lastPower = comHourModelList.get(comHourModelList.size() - 1).getPower().setScale(2, RoundingMode.HALF_UP);
					// totalPowerSum 今天到现在为止的功率之和
					totalPowerSum = comHourModelList.stream().map(ComponentHourModel::getPower).reduce(BigDecimal.ZERO, BigDecimal::add)
							.setScale(2, RoundingMode.HALF_UP);
					// 应该是 *timeStep/60 后续 timeStep 按电站分开时 再进行修改
					totalPowerSum=totalPowerSum.divide(BigDecimal.valueOf(60),2,RoundingMode.HALF_UP);
					// todayKwh 今天到现在为止的发电量
					todayKwh = BigDecimal.valueOf(totalPowerSum.doubleValue() / 1000.0).setScale(2, RoundingMode.HALF_UP);
				}
				//Map<String, Object> map = new HashMap<>();
				//map.put("powerStationId",powerStationId);
				// 查询电站年发电量信息
				//List<ComponentDayModel> yearModelList = componentDayService.queryComponentByYear(map);
				// 循环累加年发电量
				//if (yearModelList != null && yearModelList.size() > 0) {
				//allKwh = yearModelList.stream().map(ComponentDayModel::getKwh).reduce(BigDecimal.ZERO, BigDecimal::add);
				//}
				// allKwh=电站所有年份发电量之和+dayKwh
				//allKwh = allKwh.add(todayKwh).setScale(2, RoundingMode.HALF_UP);

				//map.put("power", lastPower);
				//map.put("powerSum", totalPowerSum);
				PowerStationGroupByKwh powerStationGroupByKwh = new PowerStationGroupByKwh();
				powerStationGroupByKwh.setPowerStationId(powerStationId);
				powerStationGroupByKwh.setPowerStationName(powerStationName);
				powerStationGroupByKwh.setKwh(todayKwh);
				powerStationGroupByKwhs.add(powerStationGroupByKwh);
				//map.put("dayKwh", todayKwh);
				//map.put("kwh", allKwh);
			} catch (Exception e) {
				//getCallResult(callResult, language, ErrCodeExt.SYSTEM_ERR);
				//logger.error(e.getMessage());
				e.printStackTrace();
			}
		}
		callResult.setReModel(powerStationGroupByKwhs);
		return callResult;
	}

	/**
	 * 查询电站或组串的批次电气数据
	 * @param powerStationId  电站id
	 * @param day  请求日期
	 * @param isQueryGroup  是否是组串查询
	 * @param groupId  组串id
	 * @return  电站或组串的批次电气数据
	 */
	private List<ComponentHourModel> getComHourModelList(String powerStationId, String day,
														 boolean isQueryGroup, String groupId) {
		Map<String, Object> map = new HashMap<>();
		map.put("tableName", "_" + powerStationId);
		day = day.replace("-", "");
		map.put("day", day);
		// 先去历史表中取数据
		List<ComponentHourModel> comHourModelList;
		if (isQueryGroup) {
			map.put("groupId",groupId);
			comHourModelList = componentDayService.queryDataByGroupId(map);
		} else {
			comHourModelList = componentDayService.queryDataGroupByBatchNo(map);
		}

		String today = LocalDate.now().toString();
		// 如果day 是今天 从采集表中取今天的数据
		if (day.trim().equals(today.replace("-", ""))) {
			//设置原始采集表查询的开始时间和结束时间
			int comListSize = comHourModelList.size();
			if (comListSize > 0) {
				// 组串汇聚表有数据 取最后一条的汇聚时间作为开始时间
				String lastGroupCollectTime = getCreateTimeCh(comHourModelList.get(comListSize - 1).getBatchNo());
				map.put("startTime", lastGroupCollectTime);
			} else {   // 没有数据 从5点开始查询
				map.put("startTime", today + " 05:00:00");
			}
			map.put("endTime", DateUtils.getNowTime());
			List<ComponentHourModel> todayComHourModelList;
			if (isQueryGroup) {
				todayComHourModelList = componentDayService.queryTodayGroupCollectInfo(map);
			} else {
				todayComHourModelList = componentDayService.queryTodayBatchNoCollectInfo(map);
			}
			comHourModelList.addAll(todayComHourModelList);
		}
		return comHourModelList;
	}

}
