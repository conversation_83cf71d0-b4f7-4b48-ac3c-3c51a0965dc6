package com.ymx.manager.controller.member;

import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.member.entity.LoginCode;
import com.ymx.service.photovoltaic.member.service.LoginCodeService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import java.util.Date;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @DESC 登录码
 * @DATE 2018/7/30
 * @NAME LoginCodeController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("loginCode")
public class LoginCodeController  {

    /** 登录码服务层 */
    @Resource
    private LoginCodeService loginCodeService;
    @Autowired
    private ILoginLoggerService loginLoggerService;

    private static final String prefix = "logincode/";

    /**
     * 后台登录码列表请求
     * @return
     */
    @RequestMapping(value="list.htm")
    public String  loginCodeList(HttpServletRequest request){
//    	 String mkt = "";
//    	 String datamkt = "";
//         Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//         if(ob.toString().contains("en"))
//         {
//      	   mkt="en";
//      	   datamkt = "en";
//         }
//         else
//         {
//      	   mkt="cn";
//      	   datamkt="cn";
//         }
         insertlog("查询登录码数据");
//         request.setAttribute( "datamkt", datamkt );
//         request.setAttribute( "mkt", mkt );
        return prefix + "logincode_list";
    }


    /**
     * 后台登录码列表数据
     * @param model    登录码实体
     * @param view     分页实体
     * @return
     */
    @RequestMapping(value="queryLoginCodeList.web")
    @ResponseBody
    public CallResult queryLoginCodeList(LoginCode model, PageView view,HttpServletRequest request ){
    	Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//        	model.setLanguage("en");
//        }
//        else
//        {
//        	model.setLanguage("ch");
//        }
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return loginCodeService.queryLoginCodeList(view, model);
    }

    /***
     * 跳转页面
     * @param loginCode 登录码实体
     * @return
     */
    @RequestMapping(value="edit.htm")
    @ResponseBody
    public ModelAndView edit(HttpServletRequest request ,LoginCode loginCode) {
//    	String mkt = "";
//   	 String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return new ModelAndView(prefix + "logincode_edit");
    }

    /**
     * 新增登录码
     * @param loginCode 登录码实体
     * @return
     */
    @RequestMapping(value="saveLoginCode.web")
    @ResponseBody
    public CallResult saveLoginCode(LoginCode loginCode,HttpServletRequest request) {
        loginCode.setCreateUserId(SessionHelper.getUserId());
        insertlog("新增登录码");

	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    loginCode.setLanguage(TranslateLanguage.getLanguageType(ob));
        return loginCodeService.saveLoginCode(loginCode);
    }


    public  void insertlog(String content)
    {
 	   LoginLogger logger = new LoginLogger();
       // String content = "查询会员数据";
        logger.setId( UUID.randomUUID().toString() );
        logger.setUserId( SessionHelper.getUserId() );
        logger.setLastLoginTime( new Date() );
       // logger.setLoginIp( getIpAddr(request) );
        logger.setContent(content);
      //  logger.setOperation_id(UUID.randomUUID().toString());
        loginLoggerService.insert( logger );
    }








}
