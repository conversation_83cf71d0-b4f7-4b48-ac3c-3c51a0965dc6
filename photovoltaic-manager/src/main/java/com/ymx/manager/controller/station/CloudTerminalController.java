package com.ymx.manager.controller.station;

import com.ymx.common.base.config.ConfigConstants;
import com.ymx.common.base.entity.Setter;
import com.ymx.common.base.entity.User;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.station.model.CloudTerminalModel;
import com.ymx.service.photovoltaic.station.service.CloudTerminalService;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.common.utils.CommonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.UUID;

/**
 * @DESC 云终端服务层
 * @DATE 2018/8/1
 * @NAME CloudTerminalController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("cloudter")
public class CloudTerminalController {


    private static final String prefix = "cloud/";

    /** 云终端服务层 */
    @Resource
    private CloudTerminalService cloudTerminalService;
    @Autowired
    private ILoginLoggerService loginLoggerService;
    /**
     * 云终端列表页
     * @return
     */
    @RequestMapping("list.htm")
    public String list(HttpServletRequest request)
    {
//    	String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        insertlog("查询采集器列表数据");
        return prefix + "cloud_list";
    }
	/***
	 * 跳转查看组件或选择组件列表页
	 * @param cloudIdFlag 标识
	 * @param id   云终端主键
	 * @return
	 */
	@RequestMapping("componentPageList.htm")
	public String componentPageList(String cloudIdFlag, String id , HttpServletRequest request){
		request.setAttribute("cloudId" , id);
		request.setAttribute("cloudIdFlag" , cloudIdFlag);
//		String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
		return prefix + "select_component_list";
	}

    /***
     * 查询云终端数据
     * @param model    云终端实体
     * @param pageView 分页实体
     * @return
     */
    @RequestMapping("queryCloudTerminalList.web")
    @ResponseBody
    public CallResult queryCloudTerminalList(CloudTerminalModel model , PageView pageView,HttpServletRequest request){
    	Object ob =   request.getSession().getAttribute("checkrole");//
        Object uid =   request.getSession().getAttribute("userKey");
        if(ob.toString().contains("运维"))
        {
        	model.setCreateUserId(uid.toString());
        }
	    model.setLanguage("ch");
	    Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//	    if(obj.toString().contains("en"))
//	    {
//		    model.setLanguage("en");
//	    }
	    model.setLanguage(TranslateLanguage.getLanguageType(obj));

    	return cloudTerminalService.queryCloudTerminalList(model , pageView);
    }


    /**
     * 页面请求 (新增,编辑,查看)
     * @param operator    操作标识
     * @param model      人员信息
     * @return
     */
    @RequestMapping(value="viewRequest.htm")
    public ModelAndView edit(HttpServletRequest request ,String operator , CloudTerminalModel model ){
//    	String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        ModelAndView mv = new ModelAndView(prefix + "cloud_edit");
        User user = SessionHelper.getSessionUser();
        mv.addObject("operator" , operator);
        model.setCreateUserId(user.getUserId());
        model.setCreateUserName(user.getUserName());
        if(CommonUtil.isEmpty(model.getId()) && StringUtils.hasLength(model.getId())){
            model = cloudTerminalService.queryCloudTerminalModelById(model.getId());
        }
        mv.addObject("info" , model);
        return mv;
    }

    /**
     * 新增或修改
     * @param   model      云终端实体
     * @return  CallResult
     */
    @RequestMapping(value="saveOrUpdate.web")
    @ResponseBody
    public CallResult saveOrUpdate(CloudTerminalModel model,HttpServletRequest request  ){
    	Object ob =   request.getSession().getAttribute("checkrole");//
        Object uid =   request.getSession().getAttribute("userKey");
        
        	model.setCreateUserId(uid.toString());
	    Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(obj));
        if (CommonUtil.isEmpty(model.getId())) {
            //修改
        	  insertlog("修改采集器数据,名称:"+model.getCloudName());
            return cloudTerminalService.updateCloudTerminalModel(model);
        } else {
           // model.setCreateUserId(SessionHelper.getUserId());
        	  insertlog("新增采集器数据,名称:"+model.getCloudName());
            return cloudTerminalService.saveCloudTerminalModel(model);
        }
    }


    /**
     * 逻辑删除
     * @param  model  云终端实体
     * @return CallResult
     */
    @RequestMapping(value="updateDeleteClouds.web")
    @ResponseBody
    public CallResult updateDeleteClouds(HttpServletRequest request,CloudTerminalModel model) {
    	  insertlog("逻辑删除采集器数据,ID:"+model.getId());
	    Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(obj));
        return cloudTerminalService.updateDeleteClouds(model);
    }


    @RequestMapping("cloudImport.htm")
    @ResponseBody
    public ModelAndView cloudImport(HttpServletRequest request,String id)  {
        String action = "import";
        Setter setter = new Setter();
        request.setAttribute( "bean", setter );
        request.setAttribute("action", action);
        insertlog("导入采集器数据");
        return new ModelAndView("cloud/cloud_import");
    }
    @ResponseBody  
    @RequestMapping(value="cloudUpload.htm",method={RequestMethod.GET,RequestMethod.POST})  
    public String componentUpload(HttpServletRequest request,HttpServletResponse response) throws Exception {  
       
	   return cloudTerminalService.ajaxUploadExcel(request, response);
    }  

    public  void insertlog(String content)
    {
 	   LoginLogger logger = new LoginLogger();
       // String content = "查询会员数据";
        logger.setId( UUID.randomUUID().toString() );
        logger.setUserId( SessionHelper.getUserId() );
        logger.setLastLoginTime( new Date() );
       // logger.setLoginIp( getIpAddr(request) );
        logger.setContent(content);
        //logger.setOperation_id(UUID.randomUUID().toString());
        loginLoggerService.insert( logger );
    }
   
    @ResponseBody  
    @RequestMapping(value="clouddownfile.htm",method={RequestMethod.GET,RequestMethod.POST})  
    public void downloadLocal(HttpServletResponse response) throws FileNotFoundException {
        // 下载本地文件
    	 // 获取文件路径
        String REMOTE_DOWNFILE_URL = ConfigConstants.getConfig("REMOTE_DOWNFILE_URL");
        String fileName = "cloudter.xls".toString(); // 文件的默认保存名
        // 读到流中
        InputStream inStream = new FileInputStream(REMOTE_DOWNFILE_URL+fileName);// 文件的存放路径
        // 设置输出的格式
        response.reset();
        response.setContentType("bin");
        response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        // 循环取出流中的数据
        byte[] b = new byte[100];
        int len;
        try {
            while ((len = inStream.read(b)) > 0)
                response.getOutputStream().write(b, 0, len);
            inStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @RequestMapping("relayPageList.htm")
    public String relayPageList(String cloudId,String operationFlag,String powerStationId,HttpServletRequest request)
    {
        request.setAttribute("cloudId" , cloudId);
        request.setAttribute("operationFlag" , operationFlag);
        request.setAttribute("powerStationId" , powerStationId);
        return prefix + "select_relay_list";
    }


}
