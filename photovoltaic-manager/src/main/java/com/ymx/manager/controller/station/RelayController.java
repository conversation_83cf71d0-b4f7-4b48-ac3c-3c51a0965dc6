package com.ymx.manager.controller.station;

import com.ymx.common.base.entity.User;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.photovoltaic.station.model.RelayModel;
import com.ymx.service.photovoltaic.station.service.RelayService;
import com.ymx.manager.security.common.SessionHelper;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Controller
@RequestMapping("relay")
public class RelayController {

    private static final String prefix = "relay/";

    @Resource
    private RelayService relayService;

    // 中继列表页
    @RequestMapping("list.htm")
    public String list(HttpServletRequest request)
    {
        return prefix + "relay_list";
    }

    // 中继列表数据接口
    @RequestMapping("queryRelayList.web")
    @ResponseBody
    public CallResult queryRelayList(RelayModel model,PageView pageView, HttpServletRequest request){

        Object checkRole =   request.getSession().getAttribute("checkrole");//
        Object uid =   request.getSession().getAttribute("userKey");
        if(checkRole.toString().contains("运维"))
        {
            model.setCreateUserId(uid.toString());
        }
        return relayService.queryRelayList(model , pageView);
    }


    // 中继列表数据接口
    @RequestMapping("queryRelayListForCloud.web")
    @ResponseBody
    public CallResult queryRelayListForCloud(String operationFlag,String cloudId,PageView pageView, HttpServletRequest request){

        Object uid =   request.getSession().getAttribute("userKey");
        Map<String, Object> map = new HashMap<>();
        map.put("createUserId",uid.toString());
        if(operationFlag.equals("viewRelay"))
        {
            map.put("cloudId",cloudId);
        }
        return relayService.queryRelayListForCloud(map,pageView);
    }


    // 组件数据接口
    @RequestMapping("queryComponentList.web")
    @ResponseBody
    public CallResult queryComponentList(String relayId, String chipId,String groupName,
                                         String operationFlag, PageView pageView,HttpServletRequest request){
        request.setAttribute("relayId" , relayId);

        Object uid =   request.getSession().getAttribute("userKey");
        Map<String, Object> map = new HashMap<>();
        map.put("createUserId",uid.toString());
        if(operationFlag.equals("view"))
        {
            map.put("relayId",relayId);
        }
        if(chipId!=null&&!chipId.equals(""))
        {
            map.put("chipId",chipId.trim());
        }
        if(groupName!=null&&!groupName.equals(""))
        {
            map.put("groupName",groupName.trim());
        }

        return relayService.queryComponentList(map , pageView);
    }

    @RequestMapping("addAndViewByComponent.htm")
    public String addByComponentHtm(String relayId , String operationFlag,
                                             PageView pageView,HttpServletRequest request){
        request.setAttribute("relayId" , relayId);
        request.setAttribute("operationFlag" , operationFlag);
       return prefix + "addAndViewByComponent";
    }

    // 组串数据接口
    @RequestMapping("queryComponentGroupList.web")
    @ResponseBody
    public CallResult queryComponentGroupList(String relayId ,String groupName,String operationFlag, PageView pageView,HttpServletRequest request){
        request.setAttribute("relayId" , relayId);

        Object uid =   request.getSession().getAttribute("userKey");
        Map<String, Object> map = new HashMap<>();
        map.put("createUserId",uid.toString());
        if(operationFlag.equals("view"))
        {
            map.put("relayId",relayId);
        }
        if(groupName!=null&&!groupName.equals(""))
        {
            map.put("groupName",groupName.trim());
        }

        return relayService.queryComponentGroupList(map , pageView);
    }


   // 新增 查看或修改中继数据页
    @RequestMapping(value="viewRequest.htm")
    public ModelAndView edit(HttpServletRequest request ,String operator , RelayModel model ){

        ModelAndView mv = new ModelAndView(prefix + "relay_edit");
        User user = SessionHelper.getSessionUser();
        mv.addObject("operator" , operator);
        model.setCreateUserId(user.getUserId());
        model.setCreateUserName(user.getUserName());
        if(model.getId()!=null && model.getId()!=0){
            model = relayService.queryRelayById(model.getId());
        }
        mv.addObject("info" , model);
        return mv;
    }

    // 新增或修改中继数据接口
    @RequestMapping(value="saveOrUpdate.web")
    @ResponseBody
    public CallResult saveOrUpdate(RelayModel model,HttpServletRequest request  ){
        Object uid =   request.getSession().getAttribute("userKey");
        
        	model.setCreateUserId(uid.toString());

        if (model.getId()!=null&&model.getId()!=0) {
            return relayService.updateRelayModel(model);
        } else {
            return relayService.saveRelayModel(model);
        }
    }


    // 删除中继接口
    @RequestMapping(value="deleteRelay.web")
    @ResponseBody
    public CallResult deleteRelay(String id,HttpServletRequest request) {
        return relayService.deleteRelayById(id);
    }


    // 选择或移除组串
    @RequestMapping(value="changeComponentGroup.web")
    @ResponseBody
    public CallResult changeComponentGroup(String id, String relayId ,HttpServletRequest request ) {
        Object uid =   request.getSession().getAttribute("userKey");
        Map<String, Object> map = new HashMap<>();
        map.put("createUserId",uid.toString());
        map.put("relayId",relayId);
        List<String> list = new ArrayList<>();
        Collections.addAll(list, id.split(",", -1));
        map.put("list",list);

        return relayService.changeComponentGroup(map);
    }


    // 选择或移除组件
    @RequestMapping(value="changeComponent.web")
    @ResponseBody
    public CallResult changeComponent(String id, String relayId ,HttpServletRequest request ) {
        Object uid =   request.getSession().getAttribute("userKey");
        Map<String, Object> map = new HashMap<>();
        map.put("createUserId",uid.toString());
        map.put("relayId",relayId);
        List<String> list = new ArrayList<>();
        Collections.addAll(list, id.split(",", -1));
        map.put("list",list);

        return relayService.changeComponent(map);
    }


    // 采集器选择或移除中继器
    @RequestMapping(value="selectOrMoveCloud.web")
    @ResponseBody
    public CallResult selectOrMoveCloud(String id, String cloudId ,String powerStationId, HttpServletRequest request ) {

        Map<String, Object> map = new HashMap<>();
        map.put("cloudId",cloudId);
        map.put("powerStationId",powerStationId);
        List<String> list = new ArrayList<>();
        Collections.addAll(list, id.split(",", -1));
        map.put("list",list);

        return relayService.updateCloudIdForRelay(map);
    }

}
