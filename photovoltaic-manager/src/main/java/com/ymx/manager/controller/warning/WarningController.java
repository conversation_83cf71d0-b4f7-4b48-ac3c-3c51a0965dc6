package com.ymx.manager.controller.warning;

import com.sun.prism.impl.BaseContext;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.common.base.entity.User;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.manager.security.service.IUserService;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.station.model.PowerStationModel;
import com.ymx.service.photovoltaic.station.service.PowerStationService;
import com.ymx.service.photovoltaic.warning.entity.WarningCountVo;
import com.ymx.service.photovoltaic.warning.entity.WarningModel;
import com.ymx.service.photovoltaic.warning.entity.WarningTypeStatsModel;
import com.ymx.service.photovoltaic.warning.service.WarningService;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.DateUtils;

import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.xmlbeans.impl.regex.ParseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import java.beans.IntrospectionException;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @DESC 警报控制层
 * @DATE 2018/8/11
 * @NAME WarningController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("warning")
public class WarningController  {

    /** 页面前路径 */
    private static final String prefix = "warning/";

    /** 警报服务层 */
    @Resource
    private WarningService warningService;
    @Autowired
    private ILoginLoggerService loginLoggerService;
    //@Autowired
    //private SessionHelper sessionHelper;
    @Autowired
    private PowerStationService powerStationService;
    @Autowired
    private IUserService userService;
    /**
     * 警告列表页
     * @return
     */
    @RequestMapping("list.htm")
    public String list(HttpServletRequest request)
    {
//    	String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        insertlog("查询警报数据");
        return prefix + "warning_list";
    }


    /***
     * 查询警告数据
     * @param model    警告实体
     * @param pageView 分页实体
     * @return
     */
    @RequestMapping("queryWarningList.web")
    @ResponseBody
    public CallResult queryWarningList(WarningModel model , PageView pageView,HttpServletRequest request){
	    Object ob =   request.getSession().getAttribute("checkrole");//
	    Object uid =   request.getSession().getAttribute("userKey");
	    if(ob.toString().contains("运维"))
	    {
		    model.setCreateUserId(uid.toString());
	    }
	    Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//	    if(obj.toString().contains("en"))
//	    {
//		    model.setLanguage("en");
//	    }
	    model.setLanguage(TranslateLanguage.getLanguageType(obj));
	    Date date= DateUtils.changeDays(new Date(),-7);//只查询7日数据
	    model.setCreateTimeBegin(DateUtils.timestampToDate(date.getTime()));
        // 检查model对象的createTimeBegin和createTimeEnd属性是否都为null,如果都是null则查询当前往前7天数据
        /*if (model.getCreateTimeBegin() == null && model.getCreateTimeEnd() == null) {
            Date date= DateUtils.changeDays(new Date(),-7);//只查询7日数据
            model.setCreateTimeBegin(DateUtils.timestampToDate(date.getTime()));
        }*/
        return warningService.queryWarningList(model , pageView);
    }



    /**
     * 页面请求 (新增,编辑,查看)
     * @param operator    操作标识
     * @param model      警告信息
     * @return
     */
    @RequestMapping(value="viewRequest.htm")
    public ModelAndView edit(HttpServletRequest request ,String operator , WarningModel model ){
//    	String mkt = "";
//   	 String datamkt = "";
        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
        String language="cn";
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
     	   language="en";
//        }
	    language=TranslateLanguage.getLanguageType(ob);
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        ModelAndView mv = new ModelAndView(prefix + "warning_edit");
        User user = SessionHelper.getSessionUser();
        mv.addObject("operator" , operator);
        model.setCreateUserId(user.getUserId());
        model.setCreateUserName(user.getUserName());
        if(CommonUtil.isEmpty(model.getId()) && StringUtils.hasLength(model.getId())){
            model = warningService.queryWarningById(model.getId(),language);
        }
        mv.addObject("info" , model);
        return mv;
    }

    /**
     * 新增或修改
     * @param   model      警告实体
     * @return  CallResult
     */
    @RequestMapping(value="saveOrUpdate.web")
    @ResponseBody
    public CallResult saveOrUpdate( WarningModel model ,HttpServletRequest request ){
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        if (CommonUtil.isEmpty(model.getId())) {
            //修改
        	 insertlog("修改警报数据");
            return warningService.updateWarningModel(model);
        } else {
            model.setCreateUserId(SessionHelper.getUserId());
            insertlog("新增警报数据");
            return warningService.saveWarningModel(model);
        }
    }

    /**
     * 删除
     * @param  model  警告实体
     * @return CallResult
     */
    @RequestMapping(value="deleteWarnings.web")
    @ResponseBody
    public CallResult deleteWarnings(WarningModel model,HttpServletRequest request) {
    	 insertlog("删除警报数据");
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return warningService.deleteWarnings(model);
    }

    /**
     * 逻辑删除
     * @param  model  警告实体
     * @return CallResult
     */
    @RequestMapping(value="updateDeleteWarnings.web")
    @ResponseBody
    public CallResult updateDeleteWarnings(WarningModel model,HttpServletRequest request) {
    	 insertlog("逻辑删除警报数据");
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return warningService.updateDeleteWarnings(model);
    }


    /**
     * 批量处理操作
     * @param model 警告实体
     * @return
     */
    @RequestMapping(value = "/batchUpdateWarning.web")
    @ResponseBody
    public CallResult batchUpdateWarning(WarningModel model,HttpServletRequest request) {
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        Map<String ,Object> map = new HashMap<>();
        CallResult callResult = warningService.batchUpdateWarning(map , model);
        return callResult;
    }
    /**
     * 导出
     * @param model 警告导出
     * @return
     */
    @RequestMapping("/warningExport.web")
    @ResponseBody
    public void warningExport(WarningModel model,HttpServletRequest request, HttpServletResponse response) throws ClassNotFoundException, IntrospectionException, IllegalAccessException, ParseException, InvocationTargetException
    {
    	 insertlog("导出警报数据");
    	 response.reset(); //清除buffer缓存
         Map<String,Object> map=new HashMap<String,Object>();
         // 指定下载的文件名，浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
         // 所以我们用GBK解码，ISO-8859-1来编码，在浏览器那边会反过来执行。
      // 指定下载的文件名  
	        response.setContentType("application/vnd.ms-excel;charset=UTF-8");  
	        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
	        String timestring = formatter.format(new Date());
	        String filename = "警告数据"+timestring+".xlsx";
	        try {
				response.setHeader("Content-Disposition","attachment;filename="+new String(filename.getBytes(),"iso-8859-1"));
			} catch (UnsupportedEncodingException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
	        //导出Excel对象 

         response.setHeader("Pragma", "no-cache");
         response.setHeader("Cache-Control", "no-cache");
         response.setDateHeader("Expires", 0);
         XSSFWorkbook workbook=null;
         //导出Excel对象
         workbook = warningService.exportExcelInfo();
         OutputStream output;
         try {
             output = response.getOutputStream();
             BufferedOutputStream bufferedOutPut = new BufferedOutputStream(output);
             bufferedOutPut.flush();
             workbook.write(bufferedOutPut);
             bufferedOutPut.close();
             output.flush();
             output.close();
            
         } catch (IOException e) {
             e.printStackTrace();
         }

       // CallResult callResult = warningService.batchUpdateWarning(map , model);
       // return "String";
    }
   
    public  void insertlog(String content)
    {
 	   LoginLogger logger = new LoginLogger();
       // String content = "查询会员数据";
        logger.setId( UUID.randomUUID().toString() );
        logger.setUserId( SessionHelper.getUserId() );
        logger.setLastLoginTime( new Date() );
       // logger.setLoginIp( getIpAddr(request) );
        logger.setContent(content);
      //  logger.setOperation_id(UUID.randomUUID().toString());
        loginLoggerService.insert( logger );
    }


    /***
     * 警告统计，根据创建用户id
     * @param
     * @return
     */
    @GetMapping("queryWarningTypeCount.web")
    @ResponseBody
    public CallResult queryWarningTyCount(HttpServletRequest request) {
        CallResult callResult = CallResult.newInstance();
        HttpSession session = request.getSession();
        String userId = (String) session.getAttribute("userId");
        String password = (String) session.getAttribute("password");
        String createUserId = userService.queryUserIdBylocal(userId,password);


        if (createUserId == null || createUserId.equals("")) {
            callResult.setErrMsg("用户信息错误");
            return callResult;
        }
        List<String> powerStationIds = powerStationService.queryPowerStationListByCreateUserId(createUserId);
        callResult = warningService.queryWarningTypeCountTo(powerStationIds,createUserId);
        return callResult;
    }


    //查询告警列表（默认只差七天，校验date是否传参）
    @RequestMapping("queryWarningListTo.web")
    @ResponseBody
    public CallResult queryWarningListToWeb(WarningModel model , PageView pageView,HttpServletRequest request){
        Object ob =   request.getSession().getAttribute("checkrole");//
        Object uid =   request.getSession().getAttribute("userKey");
        if(ob.toString().contains("运维"))
        {
            model.setCreateUserId(uid.toString());
        }
        Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
        model.setLanguage(TranslateLanguage.getLanguageType(obj));

        String createUserId = request.getSession().getAttribute("userKey").toString();
        // 检查model对象的createTimeBegin和createTimeEnd属性是否都为null,如果都是null则查询当前往前7天数据
        if (model.getCreateTimeBegin() == null && model.getCreateTimeEnd() == null) {
            Date date= DateUtils.changeDays(new Date(),-7);//只查询7日数据
            model.setCreateTimeBegin(DateUtils.timestampToDate(date.getTime()));
        }

        CallResult callResult = warningService.queryWarningListTo(model ,pageView, createUserId);
        return callResult;
    }

}
