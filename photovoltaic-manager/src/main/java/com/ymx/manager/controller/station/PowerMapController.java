package com.ymx.manager.controller.station;

import com.ymx.common.base.entity.User;
import com.ymx.common.common.result.CallResult;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.region.service.RegionLocationService;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.manager.security.service.IUserService;
import com.ymx.manager.security.utils.JsonUtil;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.DateUtils;
import com.ymx.service.photovoltaic.station.model.*;
import com.ymx.service.photovoltaic.station.service.*;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;


/**
 * @DESC 系统(电站)控制层
 * @DATE 2018/8/3
 * @NAME PowerStationController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("powerMapView")
public class PowerMapController {


    /** 系统设备服务层 */
    @Resource
    private PowerMapService powerMapService;
    /** 系统设备服务层 */
    @Resource
    private PowerStationService powerStationService;
    @Resource
    private RegionLocationService regionLocationService;
	@Autowired
	private ILoginLoggerService loginLoggerService;
    private static final String prefix = "powerMap/";
    @Autowired
    private IUserService userService;
	@Autowired
	private ComponentDayService componentDayService;
	@Autowired
	private ComponentCollectBatchNoService componentCollectBatchNoService;
	@Resource
	private InverterCollectService inverterCollectService;
	@Resource
	private ComponentCollectService componentCollectService;
	@Resource
	private ConfigureService configureService;
    /**
     * 也可以废掉， 用  LoginController  index/portal.htm 与之相等
     * 系统地图页
     * @return
     */
    @RequestMapping("list.htm")
   //  @ResponseBody
    public String list(HttpServletRequest request){
    	// 查询国家 
//       List<PowerMapModel> powerMaplt = powerMapService.queryGuojiaListByPowId();
//       request.setAttribute( "powerMaplt", powerMaplt );
//       String mkt = "";
//  	   String datamkt = "";
//  	   String mapmkt = "";
//       Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//       if(ob.toString().contains("en"))
//       {
//    	   mkt="en";
//    	   datamkt = "en";
//    	   mapmkt = "en-us";
//       }
//       else
//       {
//    	   mkt="cn";
//    	   datamkt="cn";
//    	   mapmkt = "zh-cn";
//       }
//      /* if(ob.toString().contains("en"))
//       {
//    	   mkt = "en-us";
//       }
//       else
//       {
//    	   mkt = "zh-cn";
//       }*/
//       request.setAttribute( "datamkt", datamkt );
//       request.setAttribute( "mkt", mkt );
//       request.setAttribute( "mapmkt", mapmkt);
       insertlog("查询电站地图信息");
       return prefix + "power_map";
    }
    public static void main(String[] args) {
	    BigDecimal bd7 = new BigDecimal("1.301");
	    BigDecimal bd8 = new BigDecimal("100");
	    System.out.println("divide:" + bd7.divide(bd8));
	    System.out.println("divide:" + bd7.divide(bd8,3,BigDecimal.ROUND_HALF_UP));
	    System.out.println("divide:" + bd7.divide(bd8,9,BigDecimal.ROUND_HALF_UP));
    }

	@RequestMapping("getPowerStationInfo.web")
	@ResponseBody
	public CallResult getPowerStationInfo(HttpServletRequest request, HttpServletResponse response,String powerStationId){
		CallResult callResult = CallResult.newInstance();
		Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		if (!CommonUtil.isEmpty(powerStationId)) {
			String language=TranslateLanguage.getLanguageType(obj);
			TranslateLanguage.getCallResult(callResult,language, ErrCodeExt.PARAM_LOSS_EXCEPTION);
			return callResult;
		}
		PowerStationModel powerStation=powerStationService.queryPowerStationObjectById(powerStationId);
		BigDecimal kwh=new BigDecimal(0);
		String date= DateUtils.timestampToDate(new Date().getTime(),"yyyy-MM-dd");
		BigDecimal day=componentCollectBatchNoService.queryComponentCollectBatchNoCount(powerStationId,date,null);//当天发电量
		if(day!=null){
			kwh=kwh.add(day.divide(new BigDecimal(1000)));
		}
		Map<String, Object> map=new HashMap<>();
		map.put("powerStationId",powerStationId);
		List<ComponentDayModel> yearModelList= componentDayService.queryComponentByYear(map);//之前发电量
		if(yearModelList!=null){
			for (ComponentDayModel componentDayModel:yearModelList){
				if(componentDayModel.getKwh()!=null){
					kwh=kwh.add(componentDayModel.getKwh());
				}
			}
		}
		//		1KWh=0.32kg煤  =0.997kg二氧化碳 =0.03kg二氧化硫 =0.015kg氮氧化物 =0.272kg碳粉尘。
		BigDecimal coal =new BigDecimal(kwh.doubleValue()*0.32);//煤
		BigDecimal dioxide =new BigDecimal(kwh.doubleValue()*0.997);
		BigDecimal sulfur  =new BigDecimal(kwh.doubleValue()*0.03);
		BigDecimal nitrogen  =new BigDecimal(kwh.doubleValue()*0.015);
		BigDecimal dust =new BigDecimal(kwh.doubleValue()*0.272);

		map.put("kwh",String.valueOf(kwh.setScale(2 , BigDecimal.ROUND_HALF_UP)));//发电量

		map.put("coal",String.valueOf(coal.setScale(2 , BigDecimal.ROUND_HALF_UP)));//煤
		map.put("dioxide",String.valueOf(dioxide.setScale(2 , BigDecimal.ROUND_HALF_UP)));//二氧化碳
		map.put("sulfur",String.valueOf(sulfur.setScale(2 , BigDecimal.ROUND_HALF_UP)));//二氧化硫
		map.put("nitrogen",String.valueOf(nitrogen.setScale(2 , BigDecimal.ROUND_HALF_UP)));//氮氧化物
		map.put("dust",String.valueOf(dust.setScale(2 , BigDecimal.ROUND_HALF_UP)));//碳粉尘

		map.put("powerStation",powerStation);
//		String inverterId="";
//		List<InverterCollect>  inverterCollectList=inverterCollectService.queryInverterCollectByLast(inverterId);
		List<ComponentModel> componentList =configureService.queryComponentListByPowerStationId(powerStationId);
		if(componentList!=null) {
			int count = componentList.size();
			List<ComponentCollect> componentCollectList=componentCollectService.queryComponentCollectByLast(powerStationId,count,false);
			BigDecimal power =new BigDecimal(0);
			if(componentCollectList!=null){
				for(ComponentCollect collect:componentCollectList){
					power=power.add(new BigDecimal(collect.getOutputCurrent()/1000.0*collect.getOutputVoltage()/1000.0));
				}
			}
			map.put("power",power.setScale(2 , BigDecimal.ROUND_HALF_UP));
		}
		callResult.setReModel(map);
		return callResult;
	}

    @RequestMapping("qulist.htm")
    @ResponseBody
    public void qulist(HttpServletRequest request,HttpServletResponse response){
    	System.out.println("");
    	 List<PowerMapModel> powerMaplt = powerMapService.queryGuojiaListByPowId();
        
    	   JSONObject obj = new JSONObject();
           obj.put("list", powerMaplt);
           String a = obj.toString();
           System.out.println(a);
           JsonUtil.strOut(response,a);
   // return a;	
    }
    @RequestMapping("guojialist.htm")
    @ResponseBody
    public void guojialist(HttpServletRequest request,HttpServletResponse response){
    	System.out.println("");
    	String pid = request.getParameter("pid");
    	   List<PowerMapModel> shenglt = powerMapService.queryShengListByPId(pid);
    	   JSONObject obj = new JSONObject();
           obj.put("list", shenglt);
           String a = obj.toString();
           System.out.println(a);
           JsonUtil.strOut(response,a);
   // return a;	
    }
    
    @RequestMapping("shenglist.htm")
    @ResponseBody
    public void shenglist(HttpServletRequest request,HttpServletResponse response){
    	System.out.println("");
    	String pid = request.getParameter("pid");
    	   List<PowerMapModel> shenglt = powerMapService.queryShengListByPId(pid);
    	   JSONObject obj = new JSONObject();
           obj.put("list", shenglt);
           String a = obj.toString();
           System.out.println(a);
           JsonUtil.strOut(response,a);
   // return a;	
    }
    @RequestMapping("shilist.htm")
    @ResponseBody
    public void shilist(HttpServletRequest request,HttpServletResponse response){
    	System.out.println("");
    	   String pid = request.getParameter("pid");
    	   List<PowerMapModel> shenglt = powerMapService.queryShengListByPId(pid);
    	   JSONObject obj = new JSONObject();
           obj.put("list", shenglt);
           String a = obj.toString();
           System.out.println(a);
           JsonUtil.strOut(response,a);
   // return a;	
    }
    @RequestMapping("findlist.htm")
    @ResponseBody
    public void findlist(HttpServletRequest request,HttpServletResponse response)
    {
    	String systemName=request.getParameter("systemName");
    	String systenNo=request.getParameter("bh");
    	String quyu=request.getParameter("quyu");
    	String guojia=request.getParameter("guojiaId");
    	String sheng=request.getParameter("shengId");
    	String shi=request.getParameter("shiId");
    	String dizhi=request.getParameter("dizhi");
    	String memberid=request.getParameter("name");
    	String grade = request.getParameter("grade");
    	String name = "";
    	if(CommonUtil.isEmpty(memberid)==true && memberid.length()<20)
    	{
    		User user = userService.query(memberid);
    		memberid=user.getId();
    	}
    	List<PowerStationModel>  lt =	powerStationService.queryPowerStationMapById(systemName, systenNo,guojia,sheng,shi,dizhi,name,request,memberid,grade);
    	  insertlog("查询电站位置");
    	/*StringBuilder movejs= new StringBuilder();
    	
    	 movejs.append(UUID.randomUUID());
    	 request.setAttribute("jsjg", movejs.toString());*/
        JSONObject obj = new JSONObject();
        obj.put("powerStationlist", lt);
        String a = obj.toString();
//        System.out.println(a);
        JsonUtil.strOut(response,a);
    	
    }
   
    public  void insertlog(String content)
    {
 	   LoginLogger logger = new LoginLogger();
       // String content = "查询会员数据";
        logger.setId( UUID.randomUUID().toString() );
        logger.setUserId( SessionHelper.getUserId() );
        logger.setLastLoginTime( new Date() );
       // logger.setLoginIp( getIpAddr(request) );
        logger.setContent(content);
     //   logger.setOperation_id(UUID.randomUUID().toString());
        loginLoggerService.insert( logger );
    }

}
