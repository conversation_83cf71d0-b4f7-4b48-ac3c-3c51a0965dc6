package com.ymx.manager.controller.station;

import com.ymx.common.base.config.ConfigConstants;
import com.ymx.common.base.entity.Setter;
import com.ymx.common.base.entity.User;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.station.model.CloudTerminalModel;
import com.ymx.service.photovoltaic.station.model.InverterModel;
import com.ymx.service.photovoltaic.station.service.CloudTerminalService;
import com.ymx.service.photovoltaic.station.service.InverterService;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.common.utils.CommonUtil;

import net.sf.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @DESC 逆变器控制层
 * @DATE 2018/8/1
 * @NAME InverterController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("inverter")
public class InverterController {

    /** 逆变器服务层 */
    @Resource
    private InverterService inverterService;
    @Autowired
    private ILoginLoggerService loginLoggerService;
	@Resource
	private CloudTerminalService cloudTerminalService;

    private static final String prefix = "inverter/";

    /**
     * 逆变器列表页
     * @return
     */
    @RequestMapping("list.htm")
    public String list(HttpServletRequest request)
    {
//    	String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        insertlog("查询逆变器数据");
        return prefix + "inverter_list";
    }

    /***
     * 查询逆变器数据
     * @param model    逆变器
     * @param pageView 分页实体
     * @return
     */
    @RequestMapping("queryInverterList.web")
    @ResponseBody
    public CallResult queryInverterList(HttpServletRequest request,InverterModel model , PageView pageView){
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//	    if(ob.toString().contains("en"))
//	    {
//		    model.setLanguage("en");
//	    }
//	    else
//	    {
//		    model.setLanguage("ch");
//	    }
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
	    Object checkrole =   request.getSession().getAttribute("checkrole");//
        Object uid =   request.getSession().getAttribute("userKey");
        if(checkrole.toString().contains("运维"))
        {
        	model.setCreateUserId(uid.toString());
        }
        return inverterService.queryInverterList(model , pageView);
    }

    /**
     * 页面请求 (新增,编辑,查看)
     * @param operator    操作标识
     * @param model      人员信息
     * @return
     */
    @RequestMapping(value="viewRequest.htm")
    public ModelAndView edit(HttpServletRequest request ,String operator , InverterModel model ){
//    	String mkt = "";
//   	    String datamkt = "";
        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
////     	   mkt="en";
////     	   datamkt = "en";
//	        model.setLanguage("en");
//        }
//        else
//        {
////     	   mkt="cn";
////     	   datamkt="cn";
//	        model.setLanguage("ch");
//        }
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        ModelAndView mv = new ModelAndView(prefix + "inverter_edit");
        User user = SessionHelper.getSessionUser();
        mv.addObject("operator" , operator);
        model.setCreateUserId(user.getUserId());
        model.setCreateUserName(user.getUserName());
        if(CommonUtil.isEmpty(model.getId()) && StringUtils.hasLength(model.getId())){
            model = inverterService.queryInverterById(model.getId(),model.getLanguage());
        }
        mv.addObject("info" , model);
        return mv;
    }


    /**
     * 新增或修改
     * @param   model      逆变器
     * @return  CallResult
     */
    @RequestMapping(value="saveOrUpdate.web")
    @ResponseBody
    public CallResult saveOrUpdate( InverterModel model ,HttpServletRequest request ){
    	Object ob =   request.getSession().getAttribute("checkrole");//
        Object uid =   request.getSession().getAttribute("userKey");
       
        	model.setCreateUserId(uid.toString());

	    Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(obj));
        if (CommonUtil.isEmpty(model.getId())) {
            //修改
        	  insertlog("修改逆变器数据,名称:"+model.getInverterName());
            return inverterService.updateInverterModel(model);
        } else {
        	  insertlog("新增逆变器数据,名称:"+model.getInverterName());
          //  model.setCreateUserId(SessionHelper.getUserId());
            return inverterService.saveInverterModel(model);
        }
    }


    /**
     * 删除
     * @param  model  逆变器
     * @return CallResult
     */
    @RequestMapping(value="deleteInverterModels.web")
    @ResponseBody
    public CallResult deleteInverterModels(InverterModel model,HttpServletRequest request ) {
    	  insertlog("删除逆变器数据,id:"+model.getId());

	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return inverterService.deleteInverterModels(model);
    }


    /**
     * 逻辑删除
     * @param  model  逆变器
     * @return CallResult
     */
    @RequestMapping(value="updateDeleteInverterModels.web")
    @ResponseBody
    public CallResult updateDeleteInverterModels(InverterModel model,HttpServletRequest request) {
    	 insertlog("逻辑删除逆变器数据,id:"+model.getId());
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return inverterService.updateDeleteInverterModels(model);
    }

    /**
     * 选择逆变器
     * @param model 逆变器
     * @return
     */
    @RequestMapping(value="selectInverterData.web")
    @ResponseBody
    public CallResult selectInverterData(InverterModel model ,HttpServletRequest request){
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return inverterService.selectInverterData(model);
    }


    /**
     * 选择组串/移除组串页面
     * @param id    逆变器
     * @param request  请求对象
     * @return
     */
    @RequestMapping(value="componentGroupPageList.htm")
    public String componentGroupPageList(String inverterFlag , String id, HttpServletRequest request,String cloudIdFlag ) {
        request.setAttribute("inverterFlag" , inverterFlag);
        request.setAttribute("inverterId" , id );
        insertlog("逆变器选择组串/移除组串数据,ID:"+id);
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return  prefix + "select_componentgroup_list";
    }

    @RequestMapping("inverterImport.htm")
    @ResponseBody
    public ModelAndView inverterImport(HttpServletRequest request,String id)  {
        String action = "import";
        Setter setter = new Setter();
        request.setAttribute( "bean", setter );
        request.setAttribute("action", action);
        insertlog("导入逆变器数据");
        return new ModelAndView("inverter/inverter_import");
    }

    @ResponseBody  
    @RequestMapping(value="inverterUpload.htm",method={RequestMethod.GET,RequestMethod.POST})  
    public String componentUpload(HttpServletRequest request,HttpServletResponse response) throws Exception {  
       
	   return inverterService.ajaxUploadExcel(request, response);
    }
    /**
     * 调试完成
     * @param  model  系统设备
     * @return
     *  var num = $("#inverterNo").val();
			   var model = $("#model").val();
			   var serialNo = $("#serialNo").val();
     */
    @Deprecated
    @RequestMapping(value="validateinverter.web")
    @ResponseBody
    public CallResult validateinverter(String id,String num,String model,String serialNo,HttpServletRequest request,HttpServletResponse response){
        Map<String ,Object> map = new HashMap<>();
        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
        String message =  inverterService.validateinverter(id,num,model,serialNo,TranslateLanguage.getLanguageType(ob));
        JSONObject json = new JSONObject();
      //将返回信息保存在JSON对象中
        json.put("message",message);
       
        //设置响应编码格式，防止乱码
        response.setContentType("text/html;charset=UTF-8");
        //将数据以json格式响应给ajax
        try {
			response.getWriter().write(json.toString());
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        return null;
    }
    public  void insertlog(String content)
    {
 	   LoginLogger logger = new LoginLogger();
       // String content = "查询会员数据";
        logger.setId( UUID.randomUUID().toString() );
        logger.setUserId( SessionHelper.getUserId() );
        logger.setLastLoginTime( new Date() );
       // logger.setLoginIp( getIpAddr(request) );
        logger.setContent(content);
     //   logger.setOperation_id(UUID.randomUUID().toString());
        loginLoggerService.insert( logger );
    }
    @ResponseBody  
    @RequestMapping(value="inverterdownfile.htm",method={RequestMethod.GET,RequestMethod.POST})  
    public void downloadLocal(HttpServletResponse response) throws FileNotFoundException {
        // 下载本地文件
    	 // 获取文件路径
        String REMOTE_DOWNFILE_URL = ConfigConstants.getConfig("REMOTE_DOWNFILE_URL");
        String fileName = "inverter.xls".toString(); // 文件的默认保存名
        // 读到流中
        InputStream inStream = new FileInputStream(REMOTE_DOWNFILE_URL+fileName);// 文件的存放路径
        // 设置输出的格式
        response.reset();
        response.setContentType("bin");
        response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        // 循环取出流中的数据
        byte[] b = new byte[100];
        int len;
        try {
            while ((len = inStream.read(b)) > 0)
                response.getOutputStream().write(b, 0, len);
            inStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

	@RequestMapping("selectCloudList.htm")
	public ModelAndView selectCloudList(HttpServletRequest request,String id,String selectInverter)  {
		request.setAttribute( "id", id );
		if(selectInverter==null){
			selectInverter="";
			request.setAttribute( "inverterId", id );
		}
		request.setAttribute( "selectInverter", selectInverter );
		return new ModelAndView("inverter/select_cloud_list");
	}
	/**
	 * 关联，取消关联 逆变器  和 采集器
	 * @param  model  逆变器
	 * @return CallResult
	 */
	@RequestMapping(value="selectOrMoveCloudTerminal.web")
	@ResponseBody
	public CallResult selectOrMoveCloudTerminal(CloudTerminalModel model,HttpServletRequest request) {
		insertlog("关联，取消关联 逆变器 inverterId:"+model.getInverterId()+" 和 采集器,id:"+model.getId());

		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(ob));
		return cloudTerminalService.updateCloudTerminalByInverterId(model);
	}
}
