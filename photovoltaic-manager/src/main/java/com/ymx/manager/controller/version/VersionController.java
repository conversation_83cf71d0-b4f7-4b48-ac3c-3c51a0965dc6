package com.ymx.manager.controller.version;

import com.ymx.manager.security.common.SessionHelper;
import com.ymx.common.base.config.ConfigConstants;
import com.ymx.common.base.entity.User;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.photovoltaic.station.model.VersionModel;
import com.ymx.service.photovoltaic.station.service.VersionService;
import com.ymx.common.utils.ByteUtil;
import com.ymx.common.utils.FileUtil;
import com.ymx.common.utils.IDUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import org.w3c.dom.Document;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

@Controller
@RequestMapping("version")
public class VersionController {

    private static final String prefix = "version/";

    private static final String VERSION_FILE_ROOT_PATH = ConfigConstants.getConfig("VERSION_FILE_ROOT_PATH");

    private static final Logger logger = LoggerFactory.getLogger(VersionController.class);

    @Resource
    private VersionService versionService;

    // 版本列表页面
    @RequestMapping("list.htm")
    public String list(HttpServletRequest request) {
        return prefix + "version_list";
    }

    // 版本列表数据接口
    @RequestMapping("queryVersionList.web")
    @ResponseBody
    public CallResult queryVersionList(PageView pageView, HttpServletRequest request) {

        Object uid = request.getSession().getAttribute("userKey");
        return versionService.queryVersionList(uid.toString(), pageView);
    }


    // 新增 查看或修改版本页面  根据operator来判断操作类型
    @RequestMapping(value = "viewRequest.htm")
    public ModelAndView edit(HttpServletRequest request, String operator, Long id) {

        ModelAndView mv = new ModelAndView(prefix + "version_edit");
        User user = SessionHelper.getSessionUser();
        mv.addObject("operator", operator);
        VersionModel versionModel = new VersionModel();
        if (id != null && id != 0) {
            versionModel = versionService.queryVersionById(id);
        }
        mv.addObject("info", versionModel);
        return mv;
    }

    // 新增或修改版本数据接口
    @RequestMapping(value = "saveOrUpdate.web")
    @ResponseBody
    public CallResult saveOrUpdate(HttpServletRequest request)  {

        Object uid =   request.getSession().getAttribute("userKey");
        String createUserId=uid.toString();

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;

        MultipartFile file = multipartRequest.getFile("upFile");

        VersionModel versionModel=new VersionModel();
        CallResult callResult = CallResult.newInstance();
        if (file != null) {
            String upFileName = file.getOriginalFilename();
            String upFileNameWithNoZip=upFileName.replace(".zip", "");
            String upFileNamePath = VERSION_FILE_ROOT_PATH +createUserId+File.separator+upFileNameWithNoZip;
            File dir = new File(upFileNamePath);
            if(dir.mkdirs())
            {
                String zipFilePath = upFileNamePath + File.separator + upFileName;
                File zipFile = new File(zipFilePath);
                try {
                    file.transferTo(zipFile);
                    HashMap<String, String> crcHashMap = decompress(zipFilePath, upFileNamePath);
                    versionModel = xmlReader(upFileNamePath +File.separator+ "version.xml");

                    String crcASum = getCrcSum(upFileNamePath + File.separator + crcHashMap.get("versionA"));
                    if (!crcASum.equals(versionModel.getCrca())) {
                        FileUtil.deleteDirFile(dir);
                        callResult.setRec("FAL");
                        return callResult;
                    }

                    String crcBSum = getCrcSum(upFileNamePath + File.separator + crcHashMap.get("versionB"));
                    if (!crcBSum.equals(versionModel.getCrcb())) {
                        FileUtil.deleteDirFile(dir);
                        callResult.setRec("FAL");
                        return callResult;
                    }
                } catch (Exception e) {
                    logger.error(e.toString());
                    FileUtil.deleteDirFile(dir);
                    callResult.setRec("FAL");
                    return callResult;
                }

                versionModel.setCreateUserId(createUserId);
                versionModel.setVersionId(IDUtil.getUUIDStr(16));
                versionModel.setFileName(upFileNameWithNoZip+File.separator+versionModel.getFileName());
                callResult = versionService.saveVersionModel(versionModel);
            } else {
                callResult.setRec("FAL");
                return callResult;
            }
        }
        return callResult;
    }


    public VersionModel xmlReader(String versionFilePath) throws Exception {
        File versionFile = new File(versionFilePath);

        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document versionDoc = builder.parse(versionFile);

        VersionModel versionModel = new VersionModel();
        String boardType = versionDoc.getElementsByTagName("BoardType").item(0).getFirstChild().getNodeValue();
        String hardware = versionDoc.getElementsByTagName("Hardware").item(0).getFirstChild().getNodeValue();
        String firmware = versionDoc.getElementsByTagName("Firmware").item(0).getFirstChild().getNodeValue();
        String time = versionDoc.getElementsByTagName("Time").item(0).getFirstChild().getNodeValue();
        // 获取文件名
        String fileName = versionDoc.getElementsByTagName("Name").item(0).getFirstChild().getNodeValue();
        fileName=fileName+".ver";

        String mcuType = versionDoc.getElementsByTagName("MCUType").item(0).getFirstChild().getNodeValue();
        String bomID = versionDoc.getElementsByTagName("BomID").item(0).getFirstChild().getNodeValue();
        String boardID = versionDoc.getElementsByTagName("BoardID").item(0).getFirstChild().getNodeValue();

        String crca = versionDoc.getElementsByTagName("CRCA").item(0).getFirstChild().getNodeValue();
        String crcb = versionDoc.getElementsByTagName("CRCB").item(0).getFirstChild().getNodeValue();

        versionModel.setHardware(hardware);
        versionModel.setBoardType(boardType);
        versionModel.setFirmware(firmware);
        versionModel.setVersionCreateTime(time);
        versionModel.setFileName(fileName);
        versionModel.setMcuSupport(mcuType);
        versionModel.setBomSupport(bomID);
        versionModel.setBoardId(boardID);
        versionModel.setCrca(crca);
        versionModel.setCrcb(crcb);

        return versionModel;
    }

    private String getCrcSum(String filePath) throws Exception {
        File f = new File(filePath);
        String fileName = f.getName();
        long fileLen = f.length();
        logger.info(fileName + " 大小为：" + fileLen);

        InputStream is = new FileInputStream(f);
        int byteLen = fileLen <= Integer.MAX_VALUE ? (int) fileLen : 1024;
        byte[] wholeData = new byte[byteLen];
        is.read(wholeData);
        is.close();
        int crc = ByteUtil.crcSum(wholeData);
        String crcStr = Integer.toHexString(crc).toUpperCase();
        logger.info(fileName + " crcStr:" + crcStr);
        return crcStr;
    }

    private HashMap<String, String> decompress(String srcPath, String dest) throws Exception {
        File file = new File(srcPath);
        if (!file.exists()) {
            throw new RuntimeException(srcPath + "所指文件不存在");
        }
        ZipFile zf = new ZipFile(file);
        Enumeration entries = zf.entries();
        ZipEntry entry;
        HashMap<String, String> fileNameMap = new HashMap<>();

        while (entries.hasMoreElements()) {
            entry = (ZipEntry) entries.nextElement();
            String fileName = entry.getName();
            File f = new File(dest + File.separator + fileName);

            logger.info("解压: " + fileName);
            if (!f.exists()) {
                //String dirs = FileUtils.getParentPath(f);
                String dirs = f.getParent();
                File parentDir = new File(dirs);
                parentDir.mkdirs();
            }
            f.createNewFile();
            // 将压缩文件内容写入到这个文件中
            InputStream is = zf.getInputStream(entry);
            FileOutputStream fos = new FileOutputStream(f);
            int count;
            byte[] buf = new byte[8192];
            while ((count = is.read(buf)) != -1) {
                fos.write(buf, 0, count);
            }

            if (fileName.contains("-A")) {
                fileNameMap.put("versionA", fileName);
            } else if (fileName.contains("-B")) {
                fileNameMap.put("versionB", fileName);
            }
            is.close();
            fos.close();
        }
        return fileNameMap;
    }

    // 删除版本接口
    @RequestMapping(value = "deleteVersion.web")
    @ResponseBody
    public CallResult deleteVersion(Long id, String fileName,HttpServletRequest request) {
        Object uid =   request.getSession().getAttribute("userKey");
        String createUserId=uid.toString();
        return versionService.deleteVersionById(id,fileName,createUserId);
    }


}
