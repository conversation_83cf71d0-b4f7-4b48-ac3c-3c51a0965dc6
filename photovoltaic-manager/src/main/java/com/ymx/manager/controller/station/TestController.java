package com.ymx.manager.controller.station;

import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.photovoltaic.station.model.TestModel;
import com.ymx.service.photovoltaic.station.service.ComponentGroupService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * i
 * <AUTHOR>
 * @version 2020/11/4
 */
@Controller
@RequestMapping("test")
public class TestController {
	@Resource
	private ComponentGroupService componentGroupService;
	private static final String prefix = "test/";
	/***
	 * 跳转至组串列表页
	 * @return
	 */
	@RequestMapping("list.htm")
	public String list(HttpServletRequest request)
	{
		return prefix + "select_list";
	}
	/***
	 * 跳转至组串列表页
	 * @return
	 */
	@RequestMapping("addInfo.htm")
	public String addInfo(HttpServletRequest request)
	{
		return prefix + "addInfo";
	}
	/***
	 * 跳转至组串列表页
	 * @return
	 */
	@RequestMapping("report.htm")
	public String report(HttpServletRequest request)
	{
		return prefix + "report";
	}


	/***
	 * 查询组串数据
	 * @param model    组件
	 * @param pageView 分页实体
	 * @return
	 */
	@RequestMapping("queryTestModelList.web")
	@ResponseBody
	public CallResult queryTestModelList(TestModel model , PageView pageView, HttpServletRequest request){

		Object checkrole =   request.getSession().getAttribute("checkrole");//
		Object uid =   request.getSession().getAttribute("userKey");
		if(checkrole.toString().contains("运维"))
		{
//			model.setCreateUserId(uid.toString());
		}

		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//		model.setLanguage(TranslateLanguage.getLanguageType(ob));
		return componentGroupService.queryTestModelList(model , pageView);
	}

}
