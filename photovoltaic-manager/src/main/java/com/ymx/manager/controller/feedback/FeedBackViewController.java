package com.ymx.manager.controller.feedback;

import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.station.model.FeedBackModel;
import com.ymx.service.photovoltaic.station.service.FeedBackService;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.UUID;

/**
 * @DESC 系统视图
 * @DATE 2018/8/17
 * @NAME SystemViewController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("feedBackView")
public class FeedBackViewController 
{
	    @Resource
        private FeedBackService feedBackService;
	    @Autowired
		private ILoginLoggerService loginLoggerService;
	    private static final String prefix = "feedback/";
	    @RequestMapping("list.htm")
	    public String list(HttpServletRequest request)
	    {
//	    	String mkt = "";
//	   	    String datamkt = "";
//	        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//	        if(ob.toString().contains("en"))
//	        {
//	     	   mkt="en";
//	     	   datamkt = "en";
//	        }
//	        else
//	        {
//	     	   mkt="cn";
//	     	   datamkt="cn";
//	        }
//	        request.setAttribute( "datamkt", datamkt );
//	        request.setAttribute( "mkt", mkt );
	        return prefix + "feedback_list";
	    }
	    /***
	     * 查询反馈意见
	     * @param model    组件
	     * @param pageView 分页实体
	     * @return
	     */
	    @RequestMapping("queryFeedbackList.web")
	    @ResponseBody
	    public CallResult queryFeedbackList(FeedBackModel model , PageView pageView,HttpServletRequest request)
	    {
			//插入日志
	    	insertlog("查询反馈意见");
			// 从Session中获取当前语言区域设置
		    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
			// 根据获取到的语言区域设置，更新模型中的语言类型
			model.setLanguage(TranslateLanguage.getLanguageType(ob));
			// 调用服务层方法，查询反馈意见列表
	        return feedBackService.queryList(model , pageView);
	    }
	    public  void insertlog(String content)
	    {
	 	   LoginLogger logger = new LoginLogger();
	       // String content = "查询会员数据";
	        logger.setId( UUID.randomUUID().toString() );
	        logger.setUserId( SessionHelper.getUserId() );
	        logger.setLastLoginTime( new Date() );
	       // logger.setLoginIp( getIpAddr(request) );
	        logger.setContent(content);
	       // logger.setOperation_id(UUID.randomUUID().toString());
	        loginLoggerService.insert( logger );
	    }
}
