package com.ymx.manager.controller.station;

import com.ymx.common.base.entity.User;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.member.entity.MemberInfoModel;
import com.ymx.service.photovoltaic.member.mapper.MemberMapper;
import com.ymx.service.photovoltaic.station.model.PowerStationModel;
import com.ymx.service.photovoltaic.station.service.PowerStationService;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.manager.security.service.IUserService;
import com.ymx.common.utils.CommonUtil;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * @DESC 系统(电站)控制层
 * @DATE 2018/8/3
 * @NAME PowerStationController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("powerstation")
public class PowerStationController {


    /** 系统设备服务层 */
    @Resource
    private PowerStationService powerStationService;
    @Autowired
    private ILoginLoggerService loginLoggerService;
	@Autowired
	private MemberMapper memberMapper;
	@Autowired
	private IUserService userService;

    private static final String prefix = "station/";

    private static final Logger logger = LoggerFactory.getLogger(PowerStationController.class);

    /**
     * 系统管理列表页
     * @return
     */
    @RequestMapping("list.htm")
    public String list(HttpServletRequest request,String memberId,String type,String id)
    {
//    	String mkt = "";
//   	     String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
	    if(memberId!=null){
//        	if("1".equals(type)){
//		        MemberInfoModel model = new MemberInfoModel();
//		        model.setId(memberId);
//		        MemberInfoModel memberModel = memberMapper.queryMemberByPhone(model);
//		        if(memberModel!=null){
//			        request.setAttribute( "userName2", memberModel.getName());
//		        }
//	        }else{
//		        User user=userService.queryUserById(memberId);
//		        if(user!=null){
//			        request.setAttribute( "userName", user.getUserName() );
//		        }
//	        }
//		    User user=userService.queryUserById(memberId);
//		    if(user!=null){
//			    request.setAttribute( "userName", user.getUserName() );
//		    }
//		    普通用户 1
		    MemberInfoModel model = new MemberInfoModel();
		    model.setId(memberId);

		    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		    model.setLanguage(TranslateLanguage.getLanguageType(ob));
		    MemberInfoModel memberModel = memberMapper.queryMemberByPhone(model);
		    if(memberModel!=null){
			    request.setAttribute( "userName", memberModel.getXing()+memberModel.getName());
		    }
	    }
        if(memberId==null){
	        memberId="";
        }
	    request.setAttribute( "memberId", memberId );
	    request.setAttribute( "memberType", type );
	    request.setAttribute( "id", id );



//        request.setAttribute( "datamkt2", datamkt );
//        request.setAttribute( "mkt2", mkt );
        insertlog("查询电站数据");
        return prefix + "station_list";
    }

    /**
     * 电站云终端列表
     * @return
     */
    @RequestMapping("stationCloudList.htm")
    public String stationCloudList(String id ,String flag , HttpServletRequest request ){
        request.setAttribute("powerStationId" , id );
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        if ("selectCloudTerminal".equals(flag) ){
           return prefix + "select_cloud_list";
        }
        return  prefix + "station_cloud_list";
    }

    /**
     * 电站逆变器列表
     * @return
     */
    @RequestMapping("stationInverterList.htm")
    public String stationInverterList(String id , String flag , HttpServletRequest request ){
        request.setAttribute("powerStationId" , id );
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        if ("selectInverter".equals(flag) ){
            return prefix + "select_inverter_list";
        }
        return  prefix + "station_inverter_list";
    }

    /**
     * 电站组件列表
     * @return
     */
    @RequestMapping("stationComponentList.htm")
    public String stationComponentList(String id , HttpServletRequest request){
        request.setAttribute("powerStationId" , id );
//        String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        return  prefix + "station_component_list";
    }

    /***
     * 查询系统设备数据
     * @param model    系统设备
     * @param pageView 分页实体
     * @return
     */
    @RequestMapping("queryPowerStationList.web")
    @ResponseBody
    public CallResult queryPowerStationList(PowerStationModel model , PageView pageView, HttpServletRequest request){
    	Object checkrole =   request.getSession().getAttribute("checkrole");//
        Object uid =   request.getSession().getAttribute("userKey");
        if(checkrole.toString().contains("运维")||checkrole.toString().contains("客户"))
        {
        	model.setCreateUserId(uid.toString());
        }
        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//        	model.setLanguage("en");
//        }
//        else
//        {
//        	model.setLanguage("ch");
//        }
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
      return powerStationService.queryPowerStationList(model , pageView);
    }



    /**
     * 页面请求 (新增,编辑,查看)
     * @param operator    操作标识
     * @param model      人员信息
     * @return
     */
    @RequestMapping(value="viewRequest.htm")
    public ModelAndView edit(HttpServletRequest request ,String operator , PowerStationModel model ){
//    	String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        ModelAndView mv = new ModelAndView(prefix + "station_edit");
        User user = SessionHelper.getSessionUser();
        mv.addObject("operator" , operator);
        //model.setCreateUserId(user.getUserId());
        model.setCreateUserId(user.getId());
        model.setCreateUserName(user.getUserName());

	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        if(CommonUtil.isEmpty(model.getId()) && StringUtils.hasLength(model.getId())){
            model = powerStationService.queryPowerStationObjectById(model.getId(),model.getLanguage());
        }
        mv.addObject("info" , model);
        return mv;
    }

    /**
     * 新增或修改
     * @param   model      系统设备
     * @return  CallResult
     */
    @RequestMapping(value="saveOrUpdate.web")
    @ResponseBody
    public CallResult saveOrUpdate( PowerStationModel model , HttpServletRequest request){
    	Object ob =   request.getSession().getAttribute("checkrole");//
        Object uid =   request.getSession().getAttribute("userKey");
        model.setCreateUserId(uid.toString());

	    Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(obj));
    	if (CommonUtil.isEmpty(model.getId())) {
            //修改
    		 insertlog("修改电站数据,名称:"+model.getSystemName());
            return powerStationService.updatePowerStationModel(model);
        } 
    	else {
            //model.setCreateUserId(SessionHelper.getUserId());
            // 电站创建人员 由运维人员创建 与 APP会员用户id 一致 ,用于推送消息使用
          //  model.setCreateUserId(SessionHelper.getSessionUser().getId());
    		insertlog("新增电站数据，名称:"+model.getSystemName());
            return powerStationService.savePowerStationModel(model);
        }
    }

    /**
     * 调试完成
     * @param  model  系统设备
     * @return
     */
    @RequestMapping(value="powerstationTryStatus.web")
    @ResponseBody
    public CallResult powerstationTryStatus(PowerStationModel model,HttpServletRequest request,HttpServletResponse response){
        Map<String ,Object> map = new HashMap<>();
        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);

        int status=model.getStatus();
          // 如果状态是已调试，就改为未调试，如果是未调试，就改为已调试
          status= status==1?2:1;
          model.setStatus(status);

	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        logger.info("powerStation switch ok, id {}",model.getId());
        String message = powerStationService.batchUpdatePowerStationModel(map , model);
        JSONObject json = new JSONObject();
      //将返回信息保存在JSON对象中
        json.put("message",message);

        //设置响应编码格式，防止乱码
        response.setContentType("text/html;charset=UTF-8");
        //将数据以json格式响应给ajax
        try {
			response.getWriter().write(json.toString());
		} catch (IOException e) {
		   logger.error("IOException",e);
		}

        return null;
    }

    /**
     * 移除设备
     * @param model 系统设备
     * @return
     */
    @RequestMapping(value="stationDeleteEquipment.web")
    @ResponseBody
    public CallResult stationDeleteEquipment(PowerStationModel model,HttpServletRequest request){
    	  insertlog("电站移除设备,id:"+model.getId());

	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return powerStationService.stationDeleteEquipment( model );
    }

    /**
     * 选择设备
     * @param model 系统设备
     * @return
     */
    @RequestMapping(value="selectEquipment.web")
    @ResponseBody
    public CallResult selectEquipment(PowerStationModel model,HttpServletRequest request) {
    	  insertlog("电站选择设备,id:"+model.getId());

	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        return powerStationService.selectEquipment( model );
    }

    /**
     * 选择业主
     * @return
     */
    @RequestMapping("selectpeopler.htm")
    public String selectpeopler(HttpServletRequest request,String id)
    {
    	 insertlog("电站选择业主,id:"+id);
//    	String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        request.setAttribute( "powerStationId", id );
        return prefix + "select_peopler";
    }

    @RequestMapping(value="selectMemberInsert.web")
    @ResponseBody
    public CallResult selectMemberInsert(String powerStationId,String memberId,HttpServletRequest request,HttpServletResponse response) 
    {
    	 String message = "";
    	 PowerStationModel psm = new PowerStationModel();
    	 psm.setId(powerStationId);
    	 psm.setMemberId(memberId);

	    Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    psm.setLanguage(TranslateLanguage.getLanguageType(obj));
    	 int i = powerStationService.updatePowerStationById(psm);
//    	 Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//         if(ob.toString().contains("en"))
//         {
//        	 if(i!=0)
//        	 {
//		         TranslateLanguage.getCallResult(ob, ErrCodeExt.INSERT_OWNER_OK);
//        		 message = "Owner added successfully";
//        	 }
//        	 else
//        	 {
//		         TranslateLanguage.getCallResult(ob, ErrCodeExt.INSERT_OWNER_OK);
//        		 message = "Owner added failed";
//        	 }
//         }
//         else
//         {
//        	 if(i!=0)
//        	 {
//        		 message = "业主添加成功";
//        	 }
//        	 else
//        	 {
//        		 message = "业主添加失败";
//        	 }
//         }
	        String language=TranslateLanguage.getLanguageType(obj);
		    if(i!=0)
		    {
			    message=TranslateLanguage.getCallResult(language, ErrCodeExt.INSERT_OWNER_OK);
		    }
		    else
		    {
			    message=TranslateLanguage.getCallResult(language, ErrCodeExt.INSERT_OWNER_ERROR);
		    }
    	 
    	 JSONObject json = new JSONObject();
         //将返回信息保存在JSON对象中
           json.put("message",message);
          
           //设置响应编码格式，防止乱码
           response.setContentType("text/html;charset=UTF-8");
           //将数据以json格式响应给ajax
           try {
   			response.getWriter().write(json.toString());
   		} catch (IOException e) {
   			// TODO Auto-generated catch block
   			e.printStackTrace();
   		}
    	
    	return null;//powerStationService.selectEquipment( model );
    }

    public  void insertlog(String content)
    {
 	   LoginLogger logger = new LoginLogger();
       // String content = "查询会员数据";
        logger.setId( UUID.randomUUID().toString() );
        logger.setUserId( SessionHelper.getUserId() );
        logger.setLastLoginTime( new Date() );
       // logger.setLoginIp( getIpAddr(request) );
        logger.setContent(content);
     //   logger.setOperation_id(UUID.randomUUID().toString());
        loginLoggerService.insert( logger );
    }

	/**
	 * 删除电站
	 * @param  model
	 * @return CallResult
	 */
	@RequestMapping(value="deletePowerStationModel.web")
	@ResponseBody
	public CallResult deletePowerStationModel(PowerStationModel model,HttpServletRequest request) {
		User user = SessionHelper.getSessionUser();
		insertlog("删除电站数据,id:"+model.getId()+" userKey:"+user.getId());
		model.setCreateUserId(user.getUserId());
		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(ob));
		return powerStationService.deletePowerStation(model);
	}


    /*
    * 重写返回给第三方调用
    * */
    @RequestMapping(value="powerstationTryStatusTo.web")
    @ResponseBody
    public CallResult powerstationTryStatusTo(PowerStationModel model,HttpServletRequest request,HttpServletResponse response){
        Map<String ,Object> map = new HashMap<>();
        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);

        int status=model.getStatus();
        // 如果状态是已调试，就改为未调试，如果是未调试，就改为已调试
        status= status==1?2:1;
        model.setStatus(status);

        model.setLanguage(TranslateLanguage.getLanguageType(ob));
        logger.info("powerStation switch ok, id {}",model.getId());
        String message = powerStationService.batchUpdatePowerStationModel(map , model);
        JSONObject json = new JSONObject();
        //将返回信息保存在JSON对象中
        json.put("message",message);

        //设置响应编码格式，防止乱码
        response.setContentType("text/html;charset=UTF-8");
        //将数据以json格式响应给ajax
        /*try {
			response.getWriter().write(json.toString());
		} catch (IOException e) {
		   logger.error("IOException",e);
		}*/

        CallResult callResult = CallResult.newInstance();
        callResult.setRec(message);
        return callResult;
    }

}
