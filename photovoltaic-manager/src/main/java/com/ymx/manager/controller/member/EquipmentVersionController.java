package com.ymx.manager.controller.member;

import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.station.model.EquipmentVersionModel;
import com.ymx.service.photovoltaic.station.service.EquipmentVersionService;
import com.ymx.manager.controller.BaseController;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.UUID;

/**
 * 设备版本
 * <AUTHOR> @version 2018/11/7
 */
@Controller
@RequestMapping("equipment")
public class EquipmentVersionController extends BaseController {
	@Resource
	private EquipmentVersionService equipmentVersionService;
	@Autowired
	private ILoginLoggerService loginLoggerService;
	private static final String prefix = "equipment/";
	/**
	 *
	 * @return
	 */
	@RequestMapping(value="list.htm")
	public String  loginCodeList(HttpServletRequest request){
		getLanguage(request);
		return  prefix+"equipment_list";
	}
	/**
	 * 查询设备版本
	 * @param model
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="infoEquipmentVersion.htm")
	public ModelAndView infoEquipmentVersion(EquipmentVersionModel model,String operator ,
	                                   HttpServletRequest request, HttpServletResponse response){
		getLanguage(request);
		ModelAndView mv = new ModelAndView( prefix+"equipment_edit");
//		User user = SessionHelper.getSessionUser();
		mv.addObject("operator" , operator);
//		model.setCreateUserId(user.getUserId());
//		model.setCreateUserName(user.getUserName());
		model = equipmentVersionService.queryEquipmentVersionModelById(model.getId());
		mv.addObject("info" , model);
		insertlog("查询设备版本");
		return  mv;
	}

	/**
	 * 设备版本
	 * @param model
	 * @param view
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="queryEquipmentVersionList.web")
	@ResponseBody
	public CallResult queryMemberList(EquipmentVersionModel model, PageView view ,
	                                  HttpServletRequest request, HttpServletResponse response){

		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(ob));
		return equipmentVersionService.queryEquipmentVersionModelByAll(model,view);
	}

	/**
	 * 新增设备版本/修改设备版本
	 * @param model
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="saveOrUpdate.web")
	@ResponseBody
	public CallResult saveOrUpdate(EquipmentVersionModel model,
	                                  HttpServletRequest request, HttpServletResponse response){
		if(model.getContent()!=null){
			if(model.getContent().length()>2000){
				model.setContent(model.getContent().substring(0,2000));
			}
		}
		if(model.getUrl()!=null){
			if(model.getUrl().length()>100){
				model.setUrl(model.getUrl().substring(0,100));
			}
		}

		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(ob));
		insertlog("新增设备版本/修改设备版本");
		return equipmentVersionService.saveOrUpdate(model);
	}

	/**
	 * 查询设备版本
	 * @param model
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="deleteEquipmentVersion.web")
	@ResponseBody
	public CallResult deleteEquipmentVersion(EquipmentVersionModel model,
	                                      HttpServletRequest request, HttpServletResponse response){

		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(ob));
		return equipmentVersionService.deleteEquipmentVersion(model);
	}
	public  void insertlog(String content)
    {
 	   LoginLogger logger = new LoginLogger();
       // String content = "查询会员数据";
        logger.setId( UUID.randomUUID().toString() );
        logger.setUserId( SessionHelper.getUserId() );
        logger.setLastLoginTime( new Date() );
       // logger.setLoginIp( getIpAddr(request) );
        logger.setContent(content);
       // logger.setOperation_id(UUID.randomUUID().toString());
        loginLoggerService.insert( logger );
    }
}
