package com.ymx.manager.controller.view;

import com.ymx.common.common.result.CallResult;
import com.ymx.common.utils.*;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.manager.security.utils.JsonUtil;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.station.mapper.ComponentGroupMapper;
import com.ymx.service.photovoltaic.station.model.*;
import com.ymx.service.photovoltaic.station.service.*;
import net.sf.json.JSONObject;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.beans.IntrospectionException;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @DESC 系统视图
 * @DATE 2018/8/17
 * @NAME SystemViewController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("systemView")
public class SystemViewController {

	@Resource
    private ComponentService componentService;
	@Resource
    private ComponentDayService componentDayService;
	@Resource
    private ChangeService changeService;
	/** 逆变器服务层 */
    @Resource
    private InverterService inverterService;
    /** 云终端服务层 */
    @Resource
    private CloudTerminalService cloudTerminalService;
    /** 组串服务层 */
    @Resource
    private ComponentGroupService componentGroupService;
    private static final String prefix = "view/";
    @Resource
    private ComponentCollectService componentCollectService;
    @Resource
	private ComponentCollectBatchNoService componentCollectBatchNoService;
    @Resource
	private ConfigureService configureService;
    @Autowired
    private ILoginLoggerService loginLoggerService;
	@Resource
	private InverterCollectService inverterCollectService;

	private static final Logger logger = LoggerFactory.getLogger(SystemViewController.class);
    @Qualifier("componentGroupMapper")
    @Autowired
    private ComponentGroupMapper componentGroupMapper;

	/**
     * 与系统设备列表同样
     * @return
     */
    @RequestMapping("list.htm")
    public String list(HttpServletRequest request)
    {
//    	String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        insertlog("查询系统视图电站信息");
        return prefix + "system_list";
    }

    /***
     * 全年视图日历页面
     * @param model 系统(电站)实体
     * @return
     */
    @RequestMapping("calendarView.htm")
    public String calendarView(PowerStationModel model , HttpServletRequest request){
        request.setAttribute("model" , model );
        insertlog("查询全年视图日历页面");
        return prefix + "calendar_view";
    }
	@RequestMapping("findmoveView.htm")
	public String findmoveView2(PowerStationModel model , HttpServletRequest request){
		request.setAttribute("model" , model );
		return prefix + "findmove_view";
	}

	@RequestMapping("groupView.htm")
	public String findMoveGroupView(PowerStationModel model , HttpServletRequest request){
		request.setAttribute("model" , model );
		return prefix + "moveGroupView";
	}

	@RequestMapping("componentView.htm")
	public String findMoveComponentView(String groupId , HttpServletRequest request){
		request.setAttribute("groupId" , groupId );
		return prefix + "moveComponentView";
	}

	@RequestMapping("queryComponentPositionList.web")
	@ResponseBody
	public CallResult queryComponentPositionList(String groupId, HttpServletRequest request) {
		CallResult callResult = CallResult.newInstance();
		List<AllGroupModel> agmList = new ArrayList<>();
		List<ComponentModel> componentModelList = componentService.queryComponentPosition(groupId);
		AllGroupModel agm;
		for (ComponentModel cm : componentModelList) {
			agm = new AllGroupModel();//获取组件详情
			agm.setId(cm.getId());
			agm.setChipid(cm.getId());
			agm.setPowerId(cm.getPowerStationId());

			//agm.setGroupId(cm.getId());
			agm.setGapleft(cm.getXz());
			agm.setGaptop(cm.getYz());
			agm.setDivleft(cm.getDivleft());
			agm.setDivtop(cm.getDivtop());
			agm.setHv(cm.getPosition());

			if (CommonUtil.isEmpty(cm.getChipId()) && cm.getChipId().length() > 4) {
				agm.setAllname(cm.getChipId().substring(cm.getChipId().length() - 4, cm.getChipId().length()));
			} else {
				agm.setAllname("无");
			}
			agm.setGenre("3");
			if (cm.getXz() == null) {
				agm.setQb("new");
			} else {
				agm.setQb("update");
			}

			agmList.add(agm);
		}
		Map<String, Object> map = new HashMap<>();

		map.put("agmlt", agmList);
		callResult.setReModel(map);
		return callResult;
	}

	@RequestMapping("queryGroupList.web")
	@ResponseBody
	public CallResult queryGroupList(PowerStationModel model , HttpServletRequest request) {
		CallResult callResult = CallResult.newInstance();
		List<AllGroupModel> agmList = new ArrayList<>();
		List<ComponentGroupModel> componentGroupList = componentGroupService.queryGroupPosition(model.getId());
		AllGroupModel agm;
		for (ComponentGroupModel comGroup : componentGroupList) {
			agm = new AllGroupModel();//获取组件详情
			agm.setId(comGroup.getId());
			agm.setGroupId(comGroup.getId());

			agm.setPowerId(comGroup.getPowerStationId());
			agm.setGapleft(comGroup.getXz());
			agm.setGaptop(comGroup.getYz());
			agm.setDivleft(comGroup.getDivleft());
			agm.setDivtop(comGroup.getDivtop());

			agm.setHv(comGroup.getPosition());
			agm.setAllname(comGroup.getGroupName());

			agm.setGenre("3");
			// qb字段没有用到 用来作为新增或修改记录标志位
			if (comGroup.getXz() == null) {
				agm.setQb("new");
			} else {
				agm.setQb("update");
			}

			agmList.add(agm);
		}
		Map<String, Object> map = new HashMap<>();

		map.put("agmlt", agmList);
		callResult.setReModel(map);
		return callResult;
	}

	// 修改组串位置信息
	@RequestMapping("updateGroupPosition.web")
	@ResponseBody
	public void updateGroupPosition(HttpServletRequest request,HttpServletResponse response) {

		ChangeModel changeModel=getChangeModel(request);
		if (changeModel.getQb().equals("new")) {
			changeService.saveGroupPosition(changeModel);
		} else {
			changeService.updateGroupPosition(changeModel);
		}
	}

	// 修改组串位置信息
	@RequestMapping("updateComponentPosition.web")
	@ResponseBody
	public void updateComponentPosition(HttpServletRequest request,HttpServletResponse response){

		ChangeModel changeModel=getChangeModel(request);
		if (changeModel.getQb().equals("new")) {
			changeService.saveComponentPosition(changeModel);
		} else {
			changeService.updateComponentPosition(changeModel);
		}
	}

	private ChangeModel getChangeModel(HttpServletRequest request)
	{
		String qb = request.getParameter("qb");// 判断是新增还是修改
		String pid = request.getParameter("pid");//电站id
		String id = request.getParameter("id");//组件id
		//String lb = request.getParameter("lb");// 1  竖   2 横
		String top = request.getParameter("divtop").replace("px", "");
		String left = request.getParameter("divleft").replace("px", "");

		ChangeModel changeModel = new ChangeModel();
		changeModel.setPowerId(pid);
		//groupModel.setPosition(lb);
		if (!left.trim().equals("")) {
			int intX = Integer.parseInt(left);
			changeModel.setXz(String.valueOf(Math.round(intX / 10.0)));
		}

		if (!top.trim().equals("")) {
			int intTop = Integer.parseInt(top);
			changeModel.setYz(String.valueOf(Math.round(intTop / 10.0)));
		}
		changeModel.setId(id);
		changeModel.setDivtop(top);
		changeModel.setDivleft(left);
		changeModel.setQb(qb);
		return  changeModel;
	}

	// 查询电站组件信息 逆变器信息 采集器信息
	@RequestMapping("queryPowerStationList.web")
	@ResponseBody
	public CallResult queryPowerStationList(PowerStationModel model , HttpServletRequest request) {
		CallResult callResult = CallResult.newInstance();
		List<AllGroupModel> allGroupModelList = new ArrayList<>();
		// 获取电站组件位置信息
		List<ComponentModel> comModelList = componentService.queryComponentListByData(model.getId(), null);
		AllGroupModel agm;
		for(ComponentModel comModel:comModelList)
		{
			agm = new AllGroupModel(comModel);//获取组件详情
			allGroupModelList.add(agm);
		}
		Map<String, Object> map = new HashMap<>();
		map.put("powerStationId",model.getId());

		//获取云终端数据  获取采集器数据
		List<CloudTerminalModel> ctModelList = cloudTerminalService.queryCloudListByData(map);
		for(CloudTerminalModel ctModel :ctModelList)
		{
			agm = new AllGroupModel(ctModel);
			allGroupModelList.add(agm);
		}

		if(model.getCreateType()==1){
			//获取电气信息 可以用和app相同的方法获取
			List<ComponentViewModel> comViewList=getLastCollectInfo(model.getId(),comModelList.size());
			for(ComponentViewModel ccm:comViewList)
			{
				for(AllGroupModel agModel:allGroupModelList)
				{
					if("3".equals(agModel.getGenre()) && ccm.getChipId().equals(agModel.getChipid()))
					{
						agModel.setComponentTemperature(ccm.getComponentTemperature());
						agModel.setOutputCurrent(ccm.getOutputCurrent());
						agModel.setOutputVoltage(ccm.getOutputVoltage());
					}
				}
			}
		}
		map.put("agmlt",allGroupModelList);
		callResult.setReModel(map);
		return callResult;
	}


	private Map<String, Object> getQueryMap(String powerStationId,int size) {
		Map<String, Object> map = new HashMap<>();
		map.put("tableName", "_" + powerStationId);
		String timeAxisCreateTime;
		int timeStep = ConfigUtil.getTimeStep();

		// 取出最近入库的两倍组件数量  防止入库时间不一致
		LocalDateTime createTime = LocalDateTime.now();
		createTime = createTime.plusMinutes(-timeStep);
		timeAxisCreateTime = createTime.withNano(0).toString().replace("T", " ");
		map.put("collectTimeStart", timeAxisCreateTime);
		map.put("limit", size * 2);

		return map;
	}

	private List<ComponentViewModel> getLastCollectInfo(String powerStationId,int size) {
		Map<String, Object> queryMap = getQueryMap(powerStationId,size);
		List<ComponentViewModel> componentViewModelList = componentCollectService.queryLastCollectInfo(queryMap);
		if (componentViewModelList.size() != 0) {
			// 获取每个批次的组件数量  最后是否需要batchNo 还是只需要采集时间
			Map<String, Long> collectTimeMap = Optional.of(componentViewModelList).
					orElse(new ArrayList<>()).stream().
					collect(Collectors.groupingBy(ComponentViewModel::getCollectTime, Collectors.counting()));

			collectTimeMap.forEach((k, v) -> {
				logger.info("key:value = " + k + ":" + v);
			});
			String maxCollectTime = getMaxCollectTime(collectTimeMap);

			logger.info("maxCollectTime: " + maxCollectTime);
			// 用这个批次号去过滤组件
			componentViewModelList = Optional.of(componentViewModelList).
					orElse(new ArrayList<>()).stream().
					filter(componentViewModel -> componentViewModel.getCollectTime().equals(maxCollectTime))
					.collect(Collectors.toList());

		}
		return componentViewModelList;
	}

	//获取最大的批次号
	private String getMaxCollectTime(Map<String, Long> collectTimeMap) {
		List<String> keyList = collectTimeMap.keySet().stream().
				sorted(Comparator.comparing(collectTimeMap::get)).collect(Collectors.toList());
		if (keyList.size() == 1) {
			return keyList.get(0);
		}

			String firstKey = keyList.get(0);
			String secondKey = keyList.get(1);

			long firstValue = collectTimeMap.get(firstKey);
			long secondValue = collectTimeMap.get(secondKey);

			// 哪个批次的数据全就取哪个批次的
			if (firstValue > secondValue) {
				return firstKey;
			}
			if (firstValue < secondValue) {
				return secondKey;
			}
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

			// 如果两个批次的数据量相同 取最近的批次号
			LocalDateTime firstCollectTime = LocalDateTime.parse(firstKey, formatter);
			LocalDateTime secondCollectTime = LocalDateTime.parse(secondKey, formatter);
			if (firstCollectTime.isAfter(secondCollectTime)) {
				return firstKey;
			} else {
				return secondKey;
			}
	}


		/***
		 * 拖动组件
		 * @param model 系统(电站)实体
		 * @return
		 */
    @RequestMapping("findmoveView2.htm")
    public String findmoveView(PowerStationModel model , HttpServletRequest request)
    {
    	 List<AllGroupModel> agmlt = new ArrayList<AllGroupModel>();
    	 List<AllGroupModel> yagmlt = new ArrayList<AllGroupModel>();
    	 List<AllGroupModel> nagmlt = new ArrayList<AllGroupModel>();
    	 List<AllGroupModel> zagmlt = new ArrayList<AllGroupModel>();
    	 // String ymd=request.getParameter("ymd");
    	 String powerStationId=request.getParameter("id");
    	 //获取组件数据
    	 List<ComponentModel> lt = componentService.queryComponentListByData(powerStationId, null);

    	 AllGroupModel agm = null;
    	 for(int p=0;p<lt.size();p++)
    	 {
    		 agm = new AllGroupModel();
    		 ComponentModel cm = lt.get(p);
    		//获取组件详情

    		 agm.setId(cm.getId());
    		 agm.setPowerId(cm.getPowerStationId());
    		 agm.setGapleft(cm.getXz());
    		 agm.setGaptop(cm.getYz());
    		 agm.setHv(cm.getPosition());
    		 //aa.substring(aa.length()-n,aa.length())
    		 if(CommonUtil.isEmpty(cm.getChipId())==true && cm.getChipId().length()>4)
    		 {
    			 agm.setAllname("组件芯片ID后4位:"+cm.getChipId().substring(cm.getChipId().length()-4, cm.getChipId().length()));
    		 }
    		 else
    		 {
    			 agm.setAllname("组件芯片ID:无");
    		 }
    		 agm.setChipid(cm.getChipId());

    		 agm.setGenre("3");
    		 agm.setQb(cm.getQb());

    		 agm.setDivleft(cm.getDivleft());
    		 agm.setDivtop(cm.getDivtop());
    		 agmlt.add(agm);
    		 zagmlt.add(agm);
    	 }
    	 Map<String, Object> map = new HashMap<String, Object>();
 		 map.put("powerStationId",powerStationId);
 		 //暂时不传值
 		// map.put("ymd",ymd);
    	 //获取 逆变器数据
    	 List<InverterModel> Imlt = inverterService.queryInverterListByData(map);
    	 for(int p=0;p<Imlt.size();p++)
    	 {
    		 agm = new AllGroupModel();
    		 InverterModel cm = Imlt.get(p);
    		 agm.setId(cm.getId());
    		 agm.setPowerId(cm.getPowerStationId());
    		 agm.setGapleft(cm.getXz());
    		 agm.setGaptop(cm.getYz());
    		 agm.setHv(cm.getPosition());
    		 agm.setAllname(cm.getInverterName());
    		 agm.setGenre("2");
    		 agmlt.add(agm);
    		 nagmlt.add(agm);
    	 }

    	 //获取云终端数据
    	 List<CloudTerminalModel> ctlt = cloudTerminalService.queryCloudListByData(map);
    	 for(int p=0;p<ctlt.size();p++)
    	 {
    		 agm = new AllGroupModel();
    		 CloudTerminalModel cm = ctlt.get(p);
    		 agm.setId(cm.getId());
    		 agm.setPowerId(cm.getPowerStationId());
    		 agm.setGapleft(cm.getXz());
    		 agm.setGaptop(cm.getYz());
    		 agm.setHv(cm.getPosition());
    		 agm.setAllname(cm.getCloudName());
    		 agm.setGenre("1");
    		 agmlt.add(agm);
    		 yagmlt.add(agm);
    	 }
    	 /*// 获取组串数据
    	 List<ComponentGroupModel> cglt = componentGroupService.queryComponentGroupListByData(map);
    	 for(int p=0;p<cglt.size();p++)
    	 {
    		 agm = new AllGroupModel();
    		 ComponentGroupModel cm = cglt.get(p);
    		 agm.setId(cm.getId());
    		 agm.setPowerId(powerStationId);
    		 agm.setGapleft(cm.getXz());
    		 agm.setGaptop(cm.getYz());
    		 agm.setHv(cm.getPosition());
    		 agm.setAllname(cm.getGroupName());
    		 agm.setGenre("3");
    		 agmlt.add(agm);
    	 }*/
    	 String bianliang= PageUtil.getBasePath(request);
    	 List<AllGroupModel> zhlt = new  ArrayList<AllGroupModel>();
    	 AllGroupModel agmodel = null;
    	 // 获取输出电压 电流 温度等电气信息
    	 List<ComponentCollectModel> ccmlt = returnTckData(powerStationId);
    	 if(null != ccmlt && ccmlt.size()>0)
    	 {
    		 for(int r=0;r<ccmlt.size();r++)
        	 {
    			 ComponentCollectModel ccm =  ccmlt.get(r);
    			 for(int u=0;u<agmlt.size();u++)
    			 {
    				 agmodel = new AllGroupModel();
    				 AllGroupModel agms =  agmlt.get(u);
    				 if(ccm.getChipId().equals(agms.getChipid()))
    				 {
    					  agmodel.setId(agms.getId());
    					  agmodel.setChipid(ccm.getChipId());
    					  agmodel.setGenre("3");
    					  agmodel.setComponentTemperature(ccm.getComponentTemperature());
    				      agmodel.setInputCurrent(ccm.getInputCurrent());
    				      agmodel.setInputVoltage(ccm.getInputVoltage());
    				      agmodel.setOutputCurrent(ccm.getOutputCurrent());
    				      agmodel.setOutputVoltage(ccm.getOutputVoltage());
    				      zhlt.add(agmodel);
    				 }
    			 }
        	 }
    	  }

		 String showview =returntsk(zhlt);
	    Map<String, Object> mapzhlt = new HashMap<String, Object>();
	    mapzhlt.put("mapzhlt",mapzhlt);
	    request.setAttribute("mapzhlt", mapzhlt);
	    request.setAttribute("zhlt", zhlt);
		 int yq = ctlt.size()%10;
		 int ylist=0;
		 if(yq>0)
		 {
			 ylist=ctlt.size()/10+1;
		 }
		 else
		 {
			 ylist=ctlt.size()/10;
		 }
		 int nlist=0;
		 int nq = Imlt.size()%10;
		 if(nq>0)
		 {
			 nlist=Imlt.size()/10+1;
		 }
		 else
		 {
			 nlist=Imlt.size()/10;
		 }
		 int zlist=0;
		 int zq = lt.size()%10;
		 if(zq>0)
		 {
			 zlist=lt.size()/10+1;
		 }
		 else
		 {
			 zlist=lt.size()/10;
		 }
    	 String jg= returnzj(agmlt,bianliang,powerStationId,ylist,nlist,zlist,yagmlt,nagmlt,zagmlt);// movejs.toString();
    	 //功率弹出框
    	 request.setAttribute("showview", showview.toString());
    	 //功率拖动
    	 request.setAttribute("jsjg", jg);
    	 //初始化脚本
    	 request.setAttribute("initjs", returnfunction(agmlt));
    	 //功率不能移动
    	 request.setAttribute("moveblock", returnfixedly(agmlt,bianliang,powerStationId).toString());
    	 // 电流不可移动
    	 request.setAttribute("showdlview", returndlfixedly(lt,bianliang,powerStationId).toString());
    	// 电压不可移动
    	 request.setAttribute("showdyview", returndyfixedly(lt,bianliang,powerStationId).toString());

    	 //电压可拖动
    	 request.setAttribute("showdydragview", dragdy(agmlt,bianliang,powerStationId,ylist,nlist,zlist,yagmlt,nagmlt,zagmlt).toString());
    	 request.setAttribute("showdydragjs", returndyfunction(agmlt).toString());
    	//电流可拖动
    	 request.setAttribute("showdldragview", dragdl(agmlt,bianliang,powerStationId,ylist,nlist,zlist,yagmlt,nagmlt,zagmlt).toString());
    	 request.setAttribute("showdldragjs", returndlfunction(agmlt).toString());

    	 request.setAttribute("cmlt" , lt);
         request.setAttribute("model" , model );
         request.setAttribute("powerid",powerStationId );
         insertlog("查询逆变器，采集器，组件布局");
        return prefix + "findmove_view5";
    }
    //功率返回不可移动 蓝色
    public String returnfixedly(List<AllGroupModel> agmlt,String bianliang,String powerStationId)
    {
         StringBuilder movejs= new StringBuilder();
    	 String greenbj=bianliang+"images\\/zj_gllh.png";
    	 //设定宽度 一行显示20个
    	 movejs.append("<div id=\"panelo\" class=\"container1\">");
    	 if(agmlt.size()>0)
    	 {
              int imagelx = 20;

    		 for(int i=0;i<agmlt.size();i++)
    		 {
    			 AllGroupModel cm = agmlt.get(i);
    	    	 movejs.append("<div class=\"draggable\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div>");
    		 }
    	 }
    	movejs.append("</div>");
    	return movejs.toString();
    }
    //电流返回不可移动 图片红色
    public String returndlfixedly(List<ComponentModel> agmlt,String bianliang,String powerStationId)
    {
         StringBuilder movejs= new StringBuilder();
    	 String greenbj=bianliang+"images\\/zj_dlh.png";
    	 //设定宽度 一行显示20个
    	 movejs.append("<div id=\"panelo\" class=\"container1\">");
    	 if(agmlt.size()>0)
    	 {

    		 for(int i=0;i<agmlt.size();i++)
    		 {
    			 ComponentModel cm = agmlt.get(i);
    	    	 movejs.append("<div class=\"draggable\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">组件芯片ID:"+cm.getChipId()+"</div>");
    		 }
    	 }
    	movejs.append("</div>");
    	return movejs.toString();
    }
    //电压返回不可移动 图片绿色
    public String returndyfixedly(List<ComponentModel> agmlt,String bianliang,String powerStationId)
    {
         StringBuilder movejs= new StringBuilder();
    	 String greenbj=bianliang+"images\\/zj_dyh.png";
    	 //设定宽度 一行显示20个
    	 movejs.append("<div id=\"panelo\" class=\"container1\">");
    	 if(agmlt.size()>0)
    	 {
              int imagelx = 20;

    		 for(int i=0;i<agmlt.size();i++)
    		 {
    			 ComponentModel cm = agmlt.get(i);
    	    	 movejs.append("<div class=\"draggable\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">组件芯片ID:"+cm.getChipId()+"</div>");
    		 }
    	 }
    	movejs.append("</div>");
    	return movejs.toString();
    }
    //拖动功率  所有的组件 组串 逆变器  云终端
    public String returnzj(List<AllGroupModel> agmlt,String bianliang,String powerStationId,int ysize,int nsize,int zsize,List<AllGroupModel> yagmlt,List<AllGroupModel> nagmlt,List<AllGroupModel> zagmlt)
    {
    	 StringBuilder movejs= new StringBuilder();

    	 movejs.append("<div id=\"panel\" class=\"container\">");
    	 //排序采集器
    	 ordercollector(agmlt, bianliang, powerStationId, ysize, nsize, zsize,movejs,yagmlt);
    	 //排序逆变器
    	 orderinverter(agmlt, bianliang, powerStationId, ysize, nsize, zsize,movejs,nagmlt);
    	 //排序组件
    	 ordersub(agmlt, bianliang, powerStationId, ysize, nsize, zsize,movejs,zagmlt);
         movejs.append("</div>");
        return movejs.toString();

    }
    //功率生成函数
    public String returnfunction(List<AllGroupModel> agmlt)
    {
    	StringBuilder initjs= new StringBuilder();
   	    initjs.append("<script type=\"text/javascript\">");
   	    initjs.append("$(function(){");

   	    if(agmlt.size()>0)
	    {

         // movejs.append("<div class=\"show-pop\" style=\"width:90px; height:120px; background-color:#00FF99\"></div>");
          // 图片横竖  1 横  2  竖
          String imagelx = "1";
		 for(int i=0;i<agmlt.size();i++)
		 {
			 AllGroupModel cm = agmlt.get(i);
			 int gsx = getRandomNum(-20,300);
	    	 int gsy = getRandomNum(-20,300);
	    	 String uuid = UUID.randomUUID().toString().trim().replace(" ", "");
	    	 //Gapleft判断xy轴有数据
	    	 /*if(cm.getGapleft()== null )
	    	 {
    	    	 initjs.append("$('#"+cm.getId().toString().trim().replace(" ", "")+"td').draggabilly({ containment: true });");
	    	 }
	    	 else
	    	 {*/
    	    	 initjs.append("$('#"+cm.getId().toString().trim().replace(" ", "")+"td').draggabilly({ containment: true });");
	    	// }

		 }
	 }
   		 initjs.append("}); </script>");
   		 return initjs.toString();

    }

    //拖动电压
    public String dragdy(List<AllGroupModel> agmlt,String bianliang,String powerStationId,int ysize,int nsize,int zsize,List<AllGroupModel> yagmlt,List<AllGroupModel> nagmlt,List<AllGroupModel> zagmlt)
    {
    	 StringBuilder movedyjs= new StringBuilder();
    	 String greenbj=bianliang+"images\\/zj_dyh.png";
    	 String shubj=bianliang+"images\\/xtdt_dyst.png";
    	 movedyjs.append("<div id=\"panel2\" class=\"container\">");
    	 /*if(agmlt.size()>0)
    	 {
	          // 图片横竖  1 横  2  竖
              String imagelx = "1";
              char divtop ='0';
              char divleft='0';
    		 for(int i=0;i<agmlt.size();i++)
    		 {
    			 AllGroupModel cm = agmlt.get(i);
    			 if(CommonUtil.isEmpty(cm.getDivleft())==true )
    	    	 {
    	    		 divleft = cm.getDivleft().charAt(cm.getDivleft().length()-1);
    	    		 divtop = cm.getDivtop().charAt(cm.getDivtop().length()-1);
    	    	 }
    			 //if(cm.getGenre().equals("4"))
    			 //{
    				 int gsx = getRandomNum(-20,100);
        	    	 int gsy = getRandomNum(-20,100);

        	    	 if(cm.getGapleft()== null )
        	    	 {
        	    		 movedyjs.append("<div class=\"show-pop-table show-pop box\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dytd\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dytd')\" style=\"left: "+gsx+"px; top: "+gsy+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dy\" class=\"total-centered\"  onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','4','2')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
            	    	// initjs.append("$('#"+cm.getId().toString().trim().replace(" ", "")+"td').draggabilly({ containment: true });");
        	    	 }
        	    	 else
        	    	 {
        	    		 movedyjs.append("<div class=\"show-pop-table show-pop box\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dytd\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dytd')\" style=\"left: "+Integer.parseInt((Integer.parseInt(cm.getGapleft())*10-5000)+"")+"px; top: "+Integer.parseInt((Integer.parseInt(cm.getGaptop())*10-5000)+"")+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dy\" class=\"total-centered\"  onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','4','2')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
            	    	// initjs.append("$('#"+cm.getId().toString().trim().replace(" ", "")+"td').draggabilly({ containment: true });");
        	    	 }
        	    	 //2  电压   判断数据库里拖动记录  直接xy轴赋值
        	    	 if( cm.getQb().equals("2") )
        	    	 {
	        	    	 if(cm.getGapleft()== null )
	        	    	 {
	        	    		 movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"left: "+gsx+"px; top: "+gsy+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
	            	    	// initjs.append("$('#"+cm.getId().toString().trim().replace(" ", "")+"td').draggabilly({ containment: true });");
	        	    	 }
	        	    	 else
	        	    	 {   int x=0,y=0;
	        	    		 if(Integer.parseInt(divleft+"")>=5)
	        	    		 {
	        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")-Integer.parseInt(divleft+"");
	        	    		 }
	        	    		 else
	        	    		 {
	        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")+Integer.parseInt(divleft+"");
	        	    		 }
	        	    		 if(Integer.parseInt(divtop+"") >=5)
	        	    		 {
	        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")-Integer.parseInt(divtop+"");
	        	    		 }
	        	    		 else
	        	    		 {
	        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")+Integer.parseInt(divtop+"");
	        	    		 }
	        	    		 if(null != cm.getHv()&& cm.getHv().equals("1"))
	        	    		 {
	        	    		  //movejs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"margin-left:"+x+"px; margin-top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\"  onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+shubj+"\');background-size:100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
	        	    		   movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
	            	    	// initjs.append("$('#"+cm.getId().toString().trim().replace(" ", "")+"td').draggabilly({ containment: true });");
	        	    		 }
	        	    		 else
	        	    		 {
	        	    			 movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
	        	    		 }
	        	    	}
        	    	 //}
    			// }


    		 }
    	 }	 */
	    	 //排序采集器
	    	 ordercollectordy(agmlt, bianliang, powerStationId, ysize, nsize, zsize,movedyjs,yagmlt);
	    	 //排序逆变器
	    	 orderinverterdy(agmlt, bianliang, powerStationId, ysize, nsize, zsize,movedyjs,nagmlt);
	    	 //排序组件
	    	 ordersubdy(agmlt, bianliang, powerStationId, ysize, nsize, zsize,movedyjs,zagmlt);
    	     movedyjs.append("</div>");
    		 return movedyjs.toString();

    }
  //生成电压函数
    public String returndyfunction(List<AllGroupModel> agmlt)
    {
    	StringBuilder initjs= new StringBuilder();
   	    initjs.append("<script type=\"text/javascript\">");
   	    initjs.append("$(function(){");

	     if(agmlt.size()>0)
		 {

	         // movejs.append("<div class=\"show-pop\" style=\"width:90px; height:120px; background-color:#00FF99\"></div>");
	          // 图片横竖  1 横  2  竖
	          String imagelx = "1";
			 for(int i=0;i<agmlt.size();i++)
			 {
				 AllGroupModel cm = agmlt.get(i);
				 int gsx = getRandomNum(-20,100);
		         int gsy = getRandomNum(-20,100);
		        /* if(cm.getQb() == null ||cm.getQb().equals("2"))
		         {*/
		             /*if(cm.getGapleft()== null )
			         {
		                 initjs.append("$('#"+cm.getId().toString().trim().replace(" ", "")+"dyf').draggabilly({ containment: true });");
			         }
			         else
			         {*/
		                 initjs.append("$('#"+cm.getId().toString().trim().replace(" ", "")+"dyf').draggabilly({ containment: true });");
			        // }
		        // }


			 }
		 }
   		 initjs.append("}); </script>");
   		 return initjs.toString();

    }
    //拖动电流
    public String dragdl(List<AllGroupModel> agmlt,String bianliang,String powerStationId,int ysize,int nsize,int zsize,List<AllGroupModel> yagmlt,List<AllGroupModel> nagmlt,List<AllGroupModel> zagmlt)
    {
    	 StringBuilder movedyjs= new StringBuilder();
    	 String greenbj=bianliang+"images\\/zj_dlh.png";
    	 String shubj=bianliang+"images\\/xtst_dlst.png";
    	 movedyjs.append("<div id=\"panel3\" class=\"container\">");
    	/* if(agmlt.size()>0)
    	 {
	          // 图片横竖  1 横  2  竖
    		 char divtop ='0';
             char divleft='0';
              String imagelx = "1";
    		 for(int i=0;i<agmlt.size();i++)
    		 {
    			 AllGroupModel cm = agmlt.get(i);
    			 if(cm.getGenre().equals("4"))
    			 {
    			 if(CommonUtil.isEmpty(cm.getDivleft())==true)
    	    	 {
    	    		 divleft = cm.getDivleft().charAt(cm.getDivleft().length()-1);
    	    		 divtop = cm.getDivtop().charAt(cm.getDivtop().length()-1);
    	    	 }
    				 int gsx = getRandomNum(-20,100);
        	    	 int gsy = getRandomNum(-20,100);
        	    	 if(cm.getQb().equals("3") )
        	    	 {
	        	    	 if(cm.getGapleft()== null )
	        	    	 {
	        	    		 movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute;left: "+gsx+"px; top: "+gsy+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\"  onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','3')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
	            	    	// initjs.append("$('#"+cm.getId().toString().trim().replace(" ", "")+"td').draggabilly({ containment: true });");
	        	    	 }
	        	    	 else
	        	    	 {
	        	    		 int x=0,y=0;
	        	    		 if(Integer.parseInt(divleft+"")>=5)
	        	    		 {
	        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")-Integer.parseInt(divleft+"");
	        	    		 }
	        	    		 else
	        	    		 {
	        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")+Integer.parseInt(divleft+"");
	        	    		 }
	        	    		 if(Integer.parseInt(divtop+"") >=5)
	        	    		 {
	        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")-Integer.parseInt(divtop+"");
	        	    		 }
	        	    		 else
	        	    		 {
	        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")+Integer.parseInt(divtop+"");
	        	    		 }
	        	    		 if(null != cm.getHv()&& cm.getHv().equals("1"))
	        	    		 {
	        	    		//movejs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"margin-left:"+x+"px; margin-top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\"  onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+shubj+"\');background-size:100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
	        	    		   movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\"  onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','3')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
	            	    	// initjs.append("$('#"+cm.getId().toString().trim().replace(" ", "")+"td').draggabilly({ containment: true });");
	        	    	     }
	        	    		 else
	        	    		 {
	        	    			 movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\"  onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','3')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
	        	    		 }
	        	         }
        	    	 //}
    			// }


    		 }*/
    	// }
    	 //排序采集器
    	 ordercollectordl(agmlt, bianliang, powerStationId, ysize, nsize, zsize,movedyjs,yagmlt);
    	 //排序逆变器
    	 orderinverterdl(agmlt, bianliang, powerStationId, ysize, nsize, zsize,movedyjs,nagmlt);
    	 //排序组件
    	 ordersubdl(agmlt, bianliang, powerStationId, ysize, nsize, zsize,movedyjs,zagmlt);
    	     movedyjs.append("</div>");
    		 return movedyjs.toString();

    }
  //生成电压函数
    public String returndlfunction(List<AllGroupModel> agmlt)
    {
    	StringBuilder initjs= new StringBuilder();
   	    initjs.append("<script type=\"text/javascript\">");
   	    initjs.append("$(function(){");

   	 if(agmlt.size()>0)
	 {

         // movejs.append("<div class=\"show-pop\" style=\"width:90px; height:120px; background-color:#00FF99\"></div>");
          // 图片横竖  1 横  2  竖
          String imagelx = "1";
		 for(int i=0;i<agmlt.size();i++)
		 {
			 AllGroupModel cm = agmlt.get(i);
			 int gsx = getRandomNum(-20,300);
	    	 int gsy = getRandomNum(-20,300);
	    	 /*if(cm.getGenre().equals("4"))
	    	 {
	    		 if(cm.getGapleft()== null )
		    	 {
	    	    	 initjs.append("$('#"+cm.getId().toString().trim().replace(" ", "")+"dlf').draggabilly({ containment: true });");
		    	 }
		    	 else
		    	 {*/
	    	    	 initjs.append("$('#"+cm.getId().toString().trim().replace(" ", "")+"dlf').draggabilly({ containment: true });");
		    /*	 }
	    	 }*/


		 }
	 }
   		 initjs.append("}); </script>");
   		 return initjs.toString();

    }
    //提示框
    public  String returntsk(List<AllGroupModel> agmlt)
    {
    	StringBuilder showview= new StringBuilder();
		// 电流  电压  功率   4:组件
    	for(int i=0;i<agmlt.size();i++)
		 {
			AllGroupModel cm = agmlt.get(i);
			if(CommonUtil.isEmpty(cm.getChipid()) && cm.getGenre().equals("3"))
			{
				showview.append("<div id=\""+cm.getId()+"tck\" style=\"display:none;\">");
				showview.append("<ul class=\"list-group\">");
				showview.append("<li class=\"list-group-item\">组件编号:"+cm.getChipid()+"</li>");
				BigDecimal powerOut=new BigDecimal(cm.getOutputCurrent()/1000.0*cm.getOutputVoltage()/1000.0).setScale(2 , BigDecimal.ROUND_HALF_UP);
				showview.append("<li class=\"list-group-item\">输出功率："+powerOut+" W</li>");
				BigDecimal powerInput=new BigDecimal(cm.getInputCurrent()/1000.0*cm.getInputVoltage()/1000.0).setScale(2 , BigDecimal.ROUND_HALF_UP);
				showview.append("<li class=\"list-group-item\">输入功率："+powerInput+" W</li>");
				showview.append("<li class=\"list-group-item\">输出电流:"+cm.getOutputCurrent()+" mA</li>");
				showview.append("<li class=\"list-group-item\">输出电压:"+cm.getOutputVoltage()+" mV</li>");
				showview.append("<li class=\"list-group-item\">输入电流:"+cm.getInputCurrent()+" mA</li>");
				showview.append("<li class=\"list-group-item\">输入电压:"+cm.getInputVoltage()+" mV</li>");
				showview.append("<li class=\"list-group-item\">组件温度:"+cm.getComponentTemperature()+"</li>");
				showview.append("</ul>");
				showview.append("</div>");
			}

		 }
    	return showview.toString();
    }
    /***
     * 报表全年视图日历页面
     * @param model 系统(电站)实体
     * @return
     */
    @RequestMapping("reportForm.htm")
    public String reportForm(PowerStationModel model , HttpServletRequest request)
    {
    	String powerStationId = request.getParameter("pid");
    	String selecttime = request.getParameter("selecttime");
    	String khly = request.getParameter("khly");
    	List<ComponentHourModel> hourlt =null;
	    List<ComponentDayModel>  lt =null;
	    request.setAttribute("powerStationId", powerStationId);
	    request.setAttribute("xyeardate", "[]");
		request.setAttribute("xyeardata", "[]");
		if(selecttime==null){
			selecttime= DateUtils.timestampToDate(new Date().getTime(),DateUtils.DATE_SMALL_STR);
		}
	    if(selecttime!=null && selecttime.length()>7){
            System.out.println(selecttime.substring(0,6));
		    selecttime=selecttime.substring(0,selecttime.lastIndexOf("-"));
		    request.setAttribute("selecttime", DateUtils.timestampToDate(DateUtils.parse(selecttime,"yyyy-MM").getTime(),"yyyy-MM"));
        }

//    	if(khly.equals("xs"))
//    	{
//    		String  xyeardate= "";
//    	    String  xyeardata= "";
//    		Map<String, Object> map = new HashMap<String, Object>();
//            map.put("powerStationId",powerStationId );
//		    map.put("selecttime",selecttime );
//            if(selecttime!=null && selecttime.length()>7){
//            	System.out.println(selecttime.substring(0,7));
//	            map.put("selecttime",selecttime.substring(0,7) );
//            }
////    		hourlt = componentDayService.queryComponentByXs(map);
////        	xyeardate=	getHourdate(hourlt);
////        	xyeardata = getHourdata(hourlt);
//
//
//		    lt = componentDayService.queryComponentByDay(map);
//		    xyeardate=	getdate(lt);
//		    xyeardata = getdata(lt);
//        	request.setAttribute("xyeardate", xyeardate);
//            request.setAttribute("xyeardata", xyeardata);
//
//		    request.setAttribute("powerStationId", powerStationId);
//    	}
//    	else
//    	{
//    		request.setAttribute("model" , model );
//            Map<String, Object> map = new HashMap<String, Object>();
//            map.put("powerStationId",powerStationId );
//            List<ComponentDayModel> yearlt = componentDayService.queryComponentByYear(map);
//             StringBuilder xyeardate = new StringBuilder();
//             StringBuilder xyeardata = new StringBuilder();
//             xyeardate.append("[");
//             xyeardata.append("[");
//             for(int i=0;i<yearlt.size();i++)
//             {
//            	 ComponentDayModel cdm = yearlt.get(i);
//            	 if(i+1==yearlt.size())
//            	 {
//            		 xyeardate.append("'"+cdm.getCreateTimeCh()+"'");
//            	 }
//            	 else
//            	 {
//            		 xyeardate.append("'"+cdm.getCreateTimeCh()+"',");
//            	 }
//            	 if(i+1==yearlt.size())
//            	 {
//            		 xyeardata.append("'"+cdm.getKwh()+"'");
//            	 }
//            	 else
//            	 {
//            		 xyeardata.append("'"+cdm.getKwh()+"',");
//            	 }
//
//             }
//             xyeardate.append("]");
//             xyeardata.append("]");
//             request.setAttribute("xyeardate", xyeardate);
//             request.setAttribute("xyeardata", xyeardata);
//             request.setAttribute("powerStationId", powerStationId);
//    	}

    	//String powerStationId = request.getParameter("id");
//    	String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
         return prefix + "calendar_report";
    }
    public int getyear(int year)
    {
    	 SimpleDateFormat format = new SimpleDateFormat("yyyy");
 		Calendar c = Calendar.getInstance();
 		c.setTime(new Date());
 		c.add(Calendar.YEAR, -year);
 		 int years = c.get(Calendar.YEAR);

    	return years;
    }
    public static int getDaysOfMonth(Date date)
    {
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(date);
    	return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }
    //报表查询数据
    @RequestMapping("finddata.htm")
    @ResponseBody
    public void finddata(HttpServletRequest request,HttpServletResponse response){
    	String pid = request.getParameter("pid");
    	String selecttime = request.getParameter("selecttime");
    	String khly = request.getParameter("khly");
    	Map<String, Object> map = new HashMap<String, Object>();
        map.put("powerStationId",pid );
        map.put("selecttime",selecttime );
        List<ComponentDayModel>  lt =null;
        String  xyeardate= "";
        String  xyeardata= "";
        if(khly.equals("sp"))
        {
        	lt = componentDayService.queryComponentByMonth(map);
        	xyeardate=	getdate(lt);
        	xyeardata = getdata(lt);
        }
        if(khly.equals("jf"))
        {
        	lt = componentDayService.queryComponentByDay(map);
	        xyeardate=	getdate(lt);
	        xyeardata = getdata(lt);
        }

        if(khly.equals("hy"))
        {
        	lt = componentDayService.queryComponentByYear(map);
        	xyeardate=	getdate(lt);
        	xyeardata = getdata(lt);
        }
	    JSONObject obj = new JSONObject();
	    obj.put("xyeardate", xyeardate);
	    obj.put("xyeardata", xyeardata);
	    obj.put("KwhName", "发电量");
	    obj.put("Kwh", " KW·H");
	    if(khly.equals("xs"))
	    {
		    if(selecttime==null ||selecttime.length()<1){
			    selecttime=DateUtils.timestampToDate(new Date().getTime(),DateUtils.DATE_SMALL_STR);
		    }
		    //原来是按照小时的，现在改成实时的
		    List<String> dateString=new ArrayList<>();
		    List<String> dataString=new ArrayList<>();
		    List<ComponentCollectBatchNo> minuteModelList =configureService.queryComponentCollectBatchNo(pid,selecttime);
		    if(minuteModelList!=null && minuteModelList.size()>0){
			    for (ComponentCollectBatchNo componentDayModel:minuteModelList){
				    try{
					    dateString.add(DateUtils.timestampToDate(DateUtils.parse(componentDayModel.getBatchNo(),DateUtils.DATE_All_KEY_STR).getTime(),"HH:mm"));
					    if(componentDayModel.getTimeStep()==null||componentDayModel.getTimeStep()<1){
						    componentDayModel.setTimeStep(1);
					    }
					    BigDecimal power=new BigDecimal(componentDayModel.getWh().doubleValue()*60/componentDayModel.getTimeStep());
					    dataString.add( power.setScale(2 , BigDecimal.ROUND_HALF_UP)+"");//功率
				    }catch (Exception e){
					    e.printStackTrace();
				    }
			    }
		    }
		    obj.put("KwhName", "功率");
		    obj.put("Kwh", " W");
		    obj.put("xyeardate", dateString);
		    obj.put("xyeardata", dataString);
	    }
        insertlog("查询报表数据");
       String a = obj.toString();
       System.out.println(a);
       JsonUtil.strOut(response,a);
    }
   public String getdate(List<ComponentDayModel>  lt)
   {
	   StringBuilder xyeardate = new StringBuilder();
	   xyeardate.append("[");
	   for(int i=0;i<lt.size();i++)
       {
      	 ComponentDayModel cdm = lt.get(i);
      	 if(i+1==lt.size())
      	 {
      		 xyeardate.append("'"+cdm.getCreateTimeCh()+"'");
      	 }
      	 else
      	 {
      		 xyeardate.append("'"+cdm.getCreateTimeCh()+"',");
      	 }
       }
	   xyeardate.append("]");
	   return xyeardate.toString();
   }
   public String getdata(List<ComponentDayModel>  lt)
   {
	   StringBuilder xyeardata = new StringBuilder();
	   xyeardata.append("[");
	   for(int i=0;i<lt.size();i++)
       {
      	 ComponentDayModel cdm = lt.get(i);
      	if(i+1==lt.size())
		   	 {
		   		 xyeardata.append("'"+cdm.getKwh()+"'");
		   	 }
		   	 else
		   	 {
		   		 xyeardata.append("'"+cdm.getKwh()+"',");
		   	 }
       }
	   xyeardata.append("]");
	   return xyeardata.toString();
   }
   public String getHourdate(List<ComponentHourModel>  lt)
   {
	   StringBuilder xyeardate = new StringBuilder();
	   xyeardate.append("[");
	   for(int i=0;i<lt.size();i++)
       {
		   ComponentHourModel cdm = lt.get(i);
      	 if(i+1==lt.size())
      	 {
      		 xyeardate.append("'"+cdm.getCreateTimeCh()+"'");
      	 }
      	 else
      	 {
      		 xyeardate.append("'"+cdm.getCreateTimeCh()+"',");
      	 }
       }
	   xyeardate.append("]");
	   return xyeardate.toString();
   }
   public String getHourdata(List<ComponentHourModel>  lt)
   {
	   StringBuilder xyeardata = new StringBuilder();
	   xyeardata.append("[");
	   for(int i=0;i<lt.size();i++)
       {
		   ComponentHourModel cdm = lt.get(i);
      	if(i+1==lt.size())
		   	 {
		   		 xyeardata.append("'"+cdm.getKwh()+"'");
		   	 }
		   	 else
		   	 {
		   		 xyeardata.append("'"+cdm.getKwh()+"',");
		   	 }
       }
	   xyeardata.append("]");
	   return xyeardata.toString();
   }
 //拖动新增修改数据
   @RequestMapping("moveupdatedata.htm")
   @ResponseBody
   public void moveupdatedata(HttpServletRequest request,HttpServletResponse response){

   	String pid = request.getParameter("pid");//电站id
   	String id = request.getParameter("id");//组件id
   	String lx = request.getParameter("lx");//1 云终端   2  逆变器   3 组串  4  组件
   	String lb = request.getParameter("lb");// 1  竖   2 横
	String qb = request.getParameter("qb");// 1 功率   2  电压  3  电流
   	String top = request.getParameter("divtop").replace("px", "");
   	String left = request.getParameter("divleft").replace("px", "");
   	String parentwidth = request.getParameter("parentwidth").replace("px", "");
   	String parentheight = request.getParameter("parentheight").replace("px", "");
   	Map<String, Object> map = new HashMap<String, Object>();
    map.put("pid",pid );
    map.put("zjid",id );
    map.put("lx",lx );
    map.put("lb",lb );
   // map.put("qb",qb);
//    int xz=0;
//    int yz =0;
//    int yt = (Integer.parseInt(top))/10;
//    int xl = (Integer.parseInt(left))/10;
//    char divtop ='0';
//    char divleft='0';
//	 divtop= top.charAt(top.length()-1);
//	 divleft = left.charAt(left.length()-1);
//	if(Integer.parseInt(divleft+"")>=5)
//	{
//		xz = xl+1;
//	}
//	else
//	{
//		xz=xl;
//	}
//	if(Integer.parseInt(divtop+"")>=5)
//	{
//		yz = yt+1;
//	}
//	else
//	{
//		yz = yt;
//	}
    ChangeModel changeModel =  changeService.queryChange(map);
    ChangeModel cm = null ;
    if(changeModel == null)
    {
    	cm = new ChangeModel();
    	cm.setId(UUID.randomUUID().toString());
    	cm.setPowerId(pid);
    	cm.setLb(lb);
    	cm.setLx(lx);
	    if(left!=null){
		    int intX= Integer.parseInt(left);
		    cm.setXz(String.valueOf(Math.round(intX/10.0)));
	    }
	    if(top!=null){
		    int intTop= Integer.parseInt(top);
		    cm.setYz(String.valueOf(Math.round(intTop/10.0)));
	    }
//    	cm.setQb(qb);
    	cm.setZjid(id);
    	cm.setWidth(parentwidth);
    	cm.setHeight(parentheight);
    	cm.setDivtop(top);
    	cm.setDivleft(left);
    	cm.setLaystatus("1");
    	changeService.saveChange(cm);

    }
    else
    {
    	cm = new ChangeModel();
    	cm.setPowerId(pid);
    	cm.setLb(lb);
    	cm.setLx(lx);
    	if(left!=null){
		   int intX= Integer.parseInt(left);
		    cm.setXz(String.valueOf(Math.round(intX/10.0)));
	    }
	    if(top!=null){
		    int intTop= Integer.parseInt(top);
		    cm.setYz(String.valueOf(Math.round(intTop/10.0)));
	    }
    	cm.setZjid(id);
//    	cm.setQb(qb);
    	cm.setWidth(parentwidth);
    	cm.setHeight(parentheight);
    	cm.setDivtop(top);
    	cm.setDivleft(left);
    	changeService.updateChange(cm);

    }
    insertlog("记录拖动位置数据");
   }

   public static int getRandomNum(int smallistNum, int BiggestNum) {
	    Random random = new Random();
	    return (Math.abs(random.nextInt()) % (BiggestNum - smallistNum + 1))+ smallistNum;
	}

   //报表查询数据
   @RequestMapping("exportdata.htm")
   @ResponseBody
   public void exportdata(HttpServletRequest request,HttpServletResponse response){
	   insertlog("导出报表数据");
   	System.out.println("");
   	String pid = request.getParameter("pid");
   	String selecttime = request.getParameter("selecttime");
   	String khly = request.getParameter("khly");
   	Map<String, Object> map = new HashMap<String, Object>();
       map.put("powerStationId",pid );
       map.put("selecttime",selecttime );
       List<ComponentDayModel>  lt =null;
       List<ComponentHourModel> hourlt =null;

      // map.put("khly",khly );
       if(khly.equals("sp"))
       {
       	lt = componentDayService.queryComponentByMonth(map);

       }
       if(khly.equals("jf"))
       {
       	lt = componentDayService.queryComponentByDay(map);

       }
       if(khly.equals("xs"))
       {
       	hourlt = componentDayService.queryComponentByXs(map);

       }
       if(khly.equals("hy"))
       {
       	lt = componentDayService.queryComponentByYear(map);

       }
       response.reset(); //清除buffer缓存

       // 指定下载的文件名，浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
       // 所以我们用GBK解码，ISO-8859-1来编码，在浏览器那边会反过来执行。
       // 指定下载的文件名
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestring = formatter.format(new Date());
        String filename = "报表数据"+timestring+".xlsx";
        try {
			response.setHeader("Content-Disposition","attachment;filename="+new String(filename.getBytes(),"iso-8859-1"));
		} catch (UnsupportedEncodingException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
        //导出Excel对象

       response.setHeader("Pragma", "no-cache");
       response.setHeader("Cache-Control", "no-cache");
       response.setDateHeader("Expires", 0);
       XSSFWorkbook workbook=null;
       //导出Excel对象
       try {
		try {
			if(khly.equals("xs"))
		    {

				workbook = this.exportExcelInfoHour(hourlt);
		    }
			else
			{
				workbook = this.exportExcelInfo(lt);
			}

		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	} catch (InvocationTargetException e1) {
		// TODO Auto-generated catch block
		e1.printStackTrace();
	} catch (ClassNotFoundException e1) {
		// TODO Auto-generated catch block
		e1.printStackTrace();
	} catch (org.apache.xmlbeans.impl.regex.ParseException e1) {
		// TODO Auto-generated catch block
		e1.printStackTrace();
	} catch (IllegalAccessException e1) {
		// TODO Auto-generated catch block
		e1.printStackTrace();
	} catch (IntrospectionException e1) {
		// TODO Auto-generated catch block
		e1.printStackTrace();
	}
       OutputStream output;
       try {
           output = response.getOutputStream();
           BufferedOutputStream bufferedOutPut = new BufferedOutputStream(output);
           bufferedOutPut.flush();
           workbook.write(bufferedOutPut);
           bufferedOutPut.close();
           output.flush();
           output.close();

       } catch (IOException e) {
           e.printStackTrace();
       }
  // return a;
   }

	public XSSFWorkbook exportExcelInfo(List<ComponentDayModel>  lt) throws InvocationTargetException, ClassNotFoundException,
			IntrospectionException, ParseException, IllegalAccessException {
		// TODO Auto-generated method stub
		  Map<String,Object> mp = new HashMap<>();

	    List<ComponentDayModel> lst =lt;
         //List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();//sysExcelDao.findUserObject();
  		//System.out.println(list);
  		List<ExcelBean> excel = new ArrayList<>();
  		Map<Integer,List<ExcelBean>> map = new LinkedHashMap<>();
  		//设置标题栏
  		excel.add(new ExcelBean("DATETIME","createTimeCh",0));
  		excel.add(new ExcelBean("Power Factory(kWh)","kwh",0));


  		map.put(0,excel);
        String sheetName = "报表数据";
       //调用ExcelUtil的方法
       // xssfWorkbook = ExcelTools.createExcelFile(WarningModel.class, list, map, sheetName);
       //调用ExcelUtil方法
        XSSFWorkbook   xssfWorkbook = ExcelExport.createExcelFile(ComponentDayModel.class, lst, map, sheetName);
       return xssfWorkbook;
	}
	public XSSFWorkbook exportExcelInfoHour(List<ComponentHourModel>  lt) throws InvocationTargetException, ClassNotFoundException,
		IntrospectionException, ParseException, IllegalAccessException {
		// TODO Auto-generated method stub
		  Map<String,Object> mp = new HashMap<>();

		List<ComponentHourModel> lst =lt;
		 //List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();//sysExcelDao.findUserObject();
			//System.out.println(list);
			List<ExcelBean> excel = new ArrayList<>();
			Map<Integer,List<ExcelBean>> map = new LinkedHashMap<>();
			//设置标题栏
			excel.add(new ExcelBean("DATETIME","createTimeCh",0));
			excel.add(new ExcelBean("Power Factory(kWh)","kwh",0));

			map.put(0,excel);
		String sheetName = "报表警告数据";
		//调用ExcelUtil的方法
		// xssfWorkbook = ExcelTools.createExcelFile(WarningModel.class, list, map, sheetName);
		//调用ExcelUtil方法
		XSSFWorkbook   xssfWorkbook = ExcelExport.createExcelFile(ComponentHourModel.class, lst, map, sheetName);
		return xssfWorkbook;
	}
	// 查询采集表,获取弹出框数据
	public List<ComponentCollectModel> returnTckData(String powerStationId)
	{
	    List<ComponentCollectModel> comCollectList = new ArrayList<>();
		List<ComponentModel> componentList =configureService.queryComponentListByPowerStationId(powerStationId);
		if(componentList==null){
			return null;
		}
		int count=0;
		for(ComponentModel componentModel:componentList){
			if(componentModel.getIsAuth()!=null && componentModel.getIsAuth()==2){
				count++;
			}
		}
		List<ComponentCollect> componentCollectList = componentCollectService.queryComponentCollectByLast(powerStationId,count,true);
        if(null != componentCollectList)
        {
        	List<ComponentCollectBatchNo> list= componentCollectBatchNoService.doDataToBatch(componentList,componentCollectList,powerStationId);
    		if(list!=null && list.size()>0){
    			comCollectList=list.get(0).getList();
    		}
        }
		return comCollectList;
	}
		//ajax 返回电压数据
		@RequestMapping("returndy.htm")
		@ResponseBody
		public void returndy(HttpServletRequest request,HttpServletResponse response)
		{
			   JSONObject obj = new JSONObject();
			   List<AllGroupModel> agmlt = new ArrayList<AllGroupModel>();
			   List<AllGroupModel> yagmlt = new ArrayList<AllGroupModel>();
		       List<AllGroupModel> nagmlt = new ArrayList<AllGroupModel>();
		       List<AllGroupModel> zagmlt = new ArrayList<AllGroupModel>();
			   String pid = request.getParameter("pid");
			   //获取组件数据
		    	 List<ComponentModel> lt = componentService.queryComponentListByData(pid, null);
		    	 AllGroupModel agm = null;
		    	 for(int p=0;p<lt.size();p++)
		    	 {
		    		 agm = new AllGroupModel();
		    		 ComponentModel cm = lt.get(p);
		    		//获取组件详情

		    		 agm.setId(cm.getId());
		    		 agm.setPowerId(cm.getPowerStationId());
		    		 agm.setGapleft(cm.getXz());
		    		 agm.setGaptop(cm.getYz());
		    		 agm.setHv(cm.getPosition());
		    		 if(CommonUtil.isEmpty(cm.getChipId())==true)
		    		 {
		    			 agm.setAllname("组件芯片ID后4位:"+cm.getChipId().substring(cm.getChipId().length()-4, cm.getChipId().length()));
		    		 }
		    		 else
		    		 {
		    			 agm.setAllname("组件芯片ID:无");
		    		 }
		    		 agm.setChipid(cm.getChipId());

		    		 agm.setGenre("3");
		    		 agm.setQb(cm.getQb());

		    		 agm.setDivleft(cm.getDivleft());
		    		 agm.setDivtop(cm.getDivtop());
		    		 agmlt.add(agm);
		    		 zagmlt.add(agm);
		    	 }
		    	 Map<String, Object> map = new HashMap<String, Object>();
		 		 map.put("powerStationId",pid);
		 		 //暂时不传值
		 		// map.put("ymd",ymd);
		    	 //获取 逆变器数据
		    	 List<InverterModel> Imlt = inverterService.queryInverterListByData(map);
		    	 for(int p=0;p<Imlt.size();p++)
		    	 {
		    		 agm = new AllGroupModel();
		    		 InverterModel cm = Imlt.get(p);
		    		 agm.setId(cm.getId());
		    		 agm.setPowerId(cm.getPowerStationId());
		    		 agm.setGapleft(cm.getXz());
		    		 agm.setGaptop(cm.getYz());
		    		 agm.setHv(cm.getPosition());
		    		 agm.setAllname(cm.getInverterName());
		    		 agm.setGenre("2");
		    		 agmlt.add(agm);
		    		 nagmlt.add(agm);
		    	 }

		    	 //获取云终端数据
		    	 List<CloudTerminalModel> ctlt = cloudTerminalService.queryCloudListByData(map);
		    	 for(int p=0;p<ctlt.size();p++)
		    	 {
		    		 agm = new AllGroupModel();
		    		 CloudTerminalModel cm = ctlt.get(p);
		    		 agm.setId(cm.getId());
		    		 agm.setPowerId(cm.getPowerStationId());
		    		 agm.setGapleft(cm.getXz());
		    		 agm.setGaptop(cm.getYz());
		    		 agm.setHv(cm.getPosition());
		    		 agm.setAllname(cm.getCloudName());
		    		 agm.setGenre("1");
		    		 agmlt.add(agm);
		    		 yagmlt.add(agm);
		    	 }
		    	 String bianliang=PageUtil.getBasePath(request);
		    	 List<AllGroupModel> zhlt = new  ArrayList<AllGroupModel>();
		    	 AllGroupModel agmodel = null;
		    	 String showview =returntsk(zhlt);

		    	 int yq = ctlt.size()%10;
				 int ylist=0;
				 if(yq>0)
				 {
					 ylist=ctlt.size()/10+1;
				 }
				 else
				 {
					 ylist=ctlt.size()/10;
				 }
				 int nlist=0;
				 int nq = Imlt.size()%10;
				 if(nq>0)
				 {
					 nlist=Imlt.size()/10+1;
				 }
				 else
				 {
					 nlist=Imlt.size()/10;
				 }
				 int zlist=0;
				 int zq = lt.size()%10;
				 if(zq>0)
				 {
					 zlist=lt.size()/10+1;
				 }
				 else
				 {
					 zlist=lt.size()/10;
				 }
		    	 String jg= returnzj(agmlt,bianliang,pid,ylist,nlist,zlist, yagmlt, nagmlt, zagmlt);// movejs.toString();
		    	 //功率弹出框
		    	 obj.put("showview", showview.toString());
		    	 //功率拖动
		    	 obj.put("jsjg", jg);
		    	 //初始化脚本
		    	 obj.put("initjs", returnfunction(agmlt));
		    	 //功率不能移动
		    	 obj.put("moveblock", returnfixedly(agmlt,bianliang,pid).toString());
		    	 // 电流不可移动
		    	 obj.put("showdlview", returndlfixedly(lt,bianliang,pid).toString());
		    	// 电压不可移动
		    	 obj.put("showdyview", returndyfixedly(lt,bianliang,pid).toString());
		    	 //电压可拖动
		    	 obj.put("showdydragview", dragdy(agmlt,bianliang,pid,ylist,nlist,zlist, yagmlt, nagmlt, zagmlt).toString());
		    	 obj.put("showdydragjs", returndyfunction(agmlt).toString());
		    	//电流可拖动
		    	 obj.put("showdldragview", dragdl(agmlt,bianliang,pid,ylist,nlist,zlist, yagmlt, nagmlt, zagmlt).toString());
		    	 obj.put("showdldragjs", returndlfunction(agmlt).toString());
		    	 obj.put("cmlt" , lt);

		         obj.put("powerid",pid );

		      // obj.put("list", "[list1111]"+pid);
		       String a = obj.toString();
		       System.out.println(a);
		       JsonUtil.strOut(response,a);
		// return a;
		}
		//排序采集器
		public  StringBuilder ordercollector(List<AllGroupModel> agmlt,String bianliang,String powerStationId,int ysize,int nsize,int zsize, StringBuilder movejs,List<AllGroupModel> yagmlt)
		{
			 String greenbj=bianliang+"images\\/zj_gllh.png";
	    	 String shubj=bianliang+"images\\/xtst_gll.png";
			 if(agmlt.size()>0)
	    	 {
		         // movejs.append("<div class=\"show-pop\" style=\"width:90px; height:120px; background-color:#00FF99\"></div>");
		         // 图片横竖
				 int w=1;
	    	 /* for(int w=1;w<=ysize;w++)
	    	  {*/
	             String imagelx = "1";
	             char divtop ='0';
	             char divleft='0';
	             int  a =agmlt.size()/10;
	    		 for(int i=0;i<yagmlt.size();i++)
	    		 {
	    			 AllGroupModel cm = yagmlt.get(i);
	    			 int gsx = getRandomNum(0,100);
	    	    	 int gsy = getRandomNum(0,100);

	    	    	 if(CommonUtil.isEmpty(cm.getDivleft())==true )
	    	    	 {
	    	    		 divleft = cm.getDivleft().charAt(cm.getDivleft().length()-1);
	    	    		 divtop = cm.getDivtop().charAt(cm.getDivtop().length()-1);
	    	    	 }
	    	    	 if(cm.getGapleft()== null )
	    	    	 {
		    			 if(i<11*(w)&& w==1)
	    	    		 {
		    				 System.out.println((i-1)*85);

	    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 1\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\"  onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"position: absolute; left: "+(i)*85+"px; top: 0px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\" ondblclick=\"dbclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

	    	    		 }
		    			 else
		    			 {
		    				 if(i>=11*(w)-(w-1))
		    				 {
		    					 w=w+1;
		    				 }
		    				 if(i<11*(w)-(w-1) && i>10*(w-1))
		    	    		 {

		    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 1\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"position: absolute; left: "+(i%10-1)*85+"px; top: "+(w-1)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\" ondblclick=\"dbclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
		    	    			 System.out.println((i%10-1)*85+"######"+(w-1)*60);
		    	    		 }

		    			 }

	    	    	 }
	    	    	 else
	    	    	 {
		    	    		 //'1竖 2 横',
		    	    		 int x=0,y=0;
	        	    		 if(Integer.parseInt(divleft+"")>=5)
	        	    		 {
	        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")-Integer.parseInt(divleft+"");
	        	    		 }
	        	    		 else
	        	    		 {
	        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")+Integer.parseInt(divleft+"");
	        	    		 }
	        	    		 if(Integer.parseInt(divtop+"") >=5)
	        	    		 {
	        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")-Integer.parseInt(divtop+"");
	        	    		 }
	        	    		 else
	        	    		 {
	        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")+Integer.parseInt(divtop+"");
	        	    		 }
		    	    		 if(null != cm.getHv()&& cm.getHv().equals("1"))
		    	    		 {
		    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 1\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\" ondblclick=\"dbclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+shubj+"\');background-size:100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
		    	    		 }
		    	    		 else
		    	    		 {
		    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 1\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\" ondblclick=\"dbclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size:100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
		    	    		 }
	    	    	   // }
		    	    }
	    		 }
	    	 }
			return movejs;
		}
		        //排序采集器电压
				public StringBuilder ordercollectordy(List<AllGroupModel> agmlt,String bianliang,String powerStationId,int ysize,int nsize,int zsize, StringBuilder movejs,List<AllGroupModel> yagmlt)
				{
					 String greenbj=bianliang+"images\\/zj_gllh.png";
			    	 String shubj=bianliang+"images\\/xtst_gll.png";
					 if(agmlt.size()>0)
			    	 {
				         // movejs.append("<div class=\"show-pop\" style=\"width:90px; height:120px; background-color:#00FF99\"></div>");
				         // 图片横竖
						 int w=1;
			    	 /* for(int w=1;w<=ysize;w++)
			    	  {*/
			             String imagelx = "1";
			             char divtop ='0';
			             char divleft='0';
			             int  a =agmlt.size()/10;
			    		 for(int i=0;i<yagmlt.size();i++)
			    		 {
			    			 AllGroupModel cm = yagmlt.get(i);
			    			 int gsx = getRandomNum(0,100);
			    	    	 int gsy = getRandomNum(0,100);

			    	    	 if(CommonUtil.isEmpty(cm.getDivleft())==true )
			    	    	 {
			    	    		 divleft = cm.getDivleft().charAt(cm.getDivleft().length()-1);
			    	    		 divtop = cm.getDivtop().charAt(cm.getDivtop().length()-1);
			    	    	 }
			    	    	 if(cm.getGapleft()== null )
			    	    	 {
				    			 if(i<11*(w)&& w==1)
			    	    		 {
				    				 System.out.println((i-1)*85);
				    				// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");

			    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 1\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute; left: "+(i)*85+"px; top: 0px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\" ondblclick=\"dbclickdy(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

			    	    		 }
				    			 else
				    			 {
				    				 if(i>=11*(w)-(w-1))
				    				 {
				    					 w=w+1;
				    				 }
				    				 if(i<11*(w)-(w-1) && i>10*(w-1))
				    	    		 {
				    					// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 1\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute; left: "+(i%10-1)*85+"px; top: "+(w-1)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\" ondblclick=\"dbclickdy(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    			 System.out.println((i%10-1)*85+"######"+(w-1)*60);
				    	    		 }

				    			 }

			    	    	 }
			    	    	 else
			    	    	 {
				    	    		 //'1竖 2 横',
				    	    		 int x=0,y=0;
			        	    		 if(Integer.parseInt(divleft+"")>=5)
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")-Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")+Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 if(Integer.parseInt(divtop+"") >=5)
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")-Integer.parseInt(divtop+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")+Integer.parseInt(divtop+"");
			        	    		 }
				    	    		 if(null != cm.getHv()&& cm.getHv().equals("1"))
				    	    		 {
				    	    			// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 1\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\" ondblclick=\"dbclickdy(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+shubj+"\');background-size:100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
				    	    		 else
				    	    		 {
				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 1\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\" ondblclick=\"dbclickdy(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size:100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
			    	    	   // }
				    	    }
			    		 }
			    	 }
					return movejs;
				}
				//采集器排序电流
				public  StringBuilder ordercollectordl(List<AllGroupModel> agmlt,String bianliang,String powerStationId,int ysize,int nsize,int zsize, StringBuilder movejs,List<AllGroupModel> yagmlt)
				{
					 String greenbj=bianliang+"images\\/zj_gllh.png";
			    	 String shubj=bianliang+"images\\/xtst_gll.png";
					 if(agmlt.size()>0)
			    	 {
				         // movejs.append("<div class=\"show-pop\" style=\"width:90px; height:120px; background-color:#00FF99\"></div>");
				         // 图片横竖
						 int w=1;
			    	 /* for(int w=1;w<=ysize;w++)
			    	  {*/
			             String imagelx = "1";
			             char divtop ='0';
			             char divleft='0';
			             int  a =agmlt.size()/10;
			    		 for(int i=0;i<yagmlt.size();i++)
			    		 {
			    			 AllGroupModel cm = yagmlt.get(i);
			    			 int gsx = getRandomNum(0,100);
			    	    	 int gsy = getRandomNum(0,100);

			    	    	 if(CommonUtil.isEmpty(cm.getDivleft())==true )
			    	    	 {
			    	    		 divleft = cm.getDivleft().charAt(cm.getDivleft().length()-1);
			    	    		 divtop = cm.getDivtop().charAt(cm.getDivtop().length()-1);
			    	    	 }
			    	    	 if(cm.getGapleft()== null )
			    	    	 {
				    			 if(i<11*(w)&& w==1)
			    	    		 {
				    				 System.out.println((i-1)*85);
				    				// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");

			    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 1\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute; left: "+(i)*85+"px; top: 0px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\" ondblclick=\"dbclickdl(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

			    	    		 }
				    			 else
				    			 {
				    				 if(i>=11*(w)-(w-1))
				    				 {
				    					 w=w+1;
				    				 }
				    				 if(i<11*(w)-(w-1) && i>10*(w-1))
				    	    		 {
				    					// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 1\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute; left: "+(i%10-1)*85+"px; top: "+(w-1)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\" ondblclick=\"dbclickdl(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    			 System.out.println((i%10-1)*85+"######"+(w-1)*60);
				    	    		 }

				    			 }

			    	    	 }
			    	    	 else
			    	    	 {
				    	    		 //'1竖 2 横',
				    	    		 int x=0,y=0;
			        	    		 if(Integer.parseInt(divleft+"")>=5)
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")-Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")+Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 if(Integer.parseInt(divtop+"") >=5)
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")-Integer.parseInt(divtop+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")+Integer.parseInt(divtop+"");
			        	    		 }
				    	    		 if(null != cm.getHv()&& cm.getHv().equals("1"))
				    	    		 {
				    	    			// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 1\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\" ondblclick=\"dbclickdl(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+shubj+"\');background-size:100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
				    	    		 else
				    	    		 {
				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 1\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\" ondblclick=\"dbclickdl(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\"  onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size:100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
			    	    	   // }
				    	    }
			    		 }
			    	 }
					return movejs;
				}
		       //功率排序逆边器
				public  StringBuilder orderinverter(List<AllGroupModel> agmlt,String bianliang,String powerStationId,int ysize,int nsize,int zsize, StringBuilder movejs,List<AllGroupModel> nagmlt)
				{
					 //String greenbj=bianliang+"images\\/zj_gllh.png";
			    	// String shubj=bianliang+"images\\/xtst_gll.png";
			    	 String greenbj=bianliang+"images\\/zj_dyh.png";
			    	 String shubj=bianliang+"images\\/xtdt_dyst.png";
					 if(agmlt.size()>0)
			    	 {
						 int w=1;
				         // movejs.append("<div class=\"show-pop\" style=\"width:90px; height:120px; background-color:#00FF99\"></div>");
				         // 图片横竖
			    	  /*for(;w<=nsize;w++)
			    	  {*/
			             String imagelx = "1";
			             char divtop ='0';
			             char divleft='0';
			             int  a =agmlt.size()/10;
			    		 for(int i=0;i<nagmlt.size();i++)
			    		 {
			    			 AllGroupModel cm = nagmlt.get(i);
			    			 int gsx = getRandomNum(0,100);
			    	    	 int gsy = getRandomNum(0,100);

			    	    	 if(CommonUtil.isEmpty(cm.getDivleft())==true )
			    	    	 {
			    	    		 divleft = cm.getDivleft().charAt(cm.getDivleft().length()-1);
			    	    		 divtop = cm.getDivtop().charAt(cm.getDivtop().length()-1);
			    	    	 }
			    	    	 if(cm.getGapleft()== null )
			    	    	 {


			    	    		 if(i<11*(w)&& w==1)
			    	    		 {
				    				 System.out.println((i-1)*85);

			    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 2\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"position: absolute; left: "+(i)*85+"px; top: "+(ysize)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\" ondblclick=\"dbclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

			    	    		 }
				    			 else
				    			 {
				    				 if(i>=11*(w)-(w-1))
				    				 {
				    					 w=w+1;
				    				 }
				    				 if(i<11*(w)-(w-1) && i>10*(w-1))
				    	    		 {

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 2\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"position: absolute; left: "+(i%10-1)*85+"px; top: "+(ysize+w-1)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\" ondblclick=\"dbclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    			 System.out.println("left: "+(i%10-1)*85+"px; top: "+(ysize+w-1)*60+"px;");
				    	    		 }

				    			 }


			    	    	 }
			    	    	 else
			    	    	 {
				    	    		 //'1竖 2 横',
				    	    		 int x=0,y=0;
			        	    		 if(Integer.parseInt(divleft+"")>=5)
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")-Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")+Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 if(Integer.parseInt(divtop+"") >=5)
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")-Integer.parseInt(divtop+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")+Integer.parseInt(divtop+"");
			        	    		 }
				    	    		 if(null != cm.getHv()&& cm.getHv().equals("1"))
				    	    		 {
				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 2\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\" ondblclick=\"dbclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+shubj+"\');background-size:100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
				    	    		 else
				    	    		 {
				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 2\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\" ondblclick=\"dbclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size:100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
			    	    	    }
				    	    }
			    		// }
			    	 }
					return movejs;
				}
				 //电压排序逆边器
				public  StringBuilder orderinverterdy(List<AllGroupModel> agmlt,String bianliang,String powerStationId,int ysize,int nsize,int zsize, StringBuilder movejs,List<AllGroupModel> nagmlt)
				{
					 //String greenbj=bianliang+"images\\/zj_gllh.png";
			    	// String shubj=bianliang+"images\\/xtst_gll.png";
			    	 String greenbj=bianliang+"images\\/zj_dyh.png";
			    	 String shubj=bianliang+"images\\/xtdt_dyst.png";
					 if(agmlt.size()>0)
			    	 {
						 int w=1;
				         // movejs.append("<div class=\"show-pop\" style=\"width:90px; height:120px; background-color:#00FF99\"></div>");
				         // 图片横竖
			    	  /*for(;w<=nsize;w++)
			    	  {*/
			             String imagelx = "1";
			             char divtop ='0';
			             char divleft='0';
			             int  a =agmlt.size()/10;
			    		 for(int i=0;i<nagmlt.size();i++)
			    		 {
			    			 AllGroupModel cm = nagmlt.get(i);
			    			 int gsx = getRandomNum(0,100);
			    	    	 int gsy = getRandomNum(0,100);

			    	    	 if(CommonUtil.isEmpty(cm.getDivleft())==true )
			    	    	 {
			    	    		 divleft = cm.getDivleft().charAt(cm.getDivleft().length()-1);
			    	    		 divtop = cm.getDivtop().charAt(cm.getDivtop().length()-1);
			    	    	 }
			    	    	 if(cm.getGapleft()== null )
			    	    	 {


			    	    		 if(i<11*(w)&& w==1)
			    	    		 {
				    				 System.out.println((i-1)*85);
				    				// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");

			    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 2\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute; left: "+(i)*85+"px; top: "+(ysize)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\" ondblclick=\"dbclickdy(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

			    	    		 }
				    			 else
				    			 {
				    				 if(i>=11*(w)-(w-1))
				    				 {
				    					 w=w+1;
				    				 }
				    				 if(i<11*(w)-(w-1) && i>10*(w-1))
				    	    		 {
				    					// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 2\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute; left: "+(i%10-1)*85+"px; top: "+(ysize+w-1)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\" ondblclick=\"dbclickdy(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    			 System.out.println("left: "+(i%10-1)*85+"px; top: "+(ysize+w-1)*60+"px;");
				    	    		 }

				    			 }


			    	    	 }
			    	    	 else
			    	    	 {
				    	    		 //'1竖 2 横',
				    	    		 int x=0,y=0;
			        	    		 if(Integer.parseInt(divleft+"")>=5)
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")-Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")+Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 if(Integer.parseInt(divtop+"") >=5)
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")-Integer.parseInt(divtop+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")+Integer.parseInt(divtop+"");
			        	    		 }
				    	    		 if(null != cm.getHv()&& cm.getHv().equals("1"))
				    	    		 {
				    	    			// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 2\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\" ondblclick=\"dbclickdy(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+shubj+"\');background-size:100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
				    	    		 else
				    	    		 {
				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 2\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\" ondblclick=\"dbclickdy(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size:100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
			    	    	    }
				    	    }
			    		// }
			    	 }
					return movejs;
				}
				 //电流排序逆边器
				public  StringBuilder orderinverterdl(List<AllGroupModel> agmlt,String bianliang,String powerStationId,int ysize,int nsize,int zsize, StringBuilder movejs,List<AllGroupModel> nagmlt)
				{
					 //String greenbj=bianliang+"images\\/zj_gllh.png";
			    	// String shubj=bianliang+"images\\/xtst_gll.png";
			    	 String greenbj=bianliang+"images\\/zj_dyh.png";
			    	 String shubj=bianliang+"images\\/xtdt_dyst.png";
					 if(agmlt.size()>0)
			    	 {
						 int w=1;
				         // movejs.append("<div class=\"show-pop\" style=\"width:90px; height:120px; background-color:#00FF99\"></div>");
				         // 图片横竖
			    	  /*for(;w<=nsize;w++)
			    	  {*/
			             String imagelx = "1";
			             char divtop ='0';
			             char divleft='0';
			             int  a =agmlt.size()/10;
			    		 for(int i=0;i<nagmlt.size();i++)
			    		 {
			    			 AllGroupModel cm = nagmlt.get(i);
			    			 int gsx = getRandomNum(0,100);
			    	    	 int gsy = getRandomNum(0,100);

			    	    	 if(CommonUtil.isEmpty(cm.getDivleft())==true )
			    	    	 {
			    	    		 divleft = cm.getDivleft().charAt(cm.getDivleft().length()-1);
			    	    		 divtop = cm.getDivtop().charAt(cm.getDivtop().length()-1);
			    	    	 }
			    	    	 if(cm.getGapleft()== null )
			    	    	 {


			    	    		 if(i<11*(w)&& w==1)
			    	    		 {
				    				 System.out.println((i-1)*85);
				    				// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\"  onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','3')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

			    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 2\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute; left: "+(i)*85+"px; top: "+(ysize)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\" ondblclick=\"dbclickdl(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

			    	    		 }
				    			 else
				    			 {
				    				 if(i>=11*(w)-(w-1))
				    				 {
				    					 w=w+1;
				    				 }
				    				 if(i<11*(w)-(w-1) && i>10*(w-1))
				    	    		 {
				    					// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 2\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute; left: "+(i%10-1)*85+"px; top: "+(ysize+w-1)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\" ondblclick=\"dbclickdl(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    			 System.out.println("left: "+(i%10-1)*85+"px; top: "+(ysize+w-1)*60+"px;");
				    	    		 }

				    			 }


			    	    	 }
			    	    	 else
			    	    	 {
				    	    		 //'1竖 2 横',
				    	    		 int x=0,y=0;
			        	    		 if(Integer.parseInt(divleft+"")>=5)
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")-Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")+Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 if(Integer.parseInt(divtop+"") >=5)
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")-Integer.parseInt(divtop+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")+Integer.parseInt(divtop+"");
			        	    		 }
				    	    		 if(null != cm.getHv()&& cm.getHv().equals("1"))
				    	    		 {
				    	    			// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 2\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\" ondblclick=\"dbclickdl(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+shubj+"\');background-size:100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
				    	    		 else
				    	    		 {
				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 2\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\" ondblclick=\"dbclickdl(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size:100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
			    	    	    }
				    	    }
			    		// }
			    	 }
					return movejs;
				}
				//功率排序组件
				public  StringBuilder ordersub(List<AllGroupModel> agmlt,String bianliang,String powerStationId,int ysize,int nsize,int zsize, StringBuilder movejs,List<AllGroupModel> zagmlt)
				{
					// String greenbj=bianliang+"images\\/zj_gllh.png";
			    	// String shubj=bianliang+"images\\/xtst_gll.png";
			    	 String greenbj=bianliang+"images\\/zj_dlh.png";
			    	 String shubj=bianliang+"images\\/xtst_dlst.png";
					 if(agmlt.size()>0)
			    	 {
				       // movejs.append("<div class=\"show-pop\" style=\"width:90px; height:120px; background-color:#00FF99\"></div>");
				      // 图片横竖
						 int w=1;
			    	  /*for(int w=1;w<=nsize;w++)
			    	  {*/
			             String imagelx = "1";
			             char divtop ='0';
			             char divleft='0';
			             int  a =agmlt.size()/10;
			    		 for(int i=0;i<zagmlt.size();i++)
			    		 {
			    			 AllGroupModel cm = zagmlt.get(i);
			    			 int gsx = getRandomNum(0,100);
			    	    	 int gsy = getRandomNum(0,100);

			    	    	 if(CommonUtil.isEmpty(cm.getDivleft())==true )
			    	    	 {
			    	    		 divleft = cm.getDivleft().charAt(cm.getDivleft().length()-1);
			    	    		 divtop = cm.getDivtop().charAt(cm.getDivtop().length()-1);
			    	    	 }
			    	    	 if(cm.getGapleft()== null )
			    	    	 {
			    	    		 if(i<11*(w)&& w==1)
			    	    		 {
				    				 System.out.println((i-1)*85);

			    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 3\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"position: absolute; left: "+(i)*85+"px; top: "+(ysize+nsize)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\" ondblclick=\"dbclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

			    	    		 }
				    			 else
				    			 {
				    				 if(i>=11*(w)-(w-1))
				    				 {
				    					 w=w+1;
				    				 }
				    				 if(i<11*(w)-(w-1) && i>10*(w-1))
				    	    		 {

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 3\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"position: absolute; left: "+(i%10-1)*85+"px; top: "+(ysize+nsize+w-1)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\" ondblclick=\"dbclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    			 System.out.println((i%10-1)*85+"######"+(w-1)*60);
				    	    		 }

				    			 }
			    	    	 }
			    	    	 else
			    	    	 {
				    	    		 //'1竖 2 横',
				    	    		 int x=0,y=0;
			        	    		 if(Integer.parseInt(divleft+"")>=5)
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")-Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")+Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 if(Integer.parseInt(divtop+"") >=5)
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")-Integer.parseInt(divtop+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")+Integer.parseInt(divtop+"");
			        	    		 }
				    	    		 if(null != cm.getHv()&& cm.getHv().equals("1"))
				    	    		 {
				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 3\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\" ondblclick=\"dbclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+shubj+"\');background-size:100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
				    	    		 else
				    	    		 {
				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 3\" id=\""+cm.getId().toString().trim().replace(" ", "")+"td\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"td')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"\" class=\"total-centered\" ondblclick=\"dbclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"zcclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size:100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
			    	    	    //}
				    	    }
			    		 }
			    	 }
					return movejs;
				}
				//电压排序组件
				public  StringBuilder ordersubdy(List<AllGroupModel> agmlt,String bianliang,String powerStationId,int ysize,int nsize,int zsize, StringBuilder movejs,List<AllGroupModel> zagmlt)
				{
					// String greenbj=bianliang+"images\\/zj_gllh.png";
			    	// String shubj=bianliang+"images\\/xtst_gll.png";
			    	 String greenbj=bianliang+"images\\/zj_dlh.png";
			    	 String shubj=bianliang+"images\\/xtst_dlst.png";
					 if(agmlt.size()>0)
			    	 {
				       // movejs.append("<div class=\"show-pop\" style=\"width:90px; height:120px; background-color:#00FF99\"></div>");
				      // 图片横竖
						 int w=1;
			    	  /*for(int w=1;w<=nsize;w++)
			    	  {*/
			             String imagelx = "1";
			             char divtop ='0';
			             char divleft='0';
			             int  a =agmlt.size()/10;
			    		 for(int i=0;i<zagmlt.size();i++)
			    		 {
			    			 AllGroupModel cm = zagmlt.get(i);
			    			 int gsx = getRandomNum(0,100);
			    	    	 int gsy = getRandomNum(0,100);

			    	    	 if(CommonUtil.isEmpty(cm.getDivleft())==true )
			    	    	 {
			    	    		 divleft = cm.getDivleft().charAt(cm.getDivleft().length()-1);
			    	    		 divtop = cm.getDivtop().charAt(cm.getDivtop().length()-1);
			    	    	 }
			    	    	 if(cm.getGapleft()== null )
			    	    	 {
			    	    		 if(i<11*(w)&& w==1)
			    	    		 {
				    				 System.out.println((i-1)*85);
				    				//movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

			    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 3\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute; left: "+(i)*85+"px; top: "+(ysize+nsize)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\" ondblclick=\"dbclickdy(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

			    	    		 }
				    			 else
				    			 {
				    				 if(i>=11*(w)-(w-1))
				    				 {
				    					 w=w+1;
				    				 }
				    				 if(i<11*(w)-(w-1) && i>10*(w-1))
				    	    		 {

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 3\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute; left: "+(i%10-1)*85+"px; top: "+(ysize+nsize+w-1)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\" ondblclick=\"dbclickdy(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    			 System.out.println((i%10-1)*85+"######"+(w-1)*60);
				    	    		 }

				    			 }
			    	    	 }
			    	    	 else
			    	    	 {
				    	    		 //'1竖 2 横',
				    	    		 int x=0,y=0;
			        	    		 if(Integer.parseInt(divleft+"")>=5)
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")-Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")+Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 if(Integer.parseInt(divtop+"") >=5)
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")-Integer.parseInt(divtop+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")+Integer.parseInt(divtop+"");
			        	    		 }
				    	    		 if(null != cm.getHv()&& cm.getHv().equals("1"))
				    	    		 {
				    	    			// movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+shubj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 3\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\" ondblclick=\"dbclickdy(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+shubj+"\');background-size:100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
				    	    		 else
				    	    		 {
				    	    			 //movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\"  onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','2')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 3\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dyf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dyf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dyz\" class=\"total-centered\" ondblclick=\"dbclickdy(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dyclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size:100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
			    	    	    //}
				    	    }
			    		 }
			    	 }
					return movejs;
				}
				//电流排序组件
				public  StringBuilder ordersubdl(List<AllGroupModel> agmlt,String bianliang,String powerStationId,int ysize,int nsize,int zsize, StringBuilder movejs,List<AllGroupModel> zagmlt)
				{
					// String greenbj=bianliang+"images\\/zj_gllh.png";
			    	// String shubj=bianliang+"images\\/xtst_gll.png";
			    	 String greenbj=bianliang+"images\\/zj_dlh.png";
			    	 String shubj=bianliang+"images\\/xtst_dlst.png";
					 if(agmlt.size()>0)
			    	 {
				       // movejs.append("<div class=\"show-pop\" style=\"width:90px; height:120px; background-color:#00FF99\"></div>");
				      // 图片横竖
						 int w=1;
			    	  /*for(int w=1;w<=nsize;w++)
			    	  {*/
			             String imagelx = "1";
			             char divtop ='0';
			             char divleft='0';
			             int  a =agmlt.size()/10;
			    		 for(int i=0;i<zagmlt.size();i++)
			    		 {
			    			 AllGroupModel cm = zagmlt.get(i);
			    			 int gsx = getRandomNum(0,100);
			    	    	 int gsy = getRandomNum(0,100);

			    	    	 if(CommonUtil.isEmpty(cm.getDivleft())==true )
			    	    	 {
			    	    		 divleft = cm.getDivleft().charAt(cm.getDivleft().length()-1);
			    	    		 divtop = cm.getDivtop().charAt(cm.getDivtop().length()-1);
			    	    	 }
			    	    	 if(cm.getGapleft()== null )
			    	    	 {
			    	    		 if(i<11*(w)&& w==1)
			    	    		 {
				    				 System.out.println((i-1)*85);
				    				 //movedyjs.append("<div class=\"show-pop-table show-pop draggable\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute;left: "+gsx+"px; top: "+gsy+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\"  onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','3')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

			    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 3\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute; left: "+(i)*85+"px; top: "+(ysize+nsize)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\" ondblclick=\"dbclickdl(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");

			    	    		 }
				    			 else
				    			 {
				    				 if(i>=11*(w)-(w-1))
				    				 {
				    					 w=w+1;
				    				 }
				    				 if(i<11*(w)-(w-1) && i>10*(w-1))
				    	    		 {

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 3\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute; left: "+(i%10-1)*85+"px; top: "+(ysize+nsize+w-1)*60+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\" ondblclick=\"dbclickdl(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size: 100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    			 System.out.println((i%10-1)*85+"######"+(w-1)*60);
				    	    		 }

				    			 }
			    	    	 }
			    	    	 else
			    	    	 {
				    	    		 //'1竖 2 横',
				    	    		 int x=0,y=0;
			        	    		 if(Integer.parseInt(divleft+"")>=5)
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")-Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  x=Integer.parseInt((Integer.parseInt(cm.getGapleft())*10)+"")+Integer.parseInt(divleft+"");
			        	    		 }
			        	    		 if(Integer.parseInt(divtop+"") >=5)
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")-Integer.parseInt(divtop+"");
			        	    		 }
			        	    		 else
			        	    		 {
			        	    			  y=Integer.parseInt((Integer.parseInt(cm.getGaptop())*10)+"")+Integer.parseInt(divtop+"");
			        	    		 }
				    	    		 if(null != cm.getHv()&& cm.getHv().equals("1"))
				    	    		 {

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 3\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 55px; height: 80px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\" ondblclick=\"dbclickdl(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+shubj+"\');background-size:100% 100%;background-repeat: no-repeat;width:55px; height:80px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
				    	    		 else
				    	    		 {

				    	    			 movejs.append("<div class=\"show-pop-table show-pop draggable 3\" id=\""+cm.getId().toString().trim().replace(" ", "")+"dlf\" onMouseDown=\"mousedowns('"+cm.getId().toString().trim().replace(" ", "")+"dlf')\" style=\"position: absolute;left:"+x+"px; top:"+y+"px; width: 80px; height: 55px;\" ><div id=\""+cm.getId().toString().trim().replace(" ", "")+"dlz\" class=\"total-centered\" ondblclick=\"dbclickdl(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" onClick=\"dlclick(\'"+cm.getId().toString().trim().replace(" ", "")+"\','"+imagelx+"','"+powerStationId+"','"+cm.getGenre()+"','1')\" style=\"background:url(\'"+greenbj+"\');background-size:100% 100%;background-repeat: no-repeat;width:80px; height:55px;\">"+cm.getAllname()+"</div></div>");
				    	    		 }
			    	    	    //}
				    	    }
			    		 }
			    	 }
					return movejs;
				}
				public  void insertlog(String content)
			    {
			 	   LoginLogger logger = new LoginLogger();
			       // String content = "查询会员数据";
			        logger.setId( UUID.randomUUID().toString() );
			        logger.setUserId( SessionHelper.getUserId() );
			        logger.setLastLoginTime( new Date() );
			       // logger.setLoginIp( getIpAddr(request) );
			        logger.setContent(content);
			    //    logger.setOperation_id(UUID.randomUUID().toString());
			        loginLoggerService.insert( logger );
			    }


	//拖动新增修改数据
	@RequestMapping("moveupdatedata.web")
	@ResponseBody
	public void moveUpdateView(HttpServletRequest request,HttpServletResponse response){

		String pid = request.getParameter("pid");//电站id
		String id = request.getParameter("id");//组件id
		String lx = request.getParameter("lx");//1 采集器   2  逆变器   3 组件
		String lb = request.getParameter("lb");// 窗口是竖着的还是横着的1  竖   2 横
		String qb = request.getParameter("qb");// 1 功率   2  电压  3  电流
		String top = request.getParameter("divtop").replace("px", "");//窗口距离顶部的位置
		String left = request.getParameter("divleft").replace("px", "");//窗口距离左边的位置
		String parentwidth = request.getParameter("parentwidth").replace("px", "");//父窗口对象宽度
		String parentheight = request.getParameter("parentheight").replace("px", "");//父窗口对象高度
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("pid",pid );
		map.put("zjid",id );
		map.put("lx",lx );
		map.put("lb",lb );

		ChangeModel changeModel =  changeService.queryChange(map);
		ChangeModel cm = null ;
		if(changeModel == null)
		{
			cm = new ChangeModel();
			cm.setId(UUID.randomUUID().toString());
			cm.setPowerId(pid);
			cm.setLb(lb);
			cm.setLx(lx);
			if(left!=null){
				int intX= Integer.parseInt(left);
				cm.setXz(String.valueOf(Math.round(intX/10.0)));
			}
			if(top!=null){
				int intTop= Integer.parseInt(top);
				cm.setYz(String.valueOf(Math.round(intTop/10.0)));
			}
//    	cm.setQb(qb);
			cm.setZjid(id);
			cm.setWidth(parentwidth);
			cm.setHeight(parentheight);
			cm.setDivtop(top);
			cm.setDivleft(left);
			cm.setLaystatus("1");
			changeService.saveChange(cm);

		}
		else
		{
			cm = new ChangeModel();
			cm.setPowerId(pid);
			cm.setLb(lb);
			cm.setLx(lx);
			if(left!=null){
				int intX= Integer.parseInt(left);
				cm.setXz(String.valueOf(Math.round(intX/10.0)));
			}
			if(top!=null){
				int intTop= Integer.parseInt(top);
				cm.setYz(String.valueOf(Math.round(intTop/10.0)));
			}
			cm.setZjid(id);
//    	cm.setQb(qb);
			cm.setWidth(parentwidth);
			cm.setHeight(parentheight);
			cm.setDivtop(top);
			cm.setDivleft(left);
			changeService.updateChange(cm);

		}
		insertlog("记录拖动位置数据");
	}


	/**
	 * 新增或修改组件（优化器）信息，新增完组件可以调moveupdatedata.htm去保存位置信息
	 * @param   model      组件
	 * @return  CallResult
	 */
	@RequestMapping(value="saveOrUpdateComponent.web")
	@ResponseBody
	public CallResult saveOrUpdate( ComponentModel model,HttpServletRequest request  ){
		Object uid =   request.getSession().getAttribute("userKey");

		model.setCreateUserId(uid.toString());
		Object obj =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(obj));
		if (CommonUtil.isEmpty(model.getId())) {
			//修改
			insertlog("修改组件数据,Chipid:"+model.getChipId());
			return componentService.updateComponentModel(model);
		} else {
			insertlog("新增组件数据,Chipid"+model.getChipId());
			String id = componentService.selectByChipId(model.getChipId());
			if(null != id) {
				model.setId(id);
				return componentService.updateComponentModel(model);
			} else {
				return componentService.saveComponentModel(model);
			}
		}
	}


	/**
	 * 新增或修改组件属性信息
	 * @param   model      组件
	 * @return  CallResult
	 */
	@RequestMapping(value="saveOrUpdateChangeDto.web")
	@ResponseBody
	public CallResult saveOrUpdateChangeDto( ChangeDto model,HttpServletRequest request ){
		CallResult callResult = CallResult.newInstance();
		String uid = (String) request.getSession().getAttribute("userKey");
		ComponentModel cp = new ComponentModel();
		/*if (uid != null && !uid.isEmpty()){
			cp.setCreateUserId(uid.toString());
		}
		else {
			throw new IllegalArgumentException("错误的请求参数");
		}*/
		//电站id
		String pid = model.getPowerid();
		//组件id
		String zjid = model.getZjid();
		//类型1 采集器   2  逆变器   3 组件
		String lx = model.getLx();
		// 窗口是竖着的还是横着的1  竖   2 横
		String lb = model.getLb();
        String divtop = model.getDivtop();
		String top = (divtop != null) ? divtop.replace("px", "") : "0";
		String divleft = model.getDivleft();
		String left = (divleft != null) ? divleft.replace("px", "") : "0";
		String parentwidthParam = model.getWidth();
		String parentwidth = (parentwidthParam != null) ? parentwidthParam.replace("px", "") : "0";
		String parentheightParam = model.getHeight();
		String parentheight = (parentheightParam != null) ? parentheightParam.replace("px", "") : "0";
		//新增的字段
		String xzjd = model.getXzjd();
		String bkbj = model.getBkbj();
		String btmd = model.getBtmd();
		//优化器ID
		String chipId = model.getChipId();
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("pid",pid );
		map.put("zjid",zjid );
		map.put("lx",lx );
		map.put("lb",lb );
		map.put("chipId",chipId);
		ChangeDto changeDto =  changeService.queryChangeDto(map);
		ChangeDto cd = new ChangeDto();
		//查询组件Id
		String componentId = componentService.selectByChipId(model.getChipId());
		//如果组件表查不到数据
		if(componentId == null || componentId.isEmpty()){
			cd.setId(UUID.randomUUID().toString());
			cd.setPowerid(pid);
			cd.setLb(lb);
			cd.setLx(lx);
			if(left!=null){
				int intX= Integer.parseInt(left);
				cd.setXz(String.valueOf(Math.round(intX/10.0)));
			}
			if(top!=null){
				int intTop= Integer.parseInt(top);
				cd.setYz(String.valueOf(Math.round(intTop/10.0)));
			}
			if (zjid != null){
				cd.setZjid(zjid);
			}

			cd.setWidth(parentwidth);
			cd.setHeight(parentheight);
			cd.setDivtop(top);
			cd.setDivleft(left);
			cd.setLaystatus("1");
			cd.setBkbj(bkbj);
			cd.setBtmd(btmd);
			cd.setXzjd(xzjd);
			cd.setChipId(chipId);
			//如果传参没有chipID,返回错误信息
			if (model.getChipId() != null && !model.getChipId().isEmpty()){
				cp.setChipId(model.getChipId());
			}
			/*else {
				callResult.setErrMsg("错误的请求参数1");
				return callResult;
			}*/
			//如果传参没有电站Id,返回错误信息
			if (model.getPowerid() != null && !model.getPowerid().isEmpty()){
				cp.setPowerStationId(model.getPowerid());
			}
			/*else {
				callResult.setErrMsg("错误的请求参数2");
				return callResult;
			}*/
			cp.setStatus(2);
			cp.setCreateUserId(uid);

			componentService.saveComponentModel(cp);
			insertlog("新增组件,Chipid"+model.getChipId());
			//如果change表没有数据，新增物理视图，在change表中插入一条数据
			if(changeDto == null){
				cd.setZjid(componentService.selectByChipId(model.getChipId()));
				int num1 = changeService.saveChangeDto(cd);
				callResult.setReModel("新增组件视图成功");
				callResult.setCount(num1);
				return callResult;
			}
		}//如果组件表查到数据
		else
		{
			cd = new ChangeDto();
			cd.setId(UUID.randomUUID().toString());
			cd.setPowerid(pid);
			cd.setLb(lb);
			cd.setLx(lx);
			if(left!=null){
				int intX= Integer.parseInt(left);
				cd.setXz(String.valueOf(Math.round(intX/10.0)));
			}
			if(top!=null){
				int intTop= Integer.parseInt(top);
				cd.setYz(String.valueOf(Math.round(intTop/10.0)));
			}
			cd.setZjid(componentId);
			cd.setWidth(parentwidth);
			cd.setHeight(parentheight);
			cd.setDivtop(top);
			cd.setDivleft(left);
			cd.setLaystatus("1");
			cd.setBkbj(bkbj);
			cd.setBtmd(btmd);
			cd.setXzjd(xzjd);
			cd.setChipId(chipId);
            //如果传参没有chipID,返回错误信息
			if (model.getChipId() != null && !model.getChipId().isEmpty()){
				cp.setChipId(model.getChipId());
			}
			/*else {
				callResult.setErrMsg("错误的请求参数3");
				return callResult;
			}*/

			if (model.getPowerid() != null && !model.getPowerid().isEmpty()){
				cp.setPowerStationId(model.getPowerid());
			}
			/*else {
				callResult.setErrMsg("错误的请求参数4");
				return callResult;
			}*/
			cp.setStatus(2);
			componentService.updateComponentModel(cp);
			insertlog("修改组件,Chipid"+model.getChipId());

			//如果change表没有数据，新增物理视图，在change表中插入一条数据
			if (changeDto == null){
				int num1 = changeService.saveChangeDto(cd);
				callResult.setReModel("新增组件视图成功");
				callResult.setCount(num1);
				return callResult;
			}else{
				int num1 = changeService.updateChangeDto(cd);
				callResult.setReModel("修改组件视图成功");
				callResult.setCount(num1);
				return callResult;
			}
		}
		callResult.setErrMsg("错误信息");
		return callResult;
	}


	/**
	 * 新增或修改组串属性信息
	 * @param   groupModel      组件
	 * @return  CallResult
	 */
	@PostMapping(value="saveOrUpdateGroupView.web")
	@ResponseBody
	public CallResult saveOrUpdateGroupView(@RequestBody GroupModel groupModel,HttpServletRequest request){

		CallResult callResult = CallResult.newInstance();

		//电站id
		String powerStationId = groupModel.getPowerStationId();
		//组串名称
		String groupName = groupModel.getGroupName();
		//组串功率
		Integer power = groupModel.getPower();
		//组串id
		String groupId = groupModel.getGroupId();
		// 窗口是竖着的还是横着的1  竖   2 横
		String lb = groupModel.getLb();
		//距离顶端
		String divtop = groupModel.getDivtop();
		String top = (divtop != null) ? divtop.replace("px", "") : "0";
		//距离左边
		String divleft = groupModel.getDivleft();
		String left = (divleft != null) ? divleft.replace("px", "") : "0";
		//父窗口对象宽度
		String parentwidthParam = groupModel.getWidth();
		String parentwidth = (parentwidthParam != null) ? parentwidthParam.replace("px", "") : "0";
		//父窗口对象高度
		String parentheightParam = groupModel.getHeight();
		String parentheight = (parentheightParam != null) ? parentheightParam.replace("px", "") : "0";
		//放置状态
		String laystatus = groupModel.getLaystatus();
		//新增的字段
		/*String xzjd = model.getXzjd();
		String bkbj = model.getBkbj();
		String btmd = model.getBtmd();*/
		/*Map<String, Object> map = new HashMap<String, Object>();
		map.put("powerStationId",powerStationId );
		map.put("groupId",groupId );
		GroupModel gm = componentGroupService.queryGroupInfo(map);*/

		String pid = componentGroupMapper.queryPositionByGroupId(groupId);
		String gId = componentGroupService.queryGroupById(groupId);
		GroupModel gm1 = new GroupModel();

		if (gId == null || gId.isEmpty()){
			gm1.setPowerStationId(powerStationId);
			gm1.setLb(lb);
			if(left!=null){
				int intX= Integer.parseInt(left);
				gm1.setXz(String.valueOf(Math.round(intX/10.0)));
			}
			if(top!=null){
				int intTop= Integer.parseInt(top);
				gm1.setYz(String.valueOf(Math.round(intTop/10.0)));
			}
			gm1.setGroupName(groupName);
			gm1.setGroupId(groupId);
			gm1.setWidth(parentwidth);
			gm1.setHeight(parentheight);
			gm1.setDivtop(top);
			gm1.setDivleft(left);
			gm1.setLaystatus("1");
			/*cd.setBkbj(bkbj);
			cd.setBtmd(btmd);
			cd.setXzjd(xzjd);
			cd.setChipId(chipId);*/
			int num = componentGroupService.saveGroupModel(gm1);
			if(pid == null ){
				int num1 = componentGroupService.saveGroupPosition(gm1);
			}
			callResult.setReModel("保存成功");
			return callResult;
		}
		else if (gId != null && pid == null){
			GroupModel gm2 = new GroupModel();
			gm2.setPowerStationId(powerStationId);
			gm2.setLb(lb);;
			if(left!=null){
				int intX= Integer.parseInt(left);
				gm2.setXz(String.valueOf(Math.round(intX/10.0)));
			}
			if(top!=null){
				int intTop= Integer.parseInt(top);
				gm2.setYz(String.valueOf(Math.round(intTop/10.0)));
			}
			gm2.setWidth(parentwidth);
			gm2.setHeight(parentheight);
			gm2.setDivtop(top);
			gm2.setDivleft(left);
			gm2.setGroupId(groupId);
			/*cd.setBkbj(bkbj);
			cd.setBtmd(btmd);
			cd.setXzjd(xzjd);
			cd.setChipId(chipId);*/
			/*int num2 = componentGroupService.updateGroupPosition(gm2);*/
			int num2 = componentGroupService.saveGroupPosition(gm2);
			callResult.setReModel("保存成功");
			return callResult;
		}
		else {
			GroupModel gm3 = new GroupModel();
			gm3.setPowerStationId(powerStationId);
			gm3.setLb(lb);;
			if(left!=null){
				int intX= Integer.parseInt(left);
				gm3.setXz(String.valueOf(Math.round(intX/10.0)));
			}
			if(top!=null){
				int intTop= Integer.parseInt(top);
				gm3.setYz(String.valueOf(Math.round(intTop/10.0)));
			}
			gm3.setWidth(parentwidth);
			gm3.setHeight(parentheight);
			gm3.setDivtop(top);
			gm3.setDivleft(left);
			gm3.setGroupId(groupId);

			int num3 = componentGroupService.updateGroupPosition(gm3);
			callResult.setReModel("修改成功");
			return callResult;
		}
	}



	/**
	 * 查看组件（优化器）属性信息
	 * @param   id      组件
	 * @return  CallResult
	 */
	@RequestMapping(value = "queryChangeDtoDetail.web")
	@ResponseBody
	public CallResult queryChangeDtoDetail(String id){
		CallResult callResult = CallResult.newInstance();
		if (id == null || id.equals("")) {
			callResult.setReModel(0);
			callResult.setErrMsg("id号不能为空");
		}
		ChangeDto changeDto = changeService.queryChangeDtoDetail(id);
		callResult.setReModel(changeDto);
		return callResult;
	}
}
