package com.ymx.manager.controller.member;

import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import com.ymx.common.base.config.ConfigConstants;
import com.ymx.common.base.entity.User;
import com.ymx.common.base.entity.UserRole;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.ConstantsExt;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.member.entity.MemberInfoModel;
import com.ymx.service.photovoltaic.member.entity.MemberModel;
import com.ymx.service.photovoltaic.member.service.MemberService;
import com.ymx.manager.security.service.IUserService;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.MD5;

import net.sf.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @DESC
 * @DATE 2018/7/26
 * @NAME MemberController
 * @MOUDELNAME 模块
 */
@Controller
@RequestMapping("memberCtr")
public class MemberController {

    private static final String prefix = "member/";

    /** 会员服务层 */
    @Resource
    private MemberService memberService;
    @Autowired
    private IUserService userService;
    @Autowired
    private ILoginLoggerService loginLoggerService;
    /**
     * 后台会员列表请求
     * @return
     */
    @RequestMapping(value="list.htm")
    public String  memberList(HttpServletRequest request)
    {
    	insertlog("查询会员数据");
//    	String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
       return prefix + "member_list";
    }


    /**
     * 后台会员列表数据
     * @param model    会员实体
     * @param view     分页实体
     * @param request  请求对象
     * @param response 响应对象
     * @return
     */
    @RequestMapping(value="queryMemberList.web")
    @ResponseBody
    public CallResult queryMemberList(MemberInfoModel model, PageView view , HttpServletRequest request, HttpServletResponse response){
    	if(CommonUtil.isEmpty(model.getMembertype())== true)
    	{
    		model.setType(1);
    	}
    	Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//        	model.setLanguage("en");
//        }
//        else
//        {
//        	model.setLanguage("ch");
//        }
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
    	return memberService.queryMembers(view, model);
    }

    /**
     * 页面请求 (新增,编辑,查看)
     * @param operator    操作标识
     * @param memberModel 人员信息
     * @return
     */
    @RequestMapping(value="viewRequest.htm")
    public ModelAndView memberListRequst( HttpServletRequest request ,String operator , MemberInfoModel memberModel ){
//    	String mkt = "";
//   	    String datamkt = "";
//        Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//        if(ob.toString().contains("en"))
//        {
//     	   mkt="en";
//     	   datamkt = "en";
//        }
//        else
//        {
//     	   mkt="cn";
//     	   datamkt="cn";
//        }
//        request.setAttribute( "datamkt", datamkt );
//        request.setAttribute( "mkt", mkt );
        ModelAndView mv = new ModelAndView(prefix + "member_edit");
        User user = SessionHelper.getSessionUser();
        mv.addObject("operator" , operator);
        memberModel.setCreateUserName(user.getUserName());

	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    memberModel.setLanguage(TranslateLanguage.getLanguageType(ob));
        if(CommonUtil.isEmpty(memberModel.getId()) || CommonUtil.isEmpty(memberModel.getPhone())){
            memberModel = memberService.queryMemberInfo(memberModel);
        }
        mv.addObject("info" , memberModel);
        insertlog("查询会员详细数据");
        return mv;
    }

    /**
     * 新增或修改
     * @param model
     * @return
     */
    @RequestMapping(value="saveOrUpdate.web")
    @ResponseBody
    public CallResult saveOrUpdate( HttpServletRequest request ,MemberInfoModel model  ){

	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        if (CommonUtil.isEmpty(model.getId())) {
            //修改
            model.setModifyUserId(SessionHelper.getUserId());
            insertlog("会员信息修改,手机号:"+model.getPhone());
            return memberService.updateMemberInfoModel(model);
        } else {
            model.setCreateUserId(SessionHelper.getUserId());
            insertlog("新增会员信息,手机号:"+model.getPhone());
            return memberService.addMember(model);
        }
    }

    /**
     * 批量修改数据
     * @param model 人员信息
     * @return
     */
    @RequestMapping(value = "/batchUpdateMemberModel.web")
    @ResponseBody
    public CallResult batchUpdateMemberModel(HttpServletRequest request ,MemberInfoModel model) {
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        Map<String ,Object> map = new HashMap<>();
        insertlog("批量修改会员信息");
        CallResult callResult = memberService.batchUpdateMemberModel(map , model);
        return callResult;
    }

    /**
     * 重置密码
     * @param model 人员信息
     * @return
     */
    @RequestMapping(value = "/resetUserPassword.web")
    @ResponseBody
    public CallResult resetUserPassword(HttpServletRequest request ,MemberInfoModel model) {
	    Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
	    model.setLanguage(TranslateLanguage.getLanguageType(ob));
        Map<String ,Object> map = new HashMap<>();
        map.put("passwd", MD5.password(ConstantsExt.INITIAL_PASSWORD));
        insertlog("会员重置密码，ID:"+model.getId());
        CallResult callResult = memberService.batchUpdateMemberModelPwd(map , model);
        return callResult;
    }

    /**
     * 设置登录后台
     * @param model 人员信息
     * @return
     * setlogin.web
     */
    @RequestMapping(value = "/setlogin.web")
    @ResponseBody
    public CallResult setlogin(MemberInfoModel model,HttpServletResponse response) {
        //获取会员id  ，插入user  表
    	 String message = "设置登录后台成功";
    	 String[] ids =  model.getId().split(",", -1);
    	 User user = null;
    	 for(int i=0;i<ids.length;i++)
		 {
             String id = ids[i].toString();
             User userinfo = userService.query(id);
             if(null==userinfo)
             {

             MemberModel membermodel =  memberService.queryMemberByPhone(null, id);
             if(null != membermodel && membermodel.getType()==2)
             { 
            	 user = new User();
            	 user.setCreator( SessionHelper.getUserId() );
                 user.setId(id);
                 user.setCreateDate( new Date() );
                 if(CommonUtil.isEmpty(membermodel.getPasswd())==true)
                 {
                	 user.setUserPswd(membermodel.getPasswd());
                 }
                 else
                 {
                	 user.setUserPswd(MD5.password("123456"));
                 }
                
                 user.setUserId(membermodel.getPhone());
                 user.setUserName(membermodel.getName());
                 boolean insertuser =  userService.insert(user);
                 if(insertuser==false)
                 {
                	 message = "设置登录后台失败！";
                 }
                 else
                 {
                	//添加角色
                     String OPERATIONS_DUTY_ID = ConfigConstants.getConfig("OPERATIONSID");
                     List<UserRole> roles = new ArrayList<UserRole>();
                     UserRole ur = new UserRole();     
                     ur = new UserRole();
                     ur.setRoleId(OPERATIONS_DUTY_ID);
                     ur.setUserId(membermodel.getPhone());
                     roles.add(ur);
                     boolean result = userService.saveUserRole(id, roles );
                    //更新会员信息  islogin
                     membermodel = new MemberModel();
                     membermodel.setId(id);
                     membermodel.setIslogin(1);
                     memberService.updateMemberModelislogin(membermodel); 
                 }
                 
             }
             else{
            	 message = "普通人员不能登录后台！";
            	
             }
            }
		 }
    	 insertlog("设置会员登录后台,ID:"+model.getId());
    	 JSONObject json = new JSONObject();
         //将返回信息保存在JSON对象中
           json.put("message",message);
           //设置响应编码格式，防止乱码
           response.setContentType("text/html;charset=UTF-8");
           //将数据以json格式响应给ajax
           try {
   			response.getWriter().write(json.toString());
   		} catch (IOException e) {
   			// TODO Auto-generated catch block
   			e.printStackTrace();
   		}
           return null;
    	
       
    }

   public  void insertlog(String content)
   {
	   LoginLogger logger = new LoginLogger();
      // String content = "查询会员数据";
       logger.setId( UUID.randomUUID().toString() );
       logger.setUserId( SessionHelper.getUserId() );
       logger.setLastLoginTime( new Date() );
      // logger.setLoginIp( getIpAddr(request) );
       logger.setContent(content);
       //logger.setOperation_id(UUID.randomUUID().toString());
       loginLoggerService.insert( logger );
   }

}
