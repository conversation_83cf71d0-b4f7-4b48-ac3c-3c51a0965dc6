package com.ymx.manager.controller;

import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * <AUTHOR> @version 2018/11/8
 */
@Controller
public class BaseController {
	public void getLanguage(HttpServletRequest request){
//		String mkt = "";
//		String datamkt = "";
//		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
//		if(ob.toString().contains("en"))
//		{
//			mkt="en";
//			datamkt = "en";
//		}
//		else
//		{
//			mkt="cn";
//			datamkt="cn";
//		}
//		request.setAttribute( "datamkt", datamkt );
//		request.setAttribute( "mkt", mkt );
	}
//	/**
//	 * 系统管理列表页
//	 * @return
//	 */
//	@RequestMapping("list.htm")
//	public String list(HttpServletRequest request,String memberId,String type,String id)
//	{
//		return "station_list";
//	}

}
