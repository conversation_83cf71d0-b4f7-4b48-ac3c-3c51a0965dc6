package com.ymx.manager.controller.version;

import com.ymx.service.photovoltaic.station.model.TaskUpdateModel;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.common.base.entity.User;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.photovoltaic.station.model.UpdateTaskModel;
import com.ymx.service.photovoltaic.station.service.UpdateTaskService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Controller
@RequestMapping("updateTask")
public class UpdateTaskController {

    private static final String prefix = "updateTask/";

    @Resource
    private UpdateTaskService updateTaskService;


    // 升级列表页面
    @RequestMapping("list.htm")
    public String list(HttpServletRequest request)
    {
        return prefix + "updateTask_list"; 
    }

    // 升级列表数据接口
    @RequestMapping("queryUpdateTaskList.web")
    @ResponseBody
    public CallResult queryUpdateTaskList( PageView pageView, HttpServletRequest request){

        Object uid =   request.getSession().getAttribute("userKey");
        Object checkRole=request.getSession().getAttribute("checkrole");
        String createUserId=uid.toString();
        // 如果角色是系统管理员，则可以访问所有升级任务并进行审批
        if(checkRole.toString().contains("系统管理员"))
        {
            createUserId=null;
        }
        return updateTaskService.queryUpdateTaskList(createUserId , pageView);
    }

    // 选择版本页面
    @RequestMapping("selectVersionList.htm")
    public String selectVersionList(HttpServletRequest request,String imei,String updateBeginTime){
        return prefix + "select_version_list";
    }


    // 选择采集器页面
    @RequestMapping("selectCloudList.htm")
    public String selectCloudList(HttpServletRequest request,UpdateTaskModel updateTaskModel){
        return prefix + "select_cloud_list";
    }

    // 选择组件页面
    @RequestMapping("selectComponentList.htm")
    public String selectComponentList(HttpServletRequest request,UpdateTaskModel updateTaskModel,String operator){
        request.getSession().setAttribute("versionType",updateTaskModel.getType());
        request.getSession().setAttribute("versionBom",updateTaskModel.getBomSupport());
        request.getSession().setAttribute("versionMcu",updateTaskModel.getMcuSupport());
        request.getSession().setAttribute("versionBootPartition",updateTaskModel.getBootPartition());
        request.getSession().setAttribute("id",updateTaskModel.getId());
        request.getSession().setAttribute("imei",updateTaskModel.getImei());

        return prefix + "select_component_list";
    }

    @RequestMapping("queryComponentList.web")
    @ResponseBody
    public CallResult queryComponentList(HttpServletRequest request,UpdateTaskModel updateTaskModel,PageView pageView){

        Object uid =   request.getSession().getAttribute("userKey");
        updateTaskModel.setCreateUserId(uid.toString());

        return updateTaskService.queryComponentList(updateTaskModel,pageView);
    }


    // 查看或修改升级任务页面  选择版本 采集器  组件返回页面之前也要经过的接口
    @RequestMapping(value="viewRequest.htm")
    public ModelAndView viewRequest(HttpServletRequest request , String operator , UpdateTaskModel model ){

        ModelAndView mv = new ModelAndView(prefix + "updateTask_edit");

        switch (operator)
        {
            case "selectVersion":
                request.getSession().setAttribute("versionId",model.getVersionId());
                request.getSession().setAttribute("type",model.getType());
                request.getSession().setAttribute("mcuSupport",model.getMcuSupport());
                request.getSession().setAttribute("bomSupport",model.getBomSupport());
                request.getSession().setAttribute("fileName",model.getFileName());

                request.getSession().removeAttribute("imei");
                request.getSession().removeAttribute("componentId");
                request.getSession().removeAttribute("bootPartition");
                request.getSession().removeAttribute("beforeVersion");
                request.getSession().removeAttribute("id");
                request.getSession().removeAttribute("relayId");
                request.getSession().removeAttribute("updateBeginTime");

                break;
            case "selectCloud":
                request.getSession().setAttribute("imei",model.getImei());
                break;
            case "selectComponent":
                request.getSession().setAttribute("componentId",model.getComponentId());
                request.getSession().setAttribute("bootPartition",model.getBootPartition());
                request.getSession().setAttribute("beforeVersion",model.getBeforeVersion());
                request.getSession().setAttribute("relayId",model.getRelayId());

                break;
        }

        if (operator.equals("add") || operator.equals("edit") || operator.equals("detail")) {
            User user = SessionHelper.getSessionUser();
            mv.addObject("operator", operator);
            model.setCreateUserId(user.getUserId());
            model.setCreateUserName(user.getUserName());
            if (model.getId() != null && model.getId() != 0) {
                model = updateTaskService.queryUpdateTaskById(model.getId(), operator);
                if(operator.equals("edit"))
                {
                    request.getSession().setAttribute("versionId",model.getVersionId());
                    request.getSession().setAttribute("imei",model.getImei());
                    request.getSession().setAttribute("updateBeginTime",model.getUpdateBeginTime());
                    request.getSession().setAttribute("id",model.getId());
                    request.getSession().setAttribute("taskId",model.getTaskId());
                    request.getSession().setAttribute("fileName",model.getFileName());

                }
            }
        } else {
            model = setModelValue(request, model);
        }
        mv.addObject("info" , model);
        request.getSession().setAttribute("operator",operator);

        return mv;
    }

    private UpdateTaskModel setModelValue(HttpServletRequest request, UpdateTaskModel updateTaskModel) {

        String type = (String) request.getSession().getAttribute("type");
        updateTaskModel.setType(type != null ? type : updateTaskModel.getType());

        String fileName = (String) request.getSession().getAttribute("fileName");
        updateTaskModel.setFileName(fileName != null ? fileName : updateTaskModel.getFileName());

        String bomSupport = (String) request.getSession().getAttribute("bomSupport");
        updateTaskModel.setBomSupport(bomSupport != null ? bomSupport : updateTaskModel.getBomSupport());

        String versionId = (String) request.getSession().getAttribute("versionId");
        updateTaskModel.setVersionId(versionId != null ? versionId : updateTaskModel.getVersionId());

        String mcuSupport = (String) request.getSession().getAttribute("mcuSupport");
        updateTaskModel.setMcuSupport(mcuSupport != null ? mcuSupport : updateTaskModel.getMcuSupport());

        String imei = (String) request.getSession().getAttribute("imei");
        updateTaskModel.setImei(imei != null ? imei : updateTaskModel.getImei());

        String componentId = (String) request.getSession().getAttribute("componentId");
        updateTaskModel.setComponentId(componentId != null ? componentId : updateTaskModel.getComponentId());

        String bootPartition = (String) request.getSession().getAttribute("bootPartition");
        updateTaskModel.setBootPartition(bootPartition != null ? bootPartition : updateTaskModel.getBootPartition());


        String beforeVersion = (String) request.getSession().getAttribute("beforeVersion");
        updateTaskModel.setBeforeVersion(beforeVersion != null ? beforeVersion : updateTaskModel.getBeforeVersion());

        String updateBeginTime = (String) request.getSession().getAttribute("updateBeginTime");
        updateTaskModel.setUpdateBeginTime(updateBeginTime != null ? updateBeginTime : updateTaskModel.getUpdateBeginTime());

        Long  id = (Long) request.getSession().getAttribute("id");
        updateTaskModel.setId(id != null ? id : updateTaskModel.getId());

        String taskId = (String) request.getSession().getAttribute("taskId");
        updateTaskModel.setTaskId(taskId != null ? taskId : updateTaskModel.getTaskId());

        String relayId = (String) request.getSession().getAttribute("relayId");
        updateTaskModel.setRelayId(relayId != null ? relayId : updateTaskModel.getRelayId());
        return updateTaskModel;
    }

    // 新增或修改升级任务数据接口
    @RequestMapping(value="saveOrUpdate.web")
    @ResponseBody
    public CallResult saveOrUpdate(UpdateTaskModel model, HttpServletRequest request){
        Object uid =   request.getSession().getAttribute("userKey");

        model.setCreateUserId(uid.toString());

        if (model.getId()!=null&&model.getId()!=0) {
            return updateTaskService.updateUpdateTaskModel(model);
        } else {
            return updateTaskService.saveUpdateTaskModel(model);
        }
    }



    // 删除升级任务接口
    @RequestMapping(value="deleteUpdateTask.web")
    @ResponseBody
    public CallResult deleteUpdateTask(String id,String taskId,HttpServletRequest request) {
        return updateTaskService.deleteUpdateTaskById(id,taskId);
    }

    // 重试任务接口
    @RequestMapping(value="updateTaskRetry.web")
    @ResponseBody
    public CallResult updateTaskRetry(String taskId) {

        return updateTaskService.updateTaskRetry(taskId);
    }

    // 结束任务接口
    @RequestMapping(value="updateTaskEnd.web")
    @ResponseBody
    public CallResult updateTaskEnd(String taskId) {

        return updateTaskService.updateTaskEnd(taskId);
    }

    @RequestMapping(value="versionQuery.web")
    @ResponseBody
    public CallResult versionQuery(String taskId) {

        return updateTaskService.versionQuery(taskId);
    }

    // 查看任务详情
    @RequestMapping(value="updateTaskResult.web")
    @ResponseBody
    public CallResult updateTaskResult(String taskId, PageView pageView) {

        return updateTaskService.updateTaskResult(taskId,pageView);
    }

    // 任务详情页面
    @RequestMapping("updateTaskResult.htm")
    public String updateTaskResult(HttpServletRequest request,String taskId)
    {
        request.getSession().setAttribute("taskId",taskId);

        return prefix + "updateTaskResult";
    }


    // 升级任务审批
    @RequestMapping(value="approval.web")
    @ResponseBody
    public CallResult updateTaskApproval(String taskId,int approvalResult) {

        return updateTaskService.updateTaskApproval(taskId,approvalResult);
    }

    //获取任务的版本标识和组件标识
    @RequestMapping(value = "getTaskDetails.web")
    @ResponseBody
    public CallResult getTaskDetails(String taskId) {
        TaskUpdateModel taskUpdateModel = updateTaskService.getTaskDetails(taskId);
        CallResult callResult = CallResult.newInstance();
        if (taskUpdateModel == null) {
            callResult.setRec("任务信息不存在");
            return callResult;
        }
        callResult.setReModel(taskUpdateModel);
        return callResult;
    }


}
