package com.ymx.manager.controller.member;

import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.member.entity.SysMessageModel;
import com.ymx.service.photovoltaic.member.service.SysMessageService;
import com.ymx.manager.controller.BaseController;
import com.ymx.manager.security.common.SessionHelper;
import com.ymx.manager.security.loginlogger.entity.LoginLogger;
import com.ymx.manager.security.loginlogger.service.ILoginLoggerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.UUID;

/**
 * 系统消息
 * <AUTHOR> @version 2018/11/5
 */
@Controller
@RequestMapping("sysMessage")
public class SysMessageController extends BaseController {
	@Resource
	private SysMessageService sysMessageService;
	@Autowired
	private ILoginLoggerService loginLoggerService;
	private static final String prefix = "sysMessage/";
	/**
	 * 系统消息列表
	 * @return
	 */
	@RequestMapping(value="list.htm")
	public String  sysMessageList(HttpServletRequest request){
		getLanguage(request);
		
		return  prefix+"sysMessage_list";
	}
	@RequestMapping(value="info.htm")
	public ModelAndView  sysMessageInfo(HttpServletRequest request,String operator ,SysMessageModel model){
		getLanguage(request);
		ModelAndView mv = new ModelAndView(  prefix+"sysMessage_edit");
		//		User user = SessionHelper.getSessionUser();
		mv.addObject("operator" , operator);
		//		model.setCreateUserId(user.getUserId());
		//		model.setCreateUserName(user.getUserName());
		model = sysMessageService.querySysMessageById(model.getId());
		mv.addObject("info" , model);
		return mv;
	}
	/**
	 * 系统消息列表
	 * @param request
	 * @param pageView
	 * @param model
	 * @return
	 */
	@RequestMapping("querySysMessageByList.web")
	@ResponseBody
	public CallResult querySysMessageByList(HttpServletRequest request , PageView pageView , SysMessageModel model){
		insertlog("查询系统消息列表");
		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(ob));
		return sysMessageService.querySysMessageByList(pageView , model);
	}


	/**
	 * 修改消息
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping("saveOrUpdate.web")
	@ResponseBody
	public CallResult saveOrUpdate(HttpServletRequest request , SysMessageModel model){
		CallResult callResult = CallResult.newInstance();
		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(ob));
		try{
			if(model.getContent()!=null){
				if(model.getContent().length()>2000){
					model.setContent(model.getContent().substring(0,2000));
				}
			}
			if (model.getId()!=null) {
				insertlog("修改系统消息");
				sysMessageService.updateSysMessageModel(model);
			}else{
				insertlog("新增系统消息");
				sysMessageService.insertSysMessage(model);
			}
			callResult.setReModel(1);
		}catch (Exception e){
			e.printStackTrace();
		}
		return callResult;
	}
	/**
	 * 修改消息
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping("publishSysMessage.web")
	@ResponseBody
	public CallResult publishSysMessage(HttpServletRequest request , SysMessageModel model){
		CallResult callResult = CallResult.newInstance();
		try{
			Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
			model.setLanguage(TranslateLanguage.getLanguageType(ob));
			insertlog("修改系统消息");
			sysMessageService.updateSysMessageModel(model);
			callResult.setReModel(1);
		}catch (Exception e){
			e.printStackTrace();
		}
		return callResult;
	}
	/**
	 * 删除消息
	 * @param model
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value="deleteSysMessage.web")
	@ResponseBody
	public CallResult deleteSysMessage(SysMessageModel model,
	                                         HttpServletRequest request, HttpServletResponse response){
		insertlog("删除系统消息");
		Object ob =   request.getSession().getAttribute(SessionLocaleResolver.LOCALE_SESSION_ATTRIBUTE_NAME);
		model.setLanguage(TranslateLanguage.getLanguageType(ob));
		return sysMessageService.deleteSysMessage(model);
	}
	 public  void insertlog(String content)
	    {
	 	   LoginLogger logger = new LoginLogger();
	       // String content = "查询会员数据";
	        logger.setId( UUID.randomUUID().toString() );
	        logger.setUserId( SessionHelper.getUserId() );
	        logger.setLastLoginTime( new Date() );
	       // logger.setLoginIp( getIpAddr(request) );
	        logger.setContent(content);
	       // logger.setOperation_id(UUID.randomUUID().toString());
	        loginLoggerService.insert( logger );
	    }
}
