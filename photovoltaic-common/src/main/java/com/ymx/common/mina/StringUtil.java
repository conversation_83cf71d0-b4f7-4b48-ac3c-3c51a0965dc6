package com.ymx.common.mina;

import org.apache.commons.lang.StringUtils;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

public class StringUtil extends StringUtils{
	
	private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	
	private static SimpleDateFormat sdfs = new SimpleDateFormat("yyyy-MM-dd");
	private static SimpleDateFormat sdfst = new SimpleDateFormat("yyyyMMdd");
	
	public static String toTimes(Date date){
		return sdfs.format(date);
	}
	
	public static String toTime(Date date){
		return sdf.format(date);
	}
	public static String toTimest(Date date){
		return sdfst.format(date);
	}
	
	/**
	 * function:判断字符串是否为空或者为null
	 * @param value
	 * @return
	 */
	public static boolean nullOrEmpty(String value){
		return value==null||"".equals(value)||"null".equals(value);
	}
	
	public static boolean nullOrEmptyx(Object value){
        return value==null||"".equals(value)||"null".equals(value);
    }
	
	/**
	 * 验证字符串数组
	 * <AUTHOR>
	 * @time   2014年7月22日 上午9:58:35
	 * @description
	 * @boolean
	 */
	public static boolean nullOrEmpty(String[] values){
		for(String temp:values){
			if(nullOrEmpty(temp)){
				return true;
			}
		}
		
		return false;
	}
	
	public static boolean notEmpty(String value){
		return !nullOrEmpty(value);
	}
	
	public static boolean notEmptyx(Object value){
        return !nullOrEmptyx(value);
    }
	
	/**
	 * empty to null
	 * @param value
	 * @return
	 */
	public static String emptyToNull(String value){
		return nullOrEmpty(value)?null:value;
	}
	
	public static Date getDate(Date date, int days, int hours, int minutes, int seconds){
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.add(Calendar.DATE, days);
		c.add(Calendar.HOUR, hours);
		c.add(Calendar.MINUTE, minutes);
		c.add(Calendar.SECOND, seconds);
		return c.getTime();
	}

	
	
	/**
	 * @function if value is null then return empty else return target value.toString();
	 * @param value
	 * @return 
	 */
	public static String nullToEmpty(Object value){
		return value==null?"":value.toString();
	}
	/**
	 * @function if value is null then return empty else return target value.toString();
	 * @param value
	 * @return 
	 */
	public static String nullToEmpty(String value){
		return nullOrEmpty(value)?"":value;
	}
	
	/**
	 * <判断对象是否为空>
	 * <功能详细描述>
	 * @param o
	 * @return [参数说明]
	 * 
	 * @see [类、类#方法、类#成员]
	 */
	public static boolean isNull(Object o){
        if(o == null){
            return true;
        }
        else if("".equals(o)){
            return true;
        }
        if(o instanceof String){
            o = o.toString().trim();
        }
        return "".equals(o) ? true : false;
	}
	
    public static boolean isNullNew(Object... o){
        if(isNotNull(o)){
            for(Object obj : o){
                if(isNull(obj)){
                    return true;
                }
            }
            return false;
        }
        return true;
    }
    
    /**
     * @description 判断所有不为空
     */
    public static boolean isNotNullAll(Object... os){
        if(isNotNull(os)){
            boolean isAllNot = true;
            for(Object obj : os){
                if(isNull(obj)){
                    isAllNot = false;
                }
            }
            return isAllNot;
        }
        return false;
    }
    
    /**
     * @description 判断不为空
     * @since 1.0
     */
    public static boolean isNotNull(Object o){
        return !isNull(o);
    }
	
	/**
	 * 判断是否为null或者0
	 * @param value
	 * @return 
	 */
	public static boolean nullOrZero(Integer value){
		return value==null||value==0;
	}
	
	/**
	 * 
	 * @param list
	 * @param split
	 * @return
	 */
	public static String getStringSplit(List<String> list,String split){
		if(list.size()==0){
			return "";
		}
		StringBuffer sb =  new StringBuffer();
		for(String t:list){
			sb.append("'"+t+"'").append(split);
		}
		return sb.substring(0,sb.length()-1);
	}
	
   public static String getStringJoin(String str,String split){
        if(StringUtil.isEmpty( str )){
            return "";
        }
        StringBuffer sb =  new StringBuffer();
        String[] array = str.split( "," );
        for(String t:array){
            sb.append(t).append(split);
        }
        return sb.substring(0,sb.length()-1);
    }
	
	/**
	 * 
	 * @param list
	 * @param split
	 * @return
	 */
	public static String list2StringSplit(List<String> list,String split){
		if(list.size()==0){
			return "";
		}
		StringBuffer sb =  new StringBuffer();
		for(String t:list){
			sb.append(t).append(split);
		}
		return sb.substring(0,sb.length()-1);
	}
	
	public static String getStringSplit(String[] val){
		StringBuffer sqlStr = new StringBuffer();
		for(String s:val){
			if(StringUtils.isNotBlank(s)){
				sqlStr.append(",");
				sqlStr.append("'");
				sqlStr.append(s.trim());
				sqlStr.append("'");
			}
		}
		return sqlStr.toString().substring(1);
	}
	
	public static List<String> getListJoinString(String[] args,String split) {
		List<String> columnNameList = new ArrayList<String>();
		if(args != null) {
			for(String arg : args) {
				if(arg!=null){
					String[] fields = arg.split(split);
					for(String field : fields) {
						columnNameList.add(field);
					}
				}
			}
		}
		return columnNameList;
	}
	
	public static List<String> getListJoinString(String args,String split) {
		List<String> columnNameList = new ArrayList<String>();
		if(!nullOrEmpty(args)){
			String[] fields = args.split(split);
			for(String field : fields) {
				columnNameList.add(field);
			}
		}
		if(columnNameList.size()<1){
			return null;
		}
		return columnNameList;
	}
	
	public static List<String> getListJoinString(String args) {
		return getListJoinString(args, ",");
	}
	
	public static List<String> getListJoinString(String[] args,String split,List<String> list) {
		if(args != null) {
			for(String arg : args) {
				if(arg!=null){
					String[] fields = arg.split(split);
					for(String field : fields) {
						list.add(field);
					}
				}
			}
		}
		return list;
	}
	
	public static String getDefaultSplit(String value){
		if(isBlank(value)){
			return "";
		}
		String[] splitArray = value.split(",");
		StringBuffer sqlStr = new StringBuffer();
		for(String s:splitArray){
			sqlStr.append(",");
			sqlStr.append("'");
			sqlStr.append(s.trim());
			sqlStr.append("'");
		}
		return sqlStr.toString().substring(1);
	}
	
	public static boolean isNumeric(String str){ 
	    Pattern pattern = Pattern.compile("[0-9]*"); 
	    return pattern.matcher(str).matches();    
	}
	
	public static Double isDouble(String value){
	    if(value==null){
	        return null;
	    }
	    
		Double v=null;
		try {
			v = Double.valueOf(value);
		} catch (NumberFormatException e) {
			return null;
		}
		return v;
	}
	
	public static Double roundDouble(double value)
	{
	    DecimalFormat df = new DecimalFormat("#.00");
	    return Double.valueOf(df.format(value));
	}
	
	
	public static Integer parseInt(String value){
	    Integer v=null;
        try {
            v = Integer.parseInt( value );
        } catch (NumberFormatException e) {
            return null;
        }
        return v;
    }
	
	
	
	public static double doCaculate(double first, double second, char method) {
		double result = 0;
		switch (method) {
		case '+':
			result = first + second;
			break;
		case '-':
			result = first - second;
			break;
		case '*':
			result = first * second;
			break;
		case '/':
			result = first / second;
			break;
		default:
			break;
		}
		return result;
	}
	
	/**
	 * 
	 * @Description: 转换成List<String>
	 */
	public static List<String> parseToListSplit(String strs,String split){
		List<String> list =  new ArrayList<String>();
		if(!isNull(strs)){
			StringTokenizer st = new StringTokenizer(strs,"["+split.trim()+"]");
			while(st.hasMoreTokens()){
				list.add(st.nextToken());
			}
		}
		return list;
	}
	
	public static String getRandomCode(int length){
		StringBuilder code = new StringBuilder();
		for(int i=0; i<length; i++){
			code.append(randomChar());
		}
		return code.toString();
	}
	
	private static char randomChar(){
		Random r = new Random();
		String s = "0123456789";
		return s.charAt(r.nextInt(s.length()));
	}
	
	/**  
     * 计算两个日期之间相差的天数  
     * @param smdate 较小的时间 
     * @param bdate  较大的时间 
     * @return 相差天数 
     * @throws ParseException  
     */ 
	public static int daysBetween(Date smdate,Date bdate) throws ParseException    
    {    
        smdate=sdfs.parse(sdfs.format(smdate));  
        bdate=sdfs.parse(sdfs.format(bdate));  
        Calendar cal = Calendar.getInstance();    
        cal.setTime(smdate);    
        long time1 = cal.getTimeInMillis();                 
        cal.setTime(bdate);    
        long time2 = cal.getTimeInMillis();         
        long between_days=(time2-time1)/(1000*3600*24);  
            
       return Integer.parseInt(String.valueOf(between_days));           
    }    
	/**
	 * 时间+1天
	 * @param date
	 * @return
	 */
	public static Date getNextDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, +1);//+1今天的时间加一天
        date = calendar.getTime();
        return date;
    }
}
