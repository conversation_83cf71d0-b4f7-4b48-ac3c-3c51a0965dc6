package com.ymx.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 
 * Copyright © 2017 意美旭科技
 *
 * @Description: string工具类
 * @date: 2017-09-30 下午5:31:38
 */
public class StringUtils {

	/** 字符串 默认分隔符 */
	private static final char CHAR_SPTOR = ',';
	/** 字符串 等于号分隔符 */
	private static final char CHAR_EQUAL = '=';

	/** 默认编码 */
	private static final String ENCODING_DEFAULT = "UTF-8";

	/**
	 * 判断字符串是否为空白
	 * 
	 * @param str
	 *            字符串
	 * @return boolean true-是，false-否
	 */
	public static boolean isBlank(String str) {
		int strLen;
		if (str == null || (strLen = str.length()) == 0) {
			return true;
		}
		for (int i = 0; i < strLen; i++) {
			if ((Character.isWhitespace(str.charAt(i)) == false)) {
				return false;
			}
		}
		return true;
	}
	
	/**
	 * 字符串中含子串个数
	 * @param str 字符串
	 * @param length 子串位数
	 * @return 子串个数
	 */
	public static int getLeve(String str,int length){ 
		if (isBlank(str) || str.length() < length || length <= 0
				|| str.length() % length != 0) {
			return 0;
		} 
		 return str.length()/length;
	}
	
	/**
	 * 000100010001=>[0001, 00010001, 000100010001]
	 * @param str 字符串
	 * @param length 子串长度
	 * @return 返回所有子串
	 */
	public static List<String> getNums(String str, int length) {
		if (isBlank(str) || str.length() < length || length <= 0
				|| str.length() % length != 0) {
			return null;
		}
		List<String> nums = new ArrayList<String>();
		Pattern p = Pattern.compile(String.format("(?:\\d{%s})", length));
		Matcher m = p.matcher(str);
		int i = 0;
		while (m.find()) {
			nums.add(i == 0 ? m.group() : m.group() + nums.get(i - 1));
			i++;
		}
		return nums;
	} 
	
	/**
	 * 去除字符串中重复子串
	 * 
	 * @param str
	 *            原字符
	 * @return
	 */
	public static String removeRepeatString(String str) {
		return removeRepeatString(str, CHAR_SPTOR);
	}

	/**
	 * 去除字符串中重复子串
	 * 
	 * @param str
	 *            原字符
	 * @param sptor
	 *            分隔符
	 * @return
	 */
	public static String removeRepeatString(String str, char sptor) {
		if (null == str || str.trim().length() < 1) {
			return null;
		}
		StringBuilder  ret = new StringBuilder();
		String[] strs = str.split(String.valueOf(sptor));
		for (int i = 0; i < strs.length; i++) {
			if ((null != strs[i] && strs[i].trim().length() > 0)
					&& ret.indexOf(sptor+strs[i]+sptor) == -1) {
				if(i==0){
					ret.append(sptor).append(strs[i]).append(sptor);
				}else{
					ret.append(strs[i]).append(sptor);
				} 
			}
		}
		if (ret.length() > 0) {
			ret.deleteCharAt(0);
			ret.deleteCharAt(ret.length() - 1);
		}
		return ret.toString();
	}

	/**
	 * map=> "{key1=value1,key2=value2}"
	 * 
	 * @param paramsMap
	 * @param encode
	 *            是否需要URL编码
	 * @return "{key1=value1,key2=value2}"
	 */
	public static String createString(Map<String, String> map, boolean encode) {
		if (null == map || map.size() < 1) {
			return "";
		}
		List<String> keys = new ArrayList<String>(map.keySet());
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < keys.size(); i++) {
			String key = keys.get(i);
			String value = map.get(key); 
			try {
				value = encode ? URLEncoder.encode(value, ENCODING_DEFAULT): value;
			} catch (UnsupportedEncodingException e) {
				//throw new IdapException(e);
			}
			if (i == 0) {
				sb.append("{");
			}
			if (i == keys.size() - 1) {
				sb.append(key).append(CHAR_EQUAL).append(value).append("}");
			} else {
				sb.append(key).append(CHAR_EQUAL).append(value).append(CHAR_SPTOR);
			}
		}
		return sb.toString();
	}
   
	/**
	 * 解析字符串 "{key1=value1,key2=value2}"=>map
	 * 
	 * @param str
	 *            需要解析的字符串
	 * @return {@code Map<String, String>} 解析的结果map
	 */
	public static Map<String, String> parseString(String str) {
		return parseString(str, CHAR_SPTOR, CHAR_EQUAL);
	}

	/**
	 * 解析字符串 "{key1cEqualvalue1cSptor key2cEqualvalue2}"=>map
	 * 
	 * @param str
	 *            需要解析的字符串
	 * @param cSptor
	 *            分隔符
	 * @param cEqual
	 *            分隔符
	 * @return {@code Map<String, String>} 解析的结果map
	 */
	public static Map<String, String> parseString(String str, char cSptor,
			char cEqual) {
		if(isBlank(str)){
			return null;
		}
		str=str.replaceAll("[{}]", "");
		Map<String, String> map = new HashMap<String, String>();
		int len = str.length();
		StringBuilder temp = new StringBuilder();
		char curChar;
		String key = null;
		boolean isKey = true;
		for (int i = 0; i < len; i++) {
			curChar = str.charAt(i);
			if (curChar == cSptor) {
				putKVToMap(temp, isKey, key, map);
				temp.setLength(0);
				isKey = true;
			} else {
				if (isKey) {
					if (curChar == cEqual) {
						key = temp.toString();
						temp.setLength(0);
						isKey = false;
					} else {
						temp.append(curChar);
					}
				} else {
					temp.append(curChar);
				}
			}
		}
		putKVToMap(temp, isKey, key, map);
		return map;
	}

	/**
	 * 将值添加到map集合
	 * 
	 * @param temp
	 *            值
	 * @param isKey
	 *            是否key
	 * @param key
	 *            key
	 * @param map
	 *            map集合
	 */
	private static void putKVToMap(StringBuilder temp, boolean isKey,
			String key, Map<String, String> map) {
		if (isKey) {
			key = temp.toString();
			if (key.length() == 0) {
			}
			map.put(key, "");
		} else {
			if (key.length() == 0) {
			}
			try {
				map.put(key,
						URLDecoder.decode(temp.toString(), ENCODING_DEFAULT));
			} catch (UnsupportedEncodingException e) {
			}
		}
	}
	
	/**
	 * 将json字符串转换成map<String,list<T>>
	 * @param jsonStr
	 * @param cls
	 * @return
	 * @throws ClassNotFoundException
	 */
	public static <T> Map<String, Object> parseJSON2Map(String jsonStr,Class<T> cls){  
		Map<String, Object> map = new HashMap<String, Object>();  
		//最外层解析  
		JSONObject json = (JSONObject) JSON.toJSON(jsonStr);
		for(Object k : json.keySet()){  
			Object v = json.get(k);   
			//如果内层还是数组的话，继续解析  
			if(v instanceof JSONArray){  
				List<T> list = new ArrayList<T>();  
				Iterator<Object> it = ((JSONArray)v).iterator();  
				while(it.hasNext()){  
					JSONObject json2 = (JSONObject) it.next();  
					list.add((T) JSON.parseObject(json2.toString(),cls));  
				}  
				map.put(k.toString(), list);  
			} else {  
				map.put(k.toString(), v);  
			}  
		}  
		return map;  
	} 
}
