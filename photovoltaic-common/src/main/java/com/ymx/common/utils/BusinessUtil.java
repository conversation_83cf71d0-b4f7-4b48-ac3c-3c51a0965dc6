package com.ymx.common.utils;

import com.ymx.common.base.config.ConfigConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BusinessUtil {

    private static final Logger logger = LoggerFactory.getLogger(BusinessUtil.class);

    public static int getTimeStep()
    {
        int timeStep;
        try {
            timeStep = Integer.parseInt(ConfigConstants.getConfig("TIME_STEP"));//获取间隔时间
        } catch (Exception e) {
            timeStep = 5;
            logger.error(e.getMessage());
        }
        return timeStep;
    }

}
