package com.ymx.common.utils;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
public class BaiDuUtil {

private final Logger LOGGER = LoggerFactory.getLogger(BaiDuUtil.class);
 public static  int indexCount=0;
 public static String getLongitudeLatitude(String address) throws ClientProtocolException, IOException
 {
	 HttpClient client = new DefaultHttpClient();
	// String url = "https://dev.ditu.live.com/REST/v1/Locations/?q='"+address+"'&o=json&culture=zh-CN&jsonp=Microsoft.Maps.NetworkCallbacks.f692be&key=AieuVxIN8dAdpzSD13FnCIK2qJxleMDUd06Z8XZ2OMSlpbuNDi5bgA0EqAP3QgW2&maxResults=5&userMapView=31.624415904082344,115.28037261962898,32.490333507190556,122.31162261962898&inclnb=0&incl=";
	//  String url =  "https://dev.ditu.live.com/REST/v1/Locations/?q='"+address+"'&o=json&culture=zh-CN&jsonp=Microsoft.Maps.NetworkCallbacks.f5de39&key=Av2tLzeBht42VXh28Yb8UyaIKX4XYw13P1i1qIZLJUfx0osO0cDa-OhhWXTDq9rC&maxResults=1&inclnb=0&incl=";
	String url="http://api.map.baidu.com/geocoder/v2/?address="+address+"&output=json&ak=SkwLqlESotgFt5LkZLVKMpbuaGUY3e25&callback=showLocation";
	 HttpGet httpGet = new HttpGet(url);
	 System.out.println(url);
	 HttpResponse resp = client.execute(httpGet);
	 HttpEntity httpEntity = resp.getEntity();
	 String result = EntityUtils.toString(httpEntity); 
	 System.out.println("#########################################################:"+result); 
	 int start = result.indexOf("lng");
	 int end = result.indexOf("lat");
	 String lngcode = null ;
	 String latcode = null ;
	 if(start!=-1)
	 {
		lngcode = result.substring(start+5, end-2);
		latcode = result.substring(end+5,end+22);
	 }
	 
	 System.out.println(lngcode+","+latcode);
     return lngcode+","+latcode; 
 }
 public static void main(String[] args)
 {
	// String country,String state,String postalcode,String city,String address
	 try {
		 String ja = 	getLongitudeLatitude("南京浦口区流芳路8号");
		
	} catch (ClientProtocolException e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	} catch (IOException e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	}
	 
 }

}
