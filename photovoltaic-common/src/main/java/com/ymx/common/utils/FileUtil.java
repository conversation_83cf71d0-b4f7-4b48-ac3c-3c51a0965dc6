package com.ymx.common.utils;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * 文件处理工具类
 */
public class FileUtil extends FileUtils
{
    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    // 文件流转换fil输出
    public static void inputstreamtofile(InputStream ins,File file){
        OutputStream os;
        try
        {
            os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch ( FileNotFoundException e )
        {
            System.out.println("文件找不到..........");
            e.printStackTrace();
        } catch ( IOException e )
        {
            System.out.println("文件转换异常..........");
            e.printStackTrace();
        }
     }

    /**
     * 根据文件获取文件后缀
     * @param
     */
    public static String getFileType(File file){
        return getFileType(file.getName());
    }
     
    /**
     * 根据文件名称获取文件后缀
     * @param fileName
     */
    public static String getFileType(String fileName){
        return StringUtils.substringAfterLast(fileName, ".");
    }
     
    /**
     * 根据文件名随机生成uuid文件名称
     */
    public static String uuidFileName(String fileName){
        return null;
    }
     
     
    /**
     * 判断文件夹是否存在，如不存在则创建
     */
    public static boolean isExitFile(String folderName){
        File file = new File(folderName);
        if(!file.exists()){
            return false;
        }
        return true;
    }
     
    /**
     * 删除单个文件
     * @param
     * @return 单个文件删除成功返回true，否则返回false
     */
    public static void deleteFile(File file) {
        // 路径为文件且不为空则进行删除
        if (file.isFile() && file.exists()) {
            file.delete();
        }
    }
     
    /**
     * @Description: 获取file文件
     * @UpdateAuthor: (这里填写修改人的姓名) 
     * @UpdateDescription:
     * @UpdateDate: (这里填写修改时间)
     * @param: @param path
     * @param: @return
     * @return: File
     * @throws
     */
    public static File getFile(String path) {
        File file = new File( path );
        return file;
    }
   
    /**
     * 删除单个文件
     * @param fileName 被删除的文件名
     * @return 如果删除成功，则返回true，否则返回false
     */
    public static boolean deleteFile(String fileName) {
        File file = new File(fileName);
        if (file.exists() && file.isFile()) {
            if (file.delete()) {
               System.out.println("删除单个文件 " + fileName + " 成功!");
                return true;
            } else {
                System.out.println("删除单个文件 " + fileName + " 失败!");
                return false;
            }
        } else {
            System.out.println(fileName + " 文件不存在!");
            return true;
        }
    }


    // 删除目录及目录下的文件
    public static boolean deleteDirFile(File dirFile)
    {
        boolean isSuccess;
        File[] listFiles = dirFile.listFiles();
        if(listFiles!=null)
        {
            for(File oneFile : listFiles){
                oneFile.delete();
            }
        }
        isSuccess= dirFile.delete();
        return isSuccess;
    }

    public static List<Integer> getNowCollectGapList(Path path)
    {
        Properties props = new Properties();
        try {
            props.load(Files.newInputStream(path));
        } catch (IOException e) {
            logger.info("getNowCollectGapList IOException",e);
            throw new RuntimeException(e);
        }
        String value = props.getProperty("collect.gap");
        String[] valueArray=  value.split(",");
        logger.info("value {}",value);

        return Arrays.stream(valueArray).map(Integer::parseInt)
                .collect(Collectors.toList());
    }
    
    
}
