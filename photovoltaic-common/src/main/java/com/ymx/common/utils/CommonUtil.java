package com.ymx.common.utils;


import com.ymx.common.common.annotation.ChinaVoAttribute;
import com.ymx.common.common.annotation.StrToDateAttribute;
import com.ymx.common.common.config.FileConfig;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

public class CommonUtil {

	private final static Logger logger = LoggerFactory.getLogger(CommonUtil.class);

	/**
	 * 获取内容
	 * @param: Map<String, Object> map
	 * @param: desc  内容
	 * @throws NullPointerException
	 */
	public static String getTextInfo(Map<String, String> map, String desc){
		// 模板
		if (null == desc ) {
			throw new NullPointerException("警报模版未配置");
		}
		logger.info("map is {}, desc is {}", map, desc);
		String text = desc ;
		// 配置模板里用$[field]来标识要替换的内容
		if(null != map && map.size() > 0){
			Set<String> set = map.keySet();
			for(Iterator it = set.iterator(); it.hasNext() ;){
				String key  = (String) it.next();
				String verReplace = "$[" + key + "]";
				text = text.replace(verReplace, map.get(key));
			}
		}
		return text;
	}

	/*public static void main(String[] args) {
		Map<String, String> map = new HashMap<>();
		map.put("warningDesc", "组件电量异常");
		map.put("systemName", "衢州电站 4组串");
		map.put("componentNo", null);
		map.put("cloudName", "衢州电站 4组串 采集器");
		String str = "$[systemName]电站中采集器$[cloudName]中的组件编号为: $[componentNo]出现$[warningDesc]";

		String textInfo = getTextInfo(map, str);
	}*/




	/***
	 * httpclient请求
	 * @param postUrl 接口
	 * @param contentType 消息类型 text/xml;charset=UTF-8
	 * @param params 参数
	 * @return
	 */
	public static String doPostHttpClient( String postUrl , String contentType ,  java.util.List<BasicNameValuePair> params ) {
		String retStr = "";
		// 创建HttpClient
		CloseableHttpClient httpclient = HttpClients.createDefault();
		// HttpClient
		HttpPost httpPost = new HttpPost(postUrl);
		//  设置请求和传输超时时间
		RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(160000).build();
		httpPost.setConfig(requestConfig);
		//设置Post参数
		try {
			if (CommonUtil.isEmpty(contentType)) {
				httpPost.setHeader("Content-Type",  contentType );
			}
			UrlEncodedFormEntity entity = new UrlEncodedFormEntity( params ,"UTF-8");
			httpPost.setEntity(entity);
			// 绑定数据内容
			// 执行
			CloseableHttpResponse response = httpclient.execute(httpPost);
			HttpEntity httpEntity = response.getEntity();
			if (httpEntity != null) {
				// 打印响应内容
				retStr = EntityUtils.toString(httpEntity, "UTF-8");
				logger.info("doPostHttpClient return {}",retStr);
			}
		} catch (Exception e) {
			logger.error("doPostHttpClient catch exception",e);
		} finally {
			// 释放资源
			try {
				if (null != httpclient)
					httpclient.close();
			} catch (IOException e) {
				logger.error("doPostHttpClient finally exception",e);
			}
		}
		return retStr;
	}











	/**
	 *
	 * @param clzz
	 * @param <T>
	 * @return
	 */
	public static  <T> T getBean(Class<T> clzz) {
		HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
		WebApplicationContext rootContext = WebApplicationContextUtils.getWebApplicationContext(request.getSession().getServletContext());
		return rootContext.getBean(clzz);
	}

	/**
	 * 获取一定长度的随机字符串
	 * @param length 指定字符串长度
	 * @return 一定长度的字符串
	 */
	public static String getRandomStringByLength(int length) {
		String base = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLNMOPQRSTUVWXYZ0123456789";
		Random random = new Random();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			int number = random.nextInt(base.length());
			sb.append(base.charAt(number));
		}
		return sb.toString();
	}

	/**
	 * 获取一定长度的随机字符串
	 * @param length 指定字符串长度
	 * @return 一定长度的字符串
	 */
	public static String getRandomStringByLength16(int length) {
		String base = "0123456789";
		Random random = new Random();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			int number = random.nextInt(base.length());
			sb.append(base.charAt(number));
		}
		return sb.toString();
	}

	/**
	 * 生成token(通过AES加密)
	 * @return token
	 */
	public static String makeToken() {
		// 当前毫秒数的AES加密
		try {
			return AESencrp.getInstance().encrypt(new Date().getTime() + "");
		} catch (Exception e) {
			return new Date().getTime() + "";
		}
	}

	/**
	 * 日期比较
	 * @company 江苏岳创信息
	 * @param date1  参考日期
	 * @param date2  需要被证实日期
	 * @return
	 * @return boolean
	 */
	public static boolean compareDate(String date1, String date2) {
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		boolean flag = true;
	     try {
	         Date dt1 = df.parse(date1);
	         Date dt2 = df.parse(date2);
	         if (dt1.getTime() <= dt2.getTime()) {
	        	 flag = false;
	         }
	     } catch (Exception exception) {
	         exception.printStackTrace();
	     }
	     return flag;
	}

	/**
	 * 判断是否为空
	 * @param str
	 * @return
	 */
	public static boolean isEmpty(String str){
		if(null != str && !"".equals(str) &&  !"".equals(str.trim()) && !"null".equals(str)){
			return true;
		}
		return false;
	}
	/**
	 * @Description: 获取随机数
	 * @param: @param 随机数长度
	 * @param: @return 随机数
	 */
	public static String randomNum(int length) {
		StringBuffer sb = new StringBuffer();
		Random random = new Random();
		for (int i = 0; i < length; i++) {
			int num = random.nextInt(10);
			sb.append(num);
		}
		return sb.toString();
	}
	/**
	 * @Description: 将秒数转化成时间中文
	 * @date: 2017-09-6 上午10:32:22
	 * @param: @param 秒数
	 * @param: @return 时间中文
	 */
	public static String timeText(int second) {
		String res = "0秒";
		if (second > 0) {
			String format;
			Object[] array;
			Integer hours = (int) (second / (60 * 60));
			Integer minutes = (int) (second / 60 - hours * 60);
			Integer seconds = (int) (second - minutes * 60 - hours * 60 * 60);
			String formatH = "";
			String formatM = "";
			String formatS = "";
			ArrayList<Integer> list = new ArrayList<>();
			if (seconds > 0) {
				formatS = ",d秒";
				list.add(hours);
			}
			if (seconds == 0 && minutes > 0) {
				formatM = ",d分钟%3$";
				list.add(minutes);
			}
			if (hours > 0) {
				formatH = ",d%2$";
			}
			format = "%1$" + formatH + formatM + formatS;
			if (formatH != "") {
			}
			if (formatM != "") {
			}
			if (formatS != "") {
				list.add(hours);
			}
			if (hours > 0) {
				if (minutes == 0 && seconds == 0) {
					format = "%1$,d小时";
					array = new Object[] { hours };
				} else if (seconds == 0) {
					format = "%1$,d小时%2$,d分钟";
					array = new Object[] { hours, minutes };
				} else {
					format = "%1$,d小时%2$,d分钟%3$,d秒";
					array = new Object[] { hours, minutes, seconds };
				}
			} else if (minutes > 0) {
				if (seconds == 0) {
					format = "%1$,d分钟";
					array = new Object[] { minutes };
				} else {
					format = "%1$,d分钟%2$,d秒";
					array = new Object[] { minutes, seconds };
				}
			} else {
				format = "%1$,d秒";
				array = new Object[] { seconds };
			}
			res = String.format(format, array);
		}
		return res;
	}
	/**
	 * 格式化保留小数点
	 */
	public static Double formatForDouble(Double d, int num) {
		Double value = 0.0;
		switch (num) {
			case 1:
				DecimalFormat df1 = new DecimalFormat("###.0");
				value = Double.valueOf(df1.format(d));
				break;
			case 2:
				DecimalFormat df2 = new DecimalFormat("###.00");
				value = Double.valueOf(df2.format(d));
				break;
			case 3:
				DecimalFormat df3 = new DecimalFormat("###.000");
				value = Double.valueOf(df3.format(d));
				break;
			case 4:
				DecimalFormat df4 = new DecimalFormat("###.0000");
				value = Double.valueOf(df4.format(d));
				break;
			default:
				break;
		}
		return value;
	}
	/**
	 * 传入class类返回对应的int类型
	 * @param: @param cls 类
	 * @param: @param name 查询的中文
	 * @return: Integer
	 * @throws
	 */
	public static <T> Integer getInteger(Class<T> cls, String name) {
		try {
			Field fields[] = cls.getDeclaredFields();
			String fName = null;
			for (Field field : fields) {
				String fieldName = field.getName();
				Object obj = field.get(null);
				if (null != obj) {
					if (field.getGenericType().toString().equals("class java.lang.String")) {
						if (name.equals(obj)) {
							fieldName = field.getName();
							int index = fieldName.lastIndexOf("_");
							fName = fieldName.substring(0, index);
							break;
						}
					}
				}
			}
			for (Field field : fields) {
				if (null != fName) {
					if (fName.equals(field.getName())) {
						Object o = field.get(cls);
						Integer i = (Integer.valueOf(o.toString()));
						return i;
					}
				} else {
					return 404;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	/**
	 * 判断int是否在指定数组中
	 * @date: 2017-09-6 上午10:32:22
	 * @param i
	 * @param ints
	 * @return
	 */
	public static boolean isInIntArry(int i, int[] ints) {
		boolean flag = false;
		for (int thisI : ints) {
			if (i == thisI) {
				return true;
			}
		}
		return flag;
	}
	/**
	* @Description: 翻译字段中文
	* @UpdateAuthor: (这里填写修改人的姓名)
	* @UpdateDescription:
	* @UpdateDate: (这里填写修改时间)
	* @param: @param list
	* @param: @return
	* @return: List<T>
	*/
	public static <T> List<T> hanldList(List<T> list) {
		if (null == list) {
			return list;
		}

			for (T t : list) {
				try {
				Field[] fields = t.getClass().getDeclaredFields();
				Field[] sFields = t.getClass().getSuperclass().getDeclaredFields();
				fields = (Field[]) ArrayUtils.addAll(fields, sFields);
				// 遍历所有字段
				for (Field field : fields) {
					// 获取注解类
					ChinaVoAttribute attribute = field.getAnnotation(ChinaVoAttribute.class);
					if (null != attribute && attribute.enField()) {
						String fieldName = attribute.column();
						String srcFieldName = attribute.srcColumn();
						field.setAccessible(true);
						if (!StringUtils.isBlank(fieldName) && null != field.get(t)) {
							// 日期
							if (attribute.columnType().equals("Date")) {
								if (null != field.get(t)) {
									Date date = (Date) field.get(t);
									String dateStr = DataUtil.FormatDate(date, "yyyy-MM-dd");
									BeanUtils.setProperty(t, fieldName, dateStr); // 设置值
								}
							} else if (attribute.columnType().equals("dateTime")) {
								Date date = (Date) field.get(t);
								String dateStr = DataUtil.FormatDate(date, "yyyy-MM-dd HH:mm:ss");
								BeanUtils.setProperty(t, fieldName, dateStr); // 设置值
							} else if (attribute.columnType().equals("time")) {
								Date date = (Date) field.get(t);
								String dateStr = DataUtil.FormatDate(date, "HH:mm:ss");
								BeanUtils.setProperty(t, fieldName, dateStr); // 设置值
							}else {
								// 其他
								int value = Integer.valueOf(field.get(t).toString());
								String fieldValue = getString(attribute.cls(), value);
								BeanUtils.setProperty(t, fieldName, fieldValue); // 设置值
							}
						} else if (!StringUtils.isBlank(srcFieldName)) {
							Field srcField = null;
							for (Field f : fields) {
								if (f.getName().equals(srcFieldName)) {
									srcField = f;
								}
							}
							if (null != srcField) {
								srcField.setAccessible(true);
								int value = Integer.valueOf(srcField.get(t).toString());
								String fieldValue = getString(attribute.cls(), value);
								BeanUtils.setProperty(t, field.getName(), fieldValue); // 设置值
							}
						}
					}
					if (null != attribute && attribute.isFilePath()) {
						String fieldName = field.getName();
						// 图片地址
						field.setAccessible(true);
						String src = (String) field.get(t);
						String dest = FileConfig.getDownloadPath(src);
						BeanUtils.setProperty(t, fieldName, dest); // 设置值
					}else 	if (null != attribute && attribute.isUrlPath()) {
						String fieldName = field.getName();
						// 图片地址
						field.setAccessible(true);
						String src = (String) field.get(t);
						String dest = FileConfig.WEB_URL()+src;
						BeanUtils.setProperty(t, fieldName, dest); // 设置值
					}
				}
				} catch (Exception e) {
					e.printStackTrace();
					continue;
				}
			}

		return list;
	}
	/**
	* @Description: 翻译字段中文
	* @UpdateAuthor: (这里填写修改人的姓名)
	* @UpdateDescription:
	* @UpdateDate: (这里填写修改时间)
	* @param: @param list
	* @param: @return
	* @return: List<T>
	*/
	public static <T> T hanldEntity(T t) {
		try {
			Field[] fields = t.getClass().getDeclaredFields();
			Field[] sFields = t.getClass().getSuperclass().getDeclaredFields();
			fields = (Field[]) ArrayUtils.addAll(fields, sFields);
			// 遍历所有字段
			for (Field field : fields) {
				// 获取注解类
				ChinaVoAttribute attribute = field.getAnnotation(ChinaVoAttribute.class);
				if (null != attribute && attribute.enField()) {
					String fieldName = field.getName() + "Ch";
					String srcFieldName = attribute.srcColumn();
					// String fieldName = attribute.column();
					field.setAccessible(true);
					if (!StringUtils.isBlank(fieldName) && null != field.get(t)) {
						// 日期
						if (attribute.columnType().equals("Date")) {
							if (null != field.get(t)) {
								Date date = (Date) field.get(t);
								String dateStr = DataUtil.FormatDate(date, "yyyy-MM-dd");
								BeanUtils.setProperty(t, fieldName, dateStr); // 设置值
							}
						} else if (attribute.columnType().equals("dateTime")) {
							Date date = (Date) field.get(t);
							String dateStr = DataUtil.FormatDate(date, "yyyy-MM-dd HH:mm:ss");
							BeanUtils.setProperty(t, fieldName, dateStr); // 设置值
						}else if (attribute.columnType().equals("time")) {
							Date date = (Date) field.get(t);
							String dateStr = DataUtil.FormatDate(date, "HH:mm:ss");
							BeanUtils.setProperty(t, fieldName, dateStr); // 设置值
						} else {
							// 其他
							int value = Integer.valueOf(field.get(t).toString());
							String fieldValue = getString(attribute.cls(), value);
							BeanUtils.setProperty(t, fieldName, fieldValue); // 设置值
						}
					} else if (!StringUtils.isBlank(srcFieldName)) {
						Field srcField = null;
						for (Field f : fields) {
							if (f.getName().equals(srcFieldName)) {
								srcField = f;
							}
						}
						if (null != srcField) {
							srcField.setAccessible(true);
							int value = Integer.valueOf(srcField.get(t).toString());
							String fieldValue = getString(attribute.cls(), value);
							BeanUtils.setProperty(t, field.getName(), fieldValue); // 设置值
						}
					}
				}
				if (null != attribute && attribute.isFilePath()) {
					String fieldName = field.getName();
					// 图片地址
					field.setAccessible(true);
					String src = (String) field.get(t);
					String dest = FileConfig.getDownloadPath(src);
					BeanUtils.setProperty(t, fieldName, dest); // 设置值
				}
				if (null != attribute && attribute.isUrlPath()) {
					String fieldName = field.getName();
					// 图片地址
					field.setAccessible(true);
					String src = (String) field.get(t);
					String dest = FileConfig.WEB_URL()+src;
					BeanUtils.setProperty(t, fieldName, dest); // 设置值
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return t;
	}
	/**
	 * 传入class类返回对应的int类型
	 * @param: @param cls 类
	 * @param: @param num 查询的编号
	 * @return: String
	 */
	public static <T> String getString(Class<T> cls, Integer num) {
		try {
			Field fields[] = cls.getDeclaredFields();
			String fName = null;
			for (Field field : fields) {
				String fieldName = field.getName();
				Object obj = field.get(null);
				if (null != obj) {
					if (field.getGenericType().toString().equals("class java.lang.Integer")) {
						if (num.equals(obj)) {
							fieldName = field.getName();
							fName = fieldName + "_NAME";
							break;
						}
					}
				}
			}
			for (Field field : fields) {
				if (null != fName) {
					if (fName.equals(field.getName())) {
						Object o = field.get(cls);
						String value = o.toString().toString();
						return value;
					}
				} else {
					return "";
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	/**
	 * 判断String是否在指定数组中
	 * @param s
	 * @param strs
	 * @return
	 */
	public static boolean isInStringArry(String s, String[] strs) {

		boolean flag = false;
		for (String thisS : strs) {
			if (s.equals(thisS)) {
				return true;
			}
		}
		return flag;
	}
	/**
	 * @Description:  app端处理null值
	 * @UpdateAuthor: (这里填写修改人的姓名)
	 * @UpdateDescription:
	 * @UpdateDate: (这里填写修改时间)
	 * @param: @param list
	 * @param: @return
	 * @return: List<T>
	 */
	public static <T> List<T> handleEmpty(List<T> list) {
		if (null == list || 0 >= list.size()) {
			return list;
		}
		for (T t : list) {
			if (null == t) {
				return list;
			}
			Field[] fields = t.getClass().getDeclaredFields();
			for (Field field : fields) {
				try {
					// 获取注解类
					ChinaVoAttribute attribute = field.getAnnotation(ChinaVoAttribute.class);
					String name = field.getName();
					field.setAccessible(true);
					Object obj = field.get(t);
					if (null == obj) {
						if (null != attribute && attribute.column().equals("birthDateCh")) {
							// 截取身份证号码上的出生日期
							String birthDate = subBirthDate(BeanUtils.getProperty(t, "idCard"), 6, 14);
							BeanUtils.setProperty(t, name, DataUtil.parseDate(birthDate, 5));
						} else {
							BeanUtils.setProperty(t, name, "");
						}
					}
					if (null != attribute && attribute.enField()) {
						String fieldName = field.getName() + "Ch";
						// String fieldName = attribute.column();
						field.setAccessible(true);
						String valueCh = BeanUtils.getProperty(t, fieldName);
						System.out.println(valueCh);
						if (null == valueCh || "".equals(valueCh)) {
							BeanUtils.setProperty(t, fieldName, "");
						}
					}
				} catch (IllegalArgumentException | IllegalAccessException e) {
					e.printStackTrace();
				} catch (InvocationTargetException e) {
					e.printStackTrace();
				} catch (NoSuchMethodException e) {
					e.printStackTrace();
				}
			}
		}
		return list;
	}
	/**
	 * 截取身份证号码上的出生日期
	 */
	public static String subBirthDate(String idCard, int start, int end) {
		String date = idCard.substring(start, end);
		String year = date.substring(0, 4);
		String month = date.substring(4, 6);
		String day = date.substring(6, 8);
		String birthDate = year + "-" + month + "-" + day;
		return birthDate;
	}
	/**
	 * 处理map为空
	 */
	public static String handleMapEmpty(String json) {
		Map<String, String> map = StringUtils.parseString(json);
		Iterator<String> iterator = map.keySet().iterator();
		while (iterator.hasNext()) {
			String key = iterator.next();
			String value = map.get(key);
			// 删除满足value以abc开头的键值对
			if (null == value || "".equals(value)) {
				// map.remove(value);
				iterator.remove(); // 关键代码，同步modCount和expectedModCount
			}
		}
		return map.toString();
	}
	/**
	 * 计算时间差
	 */
	public static String getDatePoor(Date endDate, Date nowDate) {
		if(nowDate.compareTo(endDate)> 0){
			return "已失效";
		}
		long nd = 1000 * 24 * 60 * 60;
		long nh = 1000 * 60 * 60;
		long nm = 1000 * 60;
		// long ns = 1000;
		// 获得两个时间的毫秒时间差异
		long diff = endDate.getTime() - nowDate.getTime();
		// 计算差多少天
		long day = diff / nd;
		// 计算差多少小时
		long hour = diff % nd / nh;
		// 计算差多少分钟
		long min = diff % nd % nh / nm;
		// 计算差多少秒//输出结果
		return day + "天" + hour + "小时" + min + "分钟";
	}

	/**
	 * 编辑时使用
	 * 前端向后台传递日期格式字符串　转成对应的　日期格式
	 * @company 江苏岳创信息
	 * @date 2017-4-12
	 * @param t
	 * @return
	 * T
	 */
	public static <T> T hanldStrToDate(T t) {
		try {
			Field[] fields = t.getClass().getDeclaredFields();
			Field[] sFields = t.getClass().getSuperclass().getDeclaredFields();
			fields = (Field[]) ArrayUtils.addAll(fields, sFields);
			// 遍历所有字段
			for (Field field : fields) {
				// 获取注解类
				StrToDateAttribute attribute = field.getAnnotation(StrToDateAttribute.class);
				if (null != attribute && attribute.enField()) {
					//需要翻译到哪个成员变量
					String fieldName = attribute.column();
					field.setAccessible(true);
					if (!StringUtils.isBlank(fieldName) && null != field.get(t)) {
						// 日期
						if (attribute.columnType().equals("Date")) {
							if (null != field.get(t)) {
								String dateStr = (String) field.get(t);
								Date date = DataUtil.parseDate(dateStr, "yyyy-MM-dd");
								BeanUtils.setProperty(t, fieldName, date); // 设置值
							}
						} else if (attribute.columnType().equals("dateTime")) {
							String dateStr = (String) field.get(t);
							Date date = DataUtil.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
							BeanUtils.setProperty(t, fieldName, date); // 设置值
						}
						else if (attribute.columnType().equals("time")) {
							String dateStr = (String) field.get(t);
							Date date = DataUtil.parseDate(dateStr, "HH:mm:ss");
							BeanUtils.setProperty(t, fieldName, date); // 设置值
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return t;
	}
}
