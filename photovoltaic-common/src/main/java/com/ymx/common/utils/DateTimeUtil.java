package com.ymx.common.utils;

import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.util.Assert;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * 
 * <p>
 * Title: DateTimeUtil
 * </p>
 * <p>
 * Description: DateTimeUtil
 * </p>
 * <p>
 * Copyright: Copyright (c) 2013-9-22
 * </p>
 * <p>
 * Company: ASIAINFO
 * </p>
 * 
 * @version 1.0
 * @Comment 日期公用类
 */

public class DateTimeUtil {

	public static Date addDate(Date sDate, int i, int calendarType) {
		Assert.notNull(sDate, "日期不能为空");
		Calendar localDate = new GregorianCalendar();
		localDate.setTime(sDate);
		localDate.add(calendarType, i);
		return localDate.getTime();
	}

	/*
	 * 返回指定天数后的日期
	 * 
	 * @param sDate 指定日期
	 * 
	 * @param iDay 需要增加的天数
	 * 
	 * @return 返回指定天数后的日期
	 */
	public static Date addDay(Date sDate, int iDay) {
		return DateTimeUtil.addDate(sDate, iDay, Calendar.DAY_OF_MONTH);
	}

	/*
	 * 返回指定小时后的日期
	 * 
	 * @param sDate 指定日期
	 * 
	 * @param iDay 需要增加的小时数
	 * 
	 * @return 返回指定小时数后的日期
	 */
	public static Date addHOUR(Date sDate, int iHOUR) {
		return DateTimeUtil.addDate(sDate, iHOUR, Calendar.HOUR_OF_DAY);
	}

	/*
	 * 返回指定分钟后的日期
	 * 
	 * @param sDate 指定日期
	 * 
	 * @param iDay 需要增加的分钟数
	 * 
	 * @return 返回指定分钟数后的日期
	 */
	public static Date addMINUTE(Date sDate, int iMINUTE) {
		return DateTimeUtil.addDate(sDate, iMINUTE, Calendar.MINUTE);
	}

	/*
	 * 返回指定秒后的日期
	 * 
	 * @param sDate 指定日期
	 * 
	 * @param iDay 需要增加的秒数
	 * 
	 * @return 返回指定秒数后的日期
	 */
	public static Date addSECOND(Date sDate, int iSECOND) {
		return DateTimeUtil.addDate(sDate, iSECOND, Calendar.SECOND);
	}

	/*
	 * 返回某天的初始时间
	 * 
	 * @param sDate 指定日期
	 * 
	 * @return 返回某天的初始时间
	 */
	public static Date getBeginDate(Date sDate) {
		Assert.notNull(sDate, "日期不能为空");
		Calendar localDate = new GregorianCalendar();
		localDate.setTime(sDate);
		localDate.set(Calendar.HOUR_OF_DAY, 0);
		localDate.set(Calendar.MINUTE, 0);
		localDate.set(Calendar.SECOND, 0);
		localDate.set(Calendar.MILLISECOND, 0);
		return localDate.getTime();
	}

	/*
	 * 返回某天的结束时间
	 * 
	 * @param sDate 指定日期
	 * 
	 * @return 返回某天的初始时间
	 */
	public static Date getEndDate(Date sDate) {
		Assert.notNull(sDate, "日期不能为空");
		Calendar localDate = new GregorianCalendar();
		localDate.setTime(sDate);
		localDate.set(Calendar.HOUR_OF_DAY, 23);
		localDate.set(Calendar.MINUTE, 59);
		localDate.set(Calendar.SECOND, 59);
		localDate.set(Calendar.MILLISECOND, 0);
		return localDate.getTime();
	}

	/**
	 * 返回年月日
	 * 
	 * @return yyyyMMdd
	 */
	public static String getTodayChar8() {
		return DateFormatUtils.format(new Date(), "yyyyMMdd");
	}

	/**
	 * 返回 年月日小时分秒
	 * 
	 * @return
	 */
	public static String getTodayChar14() {
		return DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
	}

	/**
	 * 返回 2位年月日小时分
	 * 
	 * @return yyMMddHHmmss
	 */
	public static String getTodayChar12Short() {
		return DateFormatUtils.format(new Date(), "yyMMddHHmmss");
	}

	/**
	 * 返回 年月日小时分秒 毫秒
	 * 
	 * @return
	 */
	public static String getTodayChar17() {
		return DateFormatUtils.format(new Date(), "yyyyMMddHHmmssS");
	}

	/**
	 * 返回 年月日小时分秒
	 * 
	 * @return
	 */
	public static String getTodayChar14En() {
		return DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
	}

	/**
	 * 比较两个时间间隔的小时数
	 * 
	 * @param
	 */
	public static Long compareDays(Date dateAfter, Date dateBefore) {
		long diff = dateAfter.getTime() - dateBefore.getTime();
		long hours = diff / (1000 * 60 * 60);
		return hours;
	}

	/**
	 * 返回2099-12-30 23:59:59
	 * 
	 * @param
	 */
	public static Date getDefaultExpDate() {
		Calendar localDate = new GregorianCalendar();
		localDate.set(2099, 12, 30, 23, 59, 59);
		return localDate.getTime();
	}

	public static void main(String[] args) {
		System.out.println(DateTimeUtil.getDefaultExpDate());
	}
	
	
	public static String getDateString12(Date date) {
		SimpleDateFormat sf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String strDate=sf.format(date);
		return strDate;
	}

	// 计算出现在距离下一分钟还有多少秒
	public static int getSecondsToNextMinute()
	{
		LocalDateTime now = LocalDateTime.now();
		int currentSecond = now.getSecond();
		return  60 - currentSecond;
	}

}
