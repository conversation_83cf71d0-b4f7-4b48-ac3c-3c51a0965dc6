package com.ymx.common.utils;

import com.ymx.common.base.config.ConfigConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 获取配置的工具类
 */
public class ConfigUtil {

    private static final Logger logger = LoggerFactory.getLogger(ConfigUtil.class);


    // 获取时间间隔数据
    public static int getTimeStep()
    {
        int timeStep;
        try {
            timeStep = Integer.parseInt(ConfigConstants.getConfig("TIME_STEP"));//获取间隔时间
        } catch (Exception e) {
            timeStep = 5;
            logger.error(e.getMessage());
        }
        return timeStep;
    }

}
