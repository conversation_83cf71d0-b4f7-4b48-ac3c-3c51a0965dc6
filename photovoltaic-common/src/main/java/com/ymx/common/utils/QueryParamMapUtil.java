package com.ymx.common.utils;

import org.apache.commons.lang.ArrayUtils;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.Map;

public class QueryParamMapUtil {
	/**
	 * 查询条件设置值
	 * @param map 集合对象
	 * @param data 数据对象
	 * @param dateFlag 日期格式
	 * @param <T>
	 */
	public static <T>  void setMapPropreties(Map<String,Object> map , T data , boolean dateFlag){
		T obj = (T) data;
		if(null != obj){
	        Field[] fields = obj.getClass().getDeclaredFields();
	        Field[] sFields = obj.getClass().getSuperclass().getDeclaredFields();
			fields = (Field[]) ArrayUtils.addAll(fields, sFields);
			
		    for (short i = 0; i < fields.length; i++ ) {
	        	Field field = fields[i];
	            String fieldName = field.getName();
	            field.setAccessible(true);
				try {
					Object val = field.get(obj);
					String fieldType = field.getGenericType().toString();
		            String textVal = "";
		            if("class java.util.Date".equals(fieldType)){
		            	if (val != null) {
		            		if(dateFlag){
		            			textVal = dateToStr(val, "yyyy-MM-dd" );
		            		} else{
		            			textVal = dateToStr(val, "yyyy-MM-dd HH:mm:ss");
		            		}
		                }
		            }else{
		                if (val != null) {
		                    textVal = val.toString();
		                }
		            }
		            map.put(fieldName, textVal);
				} catch (IllegalArgumentException e) {
					e.printStackTrace();
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				}
		    }
		}
	}
	
	/**
	 * 处理日期格式
	 * @param date 日期
	 * @param format 格式
	 * @return
	 */
	private static String dateToStr( Object date , String format ){
		String dateStr = "";
		if( date != null ){
			SimpleDateFormat formatter = new SimpleDateFormat(format);
			dateStr = formatter.format(date);
		}
		return dateStr;
	}

}
