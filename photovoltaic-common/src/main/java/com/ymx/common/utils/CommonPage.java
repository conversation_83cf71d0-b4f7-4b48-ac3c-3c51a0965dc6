package com.ymx.common.utils;

import com.ymx.common.common.result.PageInfo;
import com.ymx.common.common.result.PageView;
import org.apache.commons.lang.StringUtils;

/**
 * @Description: (封装公共的分页)
 */
public class CommonPage {
    
    /**
     * 分页和排序公共方法
     * @param total 总条数
     * @param pageView 分页参数
     * @param defaultOrder 默认排序
     * @return
     */
    public static PageInfo getPageInfo(int total, PageView pageView, String defaultOrder) {
        //不分页
        if(null == pageView || -1 == pageView.getPageSize() ) {
            PageInfo pageInfo = new PageInfo(0,pageView.getPageSize(),0);
            return pageInfo;
        }
        //起始页 
        int pageIndex=(null!=pageView&&0!=pageView.getPageNo())
                ?pageView.getPageNo():1;
        //页大小 
        int pageSize=(null!=pageView&&0!=pageView.getPageSize())
                ?pageView.getPageSize():-1;
        //分页对象      
        PageInfo pageInfo=new PageInfo(pageIndex, pageSize, total); 
        //排序信息
        String orderInfo = defaultOrder;
        if (StringUtils.isNotBlank(pageView.getOrder())
                && StringUtils.isNotBlank(pageView.getSort())) { 
            orderInfo = pageView.getSort() + " " + pageView.getOrder();
        }
        pageInfo.setOrderInfo(orderInfo);
        return pageInfo;
    }
    
    /**
     * 存在本地数据时分页和排序公共方法
     * @param total 总条数
     * @param localNum 本地数据条数
     * @param pageView 分页参数
     * @param defaultOrder 默认排序
     * @return
     */
    public static PageInfo getPageInfoExitsLocal(int total,int localNum,PageView pageView,String defaultOrder) {
    	int pageNo = pageView.getPageNo();
    	int pageSize = pageView.getPageSize();
    	int next;
    	int size;
    	if(localNum >= pageNo*pageSize){
    		next = 0;
    		size = 0;
    	}else{
    		int remain = pageNo*pageSize - localNum;
    		if(remain < pageSize){
    			next = 0;
    			size = remain;
    		}else{
    			next = remain - pageSize;
    		    size = pageSize;
    		}
    		
    	}
    	 //分页对象      
        PageInfo pageInfo=new PageInfo(next, size); 
        //排序信息
        String orderInfo = defaultOrder;
        if (StringUtils.isNotBlank(pageView.getOrder())
                && StringUtils.isNotBlank(pageView.getSort())) { 
            orderInfo = pageView.getSort() + " " + pageView.getOrder();
        }
        pageInfo.setOrderInfo(orderInfo);
        return pageInfo;
    }
}
