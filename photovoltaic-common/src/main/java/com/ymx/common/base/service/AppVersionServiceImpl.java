package com.ymx.common.base.service;

import com.ymx.common.utils.CommonPage;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.QueryParamMapUtil;
import com.ymx.common.base.dao.IAppVersionDao;
import com.ymx.common.base.entity.AppVersionModel;
import com.ymx.common.common.constant.ErrCode;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageInfo;
import com.ymx.common.common.result.PageView;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AppVersionServiceImpl implements AppVersionService {

    @Autowired
    private IAppVersionDao iAppVersionDao;

    @Override
    public CallResult queryAppVersion(Map<String, Object> map ) {
        CallResult callResult = CallResult.newInstance();
        String num = (String)map.get( "versionNum" );
        map.clear();
        AppVersionModel appVersionModel = iAppVersionDao.queryAppVersion( map );
        if(appVersionModel==null){
	        callResult.setReModel(null);
	        return callResult;
        }
        if(num==null || num.length()==0){
	        if("en".equals(map.get("language"))){
		        callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN);
	        }else{
		        callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
	        }
        }
        try {
            int result = compareVersion(num, appVersionModel.getVersionNum());
            if (result >= 0) {
                callResult.setReModel(null);
            } else {
                callResult.setReModel(appVersionModel);
            }

        }catch (Exception e){
	        if("en".equals(map.get("language"))){
		        callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN);
	        }else{
		        callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
	        }
	        e.printStackTrace();
        }

        return callResult;
    }


    /**
     * 比较版本号的大小,前者大则返回一个正数,后者大返回一个负数,相等则返回0
     * @param versionSend
     * @param versionQuery
     * @return
     */
    public static int compareVersion(String versionSend, String versionQuery) throws Exception {
        if (versionSend == null || versionQuery == null) {
            throw new Exception("compareVersion error:illegal params.");
        }
        if(versionSend.equals(versionQuery))
        {
            return 0;
        }

        String[] versionArray1 = versionSend.split("\\.");//注意此处为正则匹配，不能用"."；
        String[] versionArray2 = versionQuery.split("\\.");
        int idx = 0;
        int minLength = Math.min(versionArray1.length, versionArray2.length);//取最小长度值
        int diff = 0;
        while (idx < minLength
                && (diff = versionArray1[idx].compareTo(versionArray2[idx])) == 0) {//再比较字符
            ++idx;
        }
        //如果已经分出大小，则直接返回，如果未分出大小，则再比较位数，有子版本的为大；
        diff = (diff != 0) ? diff : versionArray1.length - versionArray2.length;
        return diff;
    }

    /**
     * @param model
     * @return
     * @since 版本管理的新增与修改
     */
    @Override
    public CallResult operateVersion(AppVersionModel model) {
        CallResult callResult = CallResult.newInstance();
        //版本号、链接地址、更新说明不为空校验
        if(StringUtils.isEmpty(model.getVersionNum())
                || StringUtils.isEmpty(model.getUrl())
                || StringUtils.isEmpty(model.getContent())){
            callResult.setErr("0000" , "版本号、链接地址、更新说明不能为空");
            return callResult;
        }
        if(null == model.getId() ||  model.getId().equals("")){
            iAppVersionDao.addVersion(model);
        }else {
            iAppVersionDao.updateVersion(model);
        }
        return callResult;
    }

    /**
     * @param pageView
     * @param model
     * @since 版本列表
     */
    @Override
    public CallResult queryVersionList(PageView pageView, AppVersionModel model) {
        CallResult callResult = CallResult.newInstance();
        Map<String, Object> map = new HashMap<String, Object>();
        int total = iAppVersionDao.queryAppVersionCount(model);
	    if (null != pageView) {
            PageInfo pageInfo = CommonPage.getPageInfo(total, pageView, "");
            map.put("page", pageInfo);
            callResult.setPageInfo(pageInfo);
        }
        QueryParamMapUtil.setMapPropreties(map, model, true);
        List<AppVersionModel> list = iAppVersionDao.queryAppVersionList(map);
        //清空map
        map.clear();
        if (!list.isEmpty()) {
            CommonUtil.hanldList(list);
        }
        map.put("data", list);
        callResult.setCount(total);
        callResult.setReModel(map);
        return callResult;
    }

    /**
     * @param model
     * @return
     * @since 版本详情
     */
    @Override
    public AppVersionModel queryVersionDetail(AppVersionModel model) {
        Map<String , Object> map = new HashMap<>();
        map.put("id" , model.getId());
        return iAppVersionDao.queryAppVersion(map);
    }

	/**
	 * 删除版本
	 * @param model
	 * @return
	 */
	@Override
	public CallResult deleteApp(AppVersionModel model) {
		CallResult callResult = CallResult.newInstance();
		
		List<String> list = new ArrayList<>();
        Collections.addAll(list, model.getId().split(",", -1));
        //map.put("list", list);
		//List<String> list = Arrays.asList(id);
		int row = iAppVersionDao.deleteAppVersion(list);
		if (0 == row) {
            callResult.setErrMsg("删除失败");
            return callResult;
        }
		else{
			callResult.setRec("删除成功");
            return callResult;
		}
	}
}