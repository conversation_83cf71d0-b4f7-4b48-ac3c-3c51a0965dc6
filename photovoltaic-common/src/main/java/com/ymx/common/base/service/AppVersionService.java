package com.ymx.common.base.service;

import com.ymx.common.base.entity.AppVersionModel;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import java.util.Map;

public interface AppVersionService {
    
    /**
     * @Description:app升级接口
     * @UpdateAuthor: (这里填写修改人的姓名) 
     * @UpdateDate: (这里填写修改时间)
     * @param: @param map
     * @param: @return
     * @return: AppVersion
     */
    public CallResult queryAppVersion(Map<String, Object> map);

    /**
     * @since 版本管理的新增与修改
     * @param model
     * @return
     */
    CallResult operateVersion(AppVersionModel model);

    /**
     * @since       版本列表
     * @param
     * @return
     */
    CallResult queryVersionList(PageView pageView , AppVersionModel model);

    /**
     * @since       版本详情
     * @param model
     * @return
     */
    AppVersionModel queryVersionDetail(AppVersionModel model);

	/**
     * 删除版本
	 * @param id
	 * @return
	 */
	CallResult deleteApp(AppVersionModel model);
}