package com.ymx.common.base.config;

import com.alibaba.druid.util.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * @DESC 基本的properties文件配置内容
 * @DATE 2018/7/23
 * @NAME ConfigConstants
 * @MOUDELNAME 模块
 */
public class ConfigConstants extends PropertyPlaceholderConfigurer {

    //存取集合
    private static Map<String, String> properties = new HashMap<String, String>();

    @Override
    protected void processProperties(ConfigurableListableBeanFactory beanFactory, Properties props)
            throws BeansException {
        // 初始化成员变量
        for (Object key : props.keySet()) {
            // 获取配置文件的key-value
            String keyStr = key.toString();
            String value = props.getProperty(keyStr);
            // 保存配置
            properties.put(keyStr, value);
        }
    }

    /**
     * 获取配置属性
     * @param key 键
     * @param defvalue 为空时的默认值
     * @return
     */
    public static String getConfig(String key, String defvalue) {
        String value = getConfig(key);
        if(StringUtils.isEmpty(value)) {
            return defvalue;
        }
        return value;
    }

    public static String getConfig(String key) {
        return properties.get(key);
    }


}
