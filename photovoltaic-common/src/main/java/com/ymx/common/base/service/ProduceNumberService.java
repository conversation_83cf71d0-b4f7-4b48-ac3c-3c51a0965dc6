package com.ymx.common.base.service;

import com.ymx.common.base.entity.ProduceNumberModel;
import java.util.List;

public interface ProduceNumberService {
	/**
     * 根据类别获取记录
	 * @param categoryType
	 * @return
	 */
	List<ProduceNumberModel> queryProduceNumberByCategoryType(String categoryType);

	/***
	 * 更新编号
	 * @param categoryType 类别(开头字母)
	 */
	void updateProduceNumber(String categoryType);

	/***
	 * 新增编号
	 * @param categoryType  类别(开头字母)
	 */
	void insertProduceNumber(String categoryType);

	/***
	 * 获取编号
	 * @param categoryType 类别(开头字母)
	 * @return
	 */
	public String getSerialNo(String categoryType);
}
