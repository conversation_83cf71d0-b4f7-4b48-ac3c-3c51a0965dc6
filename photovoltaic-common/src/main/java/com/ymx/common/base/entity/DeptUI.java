package com.ymx.common.base.entity;

import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 *
 */

@Component
public class DeptUI implements Serializable {
	
    /**
	 * 
	 */
	private static final long serialVersionUID = -2890013785211792061L;

	private boolean flag = true;
    
    private List<Dept> rows = new ArrayList<>();

	public boolean isFlag() {
		return flag;
	}

	public void setFlag(boolean flag) {
		this.flag = flag;
	}

	public List<Dept> getRows() {
		return rows;
	}

	public void setRows(List<Dept> rows) {
		this.rows = rows;
	}
}
