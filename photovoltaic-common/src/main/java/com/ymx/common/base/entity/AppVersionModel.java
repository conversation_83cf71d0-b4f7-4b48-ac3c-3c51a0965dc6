package com.ymx.common.base.entity;

import com.ymx.common.common.annotation.ChinaVoAttribute;
import java.io.Serializable;
import java.util.Date;

public class AppVersionModel implements Serializable {

	/**
     * 主键编号
	 */
	private String id;

	/**
	 * 版本号
	 */
	private String versionNum;

	/**
	 * 下载地址
	 */
//    @ChinaVoAttribute(isFilePath = true)
	private String url;

	/**
	 * 更新说明
	 */
	private String content;

	/**
	 * 创建时间
	 */
    @ChinaVoAttribute(enField = true,columnType = "dateTime",column="createTimeCh")
	private Date createTime;
    private String createTimeCh;

	private String language;

    public String getCreateTimeCh() {
        return createTimeCh;
    }

    public void setCreateTimeCh(String createTimeCh) {
        this.createTimeCh = createTimeCh;
    }

   

    public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getVersionNum()
    {
        return versionNum;
    }

    public void setVersionNum( String versionNum )
    {
        this.versionNum = versionNum;
    }

    public String getUrl()
    {
        return url;
    }

    public void setUrl( String url )
    {
        this.url = url;
    }

    public String getContent()
    {
        return content;
    }

    public void setContent( String content )
    {
        this.content = content;
    }

    public Date getCreateTime()
    {
        return createTime;
    }

    public void setCreateTime( Date createTime )
    {
        this.createTime = createTime;
    }

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}
}