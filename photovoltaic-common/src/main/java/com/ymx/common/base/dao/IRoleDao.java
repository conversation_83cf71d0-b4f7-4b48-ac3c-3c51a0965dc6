package com.ymx.common.base.dao;

import com.ymx.common.base.entity.Role;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


/**
 * Copyright © 2017 意美旭科技
 * @Description:  角色查询dao
 */
@Repository
public interface IRoleDao {

	public List<Role> searchRoles();


	public Role queryRole(Map<String, Object> paramMap);
	/**
	 * 新增角色时检查角色名称是否存在
	 */
	public int queryRoleNameForExist(Map<String, Object> paramMap);
	
	/**
	 * 增加角色信息
	 */
	public int addRole(Role role);
	
	/**
	 * 修改角色信息
	 */
	public int editRole(Role role);
	/**
	 * 逻辑删除角色信息
	 */
	public int deleteRole(Map<String, Object> paramMap);
	
	/**
	 * 删除角色的菜单信息
	 */
	public void deleteRoleMenus(Map<String, Object> paramMap);
	
	/**
	 * 删除角色的权限信息
	 */
	public void deleteRoleMenuFunctions(Map<String, Object> paramMap);
	/**
	 * 删除角色与用户的关联关系
	 */
	public void deleteRoleUsersRel(Map<String, Object> paramMap);
	
	/**
	 * 给角色进行操作权限分配
	 */
	public void setRoleMenuFunctions(List<Map<String, String>> funs);
	
	/**
	 * 给角色进行菜单权限分配
	 */
	public void setRoleMenus(List<Map<String, String>> menus);
	
	/**
	 * 新增应用角色信息
	 */
	public void insertAppRole(Map<String, Object> paramMap);
	
	/**
	 * 删除应用角色信息
	 */
	public void deleteAppRole(Map<String, Object> paramMap);
	
	/**
	 * 批量删除角色
	 * @param paramMap
	 * @return
	 */
	public int batchDeleteRole(Map<String, Object> paramMap);
	
	/**
	 * 批量删除角色所对应的菜单分配信息
	 * @param paramMap
	 */
	public void batchDeleteRoleMenus(Map<String, Object> paramMap);
	/**
	 * 批量删除角色所对应分配的操作功能
	 * @param paramMap
	 */
	public void batchDeleteRoleMenuFunctions(Map<String, Object> paramMap);
	
	/**
	 * 批量删除用户所分配的该角色
	 * @param paramMap
	 */
	public void batchDeleteRoleUsersRel(Map<String, Object> paramMap);
	
	/***
	 * 查询角色
	 * @desc
	 * @date 2017-11-27
	 * @param paramMap
	 * @return
	 */
	public List<Role> queryRoleByUser(Map<String, Object> paramMap);
	
}
