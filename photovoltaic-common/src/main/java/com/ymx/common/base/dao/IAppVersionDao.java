package com.ymx.common.base.dao;

import com.ymx.common.base.entity.AppVersionModel;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

@Repository
public interface IAppVersionDao {

    /**
     * @Description:app升级接口
     * @UpdateAuthor: (这里填写修改人的姓名) 
     * @UpdateDate: (这里填写修改时间)
     * @param: @param map
     * @param: @return
     * @return: AppVersion
     */
    public AppVersionModel queryAppVersion(Map<String, Object> map);

    /**
     * @since       新增版本管理
     * @param model
     * @return
     */
    int addVersion(AppVersionModel model);

    /**
     * @since       修改版本管理
     * @param model
     * @return
     */
    int updateVersion(AppVersionModel model);

    /**
     * @since       查询版本管理总数
     * @param model
     * @return
     */
    int queryAppVersionCount(AppVersionModel model);

    /**
     * @since       查询版本列表
     * @param map
     * @return
     */
    List<AppVersionModel> queryAppVersionList(Map<String , Object> map);

	/**
     * 删除版本
	 * @param list
	 * @return
	 */
	int deleteAppVersion(List<String> list);


    /***
     *获取最新版本apk
     * @return
     */
    AppVersionModel queryAppNewVersion();
}