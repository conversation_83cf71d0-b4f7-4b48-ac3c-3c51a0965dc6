package com.ymx.common.base.dao;

import com.ymx.common.base.entity.ProduceNumberModel;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;


@Repository
public interface ProduceNumberDAO {

	/**
	 * 根据类别获取记录
	 * @param categoryType
	 * @return
	 */
	List<ProduceNumberModel> queryProduceNumberByCategoryType(Map<String, Object> map);

	/**
	 * 修改自增值
	 * @param map
	 */
	void updateProduceNumber(Map<String, Object> map);

	/**
	 * 新增
	 * @param model
	 */
	void insertProduceNumber(ProduceNumberModel model);

}
