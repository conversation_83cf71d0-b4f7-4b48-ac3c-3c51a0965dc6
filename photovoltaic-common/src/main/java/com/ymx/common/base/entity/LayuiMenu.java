
package com.ymx.common.base.entity;

import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 菜单信息Bean
 * 
 */
@Component
public class LayuiMenu implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = -8449759038531377209L;


	private boolean flag = true;
    
    private List<Menu> rows = new ArrayList<>();

	public List<Menu> getRows() {
		return rows;
	}

	public void setRows(List<Menu> rows) {
		this.rows = rows;
	}

	public boolean isFlag() {
		return flag;
	}

	public void setFlag(boolean flag) {
		this.flag = flag;
	}


}
