package com.ymx.common.base.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * easyui page对象
 */
@SuppressWarnings("unchecked")
public class Pager implements Serializable {
	// 总记录数
	//private int total;
	// 每页数据
	private List data = new ArrayList<>();
	//第几页
	private int pageNo ;
	//每页多少数量
	private int pageSize;
	//提示信息
	private String msg = "";
	//总记录数
	private int count;
	//状态码 0代表成功
	private int code;
	
	public int getPageNo() {
		return pageNo;
	}
	public void setPageNo(int pageNo) {
		this.pageNo = pageNo;
	}
	public int getPageSize() {
		return pageSize;
	}
	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
	
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public int getCount() {
		return count;
	}
	public void setCount(int count) {
		this.count = count;
	}
	public int getCode() {
		return code;
	}
	public void setCode(int code) {
		this.code = code;
	}
	
	public List getData() {
		return data;
	}
	public void setData(List data) {
		this.data = data;
	}

	
}
