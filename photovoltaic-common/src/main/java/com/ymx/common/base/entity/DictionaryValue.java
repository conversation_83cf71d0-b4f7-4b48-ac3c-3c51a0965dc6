package com.ymx.common.base.entity;

import net.sf.json.JSONObject;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 字典值信息
 *
 */
public class DictionaryValue implements Serializable {
	
	private String id;

	/**
	 * 字典编码
	 */
	private String dicId;
	
	/**
	 * 字典值编码
	 */
	private String dicValueId;
	
	/**
	 * 字典值显示
	 */
	private String dicValueLabel;
	
	/**
	 * 字典值排序
	 */
	private int dicValueOrder;
	
	/**
	 * 字典值英文，用于代码引用
	 */
	private String dicValueEn;
	
	/***
	 * 字典组名
	 */
	private String dicName;
	
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getDicName() {
		return dicName;
	}

	public void setDicName(String dicName) {
		this.dicName = dicName;
	}

	public String getDicValueEn() {
		return dicValueEn;
	}

	public void setDicValueEn(String dicValueEn) {
		this.dicValueEn = dicValueEn;
	}

	public String getDicId() {
		return dicId;
	}

	public void setDicId(String dicId) {
		this.dicId = dicId;
	}

	public String getDicValueId() {
		return dicValueId;
	}

	public void setDicValueId(String dicValueId) {
		this.dicValueId = dicValueId;
	}

	public String getDicValueLabel() {
		return dicValueLabel;
	}

	public void setDicValueLabel(String dicValueLabel) {
		this.dicValueLabel = dicValueLabel;
	}

	public int getDicValueOrder() {
		return dicValueOrder;
	}

	public void setDicValueOrder(int dicValueOrder) {
		this.dicValueOrder = dicValueOrder;
	}
	
	public String toString()
	{
		return JSONObject.fromObject(this).toString();
	}
	
	public Map<String,String> toMap(){
	    Map<String,String> map = new HashMap<String,String>();
	    map.put( "1", dicId );
	    map.put( "2", dicValueId );
	    map.put( "3", dicValueLabel );
	    map.put( "4", String.valueOf( dicValueOrder ) );
	    return map;
	}
	
}
