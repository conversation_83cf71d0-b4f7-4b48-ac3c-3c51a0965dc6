package com.ymx.common.base.service;

import com.ymx.common.common.config.FileConfig;
import com.ymx.common.common.constant.ErrCode;
import com.ymx.common.common.result.CallResult;
import org.springframework.stereotype.Service;

@Service
public class FileHandleServiceImpl implements FileHandleService {

	@Override
	public CallResult uploadPath(String type, String fileName, long fileSize) {
		CallResult callResult = CallResult.newInstance();
		// type不能包含下划线
		if (type.contains("_")) {
			callResult.setErr(ErrCode.PARAM_FORM_EXCEPTION, ErrCode.PARAM_FORM_EXCEPTION_TEXT + "type不能包含下划线");
			return callResult;
		}
		// 检查文件大小限制
		Long limit = FileConfig.getLimit(type);
		if (null != limit && limit <= fileSize) {
			callResult.setErr(ErrCode.FILE_TOO_LARGE, ErrCode.FILE_TOO_LARGE_TEXT);
			return callResult;
		}
		// 获取文件保存路径
		String path = FileConfig.encodeUrl(type, fileName);
		callResult.setReModel(path);
		System.out.println("*************上传文件路径*********************"+path);
		return callResult;
	}
	
	/**
	 * 
	 * @company 江苏岳创信息
	 * @desc 
	 * @file FileHandleServiceImpl.java
	 * @param fileName
	 * @return
	 * @return CallResult
	 */
	@Override
	public CallResult getPath(String fileName) {
		CallResult callResult = CallResult.newInstance();
		String path = FileConfig.getDownloadPath(fileName);
		callResult.setReModel(path);
		return callResult;
	}
}
