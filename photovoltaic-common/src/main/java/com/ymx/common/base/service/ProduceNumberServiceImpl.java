package com.ymx.common.base.service;

import com.ymx.common.base.dao.ProduceNumberDAO;
import com.ymx.common.base.entity.ProduceNumberModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ProduceNumberServiceImpl implements ProduceNumberService {
	
	@Autowired
	private ProduceNumberDAO produceNumberDAO;

	@Override
	public List<ProduceNumberModel> queryProduceNumberByCategoryType(String categoryType) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("categoryType", categoryType.toUpperCase());
 		return produceNumberDAO.queryProduceNumberByCategoryType(map);
	}

	@Override
	public void updateProduceNumber(String categoryType) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("categoryType", categoryType.toUpperCase());
		produceNumberDAO.updateProduceNumber(map);
	}

	@Override
	public void insertProduceNumber(String categoryType) {
		ProduceNumberModel model = new ProduceNumberModel();
		model.setCategoryType(categoryType.toUpperCase());
		produceNumberDAO.insertProduceNumber(model);
	}
	
	
	
	/**
	 * 获取编号
	 * @param categoryType
	 * @return
	 */
	@Override
	public String getSerialNo(String categoryType){
		
		List<ProduceNumberModel> list = this.queryProduceNumberByCategoryType(categoryType);
		//自增后的完整编号
		String incrSerialNo = "";
		//model
		ProduceNumberModel model = null;
		if(null != list && list.size() > 0 ){
			model = list.get(0);
			//返回编号,并改变数据库编号值
			model = list.get(0);
			incrSerialNo = categoryType + dateToStr(new Date(), "yyMMdd" ) + getFullStr(model.getSerialNum()+"");
			//修改表中数据
			updateProduceNumber(categoryType);
		}else{
			//插入数据,并返回编号
			insertProduceNumber(categoryType);
			incrSerialNo = categoryType + dateToStr(new Date(), "yyMMdd" ) + getFullStr(0+"");
		}
		return incrSerialNo;
	}
	
	/**
	 * 日期格化为字符串形式
	 * @param date 日期
	 * @param format 格式
	 * @return
	 */
	private  String dateToStr( Object date , String format ){
		String dateStr = "";
		if( date != null ){
			SimpleDateFormat formatter = new SimpleDateFormat(format);
			dateStr = formatter.format(date);
		}
		return dateStr;
	}
	
	public  String getFullStr(String num){
		//记录多少位数
		int k = 0;
		StringBuffer sb = new StringBuffer();
		if(null != num && !"".equals(num)){
			int rVal = Integer.parseInt(num) + 1;
			char[] numArray = String.valueOf(rVal).toCharArray();
			for(int i = 0 ; i < numArray.length ; i++){
				k++;
			}
			int j = (6 - k);
			if(k < 6){
				for(int i = 0 ; i < j; i++){
					sb.append("0");	
				}
			}
			sb.append(rVal);
		}
		return sb.toString();
	}

}
