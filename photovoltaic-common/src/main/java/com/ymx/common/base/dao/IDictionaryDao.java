package com.ymx.common.base.dao;

import com.ymx.common.base.entity.Dictionary;
import com.ymx.common.base.entity.DictionaryValue;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 
 * Copyright © 2017 意美旭科技
 *
 * @Description:  字典dao
 */
@Repository
public interface IDictionaryDao {
	/**
	 * 根据字典编码删除字典信息
	 * @param dicId
	 * @return
	 */
	public int deleteDic(String dicId);

	/**
	 * 根据字典编码查询字典值信息
	 * @param dicId
	 */
	public void deleteDicValues(String dicId);
	
	/**
	 * 新增字典信息
	 * @param dic
	 * @return
	 */
	public int insertDic(Dictionary dic);

	/**
	 * 根据字典编码查询字典值信息
	 */
	public List<DictionaryValue> searchDictionaryValues(String dicId);

	/**
	 * 查询所有字典值信息
	 */
	public List<DictionaryValue> searchDictionaryValuesAll();

	/**
	 * 查询字典信息列表数据
	 */
	public List<Dictionary> searchDicList();

	/**
	 * 根据字典编码修改字典信息
	 * @param dic
	 * @return
	 */
	public int updateDic(Dictionary dic);

	/**
	 * 批量增加同一字典的字典值数据
	 * @param vals
	 */
	public void insertDicValues(List<DictionaryValue> vals);

	/**
	 * 根据字典编码查询字典信息
	 * @param dicId
	 * @return
	 */
	public Dictionary queryDic(String dicId);

	/**
	 * 查询字典维护列表数据
	 * @param map
	 * @return
	 */
	public List<DictionaryValue> searchDictionaryValuesPageList(Map<String, Object> map);

	/**
	 * 查询字典维护列表总记录数
	 * @param map
	 * @return
	 */
	public int searchDictionaryValuesPageListCount(Map<String, Object> map);

	/**
	 * 新增字典维护
	 * @param dic
	 */
	public int insertDicValue(DictionaryValue dic);

	/**
	 * 编辑字典维护
	 * @param dic
	 */
	public int updateDicValue(DictionaryValue dic);

	/**
	 * 获取字典维护单条记录
	 * @param dicValueId
	 * @return
	 */
	public DictionaryValue searchDicValuesByDicValueId(String dicValueId);

	public int dicValueDelete(List<String> list);
	
	/**
	 * 获取单条字典值
	 */
	public DictionaryValue queryDicValueSingle(Map<String, Object> map);
	
	
	public DictionaryValue searchDicValuesByDicValueName(String name);
}
