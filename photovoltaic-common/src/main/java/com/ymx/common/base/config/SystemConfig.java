package com.ymx.common.base.config;

import com.ymx.common.base.entity.Dictionary;
import com.ymx.common.base.entity.DictionaryValue;
import com.ymx.common.base.dao.IDictionaryDao;
import com.ymx.common.common.constant.Fields;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SystemConfig {


	private static final Logger myLogger = LoggerFactory.getLogger(SystemConfig.class);

	@Autowired
	IDictionaryDao iDictionaryDao;
	
	private static SystemConfig thisConfig;

	public void init() {
		myLogger.info("初始化字典表----");
		initDictionary();
		thisConfig = this;
	}
	
	/**
	 * 从数据库字典表读出数据，反射到field类
	 */
	private void initDictionary() {
		List<Dictionary> dictionarys = null;
		List<DictionaryValue> dictionaryValues = null;
		Map<String, List<DictionaryValue>> map = new HashMap<String, List<DictionaryValue>>();
		try {
			dictionarys = iDictionaryDao.searchDicList();
			dictionaryValues = iDictionaryDao.searchDictionaryValuesAll();
			if (dictionarys == null || dictionaryValues == null) {
				throw new RuntimeException("字典表初始化失败，系统错误");
			}
			// 把value按id分类
			for (DictionaryValue value : dictionaryValues) {
				String id = value.getDicId();
				if (map.containsKey(id)) {
					List<DictionaryValue> list = map.get(id);
					list.add(value);
				} else {
					List<DictionaryValue> list = new ArrayList<DictionaryValue>();
					list.add(value);
					map.put(id, list);
				}
			}
			for (Dictionary dic : dictionarys) {
				String modularCode = dic.getDicId();
				if (map.get(modularCode) != null) {
					for (DictionaryValue value : map.get(modularCode)) {
						String fieldName = value.getDicValueLabel();
						Integer fieldValue = Integer.parseInt(value.getDicValueId());
						String fieldEn = value.getDicValueEn();
						// 此处String可能为空，要求字典表不能设置空值
						if (modularCode == null || fieldName == null || fieldEn == null) {
							continue;
						}
						// 转成大写
						modularCode = modularCode.toUpperCase();
						fieldName = fieldName.toUpperCase();
						fieldEn = fieldEn.toUpperCase();
						// 通过反射赋值
						// 找到类
						Class<?>[] declaredClasses = Fields.class.getDeclaredClasses();
						// 遍历静态类
						for (Class<?> claz : declaredClasses) {
							if (claz.getSimpleName().equals(modularCode)) {
								Field field = null;
								// 字段赋值
								try {
									field = claz.getDeclaredField(fieldEn);
								} catch (NoSuchFieldException e) {
									myLogger.info(claz.getName() + "没有此字段字段：" + fieldEn + ",跳过。");
									continue;
								}
								if (field != null) {
									field.setAccessible(true);
									field.set(null, fieldValue);
								}
								// 字段中文翻译赋值
								Field fName = claz.getDeclaredField(fieldEn + "_NAME");
								if (fName != null) {
									fName.setAccessible(true);
									fName.set(null, fieldName);
								}
								break;
							}
						}
					}
				}
			}
			// 找到类
			Class<?>[] declaredClasses = Fields.class.getDeclaredClasses();
			// 遍历静态类
			for (Class<?> claz : declaredClasses) {
				Field[] fields = claz.getFields();
				// 打印每一个字段的值
				for (Field f : fields) {
					System.out.println(claz.getSimpleName() + "-" + f.getName() + "------" + f.get(null));
					myLogger.info(claz.getSimpleName() + "-" + f.getName() + "------" + f.get(null));
				}
			}
		} catch (Exception e) {
			throw new RuntimeException("字典表初始化失败，系统错误。", e);
		}
	}
	
	public static void reInitDictionary(){
		thisConfig.initDictionary();
	}
	
}
