package com.ymx.common.base.service;

import com.ymx.common.utils.CommonUtil;
import com.ymx.common.common.config.FileConfig;
import com.ymx.common.common.constant.ErrCode;
import com.ymx.common.common.result.CallResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Copyright © 2017 意美旭科技
 * @Description: 文件服务 实现
 * @date: 2017-09-30
 */

@Service
public class FileTransServiceImpl implements FileTransService {

	private static final Logger logger = LoggerFactory.getLogger(FileTransServiceImpl.class);


	/***
	 * 批量按顺序获取文件大小
	 * @param srcFileName
	 * @return
	 */
	@Override
	public CallResult getFileSize(String srcFileName) {
		CallResult callResult = CallResult.newInstance();
		if (!CommonUtil.isEmpty(srcFileName)) {
			callResult.setErr(ErrCode.PARAM_ERR , ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
			return callResult;
		}
		String[] srcFileNames = srcFileName.split(",");
		StringBuffer sb =  new StringBuffer();
		for (int i = 0 ; i < srcFileNames.length; i++) {
			File file = new File(FileConfig.getFileRealPath(srcFileNames[i]));
			if (file.exists()) {
				sb.append(file.length());
				sb.append(",");
			}
		}
		if (sb.length() > 0) {
			String result = sb.substring(0 , sb.toString().length() - 1);
			callResult.setReModel(result);
		}
		callResult.setReModel(sb.toString());
		return callResult;
	}


	/***
	 * 创建文件
	 * @desc
	 * @date 2017-11-14
	 * @param destPath
	 * @return
	 */
	@Override
	public CallResult createFilePath(String destPath ) {
		CallResult callResult = CallResult.newInstance();
		// 创建文件
		File destFile = new File(destPath);
		// 文件存在，返回错误
		if (destFile.exists()) {
			logger.error(destFile.getPath() + "已存在");
			callResult.setErr(ErrCode.FILE_EXISTS, ErrCode.FILE_EXISTS_TEXT);
			return callResult;
		}
		// 文件创建失败，返回错误
		try {
			// 创建目录
			if (!destFile.getParentFile().exists()) {
				destFile.getParentFile().mkdirs();
			}
			destFile.createNewFile();
		} catch (IOException ioException) {
			logger.error("创建文件" + destFile.getPath() + "时发生错误", ioException);
			callResult.setErr(ErrCode.FILE_CREATE, ErrCode.FILE_CREATE_TEXT);
			return callResult;
		}
		return callResult;
	}
	
	

	@Override
	public CallResult uploadFile(String destPath, InputStream in) {
		CallResult callResult = CallResult.newInstance();
		// 创建文件
		File destFile = new File(destPath);
		// 文件存在，返回错误
		if (destFile.exists()) {
			logger.error(destFile.getPath() + "已存在");
			callResult.setErr(ErrCode.FILE_EXISTS, ErrCode.FILE_EXISTS_TEXT);
			return callResult;
		}
		// 文件创建失败，返回错误
		try {
			// 创建目录
			if (!destFile.getParentFile().exists()) {
				destFile.getParentFile().mkdirs();
			}
			destFile.createNewFile();
		} catch (IOException ioException) {
			logger.error("创建文件" + destFile.getPath() + "时发生错误", ioException);
			callResult.setErr(ErrCode.FILE_CREATE, ErrCode.FILE_CREATE_TEXT);
			return callResult;
		}
		try {
			FileOutputStream out = new FileOutputStream(destFile);
			// 写文件
			int n = -1;
			byte[] b = new byte[10240];
			while ((n = in.read(b)) != -1) {
				out.write(b, 0, n);
			}
			out.flush();
			out.close();
			in.close();
		} catch (Exception e) {
			logger.error("Exception",e);
			callResult.setErr(ErrCode.IO_EXCEPTION, ErrCode.IO_EXCEPTION_TEXT);
			return callResult;
		}
		return callResult;
	}
}
