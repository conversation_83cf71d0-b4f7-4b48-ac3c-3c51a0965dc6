package com.ymx.common.base.service;

import com.ymx.common.common.result.CallResult;

/**
 * 
 * Copyright © 2017 意美旭科技
 * @Description: 头像等文件处理服务，只处理上传下载路径
 * 
 */
public interface FileHandleService {
	/**
	 * @Description: 返回服务器保存的文件路径
	 * @UpdateAuthor: (这里填写修改人的姓名)
	 * @UpdateDescription: TODO(这里用一句话描述这个修改这个方法的作用)
	 * @UpdateDate: (这里填写修改时间)
	 * @param: @param 模块类型
	 * @param: @param 文件字节数
	 */
	public CallResult uploadPath(String type, String fileName, long fileSize);

	public CallResult getPath(String fileName);
}
