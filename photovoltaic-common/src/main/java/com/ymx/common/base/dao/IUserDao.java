package com.ymx.common.base.dao;

import com.ymx.common.base.entity.User;
import com.ymx.common.base.entity.UserRole;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 
 * Copyright © 2017 意美旭科技
 * 
 */
@Repository
public interface IUserDao {
	/**
	 * 用户登录
	 */
	User userLogin(Map<String, Object> paramMap);

	/**
	 * 按条件统计用户数
	 */
	int searchUsersCount(Map<String, Object> paramMap);

	/**
	 * 按条件查询用户
	 */
	List<User> searchUsers(Map<String, Object> paramMap);

	/**
	 * 查询用户信息
	 */
	User queryUser(String userId);
	/**
	 * 查询用户信息
	 */
	User queryUserById(String id);

	/**
	 * 批量删除用户
	 */
	int deleteUsers(List<String> list);

	/**
	 * 批量初始化用户
	 */
	int defaultUserPassword(Map<String, Object> map);

	/**
	 * 插入用户
	 */
	int insertUser(User user);

	/**
	 * 删除用户角色信息
	 */
	void deleteRoleUsers(String userId);

	/**
	 * 增加用户角色
	 */
	int insertUserRoles(List<UserRole> role);

	/**
	 * 查询固定职务下的所有用户
	 */
	List<User> queryDutyUsers(String dutyId);

	/**
	 * 修改密码
	 */
	int updatePassword(Map<String, Object> paramMap);

	/**
	 * 将所选择的部门的用户部门编码置为空
	 */
	int updateUser(User user);

	/**
	 * 查询用户所有的角色
	 */
	List<UserRole> queryUserRoles(String userId);

	/**
	 * 查询所有的角色
	 */
	List<UserRole> searchRoles();

	/**
	 * 查询客户经理列表
	 * <AUTHOR>
	 * @createTime	2017-11-30
	 * @param map
	 * @return
	 */
	List<User> queryCustomerManager(Map<String, Object> map);

	String queryUserLocal(Map<String, Object> paramMap);
}
