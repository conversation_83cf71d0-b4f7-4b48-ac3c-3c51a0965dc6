package com.ymx.common.base.service;

import com.ymx.common.common.result.CallResult;

import java.io.InputStream;

/**
 * Copyright © 2018 意美旭科技
 * @Description: 文件服务 使用hessian协议,独立服务
 * @date: 2018-07-18 下午2:42:08
 */
public interface FileTransService {
    
	/**
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @UpdateAuthor: (这里填写修改人的姓名)
	 * @UpdateDescription: TODO(这里用一句话描述这个修改这个方法的作用)
	 * @UpdateDate: (这里填写修改时间)
	 * @param: @param 保存地址
	 * @param: @param 传入文件流
	 * @return: CallResult
	 * @throws
	 */
	public CallResult uploadFile(String destPath, InputStream in);

	/***
	 * 
	 * @desc
	 * @date 2017-11-14
	 * @param destPath
	 * @return
	 */
	CallResult createFilePath(String destPath);


	/**
	 * 获取多个文件大小
	 * @param srcFileName
	 * @return
	 */
	CallResult getFileSize(String srcFileName);

}
