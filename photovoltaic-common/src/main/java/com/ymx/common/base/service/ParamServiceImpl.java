package com.ymx.common.base.service;

import com.ymx.common.utils.StringUtils;
import com.ymx.common.base.config.SystemConfig;
import com.ymx.common.common.constant.ErrCode;
import com.ymx.common.common.constant.Fields;
import com.ymx.common.common.result.CallResult;
import org.springframework.stereotype.Service;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ParamServiceImpl implements ParamService {
	@Override
	public CallResult getParam(String type) {
		CallResult callResult = CallResult.newInstance();
		if (StringUtils.isBlank(type)) {
			callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
			return callResult;
		}
		type = type.toUpperCase();
		Map<String, Object> map = new HashMap<String, Object>();
		// 通过反射获取数据
		Class<?>[] declaredClasses = Fields.class.getDeclaredClasses();
		try {
			// 遍历静态类
			for (Class<?> claz : declaredClasses) {
				// 找到类
				if (claz.getSimpleName().equals(type)) {
					Field[] fields = claz.getFields();
					Field field = fields[0];
					field.setAccessible(true);
					// 字典名
					String fieldKeyName = fields[0].getName();
					String fieldKey = field.get(claz).toString();
					map.put(fieldKeyName, fieldKey);
					// 字典值
					String fieldValueName = fieldKeyName + "_NAME";
					String fieldValue =  fields[1].get(claz).toString();
					map.put(fieldValueName, fieldValue);
					break;
				}
			}
		} catch (Exception e) {
			callResult.setErr(ErrCode.SYSTEM_ERR, ErrCode.SYSTEM_ERR_TEXT);
		}
		callResult.setReModel(map);
		return callResult;
	}

	@Override
	public CallResult getParamList(String type) {
		CallResult callResult = CallResult.newInstance();
		if (StringUtils.isBlank(type)) {
			callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
			return callResult;
		}
		type = type.toUpperCase();
		Map<String, Object> map = new HashMap<String, Object>();
		// 通过反射获取数据
		Class<?>[] declaredClasses = Fields.class.getDeclaredClasses();
		try {
			// 遍历静态类
			for (Class<?> claz : declaredClasses) {
				// 找到类
				if (claz.getSimpleName().equals(type)) {
					Field[] fields = claz.getFields();
					for(int i = 0 ; i < fields.length ; i=i+2){
					Field field = fields[i];
					field.setAccessible(true);
					// 字典名
					String fieldKeyName = fields[i].getName();
					String fieldKey = field.get(claz).toString();
					// 字典值
					String fieldValueName = fieldKeyName + "_NAME";
					String fieldValue =  fields[i+1].get(claz).toString();
					map.put(fieldValue, fieldKey);
					}
				}
			}
		} catch (Exception e) {
			callResult.setErr(ErrCode.SYSTEM_ERR, ErrCode.SYSTEM_ERR_TEXT);
		}
		callResult.setReModel(map);
		return callResult;
	}
	
	@Override
	public CallResult getParamNameIdList(String type) {
		CallResult callResult = CallResult.newInstance();
		if (StringUtils.isBlank(type)) {
			callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
			return callResult;
		}
		type = type.toUpperCase();
		// 通过反射获取数据
		Class<?>[] declaredClasses = Fields.class.getDeclaredClasses();
		List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
		
		try {
			// 遍历静态类
			for (Class<?> claz : declaredClasses) {
				// 找到类
				if (claz.getSimpleName().equals(type)) {
					Field[] fields = claz.getFields();
					for(int i = 0 ; i < fields.length ; i=i+2){
						Map<String, Object> map = new HashMap<String, Object>();
						Field field = fields[i];
						field.setAccessible(true);
						// 字典名
						//String fieldKeyName = fields[i].getName();
						String fieldKey = field.get(claz).toString();
						// 字典值
						//String fieldValueName = fieldKeyName + "_NAME";
						String fieldValue =  fields[i+1].get(claz).toString();
						map.put("id", fieldKey);
						map.put("name", fieldValue);
						list.add(map);
					}
				}
			}
		} catch (Exception e) {
			callResult.setErr(ErrCode.SYSTEM_ERR, ErrCode.SYSTEM_ERR_TEXT);
		}
		callResult.setReModel(list);
		return callResult;
	}

	@Override
	public CallResult applyParam() {
		SystemConfig.reInitDictionary();
		return CallResult.newInstance();
	}
}
