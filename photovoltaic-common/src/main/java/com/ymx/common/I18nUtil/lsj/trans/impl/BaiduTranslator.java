package com.ymx.common.I18nUtil.lsj.trans.impl;



import com.ymx.common.I18nUtil.lsj.trans.annotation.TranslatorComponent;
import com.ymx.common.I18nUtil.lsj.http.HttpParams;
import com.ymx.common.I18nUtil.lsj.http.HttpPostParams;
import com.ymx.common.I18nUtil.lsj.trans.AbstractOnlineTranslator;
import com.ymx.common.I18nUtil.lsj.trans.LANG;
import com.ymx.common.I18nUtil.lsj.trans.MD5;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@TranslatorComponent(id = "baidu")
final public class BaiduTranslator extends AbstractOnlineTranslator {

	public BaiduTranslator(){
		langMap.put(LANG.EN, "en");
		langMap.put(LANG.ZH, "zh");
	}
	
	@Override
	public String getResponse(LANG from, LANG targ, String query) throws Exception{
		// 随机数
        String salt = String.valueOf(System.currentTimeMillis());
        String src = "20181018000221224" + query + salt + "2Q2Ry81KiOyIGDd4R1MU"; // 加密前的原文
       /* try {
			params.put("sign", MD5.md5(src));
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}*/
		HttpParams params = new HttpPostParams()
				.put("from", langMap.get(from))
				.put("to", langMap.get(targ))
				.put("q", query)
				.put("appid", "20181018000221224")
				.put("salt", salt)
				.put("sign", MD5.md5(src));
		
		return params.send2String("http://api.fanyi.baidu.com/api/trans/vip/translate");
	}
	
	@Override
	protected String parseString(String jsonString){
		JSONObject jsonObject = JSONObject.fromObject(jsonString);
		System.out.println(jsonString);
		JSONArray segments = jsonObject.getJSONArray("trans_result");
		System.out.println(segments.size());
		StringBuilder result = new StringBuilder();
		
		for(int i=0; i<segments.size(); i++){
			result.append(i==0?"":"\n");
			result.append(segments.getJSONObject(i).getString("dst"));
		}
		return new String(result);
	}
}
