package com.ymx.common.I18nUtil.lonelyCountry.i18n;



import com.ymx.common.I18nUtil.lonelyCountry.constant.Engine;
import com.ymx.common.I18nUtil.lonelyCountry.constant.I18n;
import com.ymx.common.I18nUtil.lonelyCountry.utils.EditUtil;
import com.ymx.common.I18nUtil.lonelyCountry.utils.UnicodeUtil;
import com.ymx.common.I18nUtil.lsj.trans.LANG;
import com.ymx.common.I18nUtil.lsj.trans.Translator;
import com.ymx.common.I18nUtil.lsj.trans.factory.TFactory;
import com.ymx.common.I18nUtil.lsj.trans.factory.TranslatorFactory;
import com.google.common.base.Charsets;
import java.io.File;
import com.google.common.io.Files;

import java.util.Map;

public class CreateI18nImpl implements CreateI18n {
    private final Map<String, String> param;
    private final Engine engine;

    public CreateI18nImpl(Map<String, String> param, Engine engine) {
        this.param = param;
        this.engine = engine;
    }

    @Override
    public void createFile(I18n i18n, String path) throws Exception {
        String separator = System.getProperty("line.separator");
        File file = EditUtil.createOrGetFile(path);

        StringBuilder sb = new StringBuilder();
        EditUtil.writeFirst(sb);
        for (String key : param.keySet()) {
            String value = param.get(key);
            String space = EditUtil.getSpace(41 - key.length());
            switch (i18n) {
                case CN:
                    sb.append(key + space + " = " + UnicodeUtil.cnToUnicode(value) + separator);
                    break;
              
                case EN:
                    TFactory factory = new TranslatorFactory();
                    Translator lator = null;
                    switch (engine) {
                        case GOOGLE:
                            lator = factory.get("google");
                            break;
                        case BAIDU:
                            lator = factory.get("baidu");
                            break;
                        case YOUDAO:
                            lator = factory.get("youdao");
                            break;
                        case JINSHAN:
                            lator = factory.get("jinshan");
                            break;
                        case TENCENT:
                            lator = factory.get("tencent");
                            break;
                    }
                    sb.append(key + space + " = " + lator.trans(LANG.ZH, LANG.EN, value) + separator);
                    break;
            }
        }
        Files.append(sb.toString(), file, Charsets.UTF_8);
    }
}
