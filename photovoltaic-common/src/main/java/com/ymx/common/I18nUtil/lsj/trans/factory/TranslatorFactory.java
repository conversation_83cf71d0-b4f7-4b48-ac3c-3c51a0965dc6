package com.ymx.common.I18nUtil.lsj.trans.factory;

import java.net.URISyntaxException;

import com.ymx.common.I18nUtil.lsj.trans.exception.DupIdException;
import com.ymx.common.I18nUtil.lsj.trans.Translator;

final public class TranslatorFactory extends AbstractTranslatorFactory{

	public TranslatorFactory() throws ClassNotFoundException, InstantiationException, IllegalAccessException, DupIdException, URISyntaxException {
		super();
	}

	@Override
	public Translator get(String id) {
		return translatorMap.get(id);
	}


}
