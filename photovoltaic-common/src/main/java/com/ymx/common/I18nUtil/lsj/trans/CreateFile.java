package com.ymx.common.I18nUtil.lsj.trans;

import java.util.HashMap;
import java.util.Map;

import com.ymx.common.I18nUtil.lonelyCountry.constant.Engine;
import com.ymx.common.I18nUtil.lonelyCountry.factory.I18nFactory;


public class CreateFile {
	
	
	public static void test1() throws Exception {
        Map param = new HashMap();
        param.put("	请先添加云终端	", "	请先添加云终端	");
        param.put("	请输入云终端名称	", "	请输入云终端名称	");
        param.put("	请输入序列号	", "	请输入序列号	");
        param.put("	请输入标识	", "	请输入标识	");
        param.put("	保存成功	", "	保存成功	");
        param.put("	请输入组件编号	", "	请输入组件编号	");
        param.put("	请输入序列号	", "	请输入序列号	");
        param.put("	请输入组件标识	", "	请输入组件标识	");
        param.put("	请选择生产商	", "	请选择生产商	");
        param.put("	请选择型号	", "	请选择型号	");
        param.put("	请先保存逆变器	", "	请先保存逆变器	");
        param.put("	请输入逆变器名称	", "	请输入逆变器名称	");
        param.put("	请输入序列号	", "	请输入序列号	");
        param.put("	请选择生产商	", "	请选择生产商	");
        param.put("	请选择型号	", "	请选择型号	");
        param.put("	请选择控制器	", "	请选择控制器	");
        param.put("	已放置	", "	已放置	");
        param.put("	已被移除,无法操作	", "	已被移除,无法操作	");
        param.put("	录入成功	", "	录入成功	");
        param.put("	移除成功	", "	移除成功	");
        param.put("	请输入采集器名称	", "	请输入采集器名称	");
        param.put("	请输入序列号	", "	请输入序列号	");
        param.put("	请输入标识	", "	请输入标识	");
        param.put("	串联	", "	串联	");
        param.put("	并联	", "	并联	");
        param.put("	请输入组串名称	", "	请输入组串名称	");
        param.put("	请选择类型	", "	请选择类型	");
        param.put("	请输入逆变器名称	", "	请输入逆变器名称	");
        param.put("	请输入序列号	", "	请输入序列号	");
        param.put("	请选择生产商	", "	请选择生产商	");
        param.put("	请选择型号	", "	请选择型号	");
        param.put("	请选择控制器	", "	请选择控制器	");
        param.put("	关联成功	", "	关联成功	");
        param.put("	请先获取国家	", "	请先获取国家	");
        param.put("	请先获取省/地区	", "	请先获取省/地区	");
        param.put("	请输入系统名称	", "	请输入系统名称	");
        param.put("	请输入街道名称	", "	请输入街道名称	");
        param.put("	请输入邮编	", "	请输入邮编	");
        param.put("	请输入国家	", "	请输入国家	");
        param.put("	新增成功	", "	新增成功	");
        param.put("	自动打开	", "	自动打开	");
        param.put("	请联系运维人员	", "	请联系运维人员	");
        param.put("	处理成功	", "	处理成功	");
        param.put("	请输入温度警报	", "	请输入温度警报	");
        param.put("	请输入高温关断	", "	请输入高温关断	");
        param.put("	请输入输出过压	", "	请输入输出过压	");
        param.put("	请输入输入过压	", "	请输入输入过压	");
        param.put("	请输入输出过流	", "	请输入输出过流	");
        param.put("	请输入输入过流	", "	请输入输入过流	");
        param.put("	操作完成	", "	操作完成	");
        param.put("	没有数据	", "	没有数据	");
        param.put("	是否关闭组件	", "	是否关闭组件	");
        param.put("	是否打开组件	", "	是否打开组件	");
        param.put("	是否关闭全部组件	", "	是否关闭全部组件	");
        param.put("	是否打开全部组件	", "	是否打开全部组件	");
        param.put("	当前功率	", "	当前功率	");
        param.put("	已是最后一位	", "	已是最后一位	");
        param.put("	请选择正确的时间	", "	请选择正确的时间	");
        param.put("	日期	", "	日期	");
        param.put("	请输入邮箱	", "	请输入邮箱	");
        param.put("	请输入名	", "	请输入名	");
        param.put("	请输入性	", "	请输入性	");
        param.put("	该产品是意美旭正品	", "	该产品是意美旭正品	");
        param.put("	获取国家	", "	获取国家	");
        param.put("	获取省/地区	", "	获取省/地区	");
        param.put("	获取城市	", "	获取城市	");
        param.put("	列表	", "	列表	");
        param.put("	登录	", "	登录	");
        param.put("	字数超过限定	", "	字数超过限定	");
        param.put("	请输入原始密码	", "	请输入原始密码	");
        param.put("	请输入新密码	", "	请输入新密码	");
        param.put("	请再次输入新密码	", "	请再次输入新密码	");
        param.put("	两次密码不一致	", "	两次密码不一致	");
        param.put("	修改成功	", "	修改成功	");
        param.put("	请输入帐号	", "	请输入帐号	");
        param.put("	请验证用户或验证失败	", "	请验证用户或验证失败	");
        param.put("	请输入号码	", "	请输入号码	");
        param.put("	请输入验证码	", "	请输入验证码	");
        param.put("	请输入重置密码	", "	请输入重置密码	");
        param.put("	请输入再次确认密码	", "	请输入再次确认密码	");
        param.put("	两次密码不一致	", "	两次密码不一致	");
        param.put("	请输入号码/邮箱	", "	请输入号码/邮箱	");
        param.put("	验证成功	", "	验证成功	");
        param.put("	获取成功	", "	获取成功	");
        param.put("	跳过	", "	跳过	");
        param.put("	请去系统设置打开应用拍照权限	", "	请去系统设置打开应用拍照权限	");

       /*
        *
        *  param.put("	请先添加云终端	", "	请先添加云终端	");
param.put("	请输入云终端名称	", "	请输入云终端名称	");
param.put("	请输入序列号	", "	请输入序列号	");
param.put("	请输入标识	", "	请输入标识	");
param.put("	保存成功	", "	保存成功	");
param.put("	请输入组件编号	", "	请输入组件编号	");
param.put("	请输入序列号	", "	请输入序列号	");
param.put("	请输入组件标识	", "	请输入组件标识	");
param.put("	请选择生产商	", "	请选择生产商	");
param.put("	请选择型号	", "	请选择型号	");
param.put("	请先保存逆变器	", "	请先保存逆变器	");
param.put("	请输入逆变器名称	", "	请输入逆变器名称	");
param.put("	请输入序列号	", "	请输入序列号	");
param.put("	请选择生产商	", "	请选择生产商	");
param.put("	请选择型号	", "	请选择型号	");
param.put("	请选择控制器	", "	请选择控制器	");
param.put("	已放置	", "	已放置	");
param.put("	已被移除,无法操作	", "	已被移除,无法操作	");
param.put("	录入成功	", "	录入成功	");
param.put("	移除成功	", "	移除成功	");
param.put("	请输入采集器名称	", "	请输入采集器名称	");
param.put("	请输入序列号	", "	请输入序列号	");
param.put("	请输入标识	", "	请输入标识	");
param.put("	串联	", "	串联	");
param.put("	并联	", "	并联	");
param.put("	请输入组串名称	", "	请输入组串名称	");
param.put("	请选择类型	", "	请选择类型	");
param.put("	请输入逆变器名称	", "	请输入逆变器名称	");
param.put("	请输入序列号	", "	请输入序列号	");
param.put("	请选择生产商	", "	请选择生产商	");
param.put("	请选择型号	", "	请选择型号	");
param.put("	请选择控制器	", "	请选择控制器	");
param.put("	关联成功	", "	关联成功	");
param.put("	请先获取国家	", "	请先获取国家	");
param.put("	请先获取省/地区	", "	请先获取省/地区	");
param.put("	请输入系统名称	", "	请输入系统名称	");
param.put("	请输入街道名称	", "	请输入街道名称	");
param.put("	请输入邮编	", "	请输入邮编	");
param.put("	请输入国家	", "	请输入国家	");
param.put("	新增成功	", "	新增成功	");
param.put("	自动打开	", "	自动打开	");
param.put("	请联系运维人员	", "	请联系运维人员	");
param.put("	处理成功	", "	处理成功	");
param.put("	请输入温度警报	", "	请输入温度警报	");
param.put("	请输入高温关断	", "	请输入高温关断	");
param.put("	请输入输出过压	", "	请输入输出过压	");
param.put("	请输入输入过压	", "	请输入输入过压	");
param.put("	请输入输出过流	", "	请输入输出过流	");
param.put("	请输入输入过流	", "	请输入输入过流	");
param.put("	操作完成	", "	操作完成	");
param.put("	没有数据	", "	没有数据	");
param.put("	是否关闭组件	", "	是否关闭组件	");
param.put("	是否打开组件	", "	是否打开组件	");
param.put("	是否关闭全部组件	", "	是否关闭全部组件	");
param.put("	是否打开全部组件	", "	是否打开全部组件	");
param.put("	当前功率	", "	当前功率	");
param.put("	已是最后一位	", "	已是最后一位	");
param.put("	请选择正确的时间	", "	请选择正确的时间	");
param.put("	日期	", "	日期	");
param.put("	请输入邮箱	", "	请输入邮箱	");
param.put("	请输入名	", "	请输入名	");
param.put("	请输入性	", "	请输入性	");
param.put("	该产品是意美旭正品	", "	该产品是意美旭正品	");
param.put("	获取国家	", "	获取国家	");
param.put("	获取省/地区	", "	获取省/地区	");
param.put("	获取城市	", "	获取城市	");
param.put("	列表	", "	列表	");
param.put("	登录	", "	登录	");
param.put("	字数超过限定	", "	字数超过限定	");
param.put("	请输入原始密码	", "	请输入原始密码	");
param.put("	请输入新密码	", "	请输入新密码	");
param.put("	请再次输入新密码	", "	请再次输入新密码	");
param.put("	两次密码不一致	", "	两次密码不一致	");
param.put("	修改成功	", "	修改成功	");
param.put("	请输入帐号	", "	请输入帐号	");
param.put("	请验证用户或验证失败	", "	请验证用户或验证失败	");
param.put("	请输入号码	", "	请输入号码	");
param.put("	请输入验证码	", "	请输入验证码	");
param.put("	请输入重置密码	", "	请输入重置密码	");
param.put("	请输入再次确认密码	", "	请输入再次确认密码	");
param.put("	两次密码不一致	", "	两次密码不一致	");
param.put("	请输入号码/邮箱	", "	请输入号码/邮箱	");
param.put("	验证成功	", "	验证成功	");
param.put("	获取成功	", "	获取成功	");
param.put("	跳过	", "	跳过	");
param.put("	请去系统设置打开应用拍照权限	", "	请去系统设置打开应用拍照权限	");
param.put("操作", "操作");
        param.put("刷新当前选项卡", "刷新当前选项卡");
        param.put("关闭当前选项卡", "关闭当前选项卡");
        param.put("关闭所有选项卡", "关闭所有选项卡");
        param.put("关闭其他选项卡", "关闭其他选项卡");
        param.put("请传递", "请传递");
        param.put("参数值", "参数值");
        param.put("函数", "函数");
        param.put("页面过期", "页面过期");
        param.put("请重新登录", "请重新登录");
        param.put("返回信息错误", "返回信息错误");
        param.put("操作成功", "操作成功");
        param.put("确定要删除吗", "确定要删除吗");
        param.put("数据正在加载中", "数据正在加载中");
        param.put("数据解析异常", "数据解析异常");
        param.put("勾选", "勾选");
        param.put("勾选的数据", "勾选的数据");
        param.put("用户名不能有特殊字符", "用户名不能有特殊字符");
        param.put("用户名首尾不能出现下划线", "用户名首尾不能出现下划线");
        param.put("用户名不能全为数字", "用户名不能全为数字");
        param.put("密码必须6到12位", "密码必须6到12位");
        param.put("且不能出现空格", "且不能出现空格");
        param.put("数据项不允许为空", "数据项不允许为空");
        param.put("不允许为空", "不允许为空");
        param.put("请输入整数", "请输入整数");
        param.put("为整数", "为整数");
        param.put("请输入正确联系方式", "请输入正确联系方式");
        param.put("请正确输入数字", "请正确输入数字");
        param.put("请正确输入数字", "请正确输入数字");
        param.put("请正确输入邮箱格式", "请正确输入邮箱格式"); param.put("编辑参数设置", "编辑参数设置");
        param.put("请选择单条记录进行编辑", "请选择单条记录进行编辑");
        param.put("设置项名称已经存在", "设置项名称已经存在");
        param.put("请检查", "请检查");
        param.put("请选择单条记录进行编辑", "请选择单条记录进行编辑");
        param.put("请选择单条角色进行分配权限", "请选择单条角色进行分配权限");
        param.put("角色名称已经存在", "角色名称已经存在");
        param.put("请检查", "请检查");*/


        /*param.put("顶级菜单无功能", "顶级菜单无功能");
        param.put("数据解析异常", "数据解析异常");
        param.put("应用分配失败", "应用分配失败");
        param.put("分配应用取消失败", "分配应用取消失败");
        param.put("分配", "分配");
        param.put("取消分配", "取消分配");
        param.put("顶级菜单项不需要进行操作功能配置", "顶级菜单项不需要进行操作功能配置");
        param.put("同级已经存在同名菜单", "同级已经存在同名菜单");
        param.put("请选择需要重新排序的数据", "请选择需要重新排序的数据");
        param.put("设置项名称已经存在", "设置项名称已经存在");
        param.put("请选择单条记录进行删除", "请选择单条记录进行删除");*/



               /*
              param.put("请正确选择操作项", "请正确选择操作项");
        param.put("此用户不允许修改", "此用户不允许修改");
        param.put("生成登录码", "生成登录码");
        param.put("请输入0至1000的数字", "请输入0至1000的数字");
        param.put("操作成功", "操作成功");
        param.put("电站数据", "电站数据");
        param.put("功率数值", "功率数值");
        param.put("功率", "功率");

        param.put("功率数值", "功率数值");
        param.put("请选择云终端", "请选择云终端");
        param.put("提示", "提示");
        param.put("暂无数据", "暂无数据");
        param.put("确认要操作所选择的数据吗", "确认要操作所选择的数据吗");
        param.put("打开", "打开");
        param.put("关闭", "关闭");
        param.put("待调试", "待调试");
        param.put("已调试", "已调试");
        param.put("操作成功", "操作成功");
        param.put("提示", "提示");
         param.put("请选择需要操作的数据", "请选择需要操作的数据");
        param.put("导入", "导入");
        param.put("成功", "成功");
        param.put("出错", "出错");
        param.put("选择需要导入的", "选择需要导入的");
        param.put("选择", "选择");
        param.put("格式的文件导入", "格式的文件导入");
        param.put("请正确选择操作项", "请正确选择操作项");
        param.put("请选择组件", "请选择组件");
        param.put("导入云终端", "导入云终端");
        param.put("查询时间不能为空", "查询时间不能为空");
        param.put("未处理", "未处理");
          param.put("已处理", "已处理");
        param.put("帐号或密码为空", "帐号或密码为空");
        param.put("请重新输入", "请重新输入");
        param.put("页面过期", "页面过期");
        param.put("请重新登录", "请重新登录");
        param.put("操作成功", "操作成功");
        param.put("未知错误", "未知错误");
        param.put("请及时排查", "请及时排查");
        param.put("系统提示", "系统提示");
        param.put("请输入密码", "请输入密码");
         param.put("请在一次输入密码", "请在一次输入密码");
        param.put("两次密码不一至", "两次密码不一至");
        param.put("请重新输入", "请重新输入");
        param.put("密码修改成功", "密码修改成功");
        param.put("请输入新密码", "请输入新密码");
        param.put("请输入新密码", "请输入新密码");
        param.put("新密码", "新密码");

        param.put("请选择单条记录进行编辑", "请选择单条记录进行编辑");
        param.put("确认要删除此项数据吗", "确认要删除此项数据吗");
        param.put("请选择需要删除的记录", "请选择需要删除的记录");
        param.put("同级已经存在同名部门", "同级已经存在同名部门");
        param.put("请更换", "请更换");
        param.put("字典数据已应用", "字典数据已应用");
        param.put("请选择需要删除的字典", "请选择需要删除的字典");
        param.put("确定删除所选择的字典信息吗", "确定删除所选择的字典信息吗");
        param.put("请选择需要修改的字典", "请选择需要修改的字典");
        param.put("用户名已经存在", "用户名已经存在");
        param.put("请更换", "请更换");
        param.put("请选择单条记录进行编辑", "请选择单条记录进行编辑");
        param.put("确认要删除此项数据吗", "确认要删除此项数据吗");
        param.put("请选择需要删除的记录", "请选择需要删除的记录");
        param.put("删除成功", "删除成功");
        param.put("确认要将所选择的数据重新初始化密码吗", "确认要将所选择的数据重新初始化密码吗");
        param.put("密码初始化成功", "密码初始化成功");
        param.put("请选择需要密码初始化的数据", "请选择需要密码初始化的数据");
        param.put("添加应用失败", "添加应用失败");
        param.put("取消应用失败", "取消应用失败");
        param.put("请重试", "请重试");
        param.put("添加", "添加");
        param.put("取消", "取消");
        param.put("确认要删除此项数据吗", "确认要删除此项数据吗");
          param.put("帮助文档", "帮助文档");






               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");



               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");
               param.put("", "");*/
              
        I18nFactory i18nFactory = new I18nFactory.Builder(param)
                .cnPath("c:/usr/test/messages_zh_CN.txt")
                .enPath("c:/usr/test/messages_en_US.txt")
                .engine(Engine.BAIDU)
                .build();
        i18nFactory.create();
    }
    public static void main(String[] args) {
		try {
			test1();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

}
