package com.ymx.common.common.result;

import com.ymx.common.common.annotation.ChinaVoAttribute;

public class LoginResult {

	private String token;

	private String mId;

	/**
	 * 用户类型
	 */
	private int type;

	private String phone;
	/**
	 * 实名认证
	 */
	private int campusProxy;

	//邮箱认证
	private String email;
	//安全认证
	private int safeProxy;
	//银行认证
	private int bankProxy;
	//测评认证
	private int evaluationProxy;
	//银行存管认证
	private int bankDepositProxy;
	//支付密码
	private int payPwProxy;
	//用户昵称
	private String nickName;
	//用户名称
	private String name;
	// 针对其他用处 采用姓 与 名分开
	private String xing;

	//头像
	@ChinaVoAttribute(isFilePath = true)
	private String ico;
	private String icoCh;


	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public String getEmail() {
		return email;
	}

	public int getPayPwProxy() {
		return payPwProxy;
	}

	public void setPayPwProxy(int payPwProxy) {
		this.payPwProxy = payPwProxy;
	}

	public String getEamil() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public int getSafeProxy() {
		return safeProxy;
	}

	public void setSafeProxy(int safeProxy) {
		this.safeProxy = safeProxy;
	}

	public int getBankProxy() {
		return bankProxy;
	}

	public void setBankProxy(int bankProxy) {
		this.bankProxy = bankProxy;
	}

	public int getEvaluationProxy() {
		return evaluationProxy;
	}

	public void setEvaluationProxy(int evaluationProxy) {
		this.evaluationProxy = evaluationProxy;
	}

	public int getBankDepositProxy() {
		return bankDepositProxy;
	}

	public void setBankDepositProxy(int bankDepositProxy) {
		this.bankDepositProxy = bankDepositProxy;
	}

	public String getIcoCh() {
		return icoCh;
	}

	public void setIcoCh(String icoCh) {
		this.icoCh = icoCh;
	}

	public String getIco() {
		return ico;
	}

	public void setIco(String ico) {
		this.ico = ico;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getCampusProxy() {
		return campusProxy;
	}

	public void setCampusProxy(int campusProxy) {
		this.campusProxy = campusProxy;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getmId() {
		return mId;
	}

	public void setmId(String mId) {
		this.mId = mId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getXing() {
		return xing;
	}

	public void setXing(String xing) {
		this.xing = xing;
	}
}