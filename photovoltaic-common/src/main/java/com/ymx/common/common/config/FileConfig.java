package com.ymx.common.common.config;

import com.ymx.common.utils.DateUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;
import java.io.File;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * @Description: TODO(这里用一句话描述这个类的作用)
 */
public class FileConfig extends PropertyPlaceholderConfigurer {
	
	private static final String dateFormat = "yyyy-MM-dd";
	/**
	 * 系统的访问地址,下载时需要使用 外网访问地址
	 */
	private static String WEB_URL;
	/**
	 * 文件保存路径
	 */
	private static String FILE_PATH;

	/**
	 * 上传大小限制
	 */
	private static final Map<String, Long> fileSizeLimit = new HashMap<String, Long>();
	/**
	 * 文件大小限制后缀
	 */
	private static final String limitSuffix = "_SIZE";

	/**
	 * 加载配置
	 */
	@Override
	protected void processProperties(ConfigurableListableBeanFactory beanFactory, Properties props)
			throws BeansException {
		// 初始化各类型文件上传大小限制
		for (Object key : props.keySet()) {
			// 获取配置文件的key-value
			String keyStr = key.toString();
			String value = props.getProperty(keyStr);
			if (keyStr.contains(limitSuffix)) {
				fileSizeLimit.put(keyStr, Long.parseLong(value));
			}
		}
		// 初始化成员变量
		for (Object key : props.keySet()) {
			// 获取配置文件的key-value
			String keyStr = key.toString();
			String value = props.getProperty(keyStr);
			try {
				// 通过反射赋值
				Field field = this.getClass().getDeclaredField(keyStr);
				if (field != null) {
					field.setAccessible(true);
					field.set(null, value);
				}
			} catch (Exception e) {
				e.printStackTrace();
				continue;
			}
		}
	}


	/**
	 * 系统的访问地址,下载时需要使用
	 */
	public static String WEB_URL() {
		return WEB_URL;
	}
	/**
	 * 文件保存路径,以目录符号结尾
	 */
	public static String FILE_PATH() {
		// 保证路径以目录符号结尾
		if (!FILE_PATH.endsWith(File.separator)) {
			FILE_PATH = FILE_PATH + File.separator;
		}
		return FILE_PATH;
	}
	/**
	 * @param: @param 文件type
	 * @param: @return 如果有限制，返回Long 否则返回null
	 */
	public static Long getLimit(String type) {
		String key = type + limitSuffix;
		if (fileSizeLimit.containsKey(key)) {
			return fileSizeLimit.get(key);
		}
		return null;
	}
	/**
	 * 根据文件名称获取下载路径
	 * @date: 2017-09-31 下午5:53:08
	 * @param fileName 文件名称
	 * @return
	 */
	public static String getDownloadPath(String fileName) {
		if (StringUtils.isEmpty(fileName) || "(null)".equals( fileName )) {
			return "";
		}
		return WEB_URL + deCodeUrl(fileName);
	}
	
	/***
	 * 获取文件真实路径
	 * @desc
	 * @date 2017-11-14
	 * @param fileName
	 * @return
	 */
	public static String getFileRealPath(String fileName) {
		if (StringUtils.isEmpty(fileName) || "(null)".equals( fileName )) {
			return "";
		}
		return FILE_PATH() + deCodeUrl(fileName);
	}
	
	/**
	 * 通过模块号生成保存文件全路径,路径规则是 保存目录/模块/日期/模块号_毫秒数
	 * @param fileName 文件名称
	 * @param type 模块名称
	 * @date: 2017-09-31 下午5:53:08
	 */
	public static String encodeUrl(String type, String fileName) {
		long timeMillis = System.currentTimeMillis();
		// 获取日期
		String date = DateUtils.timestampToDate(timeMillis, dateFormat);
		// 获取文件后缀
		String prefix = "";
		if (fileName.indexOf(".") < fileName.length() - 1) {
			prefix = fileName.substring(fileName.lastIndexOf(".") + 1);
		}
		// 生成文件名
		String newFileName = type + "_" + timeMillis;
		// 原始文件有后缀 加上后缀
		newFileName = prefix == "" ? newFileName : newFileName + "." + prefix;
		// 生成全路径
		String path = FILE_PATH() + type + File.separator + date + File.separator + newFileName;
		return path;
	}

	public static String parentPath(String type) {
		long timeMillis = System.currentTimeMillis();
		// 获取日期
		String date = DateUtils.timestampToDate(timeMillis, dateFormat);
		return FILE_PATH() + type + File.separator + date  ;
	}

	/***
	 * 创建文件名称
	 * @param type    文件夹分类类型
	 * @param fileName 真实文件名称
	 * @return
	 */
	public static String createNewFileName(String type , String fileName ) {
		long timeMillis = System.currentTimeMillis();
		// 获取日期
		String date = DateUtils.timestampToDate(timeMillis, dateFormat);
		// 获取文件后缀
		String prefix = "";
		if (fileName.indexOf(".") < fileName.length() - 1) {
			prefix = fileName.substring(fileName.lastIndexOf(".") + 1);
		}
		// 生成文件名
		String newFileName = type + "_" + timeMillis;
		// 原始文件有后缀 加上后缀
		newFileName = prefix == "" ? newFileName : newFileName + "." + prefix;
		// 生成全路径
		return newFileName;
	}

	/**
	 * @Description: 根据文件名找到文件下载路径
	 * @date: 2017-09-31 下午5:53:08
	 * @param fileName 文件名称
	 */
	private static String deCodeUrl(String fileName) {
		// 文件名--模块号_毫秒数.后缀
		// 去除后缀
		String subName = fileName;
		if (fileName.lastIndexOf(".") < fileName.length() - 1) {
			subName = fileName.substring(0, fileName.lastIndexOf("."));
		}
		// 保存路径--保存目录/模块号/日期/模块号_毫秒数
		String[] strs = subName.split("_", -1);
		// 获取毫秒数
		long timeMillis = Long.parseLong(strs[1]);
		// 模块号
		String type = strs[0];
		// 获取日期目录
		String date = DateUtils.timestampToDate(timeMillis, dateFormat);
		// 获取完整路径
		String path = type + "/" + date + "/" + fileName;
		return path;
	}
	
	/****
	 * 删除文件
	 * @desc
	 * @date 2017-11-14
	 */
	public static void deleteFile ( String fileName ){
		String filePath = getFileRealPath(fileName) ;
		File file = new File(filePath);
		if(file.exists() && file.isFile()){
			file.delete();
		}
	}


}
