/**
 * 
 */
package com.ymx.common.common.result;

import java.io.Serializable;
import org.apache.commons.lang.StringUtils;


/**
 * @Description 处理结果类 请求方--constumer--provider都通过此类交互结果
 */
public class CallResult implements Serializable {

	private static final long serialVersionUID = 1L;
	// 处理结果(成功/失败)
	private ResultCode resultCode;
	// 返回结果码
	private String errNo;
	// 返回结果码
	private int code;
	// 返回结果信息
	private String errMsg;
	// 返回结果集  前端采用Map 后台任意 例:List , Object
	private Object reModel;
	// 返回给前端结果
	private String rec;
	//总记录数
	private int count;
	//附加分页信息
	private PageInfo pageInfo;

	public PageInfo getPageInfo() {
		return pageInfo;
	}
	public void setPageInfo(PageInfo pageInfo) {
		this.pageInfo = pageInfo;
		this.count = pageInfo.getCount() ;
	}
	/**
	 * 隐藏构造方法，只能通过newInstance获取对象 默认返回成功
	 */
	private CallResult() {
		setSuccess();
	}
	// 设置成功
	private void setSuccess() {
		this.resultCode = ResultCode.SUCCESS;
		this.code = ResultCode.CODE_SUCCESS;
		this.rec = "SUC";
	}
	// 设置失败
	private void setFailed() {
		this.resultCode = ResultCode.FAILED;
		this.code = ResultCode.CODE_FAIL;
		this.rec = "FAL";
	}
	/**
	 * @Description:
	 * @date: 2017-09-31 下午8:43:57
	 * @return 处理是否成功
	 */
	public boolean isSuccess() {
		if (this.resultCode == ResultCode.SUCCESS) {
			return true;
		}
		return false;
	}
	public static CallResult newInstance() {
		CallResult result = new CallResult();
		return result;
	}
	/**
	 * 返回失败结果
	 */
	public static CallResult newInstance(String errNo, String errMsg ) {
		CallResult result = new CallResult();
		result.setErrNo(errNo);
		result.setErrMsg(errMsg);
		return result;
	}
	/**
	 * @param errNo 错误码 不能为空
	 * @param errMsg 误信息 设置错误，将结果改为失败。
	 */
	public void setErr(String errNo, String errMsg) {
		this.errNo = errNo;
		if (!StringUtils.isBlank(errNo)) {
			// 将结果标记为失败
			setFailed();
			setErrNo(errNo);
			setErrMsg(errMsg);
		} else {
			this.errNo = null;
		}
	}
	@Override
	public String toString() {
		// 处理正常
		if (resultCode == ResultCode.SUCCESS) {
			return "CallResult [rec=SUC,reModel=" + reModel + "]";
		}
		// 处理失败
		else if (resultCode == ResultCode.FAILED) {
			return "CallResult [rec=FAL,errNo=" + errNo + ", errMsg=" + errMsg + "]";
		} else
			return "";
	}
	public void setErrNo(String errNo) {
		// 将结果标记为失败
		setFailed();
		this.errNo = errNo;
	}
	public String getErrNo() {
		return errNo;
	}
	public String getErrMsg() {
		return errMsg;
	}
	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}
	public Object getReModel() {
		return reModel;
	}
	public void setReModel(Object reModel) {
		this.reModel = reModel;
	}
	public int getCode() {
		return code;
	}
	public void setCode(int code) {
		this.code = code;
	}
	public String getRec() {
		return rec;
	}
	public void setRec(String rec) {
		this.rec = rec;
	}
	public int getCount() {
		return count;
	}
	public void setCount(int count) {
		this.count = count;
	}
	
}
