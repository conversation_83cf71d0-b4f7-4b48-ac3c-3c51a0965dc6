package com.ymx.common.common.annotation;

import com.ymx.common.common.constant.Fields;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Description: excel注解类
 * @author: zhanghc
 * @date: 2016-6-2 下午5:03:47
 */
@Retention(RetentionPolicy.RUNTIME)
@Target( { java.lang.annotation.ElementType.FIELD })
public @interface ExcelVOAttribute {

	 /** 
     * 导出到Excel中的名字. 
     */  
	public abstract String name() default "";
	
	/**
	 * 读取那一列
	 */
	public abstract int cell();

	/** 
     * 配置列的名称,对应A,B,C,D.... 
     */
	public abstract String column() default "";
	
	/**
	 * 是否非空
	 */
	public abstract boolean isEmpty() default true;
	
	/**
	 * 字段类型
	 */
	public abstract String columnType() default "";
	
	/**
     * 翻译字段
     */
    public abstract boolean enField() default false;
    
    /**
     * 需要翻译的类
     */
    public abstract Class<?> cls() default Fields.class;
    

	/** 
     * 提示信息 
     */
	public abstract String prompt() default "";

	/** 
     * 设置只能选择不能输入的列内容. 
     */ 
	public abstract String[] combo() default {};

	/** 
     * 是否导出数据,应对需求:有时我们需要导出一份模板,这是标题需要但内容需要用户手工填写. 
     */
	public abstract boolean isExport() default true;

}
