package com.ymx.common.common.annotation;

import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 编辑
 * 页面字符串日期,转成日期格式
 * Copyright © 2017 意美旭科技
 * @Description: 翻译类
 * @date: 2017-4-12
 */
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Target( { java.lang.annotation.ElementType.FIELD })
public @interface StrToDateAttribute {

	/** 
     * 需要翻译到哪个字段上去 
     */
	public abstract String column() default "";
	
	/**
	 * 字段类型
	 */
	public abstract String columnType() default "";
	
	/**
     * 是否需要翻译字段
     */
    public abstract boolean enField() default false;
    

}
