
package com.ymx.common.common.filter;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class CharacterEncodingFilter implements Filter {

    protected String encoding = "utf-8";

    protected FilterConfig filterConfig = null;

    protected boolean ignore = true;

    public void destroy() {
        this.encoding = null;
        this.filterConfig = null;
    }

    public void doFilter( ServletRequest request, ServletResponse response, FilterChain chain ) throws IOException, ServletException {
    	/**ajax跨域访问*/
    	HttpServletResponse res = (HttpServletResponse) response;
    	HttpServletRequest req = (HttpServletRequest) request;
    	res.setHeader("Access-Control-Allow-Origin", "*");
    	
        if ( (this.ignore) || (req.getCharacterEncoding() == null) ) {
            String encoding = selectEncoding( req );
            if ( encoding != null )  {
            	req.setCharacterEncoding( encoding );
            }
        }
        chain.doFilter( req, res );
    }

    public void init( FilterConfig filterConfig ) throws ServletException {
        this.filterConfig = filterConfig;
        this.encoding = filterConfig.getInitParameter( "encoding" );

        String value = filterConfig.getInitParameter( "ignore" );
        if ( value == null )
            this.ignore = true;
        else if ( value.equalsIgnoreCase( "true" ) )
            this.ignore = true;
        else if ( value.equalsIgnoreCase( "yes" ) )
            this.ignore = true;
        else
            this.ignore = false;
    }

    protected String selectEncoding( ServletRequest request ) {
        return this.encoding;
    }

}
