package com.ymx.common.common.constant;

/**
 * @DESC
 * @DATE 2018/7/20
 * @NAME Constants
 * @MOUDELNAME 模块
 */
public class Constants  {

    // MYSQL
    public static final String DATABASE_MYSQL = "mysql";

    //ORACLE
    public static final String DATABASE_ORACLE = "oracle";

    //MSSQL SERVER
    public static final String DATABASE_SQLSERVER = "sqlserver";

    //DB2
    public static final String DATABASE_DB2 = "db2";

    // 增加操作标志位
    public static final String OPERATOR_ADD = "add";

    // 修改操作标志位
    public static final String OPERATOR_EDIT = "edit";

    public static final String DEFAULT_ICON = "asterisk_yellow.png";

    // 操作按钮的Icon
    public static final String OPERATOR_ICONS = "icon-edit|icon-remove|icon-add|icon-role"
            + "|icon-choose-user|icon-role-view|icon-arrow-switch|icon-reload|icon-cancel"
            + "|icon-save|icon-help|icon-function|icon-arrow-switch|icon-up|icon-down|icon-start|icon-end";



    /**初始密码*/
    public static final String INITIAL_PASSWORD = "123456";

    /**
     * 时间戳允许误差时间（毫秒）
     */
    public static final int TIMESTAMP_ALLOWED = 600000000;
    /**
     * token失效时间（秒）
     */
    public static final int TOKEN_EXPIRY = 24 * 60 * 60;
    /**
     * token在redis中的存放模块名
     */
    public static final String TOKEN_REDIS = "token";
	/**
	 * 采集数据失效时间（秒）
	 */
	public static final int DATA_EXPIRY = 60 * 60;
	/**
	 * 采集数据缓存数据
	 */
	public static final String DATA_REDIS = "data";

	public static final String LIST_REDIS = "list";
    /**
     * 邮箱激活在redis中的存放模块名
     */
    public static final String EMAIL_REDIS = "email";

    /**
     * 邮箱激活失效时间（秒）
     */
    public static final int EMAIL_EXPIRY = 60 * 60 * 24;

    //升级任务创建
    public static final short UPDATE_TASK_CREATE = 0;
    //版本发送成功
    public static final short VERSION_SENT_SUCCESS = 1;
    //版本发送失败
    public static final short VERSION_SENT_FAIL = 2;
    //版本加载成功
    public static final short VERSION_LOAD_SUCCESS = 3;
    //版本加载部分成功
    public static final short VERSION_LOAD_PART_SUCCESS = 33;
    //版本加载失败
    public static final short VERSION_LOAD_FAIL = 4;
    //升级信息发送成功
    public static final short INFO_SENT_SUCCESS = 5;
    //升级信息发送部分成功
    public static final short INFO_SENT_PART_SUCCESS = 55;
    //升级信息发送失败
    public static final short INFO_SENT_FAIL = 6 ;
    //升级成功
    public static final short UPDATE_SUCCESS = 7;
    //版本查询完成
    public static final short VERSION_QUERY_COMPLETE = 8;
    //重试
    public static final short UPDATE_RETRY = 9;



    /** 编码 */
    public static class CHARSET {
        /** utf-8编码 */
        public static final String UTF = "UTF-8";
        /** gbk编码 */
        public static final String GBK = "GBK";
        /** GB2312编码 */
        public static final String GB2312 = "GB2312";
    }


    /**
     * 用户常量
     */
    public static class MEMBER{
        /**
         * 用户正常
         */
        public static final int STATUS_NORMAL = 1;
        /**
         * 用户注销
         */
        public static final int STATUS_DEL = 2 ;
        /**
         * 用户锁定
         */
        public static final int STATUS_LOCK = 3;

        /** 新增用户 */
        public static final int STATUS_NEW = 4;
    }

    /** 推送类型 */
    public static class PUSH_TYPE {
        /** 单个推送*/
        public static final int SINGLE = 1;
        /** 筛选推送 */
        public static final int SCREENING = 2;
        /** 全部推送 */
        public static final int ALL = 3;
    }

    /**
     * 真假常量（1、真，2，假）
     */
    public static class TRUEORFALSE{
        /**真*/
        public static final int TRUE = 1;
        /**假*/
        public static final int FALSE = 2;
    }

    /** 后台端服务 */
    public static  final String SERVER = "SERVER";
    /** APP端 */
    public static  final String APP = "APP";
    /** PC端 */
    public static  final String PC = "PC";

}
