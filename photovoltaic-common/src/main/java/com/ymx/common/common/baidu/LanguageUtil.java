package com.ymx.common.common.baidu;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 *
 * <AUTHOR> @version 2018/11/3
 */
public class LanguageUtil {
	// 在平台申请的APP_ID 详见 http://api.fanyi.baidu.com/api/trans/product/desktop?req=developer
	private static final String APP_ID = "20181018000221224";
	private static final String SECURITY_KEY = "2Q2Ry81KiOyIGDd4R1MU";

	public static void main(String[] args) {
		TransApi api = new TransApi(APP_ID, SECURITY_KEY);

		String query = "高度600米";
		System.out.println(api.getTransResult(query, "auto", "en"));


		System.out.println(getLanguageCN(query));

		query = "Height 600 m meters";
		System.out.println(api.getTransResult(query, "auto", "zh"));

		System.out.println(getLanguageCN(query));

		System.out.println(getLanguageEN("额定功率"));


	}
	public static String getLanguageCN(String name){
		try{
			TransApi api = new TransApi(APP_ID, SECURITY_KEY);
			String result=api.getTransResult(name, "auto", "zh");
			return getJSONObject(result);
		}catch (Exception e){
			System.out.println("翻译失败 LanguageUtil.getLanguageCN   "+e.getMessage());
			e.printStackTrace();
		}
		return name;
	}

	public static String getLanguageEN(String name){
		try{
			TransApi api = new TransApi(APP_ID, SECURITY_KEY);
			String result=api.getTransResult(name, "auto", "en");
			return getJSONObject(result);
		}catch (Exception e){
			System.out.println("翻译失败 LanguageUtil.getLanguageEN   "+e.getMessage());
			e.printStackTrace();
		}
		return name;
	}
	/**
	 * 中英文翻譯解析
	 * @param result
	 * @return
	 */
	private static String getJSONObject(String result){
//		{"from":"zh","to":"en","trans_result":[{"src":"\u9ad8\u5ea6600\u7c73","dst":"Height of 600 meters"}]}


		JSONObject jsonObject=JSONObject.fromObject(result);

		//检查,对 trans_result 键的存在性检查。
		if (!jsonObject.containsKey("trans_result")) {
			return null;
		}

		JSONArray jsonArray=JSONArray.fromObject(jsonObject.getString("trans_result"));
		String resultData="";
		if(!jsonArray.isEmpty()){
			JSONObject myjObject = jsonArray.getJSONObject(0);
			resultData=myjObject.get("dst").toString();
		}
		return resultData;
	}
	public static String getLanguageCHTOEN(String content){
		String contentEn="";
		if("温度报警".equals(content)){
			contentEn="temperature alarm ";
		}else if("遮挡报警".equals(content)){
			contentEn="Occluding alarm ";
		}else if("输入过压".equals(content)){
			contentEn="Input overvoltage ";
		}else if("温度解除报警".equals(content)){
			contentEn="Temperature relief alarm ";
		}else if("遮挡解除".equals(content)){
			contentEn="Occluding ";
		}else if("高温关断报警".equals(content)){
			contentEn="High temperature turn off alarm ";
		}else if("自动打开".equals(content)){
			contentEn="Automatic opening ";
		}else if("输入过流".equals(content)){
			contentEn="Input overflow ";
		}else if("输出过压".equals(content)){
			contentEn="Output overvoltage ";
		}else if("输出过流".equals(content)){
			contentEn="Output overflow ";
		}else if("解除输入过压".equals(content)){
			contentEn="Relieving input overvoltage ";
		}else if("解除输入过流".equals(content)){
			contentEn="Relieving input overcurrent ";
		}else if("解除输出过压".equals(content)){
			contentEn="Relieving output overvoltage ";
		}else if("解除输出过流".equals(content)){
			contentEn="Remove output overcurrent ";
		}else{
			contentEn=content;
		}
		return contentEn;
	}
}
