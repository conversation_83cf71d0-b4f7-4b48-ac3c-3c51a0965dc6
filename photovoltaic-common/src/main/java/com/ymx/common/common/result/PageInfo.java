package com.ymx.common.common.result;

import java.io.Serializable;

/**
 * @Description: (公共分页bean)
 */
public class PageInfo implements Serializable {
 
     /**
     * 注释内容
     */
    private static final long serialVersionUID = 1L;
    /** 当前页*/
    private int pageNo = 1 ;  
    /**   页大小*/
    private int pageSize = 10;
    /**   总记录数*/
    private int count; 
    /**   总页数*/
    private int pageCount;  
    /**   包括当前页以上所有记录 */
    private int prev; 
    /**  当前页开始记录*/
    private int next;  
    
    /**  排序字段及方向 filed1 desc,filed2 asc*/
    private String orderInfo;
  	
  	public PageInfo(){}
    /**
     * 分页信息
     * @param pageIndex 当前页
     * @param pageSize 页大小
     * @param count 总记录数
     */
    public PageInfo(int pageNo, int pageSize, int count) { 
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.count = count;
        this.prev=pageNo * pageSize + 1;
        this.next=pageSize * (pageNo -1);
        this.pageCount=(int)Math.ceil(count / (double)pageSize);
    }
    
    /**
     * 分页信息
     * @param next 开始位置
     * @param size 查询条数
     * @param count 总记录数
     */
    public PageInfo(int next, int pageSize) { 
        this.pageSize = pageSize;
        this.next= next;
    }

    /**
     * 获取总页数
     */
    public void setPageCount(int count, int pageSize) {
        if(0 < count && 0 < pageSize) {
            this.pageCount = count % pageSize == 0 ? (count / pageSize) : (count / pageSize) + 1;
        }
    }
    
    /**
     * 获取当前页
     * 
     * @return 当前页码
     */
    public int getPageNo() {
        return pageNo;
    }

    /**
     * 设置当前页
     * @param pageIndex 当前页码
     */
    public void setPageIndex(int pageNo) {
        this.pageNo = pageCount <= 1 ? 1 : pageNo;
    }

    /**
     * 获取页大小
     * @return 页大小
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * 设置页大小
     * @param pageSize  页大小
     */
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 获取总记录数
     * @return 总记录数
     */
    public int getCount() {
        return count;
    }

    /**
     * 设置总记录数
     * @param count 总记录数
     */
    public void setCount(int count) {
        this.count = count;
    }

    /**
     * 获取总页数
     * @return 总页数
     */
    public int getPageCount() {
        return pageCount;
    }

    /**
     * 获取 包括当前页以上所有记录数
     * @return
     */
    public int getPrev() {
        return prev;
    }

    /**
     * 获取当前页开始记录数
     * @return
     */
    public int getNext() {
        return next;
    }

    /**
     * 排序字段及方向 filed1 desc,filed2 asc
     * @return 排序信息
     */
    public String getOrderInfo() {
        return orderInfo;
    }

    /**
     * 排序字段及方向 filed1 desc,filed2 asc
     * @param orderInfo  排序信息
     */
    public void setOrderInfo(String orderInfo) {
        this.orderInfo = orderInfo;
    }



	public void setPageNo(int pageNo) {
		this.pageNo = pageNo;
	}



	public void setPageCount(int pageCount) {
		this.pageCount = pageCount;
	}



	public void setPrev(int prev) {
		this.prev = prev;
	}



	public void setNext(int next) {
		this.next = next;
	}



	@Override
    public String toString() {
        return "PageInfo [pageNo=" + pageNo + ", pageSize=" + pageSize
                + ", count=" + count + ", pageCount=" + pageCount
                + ", orderInfo=" + orderInfo + "]";
    }
    
}
