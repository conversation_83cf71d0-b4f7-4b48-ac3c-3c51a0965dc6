package com.ymx.common.common.filter;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class ResponseHeaderFilter implements Filter {
	FilterConfig fc;

	/**
	 * 允许ajax跨域请求 测试使用
	 */
	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException,
			ServletException {
		HttpServletResponse response = (HttpServletResponse) res;
		response.setHeader("Access-Control-Allow-Origin", "*");
		chain.doFilter(req, response);
	}
	public void init(FilterConfig filterConfig) {
		this.fc = filterConfig;
	}
	public void destroy() {
		this.fc = null;
	}
}
