package com.ymx.common.common.result;

import java.io.Serializable;


/**
 * Copyright © 2017 意美旭科技
 * @Description: (分页和排序公共类) 
 * @date: 2017-09-30 下午3:02:57
 */
public class PageView implements Serializable{

    /**   
	 * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)   
	 */   
	private static final long serialVersionUID = 1L;

	/**
     * 前台传过来的当前页码
     */
	    private int pageNo = 1;
    
    /**
     * 前台传过来的显示条数
     */
    private int pageSize = 10;
    
    /**
     * 前台传过来的排序字段
     */
    private String sort;
    
    /**
     * 前台传过来的排序类型 dsc/asc
     */
    private String order;

    public int getPageNo()
    {
        return pageNo;
    }

    public void setPageNo( int pageNo )
    {
        this.pageNo = pageNo;
    }

    public int getPageSize()
    {
        return pageSize;
    }

    public void setPageSize( int pageSize )
    {
        this.pageSize = pageSize;
    }

    public String getSort()
    {
        return sort;
    }

    public void setSort( String sort )
    {
        this.sort = sort;
    }

    public String getOrder()
    {
        return order;
    }

    public void setOrder( String order )
    {
        this.order = order;
    }
}
