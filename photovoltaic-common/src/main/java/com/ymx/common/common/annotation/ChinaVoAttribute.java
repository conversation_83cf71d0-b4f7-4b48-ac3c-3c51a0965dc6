package com.ymx.common.common.annotation;

import com.ymx.common.common.constant.Fields;

import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Copyright © 2017 意美旭科技
 * @Description: 翻译类
 * @date: 2017-09-26 下午5:03:47
 */
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Target( { java.lang.annotation.ElementType.FIELD })
public @interface ChinaVoAttribute {

	 /** 
     * 导出到Excel中的名字. 
     */  
	public abstract String name() default "";
	
	/**
	 * 读取那一列
	 */
	public abstract int cell() default 0;

	/** 
     * 需要翻译到哪个字段上去 
     */
	public abstract String column() default "";
	
	/** 
     * 需要从哪个字段翻译过来
     */
	public abstract String srcColumn() default "";
	
	/**
	 * 是否非空
	 */
	public abstract boolean isEmpty() default true;
	
	/**
	 * 字段类型
	 */
	public abstract String columnType() default "";
	
	/**
     * 是否需要翻译字段
     */
    public abstract boolean enField() default false;
    
    /**
     * 需要翻译的类
     */
    public abstract Class<?> cls() default Fields.class;
    

	/** 
     * 提示信息 
     */
	public abstract String prompt() default "";

	/** 
     * 设置只能选择不能输入的列内容. 
     */ 
	public abstract String[] combo() default {};

	/** 
     * 是否导出数据,应对需求:有时我们需要导出一份模板,这是标题需要但内容需要用户手工填写. 
     */
	public abstract boolean isExport() default true;
	
	/**
	 * 是否是下载路径(按文件名翻译地址) 
	 */
	public abstract boolean isFilePath() default false;

	/**
	 * 是否是下载地址(直接使用文件名做地址)
	 */
	public abstract boolean isUrlPath() default false;

}
