package com.ymx.common.common.constant;

public class ErrCode {

	/**
	 * 用户账户信息管理
	 */
	public static class ACCOUNTINFO {
		/**
		 * 账户数量
		 */
		public static final int COUNT = 1;
		/**
		 * 账户已在使用
		 */
		public static final String ACCOUNT_EXITS_ERR = "M0001";
		public static final String ACCOUNT_EXITS = "该账户已在使用";
		/**
		 * 账户信息不匹配
		 */
		public static final String ACCOUNT_ERR = "M0002";
		public static final String ACCOUNT_ERR_TEX = "账户信息不匹配";
		/**
		 * 银行卡绑定数量超出
		 */
		public static final String ACCOUNT_ACCOUNT_LIMIT_ERR = "M0003";
		public static final String ACCOUNT_ACCOUNT_LIMIT = "一个用户只可绑定一张银行卡";
		/**
		 * 账户信息已被绑定
		 */
		public static final String ACCOUNT_BD_ERR = "M0004";
		public static final String ACCOUNT_BD_ERR_TEX = "账户已被绑定";
	}

	/**
	 * 邮箱验证
	 */
	public static class EMAIL_ERR {
		/**
		 * 邮箱失效标识
		 */
		/**
		 * 邮箱失效
		 */
		public static final String EMAIL_ERR = "E0001";
		public static final String EMAIL_ERR_TEXT = "邮件已失效，请重新获取！";
		public static final String EMAIL_ERR_TEXT_EN = "The mail is invalid. Please get it again.";
		public static final String EMAIL_ERR_TEXT_AR = "البريد الإلكتروني غير صالحة ، يرجى إعادة";
		public static final String EMAIL_ERR_TEXT_JA = "メールが失効しました。再取得してください";
		public static final String EMAIL_ERR_TEXT_FR = "Le Courrier n 'est plus valable";
		public static final String EMAIL_ERR_TEXT_ES = "El mensaje ha caducado.Por favor,Recógelo";
		/**
		 * 邮箱已被绑定
		 */
		public static final String EMAIL_BD_ERR = "E0002";
		/**
		 * 邮箱已被绑定
		 */
		public static final String EMAIL_BD_ERR_TEXT_STRING = "邮箱已被绑定激活";
		/**
		 * 邮箱正在使用
		 */
		public static final String EMAIL_USING_ERR = "E0003";
		/**
		 * 邮箱正在使用
		 */
		public static final String EMAIL_USING_ERR_TEXT_STRING = "邮箱正在使用中";
		/**
		 * 邮箱格式不正确
		 */
		public static final String EMAIL_FORMAT_ERR = "E0004";
		public static final String EMAIL_FORMAT_ERR_TEXT = "邮箱格式不正确";
		public static final String EMAIL_FORMAT_ERR_TEXT_EN = "Incorrect mailbox format";
		public static final String EMAIL_FORMAT_ERR_TEXT_AR = "شكل علبة البريد غير صحيح";
		public static final String EMAIL_FORMAT_ERR_TEXT_JA = "メールボックスのフォーマットが間違っています";
		public static final String EMAIL_FORMAT_ERR_TEXT_FR = "Boîte aux lettres";
		public static final String EMAIL_FORMAT_ERR_TEXT_ES = "Formato de correo incorrecto";
		/**
		 * 邮箱过长
		 */
		public static final String EMAIL_LONG_ERR = "E0005";
		public static final String EMAIL_LONG_ERR_TEXT = "邮箱过长";
		public static final String EMAIL_LONG_ERR_TEXT_EN = "Mailbox is too long";
		public static final String EMAIL_LONG_ERR_TEXT_AR = "علبة طويلة جدا";
		public static final String EMAIL_LONG_ERR_TEXT_JA = "メールアドレスが長すぎます";
		public static final String EMAIL_LONG_ERR_TEXT_FR = "E - mail";
		public static final String EMAIL_LONG_ERR_TEXT_ES = "Correo electrónico";
	}

	//系统异常
	public static final String SYSTEM_ERR = "999999";
	public static final String SYSTEM_ERR_TEXT = "系统异常";
	public static final String SYSTEM_ERR_TEXT_EN = "System abnormality";
	public static final String SYSTEM_ERR_TEXT_AR = "نظام الشذوذ";
	public static final String SYSTEM_ERR_TEXT_JA = "システム異常";
	public static final String SYSTEM_ERR_TEXT_FR = "Anomalie du système";
	public static final String SYSTEM_ERR_TEXT_ES = "Sistema anormal";

	public static final String SYSTEM_PUSH_ERR = "100";
	public static final String SYSTEM_PUSH_ERR_TEXT = "推送失败";
	public static final String SYSTEM_PUSH_ERR_TEXT_EN = "Push failure";
	public static final String SYSTEM_PUSH_ERR_TEXT_AR = "دفع الفشل";
	public static final String SYSTEM_PUSH_ERR_TEXT_JA = "プッシュ失敗";
	public static final String SYSTEM_PUSH_ERR_TEXT_FR = "La livraison a échoué";
	public static final String SYSTEM_PUSH_ERR_TEXT_ES = "Fallóla entrega";

	//验证码错误
	public static final String VERIFICATION_CODE_ERR = "1001";
	public static final String VERIFICATION_CODE_ERR_TEXT = "验证码错误";
	public static final String VERIFICATION_CODE_ERR_TEXT_EN = "Verification code error";
	public static final String VERIFICATION_CODE_ERR_TEXT_AR = "رمز التحقق من الخطأ";
	public static final String VERIFICATION_CODE_ERR_TEXT_JA = "認証コードエラー";
	public static final String VERIFICATION_CODE_ERR_TEXT_FR = "Erreur de code d 'authentification";
	public static final String VERIFICATION_CODE_ERR_TEXT_ES = "Código de verificación";

	public static final String VERIFICATION_CODE_ERR_OVERTIME = "1006";
	public static final String VERIFICATION_CODE_ERR_OVERTIME_TEXT = "验证码过期";
	public static final String VERIFICATION_CODE_ERR_OVERTIME_TEXT_EN = "Verification code expires";
	public static final String VERIFICATION_CODE_ERR_OVERTIME_TEXT_AR = "رمز التحقق منتهية الصلاحية ";
	public static final String VERIFICATION_CODE_ERR_OVERTIME_TEXT_JA = "認証コードが失効しました";
	public static final String VERIFICATION_CODE_ERR_OVERTIME_TEXT_FR = "Code d 'authentification expiré";
	public static final String VERIFICATION_CODE_ERR_OVERTIME_TEXT_ES = "Código de autenticación caducado";

	//参数错误
	public static final String PARAM_ERR = "1002";
	public static final String PARAM_ERR_TEXT = "参数错误";
	public static final String PARAM_ERR_TEXT_EN = "Parameter error";
	public static final String PARAM_ERR_TEXT_AR = "المعلمة خطأ";
	public static final String PARAM_ERR_TEXT_JA = "パラメータエラー";
	public static final String PARAM_ERR_TEXT_FR = "Erreur paramétrique";
	public static final String PARAM_ERR_TEXT_ES = "Error de parámetro";


	//账号错误
	public static final String ACCOUNT_ERR = "1003";
	public static final String ACCOUNT_EXIST_TEXT = "账号已存在";



	public static final String ACCOUNT_EXIST_TEXT_EN = "Account already exists";
	public static final String ACCOUNT_ERR_TEXT = "账号或密码错误";

	public static final String ACCOUNT_LOCK = "1004";
	public static final String ACCOUNT_LOCK_TEXT = "账户已被锁定";
	public static final String ACCOUNT_LOCK_TEXT_EN = "Account has been locked";
	public static final String ACCOUNT_LOCK_TEXT_AR = "الحساب مغلق";
	public static final String ACCOUNT_LOCK_TEXT_JA = "アカウントがロックされました";
	public static final String ACCOUNT_LOCK_TEXT_FR = "Le compte est verrouillé";
	public static final String ACCOUNT_LOCK_TEXT_ES = "La cuenta estábloqueada";

	public static final String ACCOUNT_DELETE = "1007";
	public static final String ACCOUNT_DELETE_TEXT = "账户已被注销";
	public static final String ACCOUNT_DELETE_TEXT_EN = "The account has been cancelled.";
	public static final String ACCOUNT_DELETE_TEXT_AR = "تم إلغاء الحساب";
	public static final String ACCOUNT_DELETE_TEXT_JA = "アカウントがキャンセルされました";
	public static final String ACCOUNT_DELETE_TEXT_FR = "Compte annulé";
	public static final String ACCOUNT_DELETE_TEXT_ES = "Cuentas pasadas a pérdidas y ganancias";

	public static final String ACCOUNT_WAY="F0017";
	public static final String ACCOUNT_WAY_TEXT = "注册账号可以采用手机号或邮箱";
	public static final String ACCOUNT_WAY_TEXT_EN = "You can use your mobile phone number or email address to sign up an account.";
	public static final String ACCOUNT_WAY_TEXT_AR = "رقم الحساب المسجل يمكن استخدام الهاتف المحمول أو البريد الإلكتروني";
	public static final String ACCOUNT_WAY_TEXT_JA = "登録アカウントは携帯番号やメールアドレスを使ってもいいです";
	public static final String ACCOUNT_WAY_TEXT_FR = "Numéro d 'enregistrement";
	public static final String ACCOUNT_WAY_TEXT_ES = "Número de cuenta registrado:teléfono móvil o correo electrónico";


	public static final String LOGIN_CODE_ERR = "1005";
	public static final String PHONE_EXIST_TEXT = "手机号码已存在";

	public static final String NOT_LOGIN_ERR = "1008";
	public static final String NOT_LOGIN_ERR_TEXT = "请先登陆后再进行报名";

	/**
	 * 原手机号码错误
	 */
	public static final String PHONE_ERR = "F0007";
	public static final String PHONE_ERR_TEXT = "原手机号码错误";


	/**
	 * 图片操作异常
	 */
	public static final String UPLOAD_PIC_FAL = "F0001";
	public static final String UPLOAD_PIC_FAL_TEXT = "上传图片失败";
	public static final String UPLOAD_PIC_FAL_TEXT_EN = "Upload pictures failed";
	public static final String UPLOAD_FILE_FAL_TEXT = "上传文件失败";
	public static final String UPLOAD_VIDEO_FILE_FAL_TEXT = "请选择视频文件上传";
	public static final String UPLOAD_PIC_NOTEXISTS_TEXT = "请选择需要上传的文件";
	public static final String UPLOAD_PIC_TRANSACTION_TEXT = "文件处理过程中出现了异常情况";

	/**
	 * 上传图片不存在
	 */
	public static final String UPLOAD_PIC_NULL = "F0002";
	public static final String UPLOAD_PIC_NULL_TEXT = "上传图片不存在";
	public static final String UPLOAD_PIC_NULL_TEXT_EN = "Upload pictures do not exist.";
	/**
	 * 文件存在异常
	 */
	public static final String FILE_EXISTS = "F0003";
	public static final String FILE_EXISTS_TEXT = "文件已存在";
	public static final String FILE_EXISTS_TEXT_EN = "file already exist";
	/**
	 * 文件创建异常
	 */
	public static final String FILE_CREATE = "F0004";
	public static final String FILE_CREATE_TEXT = "文件创建异常";
	public static final String FILE_CREATE_TEXT_EN = "File creation exception";
	/**
	 * 文件超出大小限制异常
	 */
	public static final String FILE_TOO_LARGE = "F0005";
	public static final String FILE_TOO_LARGE_TEXT = "文件超出大小限制";
	public static final String FILE_TOO_LARGE_TEXT_EN = "File size exceeded";
	/**
	 * IO异常
	 */
	public static final String IO_EXCEPTION = "F0006";
	public static final String IO_EXCEPTION_TEXT = "IO异常";
	public static final String IO_EXCEPTION_TEXT_EN = "IO anomaly";

	/**
	 * 日期参数格式异常
	 */
	public static final String PARAM_DATE_EXCEPTION = "F1001";
	public static final String PARAM_DATE_EXCEPTION_TEXT = "日期格式错误";
	/**
	 * 整形参数格式异常
	 */
	public static final String PARAM_INTEGER_EXCEPTION = "F1002";
	public static final String PARAM_INTEGER_EXCEPTION_TEXT = "数字格式错误";
	/**
	 * 参数缺失异常
	 */
	public static final String PARAM_LOSS_EXCEPTION = "F1003";
	public static final String PARAM_LOSS_EXCEPTION_TEXT = "数据信息不全,请重新尝试";
	public static final String PARAM_LOSS_EXCEPTION_TEXT_EN = "Incomplete data, please try again.";
	public static final String PARAM_LOSS_EXCEPTION_TEXT_AR = "بيانات ناقصة ، يرجى المحاولة مرة أخرى";
	public static final String PARAM_LOSS_EXCEPTION_TEXT_JA = "データ情報が不完全です。もう一度試してみてください";
	public static final String PARAM_LOSS_EXCEPTION_TEXT_FR = "Données incomplètes. Essayez encore";
	public static final String PARAM_LOSS_EXCEPTION_TEXT_ES = "Datos incompletos.Por favor,intente de nuevo";
	/**
	 * 参数格式
	 */
	public static final String PARAM_FORM_EXCEPTION = "F1004";
	public static final String PARAM_FORM_EXCEPTION_TEXT = "参数格式异常";
	public static final String PARAM_FORM_EXCEPTION_TEXT_EN = "Parameter format exception";
	/**
	 * 密码长度不够
	 */
	public static final String PASSWORD_TOO_SHORT = "F1005";
	public static final String PASSWORD_TOO_SHORT_TEXT = "密码长度必须6位至20位之间且必须有数字和字母、字符";
	public static final String PASSWORD_TOO_SHORT_TEXT_EN = "Password length must be between 6 and 20 bits, and must have numbers and letters and characters.";
	public static final String PASSWORD_TOO_SHORT_TEXT_AR = "الحروف والحروف طول كلمة المرور يجب أن يكون بين ستة بت إلى 20 -bit ويجب أن يكون عدد ";
	public static final String PASSWORD_TOO_SHORT_TEXT_JA = "パスワードの長さは6桁から20桁の間でなければなりません。数字とアルファベット、文字が必要です";
	public static final String PASSWORD_TOO_SHORT_TEXT_FR = "La longueur du mot de passe doit être comprise entre 6 et 20 bits et doit contenir des nombres, des lettres, des caractères";
	public static final String PASSWORD_TOO_SHORT_TEXT_ES = "La longitud de la contraseña debe estar entre 6 y 20 bits y debe tener números y letras y caracteres";
	/**
	 * 功能暂未开通
	 */
	public static final String FUNCTION_NOT_OK = "F1006";
	public static final String FUNCTION_NOT_OK_TEXT = "此功能暂未开通";

	/**
	 * 提现金额有误
	 */
	public static final String CASH_AMOUNT_ERROR = "F1008";
	public static final String CASH_AMOUNT_ERROR_TEXT = "提现金额有误。";

	/**
	 * 未绑定银行卡
	 */
	public static final String BANK_NOT_BIND = "F1008";
	public static final String BANK_NOT_BIND_TEXT = "您尚未绑定银行卡";

	/**
	 * 银行卡格式错误
	 */
	public static final String BANK_ERR = "F1018";
	public static final String BANK_ERR_TEXT = "银行卡格式错误";

	 /**
	  *  操作权限不足
	  */
	public static final String PERMISSION_DENIED = "F2001";
	public static final String PERMISSION_DENIED_TEXT = "操作权限不足";

	/**
	 * session已过期
	 */
	public static final String SESSEION_TIMEOUT = "F2002";
	public static final String SESSEION_TIMEOUT_TEXT = "请重新登陆！";
	public static final String SESSEION_TIMEOUT_TEXT_EN = "Please re login.";


	/**
	 * token失效
	 */
	public static final String TOKEN_ERROR = "F2003";
	public static final String TOKEN_ERROR_TEXT = "tooken失效";
	public static final String TOKEN_ERROR_TEXT_EN = "Tooken failure";

	/**
	 * 包含非法字符
	 */
	public static final String UNSAFE_CHAR = "F2004";
	public static final String UNSAFE_CHAR_TEXT = "包含非法字符：";
	public static final String UNSAFE_CHAR_TEXT_EN = "Contains illegal characters";
	/**
	 * 验证码不匹配
	 */
	public static final String VC_UNPATCH = "F2005";
	public static final String VC_UNPATCH_TEXT = "验证码错误";
	public static final String VC_UNPATCH_TEXT_EN = "Verification code error";
	/**
	 * 用户以存在
	 */
	public static final String USER_EXIST = "F2006";
	public static final String USER_EXIST_TEXT = "用户已注册";
	public static final String USER_EXIST_TEXT_EN = "Users have registered";
	public static final String USER_EXIST_TEXT_AR = "تسجيل المستخدم";
	public static final String USER_EXIST_TEXT_JA = "ユーザが登録されました";
	public static final String USER_EXIST_TEXT_FR = "Utilisateur enregistré";
	public static final String USER_EXIST_TEXT_ES = "Registro de usuarios";
	/**
	 * 用户不存在
	 */
	public static final String USER_NOT_EXIST = "F2007";
	public static final String USER_NOT_EXIST_TEXT = "用户不存在";
	public static final String USER_NOT_EXIST_TEXT_EN = "user does not exist";
	public static final String USER_NOT_EXIST_TEXT_AR = "المستخدم غير موجود";
	public static final String USER_NOT_EXIST_TEXT_JA = "ユーザが存在しません";
	public static final String USER_NOT_EXIST_TEXT_FR = "L 'utilisateur n' existe pas";
	public static final String USER_NOT_EXIST_TEXT_ES = "Usuario no existente";
	/**
	 * 登录密码错误
	 */
	public static final String PASSWD_ERROR = "F2008";
	public static final String PASSWD_ERROR_TEXT = "密码错误";
	public static final String PASSWD_ERROR_TEXT_EN = "Password error";
	public static final String PASSWD_ERROR_TEXT_AR = " كلمة السر خطأ";
	public static final String PASSWD_ERROR_TEXT_JA = "パスワードエラー";
	public static final String PASSWD_ERROR_TEXT_FR = "Erreur de mot de passe";
	public static final String PASSWD_ERROR_TEXT_ES = "Contraseña incorrecta";

	/**
	 * 收货地址未配置
	 */
	public static final String ADDRESS_NOT_EXIT = "F2010";
	public static final String ADDRESS_NOT_EXIT_TEXT = "您还没有设置收货地址";
	public static final String ADDRESS_NOT_EXIT_TEXT_EN = "You have not set up the receipt address yet.";

	/**
	 * 购物车为空
	 */
	public static final String CART_IS_NULL = "F2011";
	public static final String CART_IS_NULL_TEXT = "您的购物车还没有商品";
	public static final String CART_IS_NULL_TEXT_EN = "Your shopping cart is not yet available.";

	/**
	 * 余额不足
	 */
	public static final String BALANCE_LOW = "F2012";
	public static final String BALANCE_LOW_TEXT = "抱歉，余额不足";
	public static final String BALANCE_LOW_TEXT_EN = "Sorry, the balance is insufficient.";
	/**
	 * 不支持的操作
	 */
	public static final String UNSUPPORT_HANDLE = "F2013";
	public static final String UNSUPPORT_HANDLE_TEXT = "不支持的操作";
	public static final String UNSUPPORT_HANDLE_TEXT_EN = "Unsupported operation";


	public static final String UNIT_DATA_EXCEPTION = "F2014";
	public static final String UNIT_DATA_EXCEPTION_TEXT = "主要数据缺失";
	public static final String UNIT_DATA_EXCEPTION_TEXT_EN = "Main data missing";


	/**
	 * 数据库数据异常
	 */
	public static final String DATA_ACCESS_EXCEPTION = "F3001";
	public static final String DATA_ACCESS_EXCEPTION_TEXT = "数据异常";
	public static final String DATA_ACCESS_EXCEPTION_TEXT_EN = "Data anomaly";
	/**
	 * 数据库操作时未知异常
	 */
	public static final String DB_EXCEPTION = "F3002";
	public static final String DB_EXCEPTION_TEXT = "数据库操作未知异常";
	public static final String DB_EXCEPTION_TEXT_EN = "Database operation unknown exception";
	public static final String DB_EXCEPTION_TEXT_AR = "تشغيل قاعدة البيانات غير معروف استثناء";
	public static final String DB_EXCEPTION_TEXT_JA = "データベース操作不明異常";
	public static final String DB_EXCEPTION_TEXT_FR = "Database Operation anomalie";
	public static final String DB_EXCEPTION_TEXT_ES = "Operación de base de datos";

	/**
	 * 新增异常
	 */
	public static final String INSERT_ERRO = "F3003";
	public static final String INSERT_ERRO_TEXT = "新增异常！";
	public static final String INSERT_ERRO_TEXT_EN = "New exception";
	public static final String INSERT_ERRO_TEXT_AR = " إضافة استثناء";
	public static final String INSERT_ERRO_TEXT_JA = "異常を追加";
	public static final String INSERT_ERRO_TEXT_FR = "Anomalie supplémentaire";
	public static final String INSERT_ERRO_TEXT_ES = "Añadir una anomalía";
	/**
	 * 修改异常
	 */
	public static final String UPDATE_ERRO = "F3004";
	public static final String UPDATE_ERRO_TEXT = "修改异常！";
	public static final String UPDATE_ERRO_TEXT_EN = "Abnormity";
	public static final String UPDATE_ERRO_TEXT_AR = "";
	public static final String UPDATE_ERRO_TEXT_JA = "";
	public static final String UPDATE_ERRO_TEXT_FR = "";
	public static final String UPDATE_ERRO_TEXT_ES = "";
	/**
	 * 删除异常
	 */
	public static final String DELETE_ERRO = "F3005";
	public static final String DELETE_ERRO_TEXT = "删除异常！";
	public static final String DELETE_ERRO_TEXT_EN = "Delete exception";
	public static final String DELETE_ERRO_TEXT_AR = "حذف استثناء";
	public static final String DELETE_ERRO_TEXT_JA = "異常を削除";
	public static final String DELETE_ERRO_TEXT_FR = "Supprimer l 'anomalie";
	public static final String DELETE_ERRO_TEXT_ES = "Eliminar anomalía";
	/**
	 * 已调试电站无法删除
	 */
	public static final String DELETE_NO_ERRO = "F3007";
	public static final String DELETE_NO_ERRO_TEXT = "已调试电站无法删除,请联系管理员";
	public static final String DELETE_NO_ERRO_TEXT_EN = "Debugged power station cannot be deleted";
	public static final String DELETE_NO_ERRO_TEXT_AR = "لا يمكن حذف محطة التصحيح";
	public static final String DELETE_NO_ERRO_TEXT_JA = "デバッグした発電所は削除できませんでした";
	public static final String DELETE_NO_ERRO_TEXT_FR = "La centrale ne peut pas être supprimée";
	public static final String DELETE_NO_ERRO_TEXT_ES = "La central no puede ser eliminada";
	/**
	 * 无权删除电站无法删除
	 */
	public static final String DELETE_PERMISSION_ERRO = "F3006";
	public static final String DELETE_PERMISSION_ERRO_TEXT = "无权删除电站";
	public static final String DELETE_PERMISSION_ERRO_TEXT_EN = "No right to delete station";
	public static final String DELETE_PERMISSION_ERRO_TEXT_AR = "لا يحق لك حذف محطة توليد الكهرباء";
	public static final String DELETE_PERMISSION_ERRO_TEXT_JA = "発電所を削除する権限がない";//日本語
	public static final String DELETE_PERMISSION_ERRO_TEXT_FR = "N'a pas le droit de supprimer l'usine";//法语
	public static final String DELETE_PERMISSION_ERRO_TEXT_ES = "No está autorizado a eliminar la central eléctrica";//西班牙

	/**
	 * 读取excel异常
	 */
	public static final String EXCEL_ERRO = "F4001";
	public static final String EXCEL_ERRO_TEXT = "excel数据格式错误！错误内容：";
	public static final String EXCEL_ERRO_TEXT_EN = "Excel data format error! Erroneous content";
	/**
	 * 读取excel异常
	 */
	public static final String READE_EXCEL = "F4002";
	public static final String READE_EXCEL_TEXT = "读取excel失败！";
	public static final String READE_EXCEL_TEXT_EN = "Failed to read Excel";
	/**
	 * 备份数据
	 */
	public static final String BACKUP_ERRO = "F4002";
	public static final String BACKUP_ERRO_TEXT = "备份数据异常！";
	public static final String BACKUP_ERRO_TEXT_EN = "Backup data exception";
	/**
	 * 旧密码错误
	 */
	public static final String PWD_ERRO = "F4003";
	public static final String PWD_ERRO_TEXT = "旧密码不正确！";
	public static final String PWD_ERRO_TEXT_EN = "Old password is incorrect.";

	/* APP 异常 */
	/**
	 * 时间戳检验失败
	 */
	public static final String TIMESTAMP_ERROR = "F5001";
	public static final String TIMESTAMP_ERROR_TEXT = "时间戳校验失败";
	public static final String TIMESTAMP_ERROR_TEXT_EN = "Timestamp check failed";

	/**
	 * 手机号格式不正确
	 */
	public static final String PHONE_ERROR = "F7001";
	public static final String PHONE_ERROR_TEXT = "手机号格式不正确";
	public static final String PHONE_ERROR_TEXT_EN = "The phone number is not correct.";

	/**
	 * 身份证格式不正确
	 */
	public static final String IDCARD_ERROR = "F7002";
	public static final String IDCARD_ERROR_TEXT = "身份证格式不正确";
	public static final String IDCARD_ERROR_TEXT_EN = "Incorrect identity card format.";
	/**
	 * 用户名格式不正确
	 */
	public static final String USERNAME_ERROR = "F7003";
	public static final String USERNAME_ERROR_TEXT = "用户名格式不正确";
	public static final String USERNAME_ERROR_TEXT_EN = "Incorrect user name format.";

	/**
	 * 获取银联ＴＮ失败提示
	 */
	public static final String UNIONPAY_TN_ERROR = "F7009";
	public static final String UNIONPAY_TN_ERROR_TEXT = "获取银联交易流水号失败";
	public static final String UNIONPAY_TN_ERROR_TEXT_EN = "Failed to get UnionPay transaction number";

	/**
	 * 存在未完成订单
	 */
	public static final String NOT_FINISH_ORDER_ERROR = "F7010";
	public static final String NOT_FINISH_ORDER_ERROR_TEXT = "当前存在未完成的支付订单,需要优先处理";
	public static final String NOT_FINISH_ORDER_ERROR_TEXT_EN = "There are currently outstanding payment orders that need priority.";

	/**
	 * 此订单非未支付订单
	 */
	public static final String NOT_PAY_ORDER_ERROR = "F7011";
	public static final String NOT_PAY_ORDER_ERROR_TEXT = "此订单非未支付订单,不支持此操作";
	public static final String NOT_PAY_ORDER_ERROR_TEXT_EN = "This order is not unpaid, and this operation is not supported.";


	/**
	 * 提现金额超出余额
	 */
	public static final String CASH_AMOUNT_INSUFFICIENT_ERROR = "F7012";
	public static final String CASH_AMOUNT_INSUFFICIENT_ERROR_TEXT = "提现金额超出现有的余额";
	public static final String CASH_AMOUNT_INSUFFICIENT_ERROR_TEXT_EN = "The cash withdrawal amount exceeds the current balance.";

	/***
	 * 尚未设置支付密码
	 */
	public static final String NO_PAYPASSWD_ERROR = "F7013";
	public static final String NO_PAYPASS_ERROR_TEXT = "您尚未设置支付密码,请设置支付密码";
	public static final String NO_PAYPASS_ERROR_TEXT_EN = "You have not set the payment password. Please set the payment password.";

	/**
	 * 未进行实名认证
	 */
	public static final String CAMPUS_PROXY_ERROR = "F7014";
	public static final String CAMPUS_PROXY_ERROR_TEXT = "您尚未进行实名认证！";
	public static final String CAMPUS_PROXY_ERROR_TEXT_EN = "You have not yet done real name authentication.";

	/**
	 * 未进行邮箱认证
	 */
	public static final String EMAIL_PROXY_ERROR = "F7015";
	public static final String EMAIL_PROXY_ERROR_TEXT = "您尚未进行邮箱认证！";
	public static final String EMAIL_PROXY_ERROR_TEXT_EN = "You have not yet mailbox authentication.";

	/**
	 * 未进行安全认证
	 */
	public static final String SAFE_PROXY_ERROR = "F7016";
	public static final String SAFE_PROXY_ERROR_TEXT = "您尚未进行安全认证！";
	public static final String SAFE_PROXY_ERROR_TEXT_EN = "You have not yet done security authentication.";
	/**
	 * 序列号已存在
	 */
	public static final String SERIAL_NO_ERROR = "F7017";
	public static final String SERIAL_NO_ERROR_TEXT = "序列号已存在！";
	public static final String SERIAL_NO_ERROR_TEXT_EN = "The serial number already exists.";
	public static final String SERIAL_NO_ERROR_TEXT_AR = "الرقم المسلسل موجود بالفعل";
	public static final String SERIAL_NO_ERROR_TEXT_JA = "シリアル番号は既に存在します";
	public static final String SERIAL_NO_ERROR_TEXT_FR = "Le numéro de série existe";
	public static final String SERIAL_NO_ERROR_TEXT_ES = "El número de serie ya existe.";

	/**
	 * 采集器链接失败 CloudTerminal
	 */
	public static final String CLOUD_TERMINAL_ERROR = "F7018";
	public static final String CLOUD_TERMINAL_ERROR_TEXT = "采集器链接失败！";
	public static final String CLOUD_TERMINAL_ERROR_TEXT_EN = "Collector link failure.";


	/**
	 * 查询天气，城市不存在
	 */
	public static final String WEATHER_ERROR = "F8001";
	public static final String WEATHER_ERROR_TEXT = "未查到城市天气！";
	public static final String WEATHER_ERROR_TEXT_EN = "No city weather was detected.";
	public static final String WEATHER_ERROR_TEXT_AR = " الطقس في لم يتم العثور على المدينة";
	public static final String WEATHER_ERROR_TEXT_JA = "都市の天気はまだ確認されていません";
	public static final String WEATHER_ERROR_TEXT_FR = "Pas de météo urbaine";
	public static final String WEATHER_ERROR_TEXT_ES = "No hay clima urbano";

	public static final String PARAM_LOSS_SYSTEMNAME = "F8002";
	public static final String PARAM_LOSS_SYSTEMNAME_TEXT = "电站名称信息不全,请重新尝试";
	public static final String PARAM_LOSS_SYSTEMNAME_TEXT_EN = "The name of the system is incomplete. Please try again.";
	public static final String PARAM_LOSS_SYSTEMNAME_TEXT_AR = " معلومات ناقصة عن اسم المحطة ، يرجى المحاولة مرة أخرى";
	public static final String PARAM_LOSS_SYSTEMNAME_TEXT_JA = "発電所名の情報が不足していますので、もう一度試してみてください";
	public static final String PARAM_LOSS_SYSTEMNAME_TEXT_FR = "Le nom de la centrale est incomplet. Essayez encore";
	public static final String PARAM_LOSS_SYSTEMNAME_TEXT_ES = "El nombre de la central no estácompleto";

	public static final String PARAM_LOSS_CITYID = "F8003";
	public static final String PARAM_LOSS_CITYID_TEXT = "城市信息不全,请重新尝试";
	public static final String PARAM_LOSS_CITYID_TEXT_EN = "City information is incomplete. Please try again.";
	public static final String PARAM_LOSS_CITYID_TEXT_AR = " مدينة المعلومات ناقصة ، يرجى المحاولة مرة أخرى";
	public static final String PARAM_LOSS_CITYID_TEXT_JA = "都市情報が不完全です。もう一度試してみてください";
	public static final String PARAM_LOSS_CITYID_TEXT_FR = "Les infos urbaines sont incomplètes. Essayez encore";
	public static final String PARAM_LOSS_CITYID_TEXT_ES = "La ciudad estáincompleta.Por favor,intente de nuevo";

	public static final String PARAM_LOSS_COUNTRIESID = "F8004";
	public static final String PARAM_LOSS_COUNTRIESID_TEXT = "国家信息不全,请重新尝试";
	public static final String PARAM_LOSS_COUNTRIESID_TEXT_EN = "National information is incomplete, please try again.";
	public static final String PARAM_LOSS_COUNTRIESID_TEXT_AR = " المعلومات الوطنية ليست كاملة ، يرجى المحاولة مرة أخرى";
	public static final String PARAM_LOSS_COUNTRIESID_TEXT_JA = "国の情報が足りないので、もう一度試してみてください";
	public static final String PARAM_LOSS_COUNTRIESID_TEXT_FR = "L 'information nationale est incomplète. Essayez encore";
	public static final String PARAM_LOSS_COUNTRIESID_TEXT_ES = "La información nacional es incompleta.Por favor,intente de nuevo";

	public static final String PARAM_LOSS_PROVINCE = "F8005";
	public static final String PARAM_LOSS_PROVINCE_TEXT = "省信息不全,请重新尝试";
	public static final String PARAM_LOSS_PROVINCE_TEXT_EN = "Provincial information is incomplete. Please try again.";
	public static final String PARAM_LOSS_PROVINCE_TEXT_AR = "معلومات ناقصة ، يرجى المحاولة مرة أخرى";
	public static final String PARAM_LOSS_PROVINCE_TEXT_JA = "省の情報が不完全です。もう一度試してみてください";
	public static final String PARAM_LOSS_PROVINCE_TEXT_FR = "Essayez encore";
	public static final String PARAM_LOSS_PROVINCE_TEXT_ES = "Por favor,intente de nuevo";

	public static final String PARAM_LOSS_ZIPCODE = "F8006";
	public static final String PARAM_LOSS_ZIPCODE_TEXT = "邮编信息不全,请重新尝试";
	public static final String PARAM_LOSS_ZIPCODE_TEXT_EN = "There's not enough information in the zip code. Please try again.";
	public static final String PARAM_LOSS_ZIPCODE_TEXT_AR = " الرمز البريدي معلومات ناقصة ، يرجى المحاولة مرة أخرى";
	public static final String PARAM_LOSS_ZIPCODE_TEXT_JA = "郵便番号の情報が不完全です。もう一度試してみてください";
	public static final String PARAM_LOSS_ZIPCODE_TEXT_FR = "Le Code Postal est incomplet. Essayez encore";
	public static final String PARAM_LOSS_ZIPCODE_TEXT_ES = "Por favor,intente de nuevo";

	public static final String PARAM_LOSS_STREETNAME = "F8007";
	public static final String PARAM_LOSS_STREETNAME_TEXT = "街道信息不全,请重新尝试";
	public static final String PARAM_LOSS_STREETNAME_TEXT_EN = "Street information is incomplete. Please try again.";
	public static final String PARAM_LOSS_STREETNAME_TEXT_AR = " شارع المعلومات ناقصة ، يرجى المحاولة مرة أخرى";
	public static final String PARAM_LOSS_STREETNAME_TEXT_JA = "街の情報が足りないので、もう一度試してみてください。";
	public static final String PARAM_LOSS_STREETNAME_TEXT_FR = "Il n 'y a pas assez d' infos. Essayez encore.";
	public static final String PARAM_LOSS_STREETNAME_TEXT_ES = "La calle estáincompleta.Por favor,intente de nuevo.";

	public static final String PARAM_LOSS = "F8008";
	public static final String PARAM_LOSS_TEXT = "该产品不存在";
	public static final String PARAM_LOSS_TEXT_EN = "The product does not exist.";
	public static final String PARAM_LOSS_TEXT_AR = "هذا المنتج غير موجود";
	public static final String PARAM_LOSS_TEXT_JA = "この製品は存在しません";
	public static final String PARAM_LOSS_TEXT_FR = "Le produit n 'existe pas";
	public static final String PARAM_LOSS_TEXT_ES = "El producto no existe";

	public static final String SCAN_RESULT = "F8008";
	public static final String SCAN_RESULT_TEXT = "扫描结果为空";
	public static final String SCAN_RESULT_TEXT_EN = "The scan result is empty.";
	public static final String SCAN_RESULT_TEXT_AR = " نتائج المسح الضوئي فارغة";
	public static final String SCAN_RESULT_TEXT_JA = "スキャン結果は空です";
	public static final String SCAN_RESULT_TEXT_FR = "Le scan est vide";
	public static final String SCAN_RESULT_TEXT_ES = "El escáner estávacío";

	public static final String USER_SURNAME = "F8009";
	public static final String USER_SURNAME_TEXT = "姓过长";
	public static final String USER_SURNAME_TEXT_EN = "Surname Too long";
	public static final String USER_SURNAME_TEXT_AR = " لقب طويل جدا";
	public static final String USER_SURNAME_TEXT_JA = "姓が長すぎる";
	public static final String USER_SURNAME_TEXT_FR = "Nom de famille";
	public static final String USER_SURNAME_TEXT_ES = "Apellido excesivo";

	public static final String USER_NAME = "F8010";
	public static final String USER_NAME_TEXT = "名过长";
	public static final String USER_NAME_TEXT_EN = "Name Too long";
	public static final String USER_NAME_TEXT_AR = "اسم طويل جدا";
	public static final String USER_NAME_TEXT_JA = "名前が長すぎる";
	public static final String USER_NAME_TEXT_FR = "Surnom";
	public static final String USER_NAME_TEXT_ES = "Nombre largo";


	public static final String COMPONENT_REPEAT = "F8020";
	public static final String COMPONENT_REPEAT_TEXT = "组件坐标重复";
	public static final String COMPONENT_REPEAT_TEXT_EN = "Component coordinate repetition";
	public static final String COMPONENT_REPEAT_TEXT_AR = "عنصر تنسيق التكرار ";
	public static final String COMPONENT_REPEAT_TEXT_JA = "コンポーネント座標の重複";
	public static final String COMPONENT_REPEAT_TEXT_FR = "Repetition rate";
	public static final String COMPONENT_REPEAT_TEXT_ES = "Coordenadas del componente";

	public static final String INVERTER_REPEAT = "F8021";
	public static final String INVERTER_REPEAT_TEXT = "逆变器编号重复，请重新输入！";
	public static final String INVERTER_REPEAT_TEXT_EN = "The inverter number is duplicated, please re-enter！";
	public static final String INVERTER_REPEAT_TEXT_AR = "العاكس رقم مكررة ، الرجاء إدخال";
	public static final String INVERTER_REPEAT_TEXT_JA = "インバータ番号を繰り返しますので、もう一度入力してください";
	public static final String INVERTER_REPEAT_TEXT_FR = "Répétez le numéro du convertisseur";
	public static final String INVERTER_REPEAT_TEXT_ES = "Número de reversa.Repito,por favor reingrese";

	public static final String INSERT_OWNER_OK = "F8022";
	public static final String INSERT_OWNER_OK_TEXT = "业主添加成功";
	public static final String INSERT_OWNER_OK_TEXT_EN = "Owner added successfully";
	public static final String INSERT_OWNER_OK_TEXT_AR = "المالك إضافة بنجاح";
	public static final String INSERT_OWNER_OK_TEXT_JA = "所有者追加成功";
	public static final String INSERT_OWNER_OK_TEXT_FR = "Ajout réussi par le maître d 'ouvrage";
	public static final String INSERT_OWNER_OK_TEXT_ES = "Añadir propietario";


	public static final String INSERT_OWNER_ERROR = "F8023";
	public static final String INSERT_OWNER_ERROR_TEXT = "业主添加失败";
	public static final String INSERT_OWNER_ERROR_TEXT_EN = "Owner added failed";
	public static final String INSERT_OWNER_ERROR_TEXT_AR = "فشل المالك إضافة";
	public static final String INSERT_OWNER_ERROR_TEXT_JA = "所有者の追加に失敗しました";
	public static final String INSERT_OWNER_ERROR_TEXT_FR = "Erreur d 'ajout du propriétaire";
	public static final String INSERT_OWNER_ERROR_TEXT_ES = "Error al añadir propietario";

	public static final String INVERTER_IDENT_REPEAT = "F8024";
	public static final String INVERTER_IDENT_REPEAT_TEXT = "逆变器标识重复";
	public static final String INVERTER_IDENT_REPEAT_TEXT_EN = "Inverter coordinate repetition";
	public static final String INVERTER_IDENT_REPEAT_TEXT_AR = " العاكس معرف التكرار";
	public static final String INVERTER_IDENT_REPEAT_TEXT_JA = "インバータマークが重複しています";
	public static final String INVERTER_IDENT_REPEAT_TEXT_FR = "Repetition rate";
	public static final String INVERTER_IDENT_REPEAT_TEXT_ES = "Identificador de reversa";

	public static final String GROUP_REPEAT = "F8025";
	public static final String GROUP_REPEAT_TEXT = "组串标识重复";
	public static final String GROUP_REPEAT_TEXT_EN = "Group coordinate repetition";
	public static final String GROUP_REPEAT_TEXT_AR = "سلسلة معرف التكرار";
	public static final String GROUP_REPEAT_TEXT_JA = "文字列の繰り返し";
	public static final String GROUP_REPEAT_TEXT_FR = "Repetition rate";
	public static final String GROUP_REPEAT_TEXT_ES = "Identificador de grupo";

	public static final String RELAY_REPEAT = "F8026";
	public static final String RELAY_REPEAT_TEXT = "中继器标识重复";

	public static final String COLLECTOR_RETURN_ERROR = "C0154";
	public static final String COLLECTOR_RETURN_ERROR_TEXT = "采集器没有连接或返回错误";

}