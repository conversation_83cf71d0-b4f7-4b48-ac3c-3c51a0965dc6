package com.ymx.common.log;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.stream.Collectors;

@Aspect
@Component("loggingAspect")
@EnableAspectJAutoProxy
public class LoggingAspect {

    private static final Logger logger = LoggerFactory.getLogger(LoggingAspect.class);

    private final ObjectMapper objectMapper;

    public LoggingAspect(ObjectMapper objectMapper) {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        this.objectMapper = objectMapper;
    }

    @Pointcut("execution(* com.ymx.agreement.listener.*Controller.*(..)) || " +
            "execution(* com.ymx.manager.controller.*.*Controller.*(..)) || "+
            "execution(* com.ymx.app.controller.*.*Controller.*(..))")
    public void controllerMethod() {
    }

    @Around("controllerMethod()")
    public Object logRequestAndResponse(ProceedingJoinPoint joinPoint) throws Throwable {

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String url = request.getRequestURL().toString();
        String lastPartUrl=url.substring(url.lastIndexOf('/') + 1);

        String params = Arrays.stream(joinPoint.getArgs())
                .filter(arg -> !(arg instanceof HttpServletRequest || arg instanceof HttpServletResponse))
                .map(arg -> {
                    try {
                        return objectMapper.writeValueAsString(arg);
                    } catch (JsonProcessingException e) {
                        logger.error(e.getMessage(),e);
                        return arg.toString();
                    }
                })
                .collect(Collectors.joining(", "));

        logger.debug("{}, Parameters:{}",lastPartUrl ,params);

        long startTime = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        long endTime = System.currentTimeMillis();

         /// 如果返回的是jsp 就不打印返回值
          if(lastPartUrl.endsWith(".jsp"))
          {
              logger.debug("{} Response: {} Time:{} ms",lastPartUrl, "jsp",endTime-startTime);
              return result;
          }

        String responseStr = "";
        if (result != null) {
            try {
                responseStr = objectMapper.writeValueAsString(result);
            } catch (JsonProcessingException e) {
                logger.error(e.getMessage(),e);
                responseStr = result.toString();
            }
        }
        logger.debug("{} Response: {} Time:{} ms",lastPartUrl, responseStr,endTime-startTime);

        return result;
    }

    @AfterThrowing(pointcut = "execution(* com.ymx.service.*.*.mapper.*.*(..))", throwing = "ex")
    public void handleMyBatisException(JoinPoint joinPoint, Exception ex) {
        // 打印myBatis异常
        logger.error("myBatis exception: " + ex.getMessage());
    }

    @AfterThrowing(pointcut = "execution(* com.ymx.agreement.quartz.*.*.*(..))", throwing = "ex")
    public void handleQuartzException(JoinPoint joinPoint, Exception ex) {
        // 打印quartz异常
        logger.error("quartz exception: " + ex.getMessage());
    }
}
