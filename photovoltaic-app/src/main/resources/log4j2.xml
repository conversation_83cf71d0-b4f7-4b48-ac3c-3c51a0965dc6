<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="debug" name="MyApp" packages="" monitorInterval="300">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} %logger{36} %msg%n"/>
        </Console>
        <Async name="Async">
            <AppenderRef ref="Console"/>
        </Async>
    </Appenders>
    <Loggers>

        <Root level="info">
            <AppenderRef ref="Async" />
        </Root>

        <Logger name="org.springframework" level="info" additivity="false">
            <AppenderRef ref="Async"/>
        </Logger>

        <Logger name="com.ymx.service.photovoltaic" level="debug" additivity="false">
            <AppenderRef ref="Async"/>
        </Logger>

        <Logger name="com.ymx.common.log" level="debug" additivity="false">
            <AppenderRef ref="Async"/>
        </Logger>
    </Loggers>
</Configuration>
