package com.ymx.app.controller.station;

import com.ymx.app.BaseController;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.photovoltaic.station.model.ComponentExceptionModel;
import com.ymx.service.photovoltaic.station.service.ComponentExceptionService;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @DESC
 * @DATE 2018/9/15
 * @NAME ComponentExceiptionAppController
 * @MOUDELNAME 模块
 */
@RestController
@RequestMapping("componentExceAppCtr")
public class ComponentExceiptionAppController extends BaseController {

    @Resource
    private ComponentExceptionService componentExceptionService;

    /**
     * 查询数据
     * @param componentExceptionModel
     * @param pageView
     * @return
     */
    @RequestMapping("queryComponentExceptionList.api")
    public CallResult queryComponentExceptionList(ComponentExceptionModel componentExceptionModel , PageView pageView) {
        return componentExceptionService.queryComponentExceptionList(componentExceptionModel ,pageView);
    }

    /***
     * 查看通信异常详情
     * @param componentExceptionModel
     * @return
     */
    @RequestMapping("queryComponentExceptionObject.api")
    public  CallResult queryComponentExceptionObject(ComponentExceptionModel componentExceptionModel) {
        CallResult callResult = CallResult.newInstance();
        if (!StringUtils.hasLength(componentExceptionModel.getId()) || StringUtils.isEmpty(componentExceptionModel.getId()) ) {
//	        if(componentExceptionModel!=null && "en".equals(componentExceptionModel.getLanguage())){
//		        callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION , ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN);
//	        }else{
//		        callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION , ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
//	        }
	        getCallResult(callResult,componentExceptionModel.getLanguage(),ErrCodeExt.PARAM_LOSS_EXCEPTION);
            return callResult;
        }
        ComponentExceptionModel model = componentExceptionService.queryComponentExceptionById(componentExceptionModel.getId());
        callResult.setReModel(model);
        model.setIsRead(2);
        componentExceptionService.updateComponentException(model);
        return callResult;
    }



}
