package com.ymx.app.controller.station;

import com.ymx.app.BaseController;
import com.ymx.common.base.config.ConfigConstants;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.common.utils.BaiDuUtil;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.IDUtil;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.region.entity.RegionLocationModel;
import com.ymx.service.photovoltaic.region.service.RegionLocationService;
import com.ymx.service.photovoltaic.station.model.PowerStationModel;
import com.ymx.service.photovoltaic.station.service.PowerStationService;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @DESC
 * @DATE 2018/9/6
 * @NAME PowerStationAppController
 * @MOUDELNAME 模块
 */
@RestController
@RequestMapping("powerStationAppCtr")
public class PowerStationAppController extends BaseController {

    /** 电站服务层  */
    @Resource
    private PowerStationService powerStationService;
    @Resource
    private RegionLocationService regionLocationService;
    /***
     * 查询系统设备数据
     * @param model    系统设备
     * @param pageView 分页实体
     * @return
     */
    @RequestMapping("queryPowerStationList.api")
    public CallResult queryPowerStationList( PowerStationModel model , PageView pageView ,HttpServletRequest request) {
	    String memberId = request.getParameter("mId");
	    String userType = request.getParameter("userType");
	    if("2".equals(userType)){//1 普通 2运维人员 3超级管理员账号
		    model.setCreateUserId(memberId);
	    }else{
		    model.setMemberId(memberId);
	    }
	    pageView.setPageSize(1000);//app 不分页
       return powerStationService.queryPowerStationList(model , pageView);
    }

	/**
	 * 删除电站
	 * @param  model
	 * @return CallResult
	 */
	@RequestMapping(value="deletePowerStationModel.api")
	public CallResult deletePowerStationModel(PowerStationModel model,HttpServletRequest request) {
		String memberId = request.getParameter("mId");
		String userType = request.getParameter("userType");
		if("2".equals(userType)){//1 普通 2运维人员 3超级管理员账号
			model.setCreateUserId(memberId);
		}else{
			model.setMemberId(memberId);
		}
		CallResult callResult = CallResult.newInstance();
		PowerStationModel powerStationModel=powerStationService.queryPowerStationObjectById(model.getId());
		if(powerStationModel!=null){
			if(powerStationModel.getCreateUserId().equals(memberId)){
				return powerStationService.deletePowerStation(model);
			}else{
				TranslateLanguage.getCallResult(callResult,model.getLanguage(), ErrCodeExt.DELETE_PERMISSION_ERRO);
				return callResult;
			}
		}
		TranslateLanguage.getCallResult(callResult,model.getLanguage(), ErrCodeExt.PARAM_ERR);
		return callResult;
	}

    public static void main(String[] args) {
	    BaiDuUtil bdu = new BaiDuUtil();
	    try {
		    String code =	bdu.getLongitudeLatitude("江苏省徐州市邳州市");
		    System.out.println("code=="+code);
		    if(CommonUtil.isEmpty(code)==true)
		    {
			    List<String> result = Arrays.asList(code.split(","));
			    String lon = result.get(0);
			    String lat= result.get(1);
			    System.out.println(lon);
			    System.out.println(lat);
		    }
	    } catch (IOException e) {
		    e.printStackTrace();
	    } catch (Exception e) {
		    e.printStackTrace();
	    }
    }
	/***
	 * 修改系统设备数据
	 * @param model    系统设备
	 * @return
	 */
	@RequestMapping("updatePowerStationModel.api")
	public CallResult updatePowerStationModel( PowerStationModel model) {
		if(model.getSunuptime()!=null && !model.getSunuptime().isEmpty()){
			model.setSunuptimech(model.getSunuptime());
		}
		if(model.getSundowntime() !=null && !model.getSundowntime().isEmpty()){
			model.setSundowntimech(model.getSundowntime());
		}
		return powerStationService.updatePowerStationModel(model );
	}


	/**
     * 查询电站详情
     * @param model 系统设备（电站实体）
     * @return
     */
    @RequestMapping("queryPowerStationById.api")
    public CallResult queryPowerStationById(PowerStationModel model ){
        CallResult callResult = CallResult.newInstance();
        if (!StringUtils.hasLength(model.getId()) || StringUtils.isEmpty(model.getId()) ) {
//	        if("en".equals(model.getLanguage())){
//		        callResult.setErr( ErrCodeExt.PARAM_LOSS_EXCEPTION , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_EN );
//	        }else{
//		        callResult.setErr( ErrCodeExt.PARAM_LOSS_EXCEPTION , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT );
//	        }
	        getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_EXCEPTION);
            return callResult;
        }
        if(model.getLanguage()==null){
	        model.setLanguage("ch");
        }
        PowerStationModel powerStationModel =  powerStationService.queryPowerStationObjectById(model.getId(),model.getLanguage());
        callResult.setReModel(powerStationModel);
        return callResult;
    }

	/**
	 * 新增电站
	 * @param model 系统设备（电站实体）
	 * @return
	 */
	@RequestMapping("savePowerStationModel.api")
	public CallResult savePowerStationModel(PowerStationModel model){
		CallResult callResult = CallResult.newInstance();
		if (!StringUtils.hasLength(model.getSystemName()) || StringUtils.isEmpty(model.getSystemName()) ) {
			getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_SYSTEMNAME);
			return callResult;
		}
		if (!StringUtils.hasLength(model.getCityId()) || StringUtils.isEmpty(model.getCityId()) ) {
			getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_CITYID);
			return callResult;
		}
		if (!StringUtils.hasLength(model.getCountriesId()) || StringUtils.isEmpty(model.getCountriesId()) ) {
			getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_COUNTRIESID);
			return callResult;
		}
		if (!StringUtils.hasLength(model.getProvince()) || StringUtils.isEmpty(model.getProvince()) ) {
			getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_PROVINCE);
			return callResult;
		}
		if (!StringUtils.hasLength(model.getStreetName()) || StringUtils.isEmpty(model.getStreetName()) ) {
			getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_STREETNAME);
			return callResult;
		}
		if(model.getSystemNo()==null ||"".equals(model.getSystemNo())){
			model.setSystemNo(IDUtil.getUUIDStr());
		}
		if(model.getSunuptime()!=null && !model.getSunuptime().isEmpty()){
			model.setSunuptimech(model.getSunuptime());
		}
		if(model.getSundowntime() !=null && !model.getSundowntime().isEmpty()){
			model.setSundowntimech(model.getSundowntime());
		}
		model.setCreateType(2);
		model.setCreateUserId(model.getMemberId());

		callResult = powerStationService.savePowerStationModel(model);
		return callResult;
	}




	/***
	 * 查询系统 组件布局数据
	 * @param model    系统设备
	 * @return
	 */
	@RequestMapping("queryPowerStation.api")
	public CallResult queryPowerStation( PowerStationModel model ) {
		return powerStationService.queryPowerStationLayout(model);
	}


	/**
	 * 查询电站级别
	 * @param language
	 * @return
	 */
	@RequestMapping("queryPowerStationGrade.api")
	public CallResult queryPowerStationGrade(String language ,Integer power){
		CallResult callResult = CallResult.newInstance();
		String grade="";
		String serverType= ConfigConstants.getConfig("POWER_STATION_TYPE");
		if(serverType==null && serverType.length()>0 && serverType.indexOf(",")>0){
			serverType="25,100,500,1000";
		}
		String types[]=serverType.split(",");
		if(power!=null){//微型 <25kw   小型 25-100kw   中型  100-500kw  大型 500-1000kw(mw) 特大型  >1kw(mw)
			if(power<Integer.parseInt(types[0])){
				grade="en".equals(language) ? "miniature":"微型";
			}else if(power<Integer.parseInt(types[1])){
				grade="en".equals(language) ? "small-scale":"小型";
			}else if(power<Integer.parseInt(types[2])){
				grade="en".equals(language) ? "Medium-sized":"中型";
			}else if(power<Integer.parseInt(types[3])){
				grade="en".equals(language) ? "large-scale":"大型";
			}else if(power>=Integer.parseInt(types[3])){
				grade="en".equals(language) ? "Oversize":"特大型";
			}
		}
		callResult.setReModel(grade);
		return callResult;
	}



	@RequestMapping("savePowerStationModelTo.api")
	public CallResult savePowerStationModelT(PowerStationModel model){
		CallResult callResult = CallResult.newInstance();
		//系统名称判空
		if (!StringUtils.hasLength(model.getSystemName()) || StringUtils.isEmpty(model.getSystemName()) ) {
			getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_SYSTEMNAME);
			return callResult;
		}
		//街道名称判空  这段注释掉，三晶要非必填
		/*if (!StringUtils.hasLength(model.getStreetName()) || StringUtils.isEmpty(model.getStreetName()) ) {
			getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_STREETNAME);
			return callResult;
		}*/
		//系统编号判空
		if(model.getSystemNo()==null ||"".equals(model.getSystemNo())){
			model.setSystemNo(IDUtil.getUUIDStr());
		}
		//城市名称判空
		if(!StringUtils.hasLength(model.getCityName()) || StringUtils.isEmpty(model.getCityName()) ){
			getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_CITYID_TEXT);
			return callResult;
		}


        //这里按照名称去查地区表的id,然后封装进model
		String cityName = model.getCityName();
		String provinceName = model.getProvinceName();
		String countries = model.getCountries();

		if(cityName!=null && !cityName.isEmpty()){
			Integer cityId=regionLocationService.getIdByName(cityName);
			model.setDistrictId(cityName);
			model.setCityId(String.valueOf(cityId));
		}
		if(provinceName!=null && !provinceName.isEmpty()){
			Integer province = regionLocationService.getIdByName(provinceName);
			model.setProvince(String.valueOf(province));
		}
		if(countries!=null && !countries.isEmpty()){
			Integer countriesId = regionLocationService.getIdByName(countries);
			model.setCountriesId(String.valueOf(countriesId));
		}

		model.setCreateType(2);
		model.setCreateUserId(model.getMemberId());
		//给默认的日出日落时间
		// 设置默认的日出和日落时间
		LocalTime defaultSunUpTime = LocalTime.of(6, 0); // 早上6点
		LocalTime defaultSunDownTime = LocalTime.of(18, 0); // 晚上6点
		model.setSunuptimech(String.valueOf(defaultSunUpTime));
		model.setSundowntimech(String.valueOf(defaultSunDownTime));

		callResult = powerStationService.savePowerStationModelTo(model);
		return callResult;


	}
}
