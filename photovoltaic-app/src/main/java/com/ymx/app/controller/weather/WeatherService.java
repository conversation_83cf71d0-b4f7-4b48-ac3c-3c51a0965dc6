package com.ymx.app.controller.weather;

import com.alibaba.fastjson.JSON;
import com.ymx.app.controller.weather.entity.WeatherNow;
import com.ymx.app.controller.weather.entity.seniverse.SeniverseResult;
import com.ymx.app.controller.weather.entity.seniverse.SeniverseWeatherResponse;
import com.ymx.common.common.result.CallResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class WeatherService {

    @Autowired
    private WeatherConfig weatherConfig;

    @Autowired
    private RestTemplate restTemplate;

    private static final String SENIVERSE_WEATHER_API_URL = "https://api.seniverse.com/v3/weather/now.json";

    /**
     * 获取心知天气信息
     * @param location 位置信息（如：北京）
     * @return 天气信息
     */
    public CallResult getSeniverseWeather(String location) {
        CallResult result = CallResult.newInstance();

        try {
            // 参数校验
            if (location == null || location.trim().isEmpty()) {
                result.setErr("400", "位置参数不能为空");
                return result;
            }

            // 构建请求URL
            String url = String.format("%s?key=%s&location=%s&language=zh-Hans&unit=c",
                    SENIVERSE_WEATHER_API_URL,
                    weatherConfig.getSeniverseKey(),
                    location);
            
            // 发送请求获取天气数据
            String response = restTemplate.getForObject(url, String.class);
            log.info("心知天气查询结果 - location: {}, response: {}", location, response);
            
            // 解析响应到实体类
            SeniverseWeatherResponse weatherResponse = JSON.parseObject(response, SeniverseWeatherResponse.class);
            
            if (weatherResponse.getResults() == null || weatherResponse.getResults().isEmpty()) {
                result.setErr("500", "获取天气信息失败");
                return result;
            }

            // 将心知天气数据转换为统一的WeatherNow格式
            SeniverseResult seniverseResult = weatherResponse.getResults().get(0);
            WeatherNow weatherNow = new WeatherNow();
            weatherNow.setText(seniverseResult.getNow().getText());
            weatherNow.setTemp(Integer.parseInt(seniverseResult.getNow().getTemperature()));
            // 设置其他字段的默认值
            weatherNow.setFeels_like(weatherNow.getTemp()); // 体感温度默认等于实际温度
            weatherNow.setRh(0); // 相对湿度默认0
            weatherNow.setWind_class(""); // 风力为空
            weatherNow.setWind_dir(""); // 风向为空
            weatherNow.setUptime(seniverseResult.getLast_update()); // 使用心知天气的更新时间

            // 设置返回数据
            result.setReModel(weatherNow);
            return result;

        } catch (Exception e) {
            log.error("获取心知天气信息异常 - location: " + location, e);
            result.setErr("500", "获取天气信息失败");
            return result;
        }
    }
} 