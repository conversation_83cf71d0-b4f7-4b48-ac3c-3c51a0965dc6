package com.ymx.app.controller.warn;

import com.ymx.app.BaseController;
import com.ymx.common.base.dao.IUserDao;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.common.utils.DateUtils;
import com.ymx.service.constant.ConstantsExt;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.photovoltaic.station.service.ConfigureService;
import com.ymx.service.photovoltaic.warning.entity.WarningModel;
import com.ymx.service.photovoltaic.warning.service.WarningService;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;


/**
 * @DESC
 * @DATE 2018/9/7
 * @NAME WarningAppController
 * @MOUDELNAME 模块
 */
@RestController
@RequestMapping("warningAppCtr")
public class WarningAppController  extends BaseController {

    @Resource
    private WarningService warningService;
	@Resource
	private ConfigureService configureService;
	@Resource
	private IUserDao userDao;


    /**
     * 警报列表和警报搜索
     * @param warningModel 警报实体
     * @param pageView  分页
     */
    @RequestMapping("queryWarningList.api")
    public CallResult queryWarningList(WarningModel warningModel , PageView pageView,HttpServletRequest request) {
//	    String memberId = request.getParameter("mId");
//    	String userType = request.getParameter("userType");
//	    if("2".equals(userType)){//1 普通 2运维人员 3超级管理员账号
//		    warningModel.setCreateUserId(memberId);
//	    }else{
//		    warningModel.setMemberId(memberId);
//	    }
	    Date date= DateUtils.changeDays(new Date(),-7);//只查询7日数据
	    warningModel.setCreateTimeBegin(DateUtils.timestampToDate(date.getTime()));//DateUtils.DATE_SMALL_STR
	    CallResult callResult=warningService.queryWarningList(warningModel, pageView);
        return callResult;
    }

    /**
     * 警报详情
     * @return
     */
    @RequestMapping("queryWarningById.api")
    public CallResult queryWarningById(WarningModel warningModel ) {
        CallResult callResult = CallResult.newInstance();
        if (!StringUtils.hasLength(warningModel.getId()) || StringUtils.isEmpty(warningModel.getId()) ) {
//	        if("en".equals(warningModel.getLanguage())){
//		        callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION , ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN);
//	        }else{
//		        callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION , ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
//	        }
	        getCallResult(callResult,warningModel.getLanguage(),ErrCodeExt.PARAM_LOSS_EXCEPTION);
            return callResult;
        }
        WarningModel model = warningService.queryWarningById(warningModel.getId(),warningModel.getLanguage());
        callResult.setReModel(model);
        warningModel.setIsRead(2);
        if(model.getWarningType()== ConstantsExt.WARNING_TYPE.TYPE_4
		        || model.getWarningType()== ConstantsExt.WARNING_TYPE.TYPE_5
		        || model.getWarningType()== ConstantsExt.WARNING_TYPE.TYPE_6
		        || model.getWarningType()== ConstantsExt.WARNING_TYPE.TYPE_7
		        || model.getWarningType()== ConstantsExt.WARNING_TYPE.TYPE_11
		        || model.getWarningType()== ConstantsExt.WARNING_TYPE.TYPE_12
		        || model.getWarningType()== ConstantsExt.WARNING_TYPE.TYPE_13
		        || model.getWarningType()== ConstantsExt.WARNING_TYPE.TYPE_14){//普通的点击后就算已处理
	        warningModel.setWarningStatus(ConstantsExt.WARN_STATUS.STATUS_2);
        }
        warningService.updateWarningModel(warningModel);
        return callResult;
    }


    @RequestMapping("v1/queryWarningById.api")
    public CallResult queryWarnDetailsById(WarningModel warningModel ) {
        CallResult callResult = CallResult.newInstance();
        if (!StringUtils.hasLength(warningModel.getId()) || StringUtils.isEmpty(warningModel.getId()) ) {
            getCallResult(callResult,warningModel.getLanguage(),ErrCodeExt.PARAM_LOSS_EXCEPTION);
            return callResult;
        }
        WarningModel model = warningService.queryWarnDetailsById(warningModel.getId(),warningModel.getLanguage());
        callResult.setReModel(model);
        warningModel.setIsRead(2);
        if(Objects.equals(model.getWarningType(), ConstantsExt.WARNING_TYPE.TYPE_4)
                || Objects.equals(model.getWarningType(), ConstantsExt.WARNING_TYPE.TYPE_5)
                || Objects.equals(model.getWarningType(), ConstantsExt.WARNING_TYPE.TYPE_6)
                || Objects.equals(model.getWarningType(), ConstantsExt.WARNING_TYPE.TYPE_7)
                || Objects.equals(model.getWarningType(), ConstantsExt.WARNING_TYPE.TYPE_11)
                || Objects.equals(model.getWarningType(), ConstantsExt.WARNING_TYPE.TYPE_12)
                || Objects.equals(model.getWarningType(), ConstantsExt.WARNING_TYPE.TYPE_13)
                || Objects.equals(model.getWarningType(), ConstantsExt.WARNING_TYPE.TYPE_14)){//普通的点击后就算已处理
            warningModel.setWarningStatus(ConstantsExt.WARN_STATUS.STATUS_2);
        }
        warningService.updateWarningModel(warningModel);
        return callResult;
    }


    /**
     * 查询是否存在未读警报
     * @return
     */
    @RequestMapping("queryWarningCountsIsRead.api")
    public CallResult queryWarningCountsIsRead(WarningModel warningModel){
	    Date date= DateUtils.changeDays(new Date(),-7);//只查询7日数据
	    warningModel.setCreateTimeBegin(DateUtils.timestampToDate(date.getTime()));//DateUtils.DATE_SMALL_STR
        return warningService.queryWarningCountsIsRead(warningModel);
    }


    /**
     * 处理操作
     * @param model 警告实体
     * @return
     */
    @RequestMapping(value = "batchUpdateWarning.api")
    public CallResult batchUpdateWarning(WarningModel model) {
        Map<String ,Object> map = new HashMap<>();
        CallResult callResult = warningService.batchUpdateWarning(map , model);
        return callResult;
    }
	/**
	 * 删除
	 * @param model 警告实体
	 * @return
	 */
	@RequestMapping(value = "updateDeleteWarnings.api")
	public CallResult updateDeleteWarnings(WarningModel model) {
		CallResult callResult = warningService.updateDeleteWarnings(model);
		return callResult;
	}

    /**
     * 查询当日各类型告警数量统计
     * @param powerIdList 电站ID列表
     * @return 统计结果
     */
    @RequestMapping("queryWarningTypeCount.api")
    public CallResult queryWarningTypeCount(@RequestParam List<String> powerIdList) {
        if (powerIdList == null || powerIdList.isEmpty()) {
            CallResult result = CallResult.newInstance();
            result.setErr("400", "电站ID列表不能为空");
            return result;
        }
        return warningService.queryWarningTypeCount(powerIdList);
    }

    /**
     * 查询当日告警详细信息
     * @param powerIdList 电站ID列表
     * @return 告警详情列表
     */
    @RequestMapping("queryTodayWarningDetails.api")
    public CallResult queryTodayWarningDetails(@RequestParam List<String> powerIdList) {
        if (powerIdList == null || powerIdList.isEmpty()) {
            CallResult result = CallResult.newInstance();
            result.setErr("400", "电站ID列表不能为空");
            return result;
        }
        return warningService.queryTodayWarningDetails(powerIdList);
    }

    /***
     * 查询警告数据
     * @param warningModel    警告实体
     * @param pageView 分页实体
     * @return
     */
    @RequestMapping("queryWarningListTo.api")
    public CallResult queryWarningListToAPP(WarningModel warningModel , PageView pageView) {

        if (warningModel.getCreateTimeBegin() == null) {
            Date date= DateUtils.changeDays(new Date(),-5);//只查询5日数据
            warningModel.setCreateTimeBegin(DateUtils.timestampToDate(date.getTime()));//DateUtils.DATE_SMALL_STR
        }
        if (warningModel.getEquipmentId() != null && !warningModel.getEquipmentId().isEmpty()) {
            warningModel.setChipId(warningModel.getEquipmentId());
        }

        return warningService.queryWarningListToAPP(warningModel, pageView);
    }


    /***
     * 查询电站警告数量
     * @param warningModel    警告实体
     * @return
     */
    @GetMapping("queryWarningCount.api")
    @ResponseBody
    public CallResult queryWarningCount(WarningModel warningModel) {
        CallResult callResult = CallResult.newInstance();
        if (warningModel.getPowerStationId() == null || warningModel.getPowerStationId().isEmpty()) {
            callResult.setErrMsg("电站信息不全");
            return callResult;
        }
        Integer count = warningService.queryWarningCount(warningModel);
        callResult.setReModel(count);
        return callResult;
    }

    /**
     * 修改告警消息已读状态
     * @param ids 告警ID列表，多个ID以逗号分隔
     * @return 更新结果
     */
    @RequestMapping("updateWarningReadStatus.api")
    public CallResult updateWarningReadStatus(String ids) {
        if (StringUtils.isEmpty(ids)) {
            CallResult result = CallResult.newInstance();
            result.setErr("400", "告警ID不能为空");
            return result;
        }
        
        // 将逗号分隔的id字符串转换为List
        List<String> idList = new ArrayList<>();
        String[] idArray = ids.split(",");
        for (String id : idArray) {
            if (!StringUtils.isEmpty(id.trim())) {
                idList.add(id.trim());
            }
        }
        
        if (idList.isEmpty()) {
            CallResult result = CallResult.newInstance();
            result.setErr("400", "告警ID不能为空");
            return result;
        }
        
        return warningService.updateWarningReadStatus(idList);
    }

    /**
     * 查询未读告警数量
     * @param powerIdList 电站ID列表
     * @return 未读告警数量
     */
    @RequestMapping("queryWarningUnreadCount.api")
    public CallResult queryWarningUnreadCount(@RequestParam List<String> powerIdList) {
        if (powerIdList == null || powerIdList.isEmpty()) {
            CallResult result = CallResult.newInstance();
            result.setErr("400", "电站ID列表不能为空");
            return result;
        }
        return warningService.queryWarningUnreadCount(powerIdList);
    }

    /**
     * 统计不同告警数量
     * @param userId 用户userId
     * @param day 告警距离现在的天数，默认7天
     * @return 统计结果
     */
    @RequestMapping("queryWarningStatsByMember.api")
    public CallResult queryWarningStatsByMember(@RequestParam String userId, 
                                                @RequestParam(required = false, defaultValue = "7") Integer day) {
        if (StringUtils.isEmpty(userId)) {
            CallResult result = CallResult.newInstance();
            result.setErr("400", "用户userId不能为空");
            return result;
        }
        if (day == null || day <= 0) {
            day = 7; // 默认7天
        }
        
        // 通过userId获取memberId
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", userId);
        String memberId = userDao.queryUserLocal(paramMap);
        if (StringUtils.isEmpty(memberId)) {
            CallResult result = CallResult.newInstance();
            result.setErr("400", "用户不存在");
            return result;
        }
        
        return warningService.queryWarningStatsByMember(memberId, day);
    }

}
