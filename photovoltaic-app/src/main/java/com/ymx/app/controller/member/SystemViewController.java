package com.ymx.app.controller.member;

import com.ymx.service.cache.Configure;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.utils.CommonUtil;
import com.ymx.service.photovoltaic.station.model.*;
import com.ymx.service.photovoltaic.station.service.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
/**
 * @DESC 系统视图
 * @DATE 2018/8/17
 * @NAME SystemViewController
 * @MOUDELNAME 模块
 */
//@Controller
//@RequestMapping("systemDragView")
@RestController
@RequestMapping("systemDragView")
public class SystemViewController {

	@Resource
    private ComponentService componentService;
	@Resource
    private ComponentDayService componentDayService;
	@Resource
    private ChangeService changeService;
	/** 逆变器服务层 */
    @Resource
    private InverterService inverterService;
    /** 云终端服务层 */
    @Resource
    private CloudTerminalService cloudTerminalService;
    /** 组串服务层 */
    @Resource
    private ComponentGroupService componentGroupService;
    /***
     * 已经放置拖动组件
     * @param powerStationId
     * @return
     */
    @RequestMapping("alreadydragView.api")
    public CallResult alreadydragView(String  powerStationId , HttpServletRequest request)
    {
    	 CallResult callResult = CallResult.newInstance();
    	 String ymd=request.getParameter("ymd");
    	 //String powerStationId=powerStationId;
    	// Map<String, Object> map = new HashMap<>();
    	 //日期暂时不传值
    	 List<AllGroupModel> agmlt = new ArrayList<AllGroupModel>();
    	
    	 //获取组件数据日期暂时不传值
    	 // String ymd=request.getParameter("ymd");
    	 String laystatus = "1";
    	 //获取组件数据
    	 List<ComponentModel> lt = componentService.queryComponentListByData(powerStationId, laystatus);
    	 AllGroupModel agm = null;
    	 for(int p=0;p<lt.size();p++)
    	 {
    		 agm = new AllGroupModel();
    		 ComponentModel cm = lt.get(p);
    		 agm.setId(cm.getId());
    		 agm.setPowerId(cm.getPowerStationId());
    		 agm.setGapleft(cm.getXz());
    		 agm.setGaptop(cm.getYz());
    		 agm.setHv(cm.getPosition());
    		 agm.setAllname("组件");
    		 agm.setChipid(cm.getChipId());
    		 agm.setSerialNo(cm.getSerialNo());
    		 agm.setGroupId(cm.getBelongsGroupId());
    		// agm.setGroupName(cm.getBelongsGroupName());
    		/* agm.setComponentTemperature(cm.getComponentTemperature());
    		 agm.setOutputCurrent(cm.getOutputCurrent());
    		 agm.setOutputVoltage(cm.getOutputVoltage());
    		 agm.setInputCurrent(cm.getInputCurrent());
    		 agm.setInputVoltage(cm.getInputVoltage());*/
    		 agm.setLaystatus(cm.getLaystatus());
    		 agm.setGenre("3");
    		 agmlt.add(agm);
    	 }
    	 Map<String, Object> map = new HashMap<String, Object>();
 		 map.put("powerStationId",powerStationId);
 		 map.put("laystatus","1");
    	 //获取 逆变器数据
    	 List<InverterModel> Imlt = inverterService.queryInverterListByData(map);
    	 for(int p=0;p<Imlt.size();p++)
    	 {
    		 agm = new AllGroupModel();
    		 InverterModel cm = Imlt.get(p);
    		 agm.setId(cm.getId());
    		 agm.setPowerId(cm.getPowerStationId());
    		 agm.setGapleft(cm.getXz());
    		 agm.setGaptop(cm.getYz());
    		 agm.setHv(cm.getPosition());
    		 agm.setAllname(cm.getInverterName());
    		 agm.setLaystatus(cm.getLaystatus());
    		 agm.setGenre("2");
    		 agm.setSerialNo(cm.getSerialNo());
    		 agmlt.add(agm);
    	 }
    	 
    	 //获取云终端数据
    	 List<CloudTerminalModel> ctlt = cloudTerminalService.queryCloudListByData(map);
    	 for(int p=0;p<ctlt.size();p++)
    	 {
    		 agm = new AllGroupModel();
    		 CloudTerminalModel cm = ctlt.get(p);
    		 agm.setId(cm.getId());
    		 agm.setPowerId(cm.getPowerStationId());
    		 agm.setGapleft(cm.getXz());
    		 agm.setGaptop(cm.getYz());
    		 agm.setHv(cm.getPosition());	
    		 agm.setAllname(cm.getCloudName());
    		 agm.setLaystatus(cm.getLaystatus());
    		 agm.setGenre("1");
    		 agm.setSerialNo(cm.getSerialNo());
    		 agmlt.add(agm);
    	 }
    	 Map<String, Object> map2 = new HashMap<String, Object>();
		// map2.put("cloudTerminalList",ctlt);
		// map2.put("inverterList",Imlt);
		// map2.put("componentList",lt);
    	 map2.put("data" , agmlt);
		 callResult.setReModel(map2);
         return callResult;
    }
    //未放置 
    @RequestMapping("unfinisheddragView.api")
    public CallResult unfinisheddragView(String  powerStationId , HttpServletRequest request)
    {
    	 CallResult callResult = CallResult.newInstance();
    	 String ymd=request.getParameter("ymd");
    	// String powerStationId=model.getId();
    	// Map<String, Object> map = new HashMap<>();
    	 //日期暂时不传值
    	 List<AllGroupModel> agmlt = new ArrayList<AllGroupModel>();
    	 AllGroupModel agm = null;
    	 String laystatus = "0";
    	 //获取组件数据
    	 List<ComponentModel> lt = componentService.queryComponentListByData(powerStationId, laystatus);
    	 for(int p=0;p<lt.size();p++)
    	 {
    		 agm = new AllGroupModel();
    		 ComponentModel cm = lt.get(p);
    		 agm.setId(cm.getId());
    		 agm.setPowerId(cm.getPowerStationId());
    		 agm.setGapleft(cm.getXz());
    		 agm.setGaptop(cm.getYz());
    		 agm.setHv(cm.getPosition());
    		 agm.setAllname("组件");
    		 agm.setChipid(cm.getChipId());
    		/* agm.setComponentTemperature(cm.getComponentTemperature());
    		 agm.setOutputCurrent(cm.getOutputCurrent());
    		 agm.setOutputVoltage(cm.getOutputVoltage());
    		 agm.setInputCurrent(cm.getInputCurrent());
    		 agm.setInputVoltage(cm.getInputVoltage());*/
    		 agm.setLaystatus(cm.getLaystatus());
    		 agm.setSerialNo(cm.getSerialNo());
    		 agmlt.add(agm);
    	 }
    	 Map<String, Object> map = new HashMap<String, Object>();
 		 map.put("powerStationId",powerStationId);
 		 map.put("laystatus","0");
    	 //获取 逆变器数据
    	 List<InverterModel> Imlt = inverterService.queryInverterListByData(map);
    	 for(int p=0;p<Imlt.size();p++)
    	 {
    		 agm = new AllGroupModel();
    		 InverterModel cm = Imlt.get(p);
    		 agm.setId(cm.getId());
    		 agm.setPowerId(cm.getPowerStationId());
    		 agm.setGapleft(cm.getXz());
    		 agm.setGaptop(cm.getYz());
    		 agm.setHv(cm.getPosition());
    		 agm.setAllname(cm.getInverterName());
    		 agm.setLaystatus(cm.getLaystatus());
    		 agm.setSerialNo(cm.getSerialNo());
    		 agmlt.add(agm);
    	 }
    	 
    	 //获取云终端数据
    	 List<CloudTerminalModel> ctlt = cloudTerminalService.queryCloudListByData(map);
    	 for(int p=0;p<ctlt.size();p++)
    	 {
    		 agm = new AllGroupModel();
    		 CloudTerminalModel cm = ctlt.get(p);
    		 agm.setId(cm.getId());
    		 agm.setPowerId(cm.getPowerStationId());
    		 agm.setGapleft(cm.getXz());
    		 agm.setGaptop(cm.getYz());
    		 agm.setHv(cm.getPosition());	
    		 agm.setAllname(cm.getCloudName());
    		 agm.setLaystatus(cm.getLaystatus());
    		 agm.setSerialNo(cm.getSerialNo());
    		 agmlt.add(agm);
    	 }
    	// map.put("cloudTerminalList",ctlt);
	    // map.put("inverterList",Imlt);
		// map.put("componentList",lt);
    	 Map<String, Object> map2 = new HashMap<String, Object>();
    	 map2.put("data" , agmlt);
		 callResult.setReModel(map2);
         return callResult;
    }
    //所有放置数据 
    @RequestMapping("alldragView.api")
    public CallResult alldragView(String  powerStationId , HttpServletRequest request)
    {
    	 CallResult callResult = CallResult.newInstance();
    	 String ymd=request.getParameter("ymd");
    	// Map<String, Object> map = new HashMap<>();
    	 //日期暂时不传值
    	 List<AllGroupModel> agmlt = new ArrayList<AllGroupModel>();
    	
    	 String laystatus = "";
    	 //获取组件数据
    	 List<ComponentModel> lt = componentService.queryComponentListByData(powerStationId, laystatus);
    	
    	 Map<String, Object> map = new HashMap<String, Object>();
 		 map.put("powerStationId",powerStationId);
 		 map.put("laystatus","");
    	 //获取 逆变器数据
    	 List<InverterModel> Imlt = inverterService.queryInverterListByData(map);
    	 
    	 
    	 //获取云终端数据
    	 List<CloudTerminalModel> ctlt = cloudTerminalService.queryCloudListByData(map);
    	
    	 map.put("cloudTerminalList",ctlt);
	     map.put("inverterList",Imlt);
		 map.put("componentList",lt);
		 callResult.setReModel(map);
         return callResult;
    }
    @RequestMapping(value="/updatedraginfo.api")
    @ResponseBody
    public CallResult updatedraginfo(String powerid , String subclass , String genre ,String hv,String divtop,String divleft,String laystatus,String serialNo,String category)
    {   
    	 
    	CallResult callResult = CallResult.newInstance();
	    if(powerid!=null && powerid.length()>0){//删除缓存
		    Map<String, List<ComponentModel>> componentMap= Configure.getComponentMap();
		    componentMap.remove(powerid);
	    }
    	Map<String, Object> map = new HashMap<String, Object>();
        map.put("pid",powerid );
        map.put("zjid",subclass );
        map.put("lx",genre );
        map.put("lb",hv );
        //修改组件组串关联关系 genre/category:  1 云终端 2 逆变器 3 组件 4组串
        if(CommonUtil.isEmpty(serialNo)==true && !subclass.equals(serialNo) && genre.equals("3"))
        {
        	ComponentModel cm = new ComponentModel();
        	cm.setId(subclass);
        	cm.setBelongsGroupId(serialNo);
        	componentService.updateComponentModelById(cm);
        }
        //变换位置
        ChangeModel changeModel =  changeService.queryChange(map);
        ChangeModel cm = null ;
        int i = 0;
        if(changeModel == null)
        {   
        	cm = new ChangeModel();
        	cm.setId(UUID.randomUUID().toString());
        	cm.setPowerId(powerid);
        	cm.setLb(hv);
        	cm.setLx(genre);
        	cm.setXz(divleft);
        	cm.setYz(divtop);
        	cm.setZjid(subclass);
        	cm.setLaystatus(laystatus);
        	i= changeService.saveChange(cm);
        	
        }
        else
        {
        	cm = new ChangeModel();
        	cm.setLb(hv);
        	cm.setLx(genre);
        	cm.setXz(divleft);
        	cm.setYz(divtop);
        	if(divleft!=null){
		        cm.setDivleft(String.valueOf(Integer.parseInt(divleft)*10));
	        }
	        if(divtop!=null){
		        cm.setDivtop(String.valueOf(Integer.parseInt(divtop)*10));
	        }
        	if(genre.equals("1"))
        	{   
        		//根据序列号查询ID
        		 CloudTerminalModel ctm = new CloudTerminalModel();
        		 ctm.setSerialNo(serialNo);
        		 CloudTerminalModel cloudTerminalModel = cloudTerminalService.queryCloudTerminalModelByImei(ctm);
        		 cm.setZjid(cloudTerminalModel.getId());
        	}
        	else if(genre.equals("2"))
        	{ 
        		String lanage = "cn";
        		InverterModel inverterModel = inverterService.queryInverterBySerialNo(serialNo, lanage);
        		cm.setZjid(inverterModel.getId());
        	}
        	else
        	{
        		cm.setZjid(subclass);
        	}
        	
        	cm.setLaystatus(laystatus);
        	cm.setPowerId(powerid);
        	i=changeService.updateChange(cm); 
        }
        if(i==1)
        {
        	callResult.setReModel(1);
        }
        else
        {
        	callResult.setReModel(null);
        }
	    Map<String, List<ComponentModel>> positionComponentMap =Configure.getPositionComponentMap();
	    positionComponentMap.remove(powerid);
	    Configure.setPositionComponentMap(positionComponentMap);
	    Map<String, List<InverterModel>> positionInverterMap =Configure.getPositionInverterMap();
	    Map<String, List<CloudTerminalModel>> positionCloudTerminalMap =Configure.getPositionCloudTerminalMap();
	    positionInverterMap.remove(powerid);
	    positionCloudTerminalMap.remove(powerid);
	    Configure.setPositionInverterMap(positionInverterMap);
	    Configure.setPositionCloudTerminalMap(positionCloudTerminalMap);
        return callResult;
    }
    @RequestMapping(value="/findComponentGroupByPowerId.api")
    @ResponseBody
    public CallResult findComponentGroupByPowerId(String powerid)
    { 
    	List<ComponentGroupModel> ltgroup = componentGroupService.querycomponentGroupByPowerId(powerid);
    	 CallResult callResult = CallResult.newInstance();
    	 Map<String, Object> map = new HashMap<String, Object>();
    	 map.put("data" , ltgroup);
    	 callResult.setReModel(map);
         return callResult;
    }
    //根据组件：chipid 采集器：serialNo  逆变器 ：serialNo 查询详细信息 category： 1 云终端 2 逆变器 3 组件
    @RequestMapping(value="/finddetailedByID.api")
    @ResponseBody
    public CallResult finddetailedByID(String category,String num,String language)
    { 
    	 CloudTerminalModel cloudTerminalModel = null;
    	 InverterModel inverterModel =null ;
    	 ComponentModel componentModel = null;
    	 Map<String, Object> map = new HashMap<String, Object>();
    	 if(category.equals("1"))
    	 {
    		// CloudTerminalModel ctm = new CloudTerminalModel();
    		// ctm.setSerialNo(num);
    		 cloudTerminalModel = cloudTerminalService.queryCloudListByserialNo(num);
    		 map.put("data" , cloudTerminalModel);
    		 map.put("genre", "1");
    	 }
    	 if(category.equals("2"))
    	 {
    		 inverterModel = inverterService.queryInverterBySerialNo(num, language);
    		 map.put("data" , inverterModel);
    		 map.put("genre", "2");
    	 }
    	 if(category.equals("3"))
    	 {
    		 componentModel = componentService.queryComponentByChipId(num, language);
    		 map.put("genre", "3");
    		 map.put("data" , componentModel);
    	 }
    	
    	 CallResult callResult = CallResult.newInstance();
    	 callResult.setReModel(map);
         return callResult;
    }
}
