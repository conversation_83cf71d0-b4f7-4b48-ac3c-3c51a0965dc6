package com.ymx.app.controller.member;

import com.ymx.app.BaseController;
import com.ymx.common.base.config.ConfigConstants;
import com.ymx.service.cache.Configure;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.ConstantsExt;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.photovoltaic.station.model.ComponentModel;
import com.ymx.service.photovoltaic.station.service.CloudTerminalService;
import com.ymx.service.photovoltaic.station.service.ComponentService;
import com.ymx.service.photovoltaic.station.service.InverterService;
import com.ymx.service.photovoltaic.zte.service.ProtocolDataService;
import com.ymx.service.third.jedis.MyJedis;
import com.ymx.common.utils.CommonUtil;
import net.sf.json.JSONObject;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @DESC
 * @DATE 2018/8/29
 * @NAME ZteController
 * @MOUDELNAME 模块
 */
@RestController
@RequestMapping("zteCtr")
public class ZteController  extends BaseController {

	/** 协议服务层 */
	@Resource
	private ProtocolDataService protocolDataService;

	/** 组件服务层 */
	@Resource
	private ComponentService componentService;
	@Resource
	private InverterService inverterService;
	@Resource
	private CloudTerminalService cloudTerminalService;
	/**
	 * 远端遥控
	 * @param model  单个打开或关闭组件  组件id , flag 状态 1 关闭 2 打开
	 *
	 *               批量打开或关闭组件  powerStationId(电站主键) ,  flag 状态 1 关闭 2 打开
	 *                status 状态 当flag 为 1 时  status值为： 1 打开  ,  当 flag 为 2 时 status 值 2 关闭
	 * @return
	 */
	@RequestMapping("remoteControllerAck.api")
	public CallResult remoteControllerAck(ComponentModel model , String flag  ){
		CallResult callResult = CallResult.newInstance();
		// 检查两个参数是否为空
		if (!StringUtils.hasLength(model.getId()) && StringUtils.isEmpty(model.getPowerStationId())
				&&StringUtils.isEmpty(model.getBelongsGroupId())) {
			getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_EXCEPTION);
			return callResult;
		}
		if (!StringUtils.hasLength(flag ) || StringUtils.isEmpty( flag )) {
			getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_EXCEPTION);
			return callResult;
		}
		String result = null;
		List<BasicNameValuePair> httpParas = new ArrayList<>();// 参数值
		httpParas.add(new BasicNameValuePair("flag" , flag  ));
//		REMOTE_CONTROLLER_URL=http://localhost:8080/photovoltaic-agreement/zteCtr/remoteControllerAck.api
//		REMOTE_CONTROLLERS_URL=http://localhost:8080/photovoltaic-agreement/zteCtr/remoteControllerBatchAck.api
		String url=ConfigConstants.getConfig("REMOTE_CONTROLLER_URL");
		String powerStationId=null;
		// 组件id  单个关闭 打开
		if (StringUtils.hasLength(model.getId()) && !StringUtils.isEmpty(model.getId())) {
			httpParas.add(new BasicNameValuePair("id" , model.getId() ));// 单个打开或关闭组件
			ComponentModel componentModel=componentService.queryComponentById(model.getId(),null);
			powerStationId=componentModel.getPowerStationId();
			result = CommonUtil.doPostHttpClient(url+"zteCtr/remoteControllerAck.api" ,null , httpParas);
		}
		// 关闭整个电站
		httpParas.add(new BasicNameValuePair("status" , model.getStatus() +"" ));
		if (StringUtils.hasLength(model.getPowerStationId()) && !StringUtils.isEmpty(model.getPowerStationId())) {
			if (!StringUtils.hasLength(model.getStatus() + "") || StringUtils.isEmpty( model.getStatus() )) {
				getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_EXCEPTION);
				return callResult;
			}
			powerStationId=model.getPowerStationId();
			httpParas.add(new BasicNameValuePair("status" , model.getStatus() +"" ));
			httpParas.add(new BasicNameValuePair("powerStationId" , model.getPowerStationId() ));
			result = CommonUtil.doPostHttpClient(url+"zteCtr/remotePowerStationAck.api" ,null , httpParas);// 批量打开或关闭组件  REMOTE_CONTROLLERS_URL
		}

		// 关闭一个组串
		if (StringUtils.hasLength(model.getBelongsGroupId()) && !StringUtils.isEmpty(model.getBelongsGroupId())) {
			if (!StringUtils.hasLength(model.getStatus() + "") || StringUtils.isEmpty( model.getStatus() )) {
				getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_EXCEPTION);
				return callResult;
			}

			httpParas.add(new BasicNameValuePair("belongsGroupId" , model.getBelongsGroupId() ));
			result = CommonUtil.doPostHttpClient(url+"zteCtr/remoteControllerGroupAck.api" ,null , httpParas);// 批量打开或关闭组件  REMOTE_CONTROLLERS_URL
		}

		if(result!=null){
			CallResult resultCall = (CallResult) JSONObject.toBean(JSONObject.fromObject(result) , CallResult.class);
			if (!"SUC".equals(resultCall.getRec())){
				getCallResult(callResult,model.getLanguage(),ErrCodeExt.OPEN_COMPONENT_CODE);
			}
			Map<String, List<ComponentModel>> componentMap= Configure.getComponentMap();
			componentMap.remove(model.getPowerStationId());
			Configure.setComponentMap(componentMap);
			MyJedis.newInstance().delObjectString(ConstantsExt.DATA_REDIS+"_ComponentList_"+powerStationId);
		}else {
			getCallResult(callResult,model.getLanguage(),ErrCodeExt.OPEN_COMPONENT_CODE);
		}
		return callResult;
	}

	/**
	 * 扫描二维码
	 * @param code
	 * @return
	 */
	@RequestMapping("getZteAck.api")
	public CallResult getZteAck(String code,String language,PageView pageView,HttpServletRequest request){
		CallResult callResult = CallResult.newInstance();
		String chipId = code;
		String sc = null;
		/*if(language==null || !"en".equals(language)){
			language="ch";
		}
		if(code.contains("_")){
			// 包含
			String[] list = code.split("_");
			chipId = list[0];
			if (list != null && list.length > 1) {
				sc = list[1];
			}
		}*/
		if (CommonUtil.isEmpty(chipId)) {

			ComponentModel model = new ComponentModel();
			model.setChipId(chipId);
			model.setSc(sc);

			CallResult callResultComponentModel = componentService.queryComponentList(model,pageView);
			Map<String, Object> map = new HashMap<>();
			if(callResultComponentModel.getCount()>0){
				map.put("data", callResultComponentModel);
			}else{
				getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS);
				/*InverterModel inverterModel = new InverterModel();
				inverterModel.setChipId(chipId);
				inverterModel.setSc(sc);
				CallResult callResultInverterModel = inverterService.queryInverterList(inverterModel,pageView);
				if(callResultInverterModel.getCount()>0){
					map.put("data", callResultInverterModel);
				}else{
					getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS);
					CloudTerminalModel cloudTerminalModel = new CloudTerminalModel();
					cloudTerminalModel.setChipId(chipId);
					cloudTerminalModel.setSc(sc);
					CallResult callResultCloudTerminalModel = cloudTerminalService.getCloudTerminalList(cloudTerminalModel,pageView);
					if(callResultCloudTerminalModel.getCount()>0) {
						map.put("data", callResultCloudTerminalModel);
					}else{
//						if("en".equals(language)){
//							callResult.setErr(ErrCodeExt.PARAM_LOSS , ErrCodeExt.PARAM_LOSS_TEXT_EN);
//						}else{
//							callResult.setErr(ErrCodeExt.PARAM_LOSS , ErrCodeExt.PARAM_LOSS_TEXT);
//						}
						getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS);
					}
				}*/
			}
			callResult.setReModel(map);
		}else{
//			if("en".equals(language)){
//				callResult.setErr(ErrCodeExt.PARAM_ERR, "The scan result is empty.");
//			}else{
//				callResult.setErr(ErrCodeExt.PARAM_ERR, "扫描结果为空");
//			}
			getCallResult(callResult,language,ErrCodeExt.SCAN_RESULT);
		}
		return callResult;
	}


}
