package com.ymx.app.controller.station;

import com.ymx.app.BaseController;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.photovoltaic.station.model.ComponentGroupModel;
import com.ymx.service.photovoltaic.station.service.ComponentGroupService;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.IDUtil;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @DESC 组串
 * @DATE 2018/8/6
 * @NAME ComponentGroupController
 * @MOUDELNAME 模块
 */
@RestController
@RequestMapping("componentGroup")
public class ComponentGroupAppController extends BaseController {

    @Resource
    private ComponentGroupService componentGroupService;



    /***
     * 查询组串数据
     * @param model    组件
     * @param pageView 分页实体
     * @return
     */
    @RequestMapping("queryComponentGroupList.api")
    public CallResult queryComponentGroupList(ComponentGroupModel model , PageView pageView,HttpServletRequest request){
	    pageView.setPageSize(10000);
	    String memberId = request.getParameter("mId");
	    String userType = request.getParameter("userType");
	    model.setCreateUserId(memberId);
	    if(model.getInverterFlag()!=null && model.getInverterFlag().length()>0){//选择逆变

	    }else{
		    if(model.getCloudId()!=null && model.getCloudId().length()>0){
			    model.setCloudIdFlag("selectComponent");
		    }else{
			    model.setCloudId(model.getCloudIdFlag());
			    model.setCloudIdFlag("addComponent");
		    }
	    }
	    return componentGroupService.queryComponentGroupList(model , pageView);
    }


    /**
     * 页面请求 (新增,编辑,查看)
     * @param model      人员信息
     * @return
     */
    @RequestMapping(value="viewRequest.api")
    public CallResult viewRequest(ComponentGroupModel model ){
	    CallResult callResult = CallResult.newInstance();
	    if (!StringUtils.hasLength(model.getId()) || StringUtils.isEmpty(model.getId()) ) {
//		    if(model!=null && "en".equals(model.getLanguage())){
//			    callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_EN );
//		    }else{
//			    callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT );
//		    }
		    getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_EXCEPTION);
		    return callResult;
	    }
	    ComponentGroupModel componentGroupModel=componentGroupService.queryComponentGroupById(model.getId());
	    callResult.setReModel(componentGroupModel);
	    return callResult;
    }

    /**
     * 新增或修改
     * @param   model      组件
     * @return  CallResult
     */
    @RequestMapping(value="saveOrUpdate.api")
    public CallResult saveOrUpdate( ComponentGroupModel model  ){
        if (CommonUtil.isEmpty(model.getId())) {
            //修改
            return componentGroupService.updateComponentGroupModel(model);
        } else {
	        if(model.getGroupNo()==null || "".equals(model.getGroupNo())){
		        model.setGroupNo(IDUtil.getUUIDStr());
	        }
	        model.setCreateType(2);
            return componentGroupService.saveComponentGroupModel(model);
        }
    }

    /**
     * 删除
     * @param  model  组串
     * @return CallResult
     */
    @RequestMapping(value="deleteComponentGroups.api")
    public CallResult deleteComponentGroups(ComponentGroupModel model) {
        return componentGroupService.deleteComponentGroups(model);
    }

    /**
     * 逻辑删除
     * @param  model  组串
     * @return CallResult
     */
    @RequestMapping(value="updateDeleteComponentGroupModels.api")
    public CallResult updateDeleteComponentGroupModels(ComponentGroupModel model) {
        return componentGroupService.updateDeleteComponentGroupModels(model);
    }


    /**
     * 逆变器中选择组串/逆变器中移除组串
     * @param model  组件
     * @return
     */
    @RequestMapping(value="selectOrMoveComponentGroup.api")
    public CallResult selectOrMoveComponentGroup(ComponentGroupModel model ) {
        return componentGroupService.selectOrMoveComponentGroup(model);
    }

	/**
	 * 采集器选择组串/采集器中移除组串
	 * @param model  组件
	 * @return
	 */
	@RequestMapping(value="selectOrMoveCloudComponentGroup.api")
	public CallResult selectOrMoveCloudComponentGroup(ComponentGroupModel model ) {
		return componentGroupService.selectOrMoveCloudComponentGroup(model);
	}


}
