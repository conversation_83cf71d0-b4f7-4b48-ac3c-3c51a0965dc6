package com.ymx.app.controller.station;

import com.ymx.common.base.dao.IDictionaryDao;
import com.ymx.common.base.entity.DictionaryValue;
import com.ymx.common.common.result.CallResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * <AUTHOR> @version 2018/10/23
 */
@RestController
@RequestMapping("/dic")
public class DictionaryController {
	@Autowired
	private IDictionaryDao dictionaryDao;
	/**
	 * @desc
	 * @param dicId
	 * @return
	 */
	@RequestMapping(value={"/queryDicList.api"})
	public CallResult queryDicList(String dicId,String language) {
		CallResult callResult = CallResult.newInstance();
		List<DictionaryValue> list = dictionaryDao.searchDictionaryValues(dicId);
		if(language!=null && !"ch".equals(language)){
			for(DictionaryValue dictionaryValue:list){
				dictionaryValue.setDicValueLabel(dictionaryValue.getDicValueEn());
			}
		}
		callResult.setReModel(list);
		return callResult;
	}
}
