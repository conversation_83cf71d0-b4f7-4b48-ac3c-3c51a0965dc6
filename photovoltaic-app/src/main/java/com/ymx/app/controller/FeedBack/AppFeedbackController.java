package com.ymx.app.controller.FeedBack;

import com.ymx.common.common.result.CallResult;
import com.ymx.service.photovoltaic.station.model.FeedBackModel;
import com.ymx.service.photovoltaic.station.service.FeedBackService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.UUID;

/**
 * 版本管理控制器
 * <AUTHOR> @version 2018/11/5
 */
@RestController
@RequestMapping("appfeedback")
public class AppFeedbackController {
	@Resource
	private FeedBackService feedBackService;

	/**
	 * 版本
	 * @param information
	 * @param content
	 * @return
	 */
	    @RequestMapping(value="/savefeedback.api")
	    @ResponseBody
	    public CallResult savefeedback(String information,String content)
	    {
	    	CallResult callResult = CallResult.newInstance();
	    	FeedBackModel feedBackModel = new FeedBackModel();
	    	feedBackModel.setId(UUID.randomUUID().toString());
	    	feedBackModel.setContent(content);
	    	feedBackModel.setInformation(information);
	    	int i =feedBackService.saveFeedBack(feedBackModel);
	    	if(i==1)
	        {
	        	callResult.setReModel(1);
	        }
	        else
	        {
	        	callResult.setReModel(null);
	        }
	        return callResult;
 		
	    }
}
