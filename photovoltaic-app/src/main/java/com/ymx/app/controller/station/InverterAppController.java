package com.ymx.app.controller.station;

import com.ymx.app.BaseController;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.photovoltaic.station.model.CloudTerminalModel;
import com.ymx.service.photovoltaic.station.model.InverterCollect;
import com.ymx.service.photovoltaic.station.model.InverterModel;
import com.ymx.service.photovoltaic.station.service.CloudTerminalService;
import com.ymx.service.photovoltaic.station.service.InverterCollectService;
import com.ymx.service.photovoltaic.station.service.InverterService;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.IDUtil;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @DESC 逆变器控制层
 * @DATE 2018/8/1
 * @NAME InverterController
 * @MOUDELNAME 模块
 */
@RestController
@RequestMapping("inverter")
public class InverterAppController extends BaseController {

    /** 逆变器服务层 */
    @Resource
    private InverterService inverterService;
	@Resource
	private CloudTerminalService cloudTerminalService;
	@Resource
	private InverterCollectService inverterCollectService;

    /***
     * 查询逆变器数据
     * @param model    逆变器
     * @param pageView 分页实体
     * @return
     */
    @RequestMapping("queryInverterList.api")
    public CallResult queryInverterList(InverterModel model , PageView pageView){
    	
         List<InverterModel> lt = inverterService.queryListTerminalModelByPowerId(model.getPowerStationId(),model.getLanguage());
    	 CallResult callResult = CallResult.newInstance();
         Map<String, Object> map = new HashMap<String, Object>();
         map.put("data", lt);
         if(lt!=null){
	         callResult.setCount(lt.size());
         }else{
	         callResult.setCount(0);
         }
         callResult.setReModel(map);
        return callResult;
    }

    /**
     * 页面请求 (新增,编辑,查看)
     * @param model      人员信息
     * @return
     */
    @RequestMapping(value="viewRequest.api")
    public CallResult viewRequest(InverterModel model ){
	    CallResult callResult = CallResult.newInstance();
	    if (!StringUtils.hasLength(model.getId()) || StringUtils.isEmpty(model.getId()) ) {
//		    if("en".equals(model.getLanguage())){
//			    callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_EN );
//		    }else{
//			    callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT );
//		    }
		    getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_EXCEPTION);
		    return callResult;
	    }
	    InverterModel inverterModel=inverterService.queryInverterById(model.getId(),model.getLanguage());
	    callResult.setReModel(inverterModel);
	    return callResult;
    }


    /**
     * 新增或修改
     * @param   model      逆变器
     * @return  CallResult
     */
    @RequestMapping(value="saveOrUpdate.api")
    public CallResult saveOrUpdate( InverterModel model  ){
        if (CommonUtil.isEmpty(model.getId())) {
            //修改
            return inverterService.updateInverterModel(model);
        } else {
        	if(model.getInverterNo()==null || "".equals(model.getInverterNo())){
		        model.setInverterNo(IDUtil.getUUIDStr());
	        }
            return inverterService.saveInverterModel(model);
        }
    }


    /**
     * 删除
     * @param  model  逆变器
     * @return CallResult
     */
    @RequestMapping(value="deleteInverterModels.api")
    public CallResult deleteInverterModels(InverterModel model) {
        return inverterService.deleteInverterModels(model);
    }


    /**
     * 逻辑删除
     * @param  model  逆变器
     * @return CallResult
     */
    @RequestMapping(value="updateDeleteInverterModels.api")
    public CallResult updateDeleteInverterModels(InverterModel model) {
        return inverterService.updateDeleteInverterModels(model);
    }

    /**
     * 选择逆变器
     * @param model 逆变器
     * @return
     */
    @RequestMapping(value="selectInverterData.api")
    public CallResult selectInverterData(InverterModel model ){
        return inverterService.selectInverterData(model);
    }
	/**
	 * 关联，取消关联 逆变器  和 采集器
	 * @param  model  逆变器
	 * @return CallResult
	 */
	@RequestMapping(value="selectOrMoveCloudTerminal.api")
	public CallResult selectOrMoveCloudTerminal(CloudTerminalModel model) {
		return cloudTerminalService.updateCloudTerminalByInverterId(model);
	}
	/**
	 *  查询逆变器采集数据列表
	 * @param
	 * @return
	 */
	@RequestMapping(value="queryInverterCollectList.api")
	public CallResult queryInverterCollectList(String inverterId,String date,String language){
		CallResult callResult = CallResult.newInstance();
		if (!StringUtils.hasLength(inverterId) || StringUtils.isEmpty(inverterId) ) {
			getCallResult(callResult,language,ErrCodeExt.PARAM_LOSS_EXCEPTION);
			return callResult;
		}
		String beginTime=date+" 00:00:00";
		String endTime=date+" 23:59:59";
		List<InverterCollect>  list=inverterCollectService.queryInverterCollectList(inverterId,beginTime,endTime);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("data", list);
		if(list!=null){
			callResult.setCount(list.size());
		}else{
			callResult.setCount(0);
		}
		callResult.setReModel(map);
		return callResult;
	}
}
