package com.ymx.app.controller.weather;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.client.RestTemplate;

@Configuration
@PropertySource(value = "classpath:app.properties", encoding = "UTF-8")
public class WeatherConfig {
    
    @Value("${seniverse.weather.key}")
    private String seniverseKey;

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    public String getSeniverseKey() {
        return seniverseKey;
    }
}