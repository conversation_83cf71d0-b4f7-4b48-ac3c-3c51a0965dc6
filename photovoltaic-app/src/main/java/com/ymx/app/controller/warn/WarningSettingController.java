package com.ymx.app.controller.warn;

import com.ymx.app.BaseController;
import com.ymx.common.common.constant.ErrCode;
import com.ymx.common.common.result.CallResult;
import com.ymx.service.photovoltaic.warning.entity.WarningSettingModel;
import com.ymx.service.photovoltaic.warning.service.WarningSettingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 警告设置
 * <AUTHOR> @version 2018/10/26
 */
@RestController
@RequestMapping("warningSetting")
public class WarningSettingController extends BaseController {

	private static final Logger logger = LoggerFactory.getLogger(WarningSettingController.class);

	@Resource
	private WarningSettingService warningSettingService;
	/**
	 * 警报设置列表
	 * @param warningModel
	 * @return
	 */
	@RequestMapping("queryWarningSettingList.api")
	public CallResult queryWarningSettingList(WarningSettingModel warningModel) {
		return warningSettingService.saveAndQueryWarnSetList(warningModel);
	}

	/**
	 * 修改设置
	 * @param warningSettingModel powerStationId
	 * @param warningSettingModel componentTemperature 温度过高
	 * @param warningSettingModel offValue  温度关断
	 * @param warningSettingModel outputCurrent 输出过流值
	 * @param warningSettingModel outputVoltage 输出过压值
	 * @param warningSettingModel inputCurrent 输入过流值
	 * @param warningSettingModel inputVoltage 输入过压值
	 * @return
	 */
	@RequestMapping("updateWarningSettingList.api")
	public CallResult updateWarningSettingList(WarningSettingModel warningSettingModel) {
		if(warningSettingModel==null)
		{   //参数为空
			return  CallResult.newInstance(ErrCode.PARAM_ERR,ErrCode.PARAM_ERR_TEXT_EN);
		}

		return warningSettingService.updateWarningSetting(warningSettingModel);
	}

	/**
	 * 修改设置,对接三晶
	 * @param warningSettingModel powerStationId
	 * @param warningSettingModel componentTemperature 温度过高
	 * @param warningSettingModel offValue  温度关断
	 * @param warningSettingModel outputCurrent 输出过流值
	 * @param warningSettingModel outputVoltage 输出过压值
	 * @param warningSettingModel inputCurrent 输入过流值
	 * @param warningSettingModel inputVoltage 输入过压值
	 * @param imei 采集器编号
	 * @return
	 */
	@RequestMapping("updateWarningSettingListTo.api")
	public CallResult updateWarningSettingListTo(WarningSettingModel warningSettingModel,String imei) {
		if(warningSettingModel==null)
		{   //参数为空
			return  CallResult.newInstance(ErrCode.PARAM_ERR,ErrCode.PARAM_ERR_TEXT_EN);
		}

		return warningSettingService.updateWarningSetting(warningSettingModel);
	}
}
