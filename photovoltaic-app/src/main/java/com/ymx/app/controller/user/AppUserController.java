package com.ymx.app.controller.user;

import com.ymx.common.common.result.CallResult;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.MD5;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.photovoltaic.member.entity.MemberInfoModel;
import com.ymx.service.photovoltaic.member.mapper.MemberMapper;
import com.ymx.service.photovoltaic.sms.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/user")
@Slf4j
public class AppUserController {

    @Autowired
    private SmsService smsService;

    @Resource
    private MemberMapper memberMapper;

    @PostMapping("/resetPassword.api")
    public CallResult resetPassword(String account,
                                    String code,
                                    String password,
                                    String type) {
        CallResult callResult = CallResult.newInstance();

        try {
            // 检查参数是否为空
            if (!CommonUtil.isEmpty(account) || !CommonUtil.isEmpty(code)
                    || !CommonUtil.isEmpty(password)) {
                callResult.setErr("400", ErrCodeExt.PARAM_LOSS_EXCEPTION);
                return callResult;
            }

            // 校验用户是否存在
            MemberInfoModel queryModel = new MemberInfoModel();
            queryModel.setPhone(account);
            MemberInfoModel realData = memberMapper.queryNumberByPhone(queryModel);
            if (null == realData) {
                callResult.setErr("400", ErrCodeExt.USER_NOT_EXIST);
                return callResult;
            }

            String okPhone;

            if (smsService.validatePhoneNumber(account)) {
                okPhone=account;
            }
            else if(smsService.validatePhoneNumber(realData.getContactNumber()))
            {
                okPhone=realData.getContactNumber();
            }
            else
            {
                callResult.setErr("400", ErrCodeExt.PHONE_ERROR_TEXT);
                return callResult;
            }

            // 验证验证码
            if (!smsService.verifyCode(okPhone, type, code)) {
                callResult.setErr("400", ErrCodeExt.VERIFICATION_CODE_ERR);
                return callResult;
            }

            // 重置密码
            MemberInfoModel memberInfo = new MemberInfoModel();
            memberInfo.setId(realData.getId());
            memberInfo.setPasswd(MD5.password(password));
            memberMapper.updateMemberModel(memberInfo);
            
        } catch (Exception e) {
            log.error("重置密码失败 - 账号: " + account, e);
            callResult.setErr("400", ErrCodeExt.SYSTEM_ERR);
            return callResult;
        }
        
        return callResult;
    }
}

