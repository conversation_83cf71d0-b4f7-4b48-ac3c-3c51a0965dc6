package com.ymx.app.controller.station;

import com.ymx.app.BaseController;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.station.model.CloudTerminalModel;
import com.ymx.service.photovoltaic.station.service.CloudTerminalService;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.IDUtil;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @DESC 云终端服务层
 * @DATE 2018/8/1
 * @NAME CloudTerminalController
 * @MOUDELNAME 模块
 */
@RestController
@RequestMapping("cloud")
public class CloudTerminalAppController extends BaseController {


    /** 云终端服务层 */
    @Resource
    private CloudTerminalService cloudTerminalService;


	/***
	 * 查询云终端数据
	 * @param model    云终端实体
	 * @param pageView 分页实体
	 * @return
	 */
	@RequestMapping("queryCloudTerminalInverterList.api")
	public CallResult queryCloudTerminalInverterList(CloudTerminalModel model , PageView pageView)
	{
		CallResult callResult = CallResult.newInstance();
		if(model.getInverterId()==null ||model.getSelectInverter()==null){
			callResult = cloudTerminalService.queryCloudTerminalList(model,pageView);
		}else{
			TranslateLanguage.getCallResult(callResult,model.getLanguage(), ErrCodeExt.PARAM_LOSS_EXCEPTION);
		}


		return callResult;
	}
    /***
     * 查询云终端数据
     * @param model    云终端实体
     * @param pageView 分页实体
     * @return
     */
    @RequestMapping("queryCloudTerminalList.api")
    public CallResult queryCloudTerminalList(CloudTerminalModel model , PageView pageView)
    {
        List<CloudTerminalModel> lt = cloudTerminalService.queryListCloudTerminalModelByPowerId(model.getPowerStationId());
        	 CallResult callResult = CallResult.newInstance();
             Map<String, Object> map = new HashMap<String, Object>();
             map.put("data", lt);
             callResult.setCount(0);
             callResult.setReModel(map);
        	return callResult;
    }


    /**
     * 页面请求 (新增,编辑,查看)
     * @param model      人员信息
     * @return
     */
    @RequestMapping(value="viewCloudTermina.api")
    public CallResult edit(CloudTerminalModel model ){
	    CallResult callResult = CallResult.newInstance();
	    if (!StringUtils.hasLength(model.getId()) || StringUtils.isEmpty(model.getId()) ) {
//		    if(model!=null && "en".equals(model.getLanguage())){
//			    callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_EN );
//		    }else{
//			    callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT );
//		    }
		    getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_EXCEPTION);
		    return callResult;
	    }
	    CloudTerminalModel cloudTerminalModel=cloudTerminalService.queryCloudTerminalModelById(model.getId());
	    callResult.setReModel(cloudTerminalModel);
	    return callResult;
    }

    /**
     * 新增或修改
     * @param   model      云终端实体
     * @return  CallResult
     */
    @RequestMapping(value="saveOrUpdate.api")
    public CallResult saveOrUpdate( CloudTerminalModel model  ){
        if (CommonUtil.isEmpty(model.getId())) {
            //修改
            return cloudTerminalService.updateCloudTerminalModel(model);
        } else {
	        if(model.getCloudNo()==null || "".equals(model.getCloudNo())){
		        model.setCloudNo(IDUtil.getUUIDStr());
	        }
	        model.setCreateType(2);
            return cloudTerminalService.saveCloudTerminalModel(model);
        }
    }


    /**
     * 逻辑删除
     * @param  model  云终端实体
     * @return CallResult
     */
    @RequestMapping(value="updateDeleteClouds.api")
    public CallResult updateDeleteClouds(CloudTerminalModel model) {
        return cloudTerminalService.updateDeleteClouds(model);
    }





}
