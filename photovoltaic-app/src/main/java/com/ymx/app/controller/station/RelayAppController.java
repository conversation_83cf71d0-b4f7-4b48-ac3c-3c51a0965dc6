package com.ymx.app.controller.station;

import com.ymx.app.BaseController;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.photovoltaic.station.model.RelayModel;
import com.ymx.service.photovoltaic.station.service.RelayService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping("relay")
public class RelayAppController extends BaseController {

    @Resource
    private RelayService relayService;

    // 中继列表数据接口
    @RequestMapping("queryRelayList.api")
    public CallResult queryRelayList(RelayModel model, PageView pageView, HttpServletRequest request) {
        Object checkRole = request.getSession().getAttribute("checkrole");
        Object uid = request.getSession().getAttribute("userKey");
        if(checkRole.toString().contains("运维")) {
            model.setCreateUserId(uid.toString());
        }
        return relayService.queryRelayList(model, pageView);
    }

    // 中继列表数据接口(云端)
    @RequestMapping("queryRelayListForCloud.api")
    public CallResult queryRelayListForCloud(String createUserId, String operationFlag,String cloudId, PageView pageView) {
        Map<String, Object> map = new HashMap<>();
        map.put("createUserId", createUserId);
        if(operationFlag.equals("view")) {
            map.put("cloudId", cloudId);
        }
        return relayService.queryRelayListForCloud(map, pageView);
    }

    // 中继列表数据接口(电站)
    @RequestMapping("queryRelayListForPowerStation.api")
    public CallResult queryRelayListForPowerStation(String createUserId, String operationFlag,String powerId, PageView pageView) {
        Map<String, Object> map = new HashMap<>();
        map.put("createUserId", createUserId);
        if(operationFlag.equals("view")) {
            map.put("powerId", powerId);
        }
        return relayService.queryRelayListForPowerStation(map, pageView);
    }

    // 组件数据接口
    @RequestMapping("queryComponentList.api")
    public CallResult queryComponentList(String relayId, String chipId, String groupName,
                                       String operationFlag, PageView pageView, String createUserId) {
        Map<String, Object> map = new HashMap<>();
        map.put("createUserId", createUserId);
        if(operationFlag.equals("view")) {
            map.put("relayId", relayId);
        }
        if(chipId != null && !chipId.equals("")) {
            map.put("chipId", chipId.trim());
        }
        if(groupName != null && !groupName.equals("")) {
            map.put("groupName", groupName.trim());
        }
        return relayService.queryComponentList(map, pageView);
    }

    // 新增或修改中继数据接口
    @RequestMapping(value="saveOrUpdate.api")
    public CallResult saveOrUpdate(RelayModel model) {

        if (model.getId() != null && model.getId() != 0) {
            return relayService.updateRelayModel(model);
        } else {
            return relayService.saveRelayModel(model);
        }
    }

    // 删除中继接口
    @RequestMapping(value="deleteRelay.api")
    public CallResult deleteRelay(String id, HttpServletRequest request) {
        return relayService.deleteRelayById(id);
    }

    // 选择或移除组串
    @RequestMapping(value="changeComponentGroup.api")
    public CallResult changeComponentGroup(String id, String relayId, String createUserId) {
        Map<String, Object> map = new HashMap<>();
        map.put("createUserId", createUserId);
        map.put("relayId", relayId);
        List<String> list = new ArrayList<>();
        Collections.addAll(list, id.split(",", -1));
        map.put("list", list);
        return relayService.changeComponentGroup(map);
    }

    // 组串数据接口
    @RequestMapping("queryComponentGroupList.api")
    public CallResult queryComponentGroupList(String relayId, String groupName,
                                              String operationFlag, String createUserId,PageView pageView) {
        Map<String, Object> map = new HashMap<>();
        map.put("createUserId", createUserId);
        
        if(operationFlag.equals("view")) {
            map.put("relayId", relayId);
        }
        
        if(groupName != null && !groupName.equals("")) {
            map.put("groupName", groupName.trim());
        }

        return relayService.queryComponentGroupList(map, pageView);
    }

    // 组件选择或移除中继器
    @RequestMapping(value="changeComponent.api") 
        public CallResult changeComponent(String id, String relayId, String createUserId) {
        Map<String, Object> map = new HashMap<>();
        map.put("createUserId", createUserId);
        map.put("relayId", relayId);
        
        List<String> list = new ArrayList<>();
        Collections.addAll(list, id.split(",", -1));
        map.put("list", list);

        return relayService.changeComponent(map);
    }

    // 采集器选择或移除中继器
    @RequestMapping(value="selectOrMoveCloud.api")
    public CallResult selectOrMoveCloud(String id, String cloudId, String powerStationId, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        map.put("cloudId", cloudId);
        map.put("powerStationId", powerStationId);
        
        List<String> list = new ArrayList<>();
        Collections.addAll(list, id.split(",", -1));
        map.put("list", list);

        return relayService.updateCloudIdForRelay(map);
    }

} 