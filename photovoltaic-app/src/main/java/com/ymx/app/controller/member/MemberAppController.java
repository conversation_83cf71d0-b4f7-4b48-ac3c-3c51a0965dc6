package com.ymx.app.controller.member;

import com.ymx.app.BaseController;
import com.ymx.service.cache.Configure;
import com.ymx.service.cache.MsgCode;
import com.ymx.common.common.constant.ErrCode;
import com.ymx.common.common.result.CallResult;
import com.ymx.service.constant.ConstantsExt;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.photovoltaic.member.entity.MemberInfoModel;
import com.ymx.service.photovoltaic.member.entity.PushUserModel;
import com.ymx.service.photovoltaic.member.mapper.MemberMapper;
import com.ymx.service.photovoltaic.member.service.MemberService;
import com.ymx.service.photovoltaic.member.service.PushUserService;
import com.ymx.service.photovoltaic.warning.entity.MsgLog;
import com.ymx.service.photovoltaic.warning.service.MsgLogService;
import com.ymx.service.third.email.MailService;
import com.ymx.service.third.sms.SmsSend;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.MD5;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @DESC 接口
 * @DATE 2018/7/30
 * @NAME MemberController
 * @MOUDELNAME 模块
 */
@RestController
@RequestMapping("member")
public class MemberAppController  extends BaseController {

	private static final Logger logger = LoggerFactory.getLogger(MemberAppController.class);

	/** 会员服务层 */
    @Resource
    private MemberService memberService;
	/** 会员操作数据层 */
	@Resource
	private MemberMapper memberMapper;
	@Resource
	private MsgLogService msgLogService;
	@Resource
	private PushUserService pushUserService;
	
    /***
     * 通过mid获取人员信息
     * @param model 人员信息
     * @return
     */
    @RequestMapping("/queryMemberByMId.api")
    @ResponseBody
    public CallResult queryMemberById(MemberInfoModel model) {
        CallResult callResult = CallResult.newInstance();
        Map<String ,Object> map = new HashMap<>();
        if (null != model && !CommonUtil.isEmpty(model.getmId())) {
//	        if("en".equals(model.getLanguage())){
//		        callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN );
//	        }else{
//		        callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT );
//	        }
	        getCallResult(callResult,model.getLanguage(), ErrCode.PARAM_LOSS_EXCEPTION);
            return callResult;
        }
        model = memberService.queryMemberInfo(model);
        map.put("data" , model);
        callResult.setReModel(map);
        return callResult;
    }


    /**
     * 修改人员信息
     * @param model 人员信息
     * @return
     */
    @RequestMapping("/updateMemberModel.api")
    @ResponseBody
    public CallResult updateMemberModel(MemberInfoModel model ) {
	    CallResult callResult = CallResult.newInstance();
	    if (null != model && !CommonUtil.isEmpty(model.getXing())) {
//		    if("en".equals(model.getLanguage())){
//			    callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN);
//		    }else{
//			    callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
//		    }
		    getCallResult(callResult,model.getLanguage(), ErrCode.PARAM_LOSS_EXCEPTION);
		    return callResult;
	    }
	    if(model.getXing().length()>20){
//		    if("en".equals(model.getLanguage())){
//			    callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, "Surname Too long");
//		    }else{
//			    callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, "姓过长");
//		    }
		    getCallResult(callResult,model.getLanguage(), ErrCode.USER_SURNAME);
		    return callResult;
	    }
	    if (null != model && !CommonUtil.isEmpty(model.getName())) {
//		    if("en".equals(model.getLanguage())){
//			    callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN);
//		    }else{
//			    callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
//		    }
		    getCallResult(callResult,model.getLanguage(), ErrCode.PARAM_LOSS_EXCEPTION);
		    return callResult;
	    }
	    if(model.getName().length()>50){
//		    if("en".equals(model.getLanguage())){
//			    callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, "Name Too long");
//		    }else{
//			    callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, "名过长");
//		    }
		    getCallResult(callResult,model.getLanguage(), ErrCode.USER_NAME);
		    return callResult;
	    }
	    if (null != model && !CommonUtil.isEmpty(model.getEmail())) {
//		    if("en".equals(model.getLanguage())){
//			    callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN);
//		    }else{
//			    callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
//		    }
		    getCallResult(callResult,model.getLanguage(), ErrCode.PARAM_LOSS_EXCEPTION);
		    return callResult;
	    }
	    if(model.getEmail().length()>32){
//		    if("en".equals(model.getLanguage())){
//			    callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, "Mailbox is too long");
//		    }else{
//			    callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, "邮箱过长");
//		    }
		    getCallResult(callResult,model.getLanguage(), ErrCode.EMAIL_ERR.EMAIL_LONG_ERR);
		    return callResult;
	    }
	    if (model.getEmail().indexOf("@")<1 || model.getEmail().substring(model.getEmail().indexOf("@")).indexOf(".")<0 ) {
//		    if("en".equals(model.getLanguage())){
//			    callResult.setErr(ErrCode.EMAIL_ERR.EMAIL_ERR, "Incorrect mailbox format");
//		    }else{
//			    callResult.setErr(ErrCode.EMAIL_ERR.EMAIL_ERR, "邮箱格式不正确");
//		    }
		    getCallResult(callResult,model.getLanguage(), ErrCode.EMAIL_ERR.EMAIL_FORMAT_ERR);
		    return callResult;
	    }
        return memberService.updateMemberInfoModelByParam( model );
    }


    /**
     * 修改用户密码
     * @param model   会员实体
     * @param moudle  短信模块
     * @param code    验证码
     * @param newPass 新密码
     * @return
     */
    @RequestMapping(value="/updateMember.api")
    @ResponseBody
    public CallResult updateMember(MemberInfoModel model , String moudle , String code , String newPass){
        return memberService.updatePasswd(model, moudle, code, newPass);
    }


    /**
     * 用户登录
     * @param  model 会员实体
     * @return CallResult
     */
    @RequestMapping(value="/login.api")
    @ResponseBody
    public CallResult login( MemberInfoModel model ){
        return memberService.memberLogin( model );
    }

    /**
     * 注册用户
     * @param  model 会员实体
     * @return CallResult
     */
    @RequestMapping(value="/registeredMember.api")
    @ResponseBody
    public CallResult registeredMember(MemberInfoModel model,String smsCode){
        return memberService.registeredMember(model,smsCode);
    }


	/**
	 * 验证 账号是否存在
	 * @param info
	 * @return
	 */
	@RequestMapping(value="/checkMemberInfo.api")
	@ResponseBody
	public CallResult checkMemberInfo(MemberInfoModel info){
		CallResult callResult = CallResult.newInstance();
		Map<String, Object> map = new HashMap<>();
		try {
			//校验参数是否为空
			if (!CommonUtil.isEmpty(info.getPhone())) {
//				if(info!=null && "en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCode.PARAM_LOSS_EXCEPTION);
				return callResult;
			}
			//校验是否存在
			MemberInfoModel realData = memberMapper.queryMemberByPhone(info);
			if (null == realData) {
//				if(info!=null && "en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.USER_NOT_EXIST, ErrCode.USER_NOT_EXIST_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.USER_NOT_EXIST, ErrCode.USER_NOT_EXIST_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCode.USER_NOT_EXIST);
				return callResult;
			}
			//校验用户是否正常
			if (ConstantsExt.MEMBER.STATUS_LOCK == realData.getStatus()) {
//				if(info!=null && "en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.ACCOUNT_LOCK, ErrCode.ACCOUNT_LOCK_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.ACCOUNT_LOCK, ErrCode.ACCOUNT_LOCK_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCode.ACCOUNT_LOCK);
				return callResult;
			}
			if (ConstantsExt.MEMBER.STATUS_DEL == realData.getStatus()) {
//				if(info!=null && "en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.ACCOUNT_DELETE, ErrCode.ACCOUNT_DELETE_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.ACCOUNT_DELETE, ErrCode.ACCOUNT_DELETE_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCode.ACCOUNT_DELETE);
				return callResult;
			}
			map.put("data" , 1);
			map.put("user" , realData);
		} catch (Exception e) {
//			if(info!=null && "en".equals(info.getLanguage())){
//				callResult.setErr(ErrCode.EMAIL_ERR.EMAIL_ERR,ErrCode.EMAIL_ERR.EMAIL_ERR_TEXT_EN);
//			}else{
//				callResult.setErr(ErrCode.EMAIL_ERR.EMAIL_ERR,ErrCode.EMAIL_ERR.EMAIL_ERR_TEXT);
//			}
			getCallResult(callResult,info.getLanguage(), ErrCode.EMAIL_ERR.EMAIL_ERR);
			e.printStackTrace();
		}
		callResult.setReModel(map);
		return callResult;
	}


	/**
	 * 发送 验证码
	 * @param  info
	 * @return CallResult
	 */
	@RequestMapping(value="/sendMsg.api")
	@ResponseBody
	public CallResult sendMsg(MemberInfoModel info){
		CallResult callResult = CallResult.newInstance();
		Map<String, Object> map = new HashMap<>();
//		String code=CommonUtil.getRandomStringByLength(6);
		String code=CommonUtil.getRandomStringByLength16(6);

		try {
			//校验参数是否为空
			if (!CommonUtil.isEmpty(info.getPhone())) {
//				if("en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCodeExt.PARAM_LOSS_EXCEPTION);
				return callResult;
			}
			//校验是否存在
			MemberInfoModel realData = memberMapper.queryMemberByPhone(info);
			if (null == realData) {
//				if("en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.USER_NOT_EXIST, ErrCode.USER_NOT_EXIST_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.USER_NOT_EXIST, ErrCode.USER_NOT_EXIST_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCodeExt.USER_NOT_EXIST);
				return callResult;
			}
			//校验用户是否正常
			if (ConstantsExt.MEMBER.STATUS_LOCK == realData.getStatus()) {
//				if("en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.ACCOUNT_LOCK, ErrCode.ACCOUNT_LOCK_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.ACCOUNT_LOCK, ErrCode.ACCOUNT_LOCK_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCodeExt.ACCOUNT_LOCK);
				return callResult;
			}
			if (ConstantsExt.MEMBER.STATUS_DEL == realData.getStatus()) {
//				if("en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.ACCOUNT_DELETE, ErrCode.ACCOUNT_DELETE_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.ACCOUNT_DELETE, ErrCode.ACCOUNT_DELETE_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCodeExt.ACCOUNT_DELETE);
				return callResult;
			}
			String phone=info.getPhone();
			System.out.println(phone+" : "+code);
			Map<String, MsgCode> msgCodeMap=Configure.getMsgCodeMap();
			if(msgCodeMap!=null){
				MsgCode msgCode=msgCodeMap.get(phone);
				if(msgCode!=null){
					if(msgCode.getCreateTime().getTime()+60*1000<new Date().getTime()){//一分钟以上
						msgCode.setCode(code);
						msgCode.setCreateTime(new Date());
						msgCode.setPhone(phone);
						msgCodeMap.put(phone,msgCode);
					}
				}else {
					msgCode=new MsgCode();
					msgCode.setCode(code);
					msgCode.setCreateTime(new Date());
					msgCode.setPhone(phone);
					msgCodeMap.put(phone,msgCode);
				}
			}else{
				msgCodeMap=new HashMap<>();
				MsgCode msgCode=new MsgCode();
				msgCode.setCode(code);
				msgCode.setCreateTime(new Date());
				msgCode.setPhone(phone);
				msgCodeMap.put(phone,msgCode);
			}
			Configure.setMsgCodeMap(msgCodeMap);
			String content="您的验证码是:"+code;
			if(phone.indexOf("@")>-1){//邮箱
				MailService.sendHtmlMail(phone, "找回密码",content);
				msgLogService.saveObjectMsgLog(content, MsgLog.TYPE.TYPE_2,phone);
			}else if(phone.length()==11){
				SmsSend.sendMsg(phone,content);
				msgLogService.saveObjectMsgLog(content, MsgLog.TYPE.TYPE_1,phone);
			}
			map.put("data" , 1);
		} catch (Exception e) {
//			if(info!=null && "en".equals(info.getLanguage())){
//				callResult.setErr(ErrCode.SYSTEM_ERR,ErrCode.SYSTEM_ERR_TEXT_EN);
//			}else{
//				callResult.setErr(ErrCode.SYSTEM_ERR,ErrCode.SYSTEM_ERR_TEXT);
//			}
			getCallResult(callResult,info.getLanguage(), ErrCodeExt.SYSTEM_ERR);
			e.printStackTrace();
		}
		callResult.setReModel(map);
		return callResult;
	}

	/**
	 * 找回密码 修改 密码
	 * @param
	 * @return CallResult
	 */
	@RequestMapping(value="/saveMemberInfo.api")
	@ResponseBody
	public CallResult saveMemberInfo(MemberInfoModel info){//phone passwd  confirmPasswd mCode
		CallResult callResult = CallResult.newInstance();
		Map<String, Object> map = new HashMap<>();
		try {
			if (!CommonUtil.isEmpty(info.getPhone()) || !CommonUtil.isEmpty(info.getmCode()) || !CommonUtil.isEmpty(info.getPasswd()) ) {//校验参数是否为空
//				if(info!=null && "en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCodeExt.PARAM_LOSS_EXCEPTION);
				return callResult;
			}
			//校验是否存在
			MemberInfoModel realData = memberMapper.queryMemberByPhone(info);
			if (null == realData) {
//				if("en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.USER_NOT_EXIST, ErrCode.USER_NOT_EXIST_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.USER_NOT_EXIST, ErrCode.USER_NOT_EXIST_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCodeExt.USER_NOT_EXIST);
				return callResult;
			}
			String phone=info.getPhone();
			Map<String, MsgCode> msgCodeMap=Configure.getMsgCodeMap();
			if(msgCodeMap!=null){
				MsgCode msgCode=msgCodeMap.get(phone);
				if(msgCode!=null){
					if(msgCode.getCreateTime().getTime()+10*60*1000<new Date().getTime()){//验证码10分钟超时
						msgCodeMap.remove(phone);
//						if(info!=null && "en".equals(info.getLanguage())){
//							callResult.setErr(ErrCode.VERIFICATION_CODE_ERR_OVERTIME,ErrCode.VERIFICATION_CODE_ERR_OVERTIME_TEXT_EN);
//						}else{
//							callResult.setErr(ErrCode.VERIFICATION_CODE_ERR_OVERTIME,ErrCode.VERIFICATION_CODE_ERR_OVERTIME_TEXT);
//						}
						getCallResult(callResult,info.getLanguage(), ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME);
					}else{//验证通过
						String codeMsg=msgCode.getCode();//.toLowerCase()
						if(codeMsg.equals(info.getmCode())){
							MemberInfoModel memberInfo=new MemberInfoModel();
							memberInfo.setId(realData.getId());
							memberInfo.setPasswd(MD5.password(info.getPasswd()));
							memberMapper.updateMemberModel(memberInfo);
							msgCodeMap.remove(phone);
							map.put("data" , 1);
						}else{
//							if(info!=null && "en".equals(info.getLanguage())){
//								callResult.setErr(ErrCode.VERIFICATION_CODE_ERR,ErrCode.VERIFICATION_CODE_ERR_TEXT_EN);
//							}else{
//								callResult.setErr(ErrCode.VERIFICATION_CODE_ERR,ErrCode.VERIFICATION_CODE_ERR_TEXT);
//							}
							getCallResult(callResult,info.getLanguage(), ErrCodeExt.VERIFICATION_CODE_ERR);
						}
					}
				}else{
//					if(info!=null && "en".equals(info.getLanguage())){
//						callResult.setErr(ErrCode.VERIFICATION_CODE_ERR,ErrCode.VERIFICATION_CODE_ERR_TEXT_EN);
//					}else{
//						callResult.setErr(ErrCode.VERIFICATION_CODE_ERR,ErrCode.VERIFICATION_CODE_ERR_TEXT);
//					}
					getCallResult(callResult,info.getLanguage(), ErrCodeExt.VERIFICATION_CODE_ERR);
				}
			}else{
//				if(info!=null && "en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.VERIFICATION_CODE_ERR,ErrCode.VERIFICATION_CODE_ERR_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.VERIFICATION_CODE_ERR,ErrCode.VERIFICATION_CODE_ERR_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCodeExt.VERIFICATION_CODE_ERR);
			}
		} catch (Exception e) {
//			if(info!=null && "en".equals(info.getLanguage())){
//				callResult.setErr(ErrCode.SYSTEM_ERR,ErrCode.SYSTEM_ERR_TEXT_EN);
//			}else{
//				callResult.setErr(ErrCode.SYSTEM_ERR,ErrCode.SYSTEM_ERR_TEXT);
//			}
			getCallResult(callResult,info.getLanguage(), ErrCodeExt.SYSTEM_ERR);
			e.printStackTrace();
		}
		callResult.setReModel(map);
		return callResult;
	}


	/**
	 * 修改 密码
	 * @param
	 * @return CallResult
	 */
	@RequestMapping(value="/changePasswd.api")
	@ResponseBody
	public CallResult changePasswd(MemberInfoModel info){//phone passwd  confirmPasswd
		CallResult callResult = CallResult.newInstance();
		Map<String, Object> map = new HashMap<>();
		try {
			if (!CommonUtil.isEmpty(info.getPhone()) || !CommonUtil.isEmpty(info.getPasswd()) || !CommonUtil.isEmpty(info.getConfirmPasswd())) {//校验参数是否为空
//				if(info!=null && "en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCodeExt.PARAM_LOSS_EXCEPTION);
				return callResult;
			}
			//校验是否存在
			MemberInfoModel realData = memberMapper.queryMemberByPhone(info);
			if (null == realData) {
//				if("en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.USER_NOT_EXIST, ErrCode.USER_NOT_EXIST_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.USER_NOT_EXIST, ErrCode.USER_NOT_EXIST_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCodeExt.USER_NOT_EXIST);
				return callResult;
			}
			//验证密码是否正确
			if (!realData.getPasswd().equals(MD5.password(info.getPasswd()))) {
//				if(info!=null && "en".equals(info.getLanguage())){
//					callResult.setErr(ErrCode.PASSWD_ERROR, ErrCode.PASSWD_ERROR_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.PASSWD_ERROR, ErrCode.PASSWD_ERROR_TEXT);
//				}
				getCallResult(callResult,info.getLanguage(), ErrCodeExt.PASSWD_ERROR);
				return callResult;
			}
			MemberInfoModel memberInfo=new MemberInfoModel();
			memberInfo.setId(realData.getId());
			memberInfo.setPasswd(MD5.password(info.getConfirmPasswd()));
			memberMapper.updateMemberModel(memberInfo);
		} catch (Exception e) {
//			if(info!=null && "en".equals(info.getLanguage())){
//				callResult.setErr(ErrCode.SYSTEM_ERR,ErrCode.SYSTEM_ERR_TEXT_EN);
//			}else{
//				callResult.setErr(ErrCode.SYSTEM_ERR,ErrCode.SYSTEM_ERR_TEXT);
//			}
			getCallResult(callResult,info.getLanguage(), ErrCodeExt.SYSTEM_ERR);
			e.printStackTrace();
		}
		callResult.setReModel(map);
		return callResult;
	}

	/**
	 * 修改 系统设置
	 * @param
	 * @return CallResult
	 */
	@RequestMapping(value="/updatePushUserModel.api")
	@ResponseBody
	public CallResult updatePushUserModel(PushUserModel model){//phone passwd  confirmPasswd
		CallResult callResult = CallResult.newInstance();
		Map<String, Object> map = new HashMap<>();
		try {
			if (!CommonUtil.isEmpty(model.getRegistId()) || !CommonUtil.isEmpty(model.getMemberId()) || !CommonUtil.isEmpty(model.getLanguage())) {//校验参数是否为空
//				if(model!=null && "en".equals(model.getLanguage())){
//					callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.PARAM_LOSS_EXCEPTION, ErrCode.PARAM_LOSS_EXCEPTION_TEXT);
//				}
				getCallResult(callResult,model.getLanguage(), ErrCodeExt.PARAM_LOSS_EXCEPTION);
				return callResult;
			}
			if(model.getLanguage()==null){
				model.setLanguage("ch");
			}
			pushUserService.updatePushUserModel(model.getMemberId(),model.getRegistId(),model.getLanguage());
			callResult.setReModel(1);
		} catch (Exception e) {
			callResult.setReModel(0);
//			if(model!=null && "en".equals(model.getLanguage())){
//				callResult.setErr(ErrCode.SYSTEM_ERR,ErrCode.SYSTEM_ERR_TEXT_EN);
//			}else{
//				callResult.setErr(ErrCode.SYSTEM_ERR,ErrCode.SYSTEM_ERR_TEXT);
//			}
			getCallResult(callResult,model.getLanguage(), ErrCodeExt.SYSTEM_ERR);
			e.printStackTrace();
		}
		return callResult;
	}

	


}
