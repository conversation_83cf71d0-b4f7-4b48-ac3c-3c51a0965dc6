package com.ymx.app.controller.member;

import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.photovoltaic.member.entity.SysMessageModel;
import com.ymx.service.photovoltaic.member.service.SysMessageService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 系统消息
 * <AUTHOR> @version 2018/11/5
 */
@RestController
@RequestMapping("sysMessage")
public class SysMessageController {
	@Resource
	private SysMessageService sysMessageService;
	/**
	 * 系统消息列表
	 * @param request
	 * @param pageView
	 * @param model
	 * @return
	 */
	@RequestMapping("querySysMessageByList.api")
	public CallResult querySysMessageByList(HttpServletRequest request , PageView pageView , SysMessageModel model){
		model.setPublish(1);//已发布
		return sysMessageService.querySysMessageByList(pageView , model);
	}

	/**
	 * 查看详情
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping("querySysMessageById.api")
	public CallResult querySysMessageById(HttpServletRequest request , SysMessageModel model){
		CallResult callResult = CallResult.newInstance();
		SysMessageModel sysMessageUser=new SysMessageModel();
		sysMessageUser.setIsRead(2);
		sysMessageUser.setId(model.getId());
		sysMessageUser.setCreateUserId(model.getCreateUserId());
		sysMessageUser.setCreateType(2);
		sysMessageService.updateSysMessageModel(sysMessageUser);
		SysMessageModel sysMessageModel=sysMessageService.querySysMessageById(model.getId());
		callResult.setReModel(sysMessageModel);
		return callResult;
	}
}
