package com.ymx.app.controller.region;

import com.ymx.app.BaseController;
import com.ymx.common.base.config.ConfigConstants;
import com.ymx.service.cache.Configure;
import com.ymx.service.cache.MsgCode;
import com.ymx.common.common.result.CallResult;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.common.mina.StringUtil;
import com.ymx.service.photovoltaic.region.entity.RegionLocationModel;
import com.ymx.service.photovoltaic.region.service.RegionLocationService;
import com.ymx.service.photovoltaic.station.model.PowerStationModel;
import com.ymx.service.photovoltaic.station.service.PowerStationService;
import com.ymx.common.utils.CommonUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * @DESC 区域位置
 * @DATE 2018/9/07
 * @NAME RegionLocationAppController
 * @MOUDELNAME 模块
 */
@RestController
@RequestMapping("regionLocationAppCtr")
public class RegionLocationAppController extends BaseController {
	/** 电站服务层  */
	@Resource
	private PowerStationService powerStationService;
    @Resource
    private RegionLocationService regionLocationService;


    /**
     * 获取数据
     * @param model 区域位置实体
     * @return
     */
    @RequestMapping("queryRegion.api")
    public CallResult queryRegion(RegionLocationModel model ){
        return regionLocationService.queryRegion(model);
    }



	@RequestMapping(value="/queryWeatherInfo.api")
	@ResponseBody
	public CallResult queryWeatherInfo(PowerStationModel model)
	{
		CallResult callResult = CallResult.newInstance();
		getCallResult(callResult,model.getLanguage(), ErrCodeExt.WEATHER_ERROR);
        return callResult;
	}

	/**
	 * 查询天气信息
	 * https://wx.jdcloud.com/api-42
	 * @param model
	 * @return
	 */
	@RequestMapping(value="/queryWeatherInfo_old.api")
	@ResponseBody
	public CallResult queryComponentList(PowerStationModel model){
		CallResult callResult = CallResult.newInstance();
		Map<String, Object> map = new HashMap<>();
		Map<String, MsgCode> msgCodeMap=Configure.getMsgCodeMap();
		MsgCode msgCode=msgCodeMap.get(model.getId()+"_Weather");
		String cityName="";
		String provinceName="";
		String cityNameEn="";
		String provinceNameEn="";
		if(msgCode!=null && msgCode.getCode()!=null && msgCode.getCode().length()>0 && msgCode.getCreateTime().getTime()+1000 * 60 * 60 * 1> new Date().getTime()){
			if(model.getLanguage()==null || "ch".equals(model.getLanguage())){//en
				map.put("city" ,msgCode.getCity());
				map.put("province" ,msgCode.getProvince());
				map.put("data" ,JSONObject.fromObject(msgCode.getCode()));
			}else{
				map.put("city" ,msgCode.getCityEn());
				map.put("province" ,msgCode.getProvinceEn());
				map.put("data" ,JSONObject.fromObject(msgCode.getCodeEn()));
			}
		}else{
			PowerStationModel powerStationModel =  powerStationService.queryPowerStationObjectById(model.getId());
			RegionLocationModel regionLocationModel=null;
			if(StringUtil.isNotNull(powerStationModel)){//城市天气
				String cityId=powerStationModel.getCityId();
				if(StringUtil.notEmpty(cityId)){
					regionLocationModel=regionLocationService.queryRegionLocationInfo(Integer.parseInt(cityId));
					if(StringUtil.isNotNull(regionLocationModel)) {//城市存在
						if(model.getLanguage() ==null || "ch".equals(model.getLanguage())){
							map.put("city" ,regionLocationModel.getName());
						}else{
							map.put("city" ,regionLocationModel.getNameEn());
						}
						cityName=regionLocationModel.getName();
						cityNameEn=regionLocationModel.getNameEn();
					}
				}
				RegionLocationModel regionLocationProvince=null;
				String province=powerStationModel.getProvince();
				if(StringUtil.notEmpty(province)){
					regionLocationProvince=regionLocationService.queryRegionLocationInfo(Integer.parseInt(province));
				}
				RegionLocationModel regionLocationCountries=null;
				String countriesId=powerStationModel.getCountriesId();
				if(StringUtil.notEmpty(countriesId)){
					regionLocationCountries=regionLocationService.queryRegionLocationInfo(Integer.parseInt(countriesId));
				}
				if(StringUtil.isNull(regionLocationModel)) {//城市存在
					regionLocationModel=regionLocationProvince;
					if(StringUtil.isNotNull(regionLocationProvince)) {//省
						if(model.getLanguage() ==null || "ch".equals(model.getLanguage())){
							map.put("city" ,regionLocationProvince.getName());
						}else {
							map.put("city" ,regionLocationModel.getNameEn());
						}
						cityName=regionLocationProvince.getName();
						cityNameEn=regionLocationProvince.getNameEn();
					}
					if(StringUtil.isNotNull(regionLocationCountries)) {//省
						if(model.getLanguage() ==null || "ch".equals(model.getLanguage())){
							map.put("province" ,regionLocationCountries.getName());
						}else{
							map.put("province" ,regionLocationCountries.getNameEn());
						}
						provinceName=regionLocationCountries.getName();
						provinceNameEn=regionLocationCountries.getNameEn();
					}
				}else{
					if(StringUtil.isNotNull(regionLocationProvince)) {//省
						if(model.getLanguage() ==null || "ch".equals(model.getLanguage())){
							map.put("province" ,regionLocationProvince.getName());
						}else{
							map.put("province" ,regionLocationProvince.getNameEn());
						}
						provinceName=regionLocationProvince.getName();
						provinceNameEn=regionLocationProvince.getNameEn();
					}
				}
			}
			String result="";
			if(StringUtil.isNotNull(regionLocationModel)){//城市存在
				try{
					result=getWeatherURL(regionLocationModel.getName());
				}catch (Exception e){
					e.printStackTrace();
				}
			}
			if(StringUtil.nullOrEmpty(result)){//未查到城市天气
//				if("en".equals(model.getLanguage())){
//					callResult.setErr(ErrCode.WEATHER_ERROR,ErrCode.WEATHER_ERROR_TEXT_EN);
//				}else{
//					callResult.setErr(ErrCode.WEATHER_ERROR,ErrCode.WEATHER_ERROR_TEXT);
//				}
				getCallResult(callResult,model.getLanguage(), ErrCodeExt.WEATHER_ERROR);
			}else{
				msgCode=new MsgCode();
				msgCode.setCode(result);
				msgCode.setCity(cityName);
				msgCode.setCreateTime(new Date());
				msgCode.setProvince(provinceName);
				Map<String,String> mapWeather=getWeatherType();
				String resultEn=result;
				for (String key : mapWeather.keySet()) {
					String value=mapWeather.get(key);
					resultEn=resultEn.replaceAll(key,value);
				}
				msgCode.setCodeEn(resultEn);
				msgCode.setCityEn(cityNameEn);
				msgCode.setProvinceEn(provinceNameEn);
				msgCodeMap.put(model.getId()+"_Weather",msgCode);
				Configure.setMsgCodeMap(msgCodeMap);

				if(model.getLanguage() ==null || "ch".equals(model.getLanguage())){
//				if("en".equals(model.getLanguage())){
					map.put("data" ,JSONObject.fromObject(result));
				}else{
					map.put("data" ,JSONObject.fromObject(resultEn));
				}
			}
		}
		callResult.setReModel(map);
		return callResult;
	}

	private static Map getWeatherType(){
		Map<String,String> weatherType=new HashMap<>();
		weatherType.put("晴","sunny");
		weatherType.put("多云","cloudy");
		weatherType.put("阴","Overcast");
		weatherType.put("阵雨","shower");
		weatherType.put("雷阵雨","Thunder shower");
		weatherType.put("雷阵雨伴有冰雹","Thunderstorm accompanied by hail");
		weatherType.put("雨夹雪","Sleet");
		weatherType.put("小雨","Light rain");

		weatherType.put("中雨","Moderate rain");
		weatherType.put("大雨","Heavy rain");
		weatherType.put("暴雨","Rainstorm");
		weatherType.put("大暴雨","Heavy rain");
		weatherType.put("特大暴雨","Heavy rain");
		weatherType.put("阵雪","Snow shower");
		weatherType.put("小雪","Light snow");
		weatherType.put("中雪","Moderate snow");

		weatherType.put("大雪","Heavy snow");
		weatherType.put("暴雪","Violence snow");
		weatherType.put("雾","Fog");
		weatherType.put("冻雨","Freezing rain");
		weatherType.put("沙尘暴","Sand storm");
		weatherType.put("小雨-中雨","Light rain - moderate rain");
		weatherType.put("中雨-大雨","Moderate rain - Heavy rain");
		weatherType.put("大雨-暴雨","Heavy rain - Rainstorm");

		weatherType.put("暴雨-大暴雨","Rainstorm - Heavy rain");
		weatherType.put("大暴雨-特大暴雨","Rainstorm");
		weatherType.put("小雪-中雪","Light snow - Moderate snow");
		weatherType.put("中雪-大雪","Moderate snow - Heavy snow");
		weatherType.put("大雪-暴雪","Heavy snow - Violence snow");
		weatherType.put("浮尘","Floating dust");
		weatherType.put("扬沙","blowing sand");
		weatherType.put("强沙尘暴","severe sand and dust storm");

		weatherType.put("霾","Haze");
		return weatherType;
	}

	/**
	 * TEST
	 * @param args
	 */
	public static void main(String[] args) {
		String city="北京";
//		String weatherJson= getWeatherURL(city);
//		System.out.println("getWeatherURL========="+weatherJson);
//		Map<String,String> map=getWeatherType();
//		for (String key : map.keySet()) {
//			String value=map.get(key);
//			weatherJson=weatherJson.replaceAll(key,value);
//		}

//		weatherJson=weatherJson.replaceAll("多云","多云222");
//		System.out.println("getWeatherURL========="+weatherJson);
////		{"basic":{"city":"北京","cnty":"中国","id":"CN101010100","lat":"39.90498734","lon":"116.4052887","update":{"loc":"2018-11-20 13:45","utc":"2018-11-20 05:45"}},
//		// "now":{"cond":{"code":"101","txt":"多云"},"fl":"7","hum":"18","pcpn":"0.0","pres":"1023","tmp":"11","vis":"27",
//		// "wind":{"deg":"356","dir":"北风","sc":"2","spd":"11"}}}
////		"多云"
//		System.out.println(temp);

		List<BasicNameValuePair> params=new ArrayList<>();
		String contentType="text/xml;charset=UTF-8";
		String postUrl="https://carmen.youzan.com/gw/web/youzan.retail.trademanager.pcorder/1.0.0/search?retail_source=WEB-RETAIL-AJAX&source=WEB_BACK_END&idempotent_no=1560765426101&page_size=20&search_label=order_no";
		String weatherResult=CommonUtil.doPostHttpClient(postUrl,contentType,params);
		System.out.println(weatherResult);
	}


	/**
	 * 根据城市获取天气
	 * @param city
	 * @return
	 */
	private static String getWeatherURL(String city){
		String appkey= ConfigConstants.getConfig("WEATHER_APPKEY");
		if(StringUtil.nullOrEmpty(appkey)){
//			appkey="c74f27da913ae768cb37f281816de60f";//test
			appkey="cddc35e208011c2d04960242714254ec";//ymx

		}
		String postUrl="https://way.jd.com/he/freecity?city="+city+"&appkey="+appkey;
		List<BasicNameValuePair> params=new ArrayList<>();
		String contentType="text/xml;charset=UTF-8";
//		String result= CommonUtil.doPostHttpClient(postUrl,contentType,params);
//		System.out.println("city=="+city);
//		System.out.println("result=="+result);
//		try{
//			JSONObject jsonObject=getHeWeather5(result);
//			if(jsonObject==null){
//				return "";
//			}
//			String code=getCityInfo(jsonObject);
//		}catch (Exception e){
//			e.printStackTrace();
//		}
		postUrl="https://way.jd.com/he/freeweather?city="+city+"&appkey="+appkey;
		String weatherResult=CommonUtil.doPostHttpClient(postUrl,contentType,params);
//		System.out.println(weatherResult);
		JSONObject jsonWeather=getHeWeather5(weatherResult);
		String weatherContent=getWeatherInfo(jsonWeather);
		return weatherContent;
	}

	/**
	 * 处理天气基本数据
	 * @param result
	 * @return
	 */
	private static JSONObject getHeWeather5(String result){
		JSONObject jsonObject=JSONObject.fromObject(result);
//		System.out.println("result : "+jsonObject.getString("result"));
		JSONObject jsonObjectResult=JSONObject.fromObject(jsonObject.getString("result"));
		String HeWeather5=jsonObjectResult.getString("HeWeather5");
		if(HeWeather5!=null && HeWeather5.length()>0){
			JSONArray jsonArray=JSONArray.fromObject(HeWeather5);
			if(!jsonArray.isEmpty()){
				JSONObject myjObject = jsonArray.getJSONObject(0);
				if("ok".equals(myjObject.get("status").toString())){//正确获取数据
					return myjObject;
				}
			}
		}
		return null;
	}

	/**
	 * 获取城市代码
	 * @param jsonObject
	 * @return
	 */
	private static String getCityInfo(JSONObject jsonObject) {
		String code="";
		String basic=jsonObject.getString("basic");
		if(StringUtil.notEmpty(basic)){
			JSONObject jsonObjectBasic=JSONObject.fromObject(basic);
			code=jsonObjectBasic.get("id").toString();
		}
		return code;
	}

	/**
	 * 获取处理后的天气信息
	 * @param jsonObject
	 * @return
	 */
	private static String getWeatherInfo(JSONObject jsonObject) {
		String result="";
		String basic=jsonObject.getString("basic");
		if(StringUtil.notEmpty(basic)){
			JSONObject jsonObjectBasic=JSONObject.fromObject(basic);
			JSONObject jsonObjectNow=JSONObject.fromObject(jsonObject.getString("now"));
			result="{\"basic\":"+jsonObjectBasic.toString()+",\"now\":"+jsonObjectNow.toString()+"}";
		}
		return result;
	}

}
