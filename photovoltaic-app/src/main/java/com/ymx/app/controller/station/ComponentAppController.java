package com.ymx.app.controller.station;

import com.ymx.app.BaseController;
import com.ymx.common.base.config.ConfigConstants;
import com.ymx.service.cache.Configure;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageView;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.common.mina.StringUtil;
import com.ymx.service.photovoltaic.station.model.*;
import com.ymx.service.photovoltaic.station.service.*;
import com.ymx.service.photovoltaic.warning.entity.WarningModel;
import com.ymx.service.photovoltaic.warning.service.WarningService;
import com.ymx.common.utils.CommonUtil;
import com.ymx.common.utils.DataUtil;
import com.ymx.common.utils.DateUtils;
import com.ymx.common.utils.IDUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @DESC 组件控制层
 * @DATE 2018/8/1
 * @NAME ComponentController
 * @MOUDELNAME 模块
 */
@RestController
@RequestMapping("component")
public class ComponentAppController extends BaseController {

    /** 组件服务层 */
    @Resource
    private ComponentService componentService;
	@Resource
	private ComponentCollectBatchNoService componentCollectBatchNoService;
	@Resource
	private ComponentCollectService componentCollectService;
	@Resource
	private ConfigureService configureService;
	/** 逆变器服务层 */
	@Resource
	private InverterService inverterService;
	/** 云终端服务层 */
	@Resource
	private CloudTerminalService cloudTerminalService;
	@Resource
	private WarningService warningService;
	@Resource
	private InverterCollectService inverterCollectService;
	@Resource
	private PowerStationService powerStationService;

	private static final Logger logger = LoggerFactory.getLogger(ComponentAppController.class);


	/**
	 * 演示版接口  使用需要把 queryComponentList2 改成 queryComponentList
	 * 查询电站内 组件和采集器信息
	 * @param powerStationId   系统设备（电站） ID
	 * @param date 查询时间
	 * @return
	 */
	@RequestMapping(value="/queryComponentList2.api")
	public CallResult queryComponentList(@RequestParam(value = "powerStationId", required = true) String powerStationId,
	                                     @RequestParam(value = "date", required = false)String date,
	                                     @RequestParam(value = "type", required = false)String type){
		if(StringUtil.nullOrEmpty(date)){
			date= DataUtil.getStrCurrentDate();
		}
		return componentService.queryComponentListByPowId(powerStationId,type,date);
	}
	/**
	 * 查询电站内 组件和采集器信息
	 * @param powerStationId   系统设备（电站） ID
	 * @param dateTime 查询时间
	 * @return
	 */
	// 只查询组件的电气数据 不查询其他信息（位置，告警等）
	@RequestMapping(value="/queryComponentListByMinute.api")
	public CallResult queryComponentListByMinute(@RequestParam(value = "powerStationId", required = true) String powerStationId,
	                                      @RequestParam(value = "dateTime", required = false)String dateTime,
	                                             @RequestParam(value = "language", required = false)String language){
		CallResult callResult = CallResult.newInstance();
		Map<String, Object> map = new HashMap<>();
		if(StringUtil.nullOrEmpty(dateTime)){
			dateTime= DateUtils.timestampToDate(new Date().getTime(),"yyyy-MM-dd HH:mm");
		}
		long beginTime=System.currentTimeMillis();
		try {
			// 组件采集批次信息
			ComponentCollectBatchNo collectBatchNo=componentCollectBatchNoService.queryComponentCollectBatchNoTimeList(powerStationId,dateTime,1);
			// 如果collectBatchNo为空 就赋0值
			if(collectBatchNo==null){
				List<ComponentModel> componentList =configureService.queryComponentListByPowerStationId(powerStationId);
				collectBatchNo=new ComponentCollectBatchNo();
				collectBatchNo.setWh(new BigDecimal(0));
				collectBatchNo.setKwh(new BigDecimal(0));
				collectBatchNo.setCreateTime(DateUtils.parse(dateTime,"yyyy-MM-dd HH:mm"));
				collectBatchNo.setBatchNo(DateUtils.timestampToDate(DateUtils.parse(dateTime,"yyyy-MM-dd HH:mm").getTime(),DateUtils.DATE_All_KEY_STR));//20181030231200
				collectBatchNo.setPowerStationId(powerStationId);
				collectBatchNo.setStatus(1);
				collectBatchNo.setTimeStep(1);
				collectBatchNo.setBatchDay(Integer.parseInt(collectBatchNo.getBatchNo().substring(0,8)));
				List<ComponentCollectModel> list=new ArrayList<>();
				for (ComponentModel componentModel:componentList){
					ComponentCollectModel componentCollectModel=new ComponentCollectModel();
					componentCollectModel.setBelongsGroupId(componentModel.getBelongsGroupId());
					componentCollectModel.setGroupName(componentModel.getBelongsGroupName());
					componentCollectModel.setType(1);
					componentCollectModel.setChipId(componentModel.getChipId());
					componentCollectModel.setComponentTemperature(0);
					componentCollectModel.setOutputCurrent(0);
					componentCollectModel.setOutputVoltage(0);
					componentCollectModel.setInputVoltage(0);
					componentCollectModel.setInputCurrent(0);
					componentCollectModel.setValue(0);
					componentCollectModel.setIsException(1);
					componentCollectModel.setId("0");
					list.add(componentCollectModel);
				}
				collectBatchNo.setList(list);
			}
			map.put("data",collectBatchNo);
			callResult.setReModel(map);
		}catch (Exception e){
//			if("en".equals(language)){
//				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt.SYSTEM_ERR_TEXT_EN);
//			}else{
//				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt.SYSTEM_ERR_TEXT);
//			}
			getCallResult(callResult,language,ErrCodeExt.SYSTEM_ERR);
			e.printStackTrace();
		}
		System.out.println(" #######  时间  "+(System.currentTimeMillis()-beginTime));
		return callResult;
	}

	/**
	 * 正式版接口  使用需要把 queryComponentList 改掉
	 * @param powerStationId
	 * @param date
	 * @param type
	 * @return
	 */
	// 手机app获取系统视图的接口
	@RequestMapping(value="/queryComponentList.api")
	public CallResult queryComponentListVO_3(@RequestParam(value = "powerStationId", required = true) String powerStationId,
	                                         @RequestParam(value = "date", required = false)String date,
	                                         @RequestParam(value = "type", required = false)String type){
        // 最终的处理结果类
		CallResult callResult = CallResult.newInstance();
	     // 如果传过来的日期为空  就赋值为现在的时间
		if(StringUtil.nullOrEmpty(date)){
			logger.info("client date is null");
			date= DateUtils.timestampToDate(new Date().getTime(),"yyyy-MM-dd HH:mm");
		}
		logger.info("date:"+date);

		Map<String, Object> map = new HashMap<>();
		long beginTime=System.currentTimeMillis();
		// 查询获得组串信息
		List<ComponentModel> componentList =configureService.queryComponentListByPowerStationId(powerStationId);//2384
		List<Map<String,Object>> groupList=new ArrayList<>();
		// 组件采集批次list
		List<ComponentCollectBatchNo> list=new ArrayList<>();
		String queryDate=null;
		// 如果组串信息不为null
		if(componentList!=null){
			int count=0;
			// 如果组件的 isAuth 不为null且 isAuth为2 循环累加count值
			for(ComponentModel componentModel:componentList){
				if(componentModel.getIsAuth()!=null && componentModel.getIsAuth()==2){
					count++;
				}
			}
			// 再次循环组件list  按组串重新分组组件 这两个循环是不是可以合并为一个
			Map<String, ComponentModel> componentGroupList = new HashMap<>();
			for(ComponentModel componentModel:componentList){
				ComponentModel component=componentGroupList.get(componentModel.getBelongsGroupId());
				if(component==null){
					componentGroupList.put(componentModel.getBelongsGroupId(),componentModel);
				}
			}
			Map<String, List<ComponentCollectModel>> mapList = new HashMap<>();
			// 如果日期的前10位等于 yyyy-MM-dd
			if(date.substring(0,10).equals(DataUtil.getStrCurrentDate())){
				queryDate=date;
				List<ComponentCollect> componentCollectList=componentCollectService.queryComponentCollectByDate(powerStationId,count,date);
				// 如果当前时间没有数据，就取上一分钟的数据
				if(componentCollectList!=null&&componentCollectList.size()==0)
				{
					Date paramDate=DateUtils.parse(date,"yyyy-MM-dd HH:mm");
					Date oneMinBeforeDate=DateUtils.changeMinute(paramDate,-1);
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
					queryDate = sdf.format(oneMinBeforeDate);
					componentCollectList=componentCollectService.queryComponentCollectByDate(powerStationId,count,queryDate);
					logger.info("oneMinBefore:"+queryDate);
				}

	             // 传入最新收集的组件数据 然后按批次处理数据
				if(componentCollectList!=null && componentCollectList.size()>0){
					list= componentCollectBatchNoService.doDataToBatch(componentList,componentCollectList,powerStationId);
				}
			}else{
				// 如果日期的前10位不等于 yyyy-MM-dd 则重新组装日期信息 加上小时 分钟信息
				date=date.substring(0,10)+DateUtils.timestampToDate(new Date().getTime()," HH:mm");
				// 查询获取组件采集批次信息
				ComponentCollectBatchNo batchNo=componentCollectBatchNoService.queryComponentCollectBatchNoTimeList(powerStationId,date,1);
				list.add(batchNo);
			}

			if(list!=null && list.size()>0){
				ComponentCollectBatchNo collectBatchNo=list.get(list.size()-1);
				if(collectBatchNo!=null){
					collectBatchNo.setContent("");
					List<ComponentCollectModel> componentCollectModelList =collectBatchNo.getList();
					// 循环组件采集ModelList
					for (ComponentCollectModel componentCollectModel:componentCollectModelList){
						// mapList 按组分配 组件采集的ModelList  key为组串id  值为组件采集的ModelList
						List<ComponentCollectModel> ccmList=mapList.get(componentCollectModel.getBelongsGroupId());
						if(ccmList!=null){
							ccmList.add(componentCollectModel);
						}else{
							ccmList=new ArrayList<>();
							ccmList.add(componentCollectModel);
						}
						mapList.put(componentCollectModel.getBelongsGroupId(),ccmList);
					}
				}
			}
			// 循环组串id
			for (String groupId : componentGroupList.keySet()) {
				// 根据组id 从组串list中取出 componentModel
				ComponentModel obj = componentGroupList.get(groupId);
				// 构造hashmap
				Map<String ,Object> groupInfo=new HashMap<>();
				groupInfo.put("belongsGroupId",obj.getBelongsGroupId());
				groupInfo.put("belongsGroupName",obj.getBelongsGroupName());
				groupInfo.put("groupType",obj.getGroupType());
				long outputCurrent=0;
				long outputVoltage=0;
				// 按组串id 取出组件采集的Model list
				List<ComponentCollectModel> objList = mapList.get(groupId);
				if(objList!=null && objList.size()>0){
					// 循环组件采集的Model list
					for (ComponentCollectModel componentCollectModel:objList){
						if(obj.getGroupType()==1){//1串联 2并联
							// 串联累加输出电压 并联累加输出电流
							outputVoltage+=componentCollectModel.getOutputVoltage();
							outputCurrent=componentCollectModel.getOutputCurrent();
						}else{
							outputVoltage=componentCollectModel.getOutputVoltage();
							outputCurrent+=componentCollectModel.getOutputCurrent();
						}
					}
				}
				// groupInfo 统计组串信息 组串输出电压 输出电流 功率
				groupInfo.put("outputCurrent",new BigDecimal(outputCurrent/1000.0).setScale(2 , BigDecimal.ROUND_HALF_UP));
				groupInfo.put("outputVoltage",new BigDecimal(outputVoltage/1000.0).setScale(2 , BigDecimal.ROUND_HALF_UP));
				BigDecimal power =new BigDecimal(outputCurrent/1000.0 * outputVoltage/1000.0);
				groupInfo.put("power",power.setScale(2 , BigDecimal.ROUND_HALF_UP));
				// groupList 组串统计信息list
				groupList.add(groupInfo);
			}
			// 统计指定日期的总瓦时数据
			BigDecimal wh =componentCollectBatchNoService.queryComponentByMinuteCount(componentList,powerStationId,date.substring(0,10));//821

			if(wh==null){
				wh=new BigDecimal(0);
			}
			// 将瓦时转换为kwh
			BigDecimal kwh =new BigDecimal(wh.doubleValue()/1000.0);
			Map<String, Object> mapTitle = new HashMap<>();
			mapTitle.put("group",groupList);
			map.put("kwh",kwh.setScale(2 , BigDecimal.ROUND_HALF_UP)+"");//计算总电量  KWH  四舍五入保留2位
			mapTitle.put("kwh",kwh);//计算总电量  KWH  四舍五入保留2位
			map.put("statistics" , mapTitle);
		}
		List<AllGroupModel> agmlt = new ArrayList<AllGroupModel>();
		String laystatus = "1";
		// 单表查询t_app_warning 获取告警数据列表  查询出所有当天未处理的告警
		List<WarningModel> warningModelList=warningService.queryWarningByStatusList(powerStationId);
		// 查询 t_app_component t_app_change 表  获取组件的位置 排列信息
		List<ComponentModel> lt = componentService.queryComponentListByData(powerStationId, laystatus);
		// 获得组件的排列信息
		getAllGroupModelC(lt,agmlt,list,componentList,warningModelList);

		map.put("powerStationId",powerStationId);
		map.put("laystatus","1");
		// 获取逆变器数据
		List<InverterModel> Imlt = getInverterModel(map,powerStationId);

        // 获取逆变器的排序信息
		getAllGroupModelI(Imlt,agmlt);
		// 获得采集器的信息
		List<CloudTerminalModel> ctlt = getCloudTerminalModel(map,powerStationId);

	// 获取采集器的排列信息
		getAllGroupModelT(ctlt,agmlt);
		// map 是最后要返回的map  agmlt 是 AllGroupModel的list 最后所有model数据的汇总
		map.put("data" , agmlt);
		// type
		if(!"1".equals(type)){
			PowerStationModel powerStationModel=powerStationService.queryCachePowerStationObjectById(powerStationId,null);
			// 查询电站信息  最后只需要 日出 日落两个字段
			String sunuptime=powerStationModel.getSunuptime();
			String sundowntime=powerStationModel.getSundowntime();
			Date beginTime1=getPowerStationTime(date.substring(0,10),sunuptime);
			Date endTime1=getPowerStationTime(date.substring(0,10),sundowntime);
			if(beginTime1.getTime()>endTime1.getTime()){
				endTime1=DateUtils.changeDays(endTime1,1);
			}
			Integer timeStep;
			try {
				timeStep=Integer.parseInt(ConfigConstants.getConfig("TIME_STEP"));//获取间隔时间
			}catch (Exception e){
				timeStep=5;
				e.printStackTrace();
			}
			List<String>axisList=new ArrayList<>();
			if(queryDate!=null)
			{
				axisList.add(queryDate+":00");
			}
			else
			{
				axisList.add(date+":00");
			}
			// 放置 时间轴相关的属性
			map.put("timeAxis",axisList);
			map.put("timeAxisBeginTime" , DateUtils.timestampToDate(beginTime1.getTime(),"yyyy-MM-dd HH:mm:00"));
			map.put("timeAxisEndTime" , DateUtils.timestampToDate(endTime1.getTime(),"yyyy-MM-dd HH:mm:00"));
			map.put("timeAxisStep" , timeStep);//步长   分钟
		}

		logger.info("total use time "+(System.currentTimeMillis()-beginTime));//100

		callResult.setReModel(map);
		return callResult;
	}


	private List<ComponentModel> getComponentModel(String powerStationId,String ymd){
		Map<String, List<ComponentModel>> positionComponentMap =Configure.getPositionComponentMap();
		List<ComponentModel> componentModelList =positionComponentMap.get(powerStationId);
		if(componentModelList==null || componentModelList.size()<1){
			componentModelList=componentService.queryComponentListByData(powerStationId, ymd);
			for(ComponentModel componentModel:componentModelList){
				componentModel.setUpdateTime(new Date());
			}
			positionComponentMap.put(powerStationId,componentModelList);
			Configure.setPositionComponentMap(positionComponentMap);
		}else{
			ComponentModel componentModel=componentModelList.get(0);
			if(componentModel.getUpdateTime()!=null && componentModel.getUpdateTime().getTime()+1000*60*30<new Date().getTime()){
				componentModelList=componentService.queryComponentListByData(powerStationId, ymd);
				if (componentModelList!=null && componentModelList.size()>0){
					for(ComponentModel componentModel1:componentModelList){
						componentModel1.setUpdateTime(new Date());
					}
				}
				positionComponentMap.put(powerStationId,componentModelList);
				Configure.setPositionComponentMap(positionComponentMap);
			}
		}
		return componentModelList;
	}
	private List<InverterModel> getInverterModel(Map<String,Object> map,String powerStationId){
		Map<String, List<InverterModel>> positionInverterMap =Configure.getPositionInverterMap();
		List<InverterModel> inverterModelList =positionInverterMap.get(powerStationId);
		if(inverterModelList==null || inverterModelList.size()<1){
			inverterModelList = inverterService.queryInverterListByData(map);
			for(InverterModel inverterModel:inverterModelList){
				inverterModel.setCreateTime(new Date());
			}
			positionInverterMap.put(powerStationId,inverterModelList);
			Configure.setPositionInverterMap(positionInverterMap);
		}else{
			InverterModel inverterModel=inverterModelList.get(0);
			if(inverterModel.getCreateTime()!=null && inverterModel.getCreateTime().getTime()+1000*60*30<new Date().getTime()){
				inverterModelList=inverterService.queryInverterListByData(map);
				for(InverterModel inverterModel2:inverterModelList){
					inverterModel2.setCreateTime(new Date());
				}
				positionInverterMap.put(powerStationId,inverterModelList);
				Configure.setPositionInverterMap(positionInverterMap);
			}
		}
		return inverterModelList;
	}
	private List<CloudTerminalModel> getCloudTerminalModel(Map<String,Object> map,String powerStationId){
		Map<String, List<CloudTerminalModel>> positionCloudTerminalMap =Configure.getPositionCloudTerminalMap();
		List<CloudTerminalModel> cloudTerminalModelList =positionCloudTerminalMap.get(powerStationId);
		if(cloudTerminalModelList==null || cloudTerminalModelList.size()<1){
			cloudTerminalModelList = cloudTerminalService.queryCloudListByData(map);
			for(CloudTerminalModel cloudTerminalModel:cloudTerminalModelList){
				cloudTerminalModel.setCreateTime(new Date());
			}
			positionCloudTerminalMap.put(powerStationId,cloudTerminalModelList);
			Configure.setPositionCloudTerminalMap(positionCloudTerminalMap);
		}else{
			CloudTerminalModel cloudTerminalModel=cloudTerminalModelList.get(0);
			if(cloudTerminalModel.getCreateTime()!=null && cloudTerminalModel.getCreateTime().getTime()+1000*60*30<new Date().getTime()){
				cloudTerminalModelList = cloudTerminalService.queryCloudListByData(map);
				for(CloudTerminalModel cloudTerminalModel2:cloudTerminalModelList){
					cloudTerminalModel2.setCreateTime(new Date());
				}
				positionCloudTerminalMap.put(powerStationId,cloudTerminalModelList);
				Configure.setPositionCloudTerminalMap(positionCloudTerminalMap);
			}
		}
		return cloudTerminalModelList;
	}

	private static Date getPowerStationTime(String date,String time){
		try{
			if(time!=null && time.indexOf(":")>0 && time.length()==5){
				return DateUtils.parse(date+" "+time,"yyyy-MM-dd HH:mm");
			}
		}catch (Exception e){
			e.printStackTrace();
		}
		return null;
	}


	/***
     * 查询组件数据
     * @param model    组件
     * @param pageView 分页实体
     * @return
     */
    @RequestMapping("queryComponentPageList.api")
    public CallResult queryComponentPageList(ComponentModel model , PageView pageView,HttpServletRequest request){
	    if(model.getBelongsGroupFlag()!=null && model.getBelongsGroupFlag().length()>0){
		    model.setPowerStationId(null);
	    }
	    if("selectComponent".equals(model.getBelongsGroupFlag())){//选择组件
		    String memberId = request.getParameter("mId");
		    String userType = request.getParameter("userType");
		    if("2".equals(userType)){//1 普通 2运维人员 3超级管理员账号
			    model.setCreateUserId(memberId);
		    }
	    }
        return componentService.queryComponentList(model , pageView);
    }


    /**
     * 页面请求 (新增,编辑,查看)
     * @param model      组件信息
     * @return
     */
    @RequestMapping(value="viewComponent.api")
    public CallResult viewComponent(ComponentModel model ){
	    CallResult callResult = CallResult.newInstance();
	    if (!StringUtils.hasLength(model.getId()) || StringUtils.isEmpty(model.getId()) ) {
//		    callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT );
//		    if(model!=null && "en".equals(model.getLanguage())){
//			    callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_EN );
//		    }else{
//			    callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT );
//		    }
		    getCallResult(callResult,model.getLanguage(),ErrCodeExt.PARAM_LOSS_EXCEPTION);
		    return callResult;
	    }
	    ComponentModel cloudTerminalModel=componentService.queryComponentById(model.getId(),model.getLanguage());
	    callResult.setReModel(cloudTerminalModel);
	    return callResult;
    }

    /**
     * 新增或修改
     * @param   model      组件
     * @return  CallResult
     */
    @RequestMapping(value="saveOrUpdate.api")
    public CallResult saveOrUpdate( ComponentModel model  ){
        if (CommonUtil.isEmpty(model.getId())) {
            //修改
            return componentService.updateComponentModel(model);
        } else {
	        if(model.getComponentNo()==null || "".equals(model.getComponentNo())){
		        model.setComponentNo(IDUtil.getUUIDStr());
	        }
	        model.setCreateType(2);
            return componentService.saveComponentModel(model);
        }
    }

    /**
     * 删除
     * @param  model  组件
     * @return CallResult
     */
    @RequestMapping(value="deleteComponents.api")
    public CallResult deleteComponents(ComponentModel model) {
        return componentService.deleteComponents(model);
    }

    /**
     * 逻辑删除
     * @param  model  组件
     * @return CallResult
     */
    @RequestMapping(value="updateDeleteComponentModels.api")
    public CallResult updateDeleteComponentModels(ComponentModel model) {
        return componentService.updateDeleteComponentModels(model);
    }


    /**
     * 组串中选择组件/组串中移除组件
     * @param model  组件
     * @return
     */
    @RequestMapping(value="selectOrMoveComponent.api")
    public CallResult selectOrMoveComponent(ComponentModel model ) {
        return componentService.selectOrMoveComponent(model);

    }


	/**
	 *
	 * 云终端中选择组件/云终端中移除组件
	 * @param model  组件
	 * @return
	 */
	@Deprecated
	@RequestMapping(value="selectOrMoveCloudComponent.api")
	public CallResult selectOrMoveCloudComponent(ComponentModel model ) {
		return componentService.selectOrMoveCloudComponent(model);
	}

	private void getAllGroupModelC(List<ComponentModel> lt,List<AllGroupModel> agmlt,List<ComponentCollectBatchNo>list,List<ComponentModel> componentList,List<WarningModel> warningModelList){
		AllGroupModel agm = null;
		ComponentCollectBatchNo collectBatchNo=null;
		if(list!=null && list.size()>0) {
			collectBatchNo = list.get(list.size() - 1);
		}
		for(ComponentModel cm:lt)
		{
			agm = new AllGroupModel();
			agm.setId(cm.getId());
			agm.setPowerId(cm.getPowerStationId());
			agm.setGapleft(cm.getXz());
			agm.setGaptop(cm.getYz());
			agm.setHv(cm.getPosition());
			agm.setAllname(cm.getChipId());
			agm.setChipid(cm.getChipId());
			agm.setStatus(cm.getStatus());
			agm.setIsException(cm.getIsException());

			agm.setWarningStatus(1);// 警告状态 1正常，2异常
			if(warningModelList!=null && warningModelList.size()>0){
				for (WarningModel warningModel:warningModelList){
					// 告警的设备id等于 组件的设备id 设置组件的告警状态为异常
					if(warningModel.getEquipmentId()!=null &&warningModel.getEquipmentId().equals(cm.getId())){
						agm.setWarningStatus(2);
					}
				}
			}
			Integer componentTemperature=0;
			Integer outputCurrent=0;
			Integer outputVoltage=0;
			Integer inputCurrent=0;
			Integer inputVoltage=0;
			if(collectBatchNo!=null){
				List<ComponentCollectModel> componentCollectModelList=collectBatchNo.getList();
				for(ComponentCollectModel componentCollectModel:componentCollectModelList){
					if(componentCollectModel.getChipId().equals(cm.getChipId())){
						componentTemperature=componentCollectModel.getComponentTemperature();
						outputCurrent=componentCollectModel.getOutputCurrent();
						outputVoltage=componentCollectModel.getOutputVoltage();
						inputCurrent=componentCollectModel.getInputCurrent();
						inputVoltage=componentCollectModel.getInputVoltage();
					}
				}
			}
			for(ComponentModel componentModel:componentList){
				if(CommonUtil.isEmpty(cm.getChipId())==true)
				{
					if(null != componentModel && CommonUtil.isEmpty(componentModel.getChipId())==true)
					{  
						if(componentModel.getChipId().equals(cm.getChipId()))
						{
							agm.setGroupId(componentModel.getBelongsGroupId());
							agm.setGroupName(componentModel.getBelongsGroupName());
//							agm.setStatus(componentModel.getStatus());
						}
					}
				}
			}
			System.out.println(agm.getChipid()+"****************************************"+agm.getGroupId());
			agm.setComponentTemperature(componentTemperature);
			agm.setOutputCurrent(outputCurrent);
			agm.setOutputVoltage(outputVoltage);
			agm.setInputCurrent(inputCurrent);
			agm.setInputVoltage(inputVoltage);
			agm.setLaystatus(cm.getLaystatus());
			agm.setGenre("3");
			if(agm.getGroupId()!=null){//如果为空手机端会报空指针异常
				agmlt.add(agm);
			}
		}
	}
	private void getAllGroupModelI(List<InverterModel> Imlt,List<AllGroupModel> agmlt){
		AllGroupModel agm = null;
		for(InverterModel cm :Imlt)
		{
			List<InverterCollect>  inverterCollectList=inverterCollectService.queryInverterCollectByLast(cm.getId());
			agm = new AllGroupModel();
			agm.setId(cm.getId());
			agm.setPowerId(cm.getPowerStationId());
			agm.setGapleft(cm.getXz());
			agm.setGaptop(cm.getYz());
			agm.setHv(cm.getPosition());
			agm.setAllname(cm.getInverterName());
			agm.setLaystatus(cm.getLaystatus());
			agm.setGenre("2");
			if(inverterCollectList!=null && inverterCollectList.size()>0){
				agm.setCollect(inverterCollectList.get(0));
			}
			agmlt.add(agm);
		}
	}
	private void getAllGroupModelT(List<CloudTerminalModel> ctlt,List<AllGroupModel> agmlt){
		AllGroupModel agm = null;
		for(CloudTerminalModel cm:ctlt)
		{
			agm = new AllGroupModel();
			agm.setId(cm.getId());
			agm.setPowerId(cm.getPowerStationId());
			agm.setGapleft(cm.getXz());
			agm.setGaptop(cm.getYz());
			agm.setHv(cm.getPosition());
			agm.setAllname(cm.getCloudName());
			agm.setLaystatus(cm.getLaystatus());
			agm.setGenre("1");
			agmlt.add(agm);
		}
	}

	/*
	 * 给三晶的查询接口
	 * */
	@RequestMapping(value="/queryComponentListTo.api")
	public CallResult queryComponentListTo(@RequestParam(value = "powerStationId", required = true) String powerStationId){
		return componentService.queryComponentCountListByPowId(powerStationId);
	}
}
