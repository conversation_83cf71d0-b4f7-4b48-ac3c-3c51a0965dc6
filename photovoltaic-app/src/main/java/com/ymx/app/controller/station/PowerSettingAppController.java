package com.ymx.app.controller.station;

import com.ymx.app.BaseController;
import com.ymx.common.common.result.CallResult;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.photovoltaic.station.model.PowerSettingModel;
import com.ymx.service.photovoltaic.station.service.PowerSettingService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 电站设置
 * <AUTHOR> @version 2018/10/29
 */
@RestController
@RequestMapping("powerSetting")
public class PowerSettingAppController extends BaseController {
	@Resource
	private PowerSettingService powerSettingService;
	/***
	 * 查询电站设置
	 * @return
	 */
	@RequestMapping("queryPowerSetting.api")
	public CallResult queryPowerSetting(String powerStationId){
		return powerSettingService.queryPowerSetting(powerStationId);
	}
	/***
	 * 修改电站设置
	 * @return
	 */
	@RequestMapping("updatePowerSetting.api")
	public CallResult updatePowerSetting(PowerSettingModel powerSetting){
		CallResult callResult = CallResult.newInstance();
		try {
			powerSettingService.updatePowerSetting(powerSetting);
			callResult.setReModel(1);
		}catch (Exception e){
//			if("en".equals(powerSetting.getLanguage())){
//				callResult.setErr(ErrCode.SYSTEM_ERR,ErrCode.SYSTEM_ERR_TEXT_EN);
//			}else{
//				callResult.setErr(ErrCode.SYSTEM_ERR,ErrCode.SYSTEM_ERR_TEXT);
//			}
			getCallResult(callResult,powerSetting.getLanguage(),ErrCodeExt.SYSTEM_ERR);
			e.printStackTrace();
		}
		return callResult;
	}

}
