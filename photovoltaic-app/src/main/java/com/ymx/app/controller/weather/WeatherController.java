package com.ymx.app.controller.weather;

import com.ymx.common.common.result.CallResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/weather")
@Slf4j
public class WeatherController {

    @Autowired
    private WeatherService weatherService;

    /**
     * 获取心知天气信息
     * @param districtId 位置信息（如：北京）
     */
    @PostMapping("/getWeather.api")
    public CallResult getSeniverseWeather(String districtId) {
        return weatherService.getSeniverseWeather(districtId);
    }
} 