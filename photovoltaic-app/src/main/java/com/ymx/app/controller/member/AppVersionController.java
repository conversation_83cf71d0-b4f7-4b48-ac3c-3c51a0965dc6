package com.ymx.app.controller.member;

import com.ymx.common.base.entity.AppVersionModel;
import com.ymx.common.base.service.AppVersionService;
import com.ymx.common.common.result.CallResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 版本管理控制器
 * <AUTHOR> @version 2018/11/5
 */
@RestController
@RequestMapping("appVersion")
public class AppVersionController {
	@Resource
	private AppVersionService appVersionService;

	/**
	 * 版本
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping("appVersionList.api")
	public CallResult appVersionList(HttpServletRequest request, AppVersionModel model){
		Map<String,Object> map= new HashMap<>();
		map.put("versionNum",model.getVersionNum());
		if(model.getLanguage()!=null){
			map.put("language",model.getLanguage());
		}
		return appVersionService.queryAppVersion(map);
	}
}
