package com.ymx.app.controller.sms;

import com.ymx.common.common.result.CallResult;
import com.ymx.service.photovoltaic.sms.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sms")
@Slf4j
public class SmsController{


    @Autowired
    private SmsService smsService;
//    /**
//     * 发送验证码
//     */
    @PostMapping("/sendSms.api")
    public CallResult sendVerificationCode(String account,String type) {
        return smsService.sendVerificationCode(account, type);
    }

    }

