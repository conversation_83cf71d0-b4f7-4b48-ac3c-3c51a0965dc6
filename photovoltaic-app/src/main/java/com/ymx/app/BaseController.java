package com.ymx.app;

import com.ymx.common.common.result.CallResult;
import com.ymx.service.constant.ErrCodeExt;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR> @version 2019/3/29
 */
@RestController
public class BaseController {
	public  CallResult getCallResult(CallResult callResult,String language,String errCode){
		if(ErrCodeExt.SYSTEM_ERR.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt.SYSTEM_ERR_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt.SYSTEM_ERR_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt.SYSTEM_ERR_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt.SYSTEM_ERR_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt.SYSTEM_ERR_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt.SYSTEM_ERR_TEXT);
			}
		}else if(ErrCodeExt.PARAM_ERR.equals(errCode)){
//			if("en".equals(language)){
//				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt.SYSTEM_ERR_TEXT_EN);
//			}else if("ja".equals(language)){//日文  ja
//				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt._TEXT_JA);
//			}else if("fr".equals(language)){//法语  FR
//				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt._TEXT_FR);
//			}else if("es".equals(language)){//西班牙语 ES
//				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt._TEXT_ES);
//			}else{
//				callResult.setErr(ErrCodeExt.SYSTEM_ERR  , ErrCodeExt.SYSTEM_ERR_TEXT);
//			}

		}else if(ErrCodeExt.PARAM_LOSS_EXCEPTION.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.PARAM_LOSS_EXCEPTION  , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.PARAM_LOSS_EXCEPTION  , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.PARAM_LOSS_EXCEPTION  , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.PARAM_LOSS_EXCEPTION  , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.PARAM_LOSS_EXCEPTION  , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.PARAM_LOSS_EXCEPTION  , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT);
			}
		}else if(ErrCodeExt.WEATHER_ERROR.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.WEATHER_ERROR  , ErrCodeExt.WEATHER_ERROR_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.WEATHER_ERROR  , ErrCodeExt.WEATHER_ERROR_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.WEATHER_ERROR  , ErrCodeExt.WEATHER_ERROR_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.WEATHER_ERROR  , ErrCodeExt.WEATHER_ERROR_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.WEATHER_ERROR  , ErrCodeExt.WEATHER_ERROR_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.WEATHER_ERROR  , ErrCodeExt.WEATHER_ERROR_TEXT);
			}
		}else if(ErrCodeExt.PARAM_LOSS.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.PARAM_LOSS  , ErrCodeExt.PARAM_LOSS_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.PARAM_LOSS  , ErrCodeExt.PARAM_LOSS_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.PARAM_LOSS  , ErrCodeExt.PARAM_LOSS_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.PARAM_LOSS  , ErrCodeExt.PARAM_LOSS_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.PARAM_LOSS  , ErrCodeExt.PARAM_LOSS_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.PARAM_LOSS  , ErrCodeExt.PARAM_LOSS_TEXT);
			}
		}else if(ErrCodeExt.SCAN_RESULT.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.SCAN_RESULT  , ErrCodeExt.SCAN_RESULT_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.SCAN_RESULT  , ErrCodeExt.SCAN_RESULT_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.SCAN_RESULT  , ErrCodeExt.SCAN_RESULT_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.SCAN_RESULT  , ErrCodeExt.SCAN_RESULT_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.SCAN_RESULT  , ErrCodeExt.SCAN_RESULT_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.SCAN_RESULT  , ErrCodeExt.SCAN_RESULT_TEXT);
			}
		}else if(ErrCodeExt.USER_SURNAME.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.USER_SURNAME  , ErrCodeExt.USER_SURNAME_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.USER_SURNAME  , ErrCodeExt.USER_SURNAME_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.USER_SURNAME  , ErrCodeExt.USER_SURNAME_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.USER_SURNAME  , ErrCodeExt.USER_SURNAME_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.USER_SURNAME  , ErrCodeExt.USER_SURNAME_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.USER_SURNAME  , ErrCodeExt.USER_SURNAME_TEXT);
			}
		}else if(ErrCodeExt.USER_NAME.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.USER_NAME  , ErrCodeExt.USER_NAME_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.USER_NAME  , ErrCodeExt.USER_NAME_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.USER_NAME  , ErrCodeExt.USER_NAME_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.USER_NAME  , ErrCodeExt.USER_NAME_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.USER_NAME  , ErrCodeExt.USER_NAME_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.USER_NAME  , ErrCodeExt.USER_NAME_TEXT);
			}
		}else if(ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_LONG_ERR_TEXT);
			}
		}else if(ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_FORMAT_ERR_TEXT);
			}
		}else if(ErrCodeExt.EMAIL_ERR.EMAIL_ERR.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_ERR_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_ERR_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_ERR_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_ERR_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_ERR_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.EMAIL_ERR.EMAIL_ERR  , ErrCodeExt.EMAIL_ERR.EMAIL_ERR_TEXT);
			}
		}else if(ErrCodeExt.PARAM_LOSS_SYSTEMNAME.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.PARAM_LOSS_SYSTEMNAME  , ErrCodeExt.PARAM_LOSS_SYSTEMNAME_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.PARAM_LOSS_SYSTEMNAME  , ErrCodeExt.PARAM_LOSS_SYSTEMNAME_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.PARAM_LOSS_SYSTEMNAME  , ErrCodeExt.PARAM_LOSS_SYSTEMNAME_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.PARAM_LOSS_SYSTEMNAME  , ErrCodeExt.PARAM_LOSS_SYSTEMNAME_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.PARAM_LOSS_SYSTEMNAME  , ErrCodeExt.PARAM_LOSS_SYSTEMNAME_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.PARAM_LOSS_SYSTEMNAME  , ErrCodeExt.PARAM_LOSS_SYSTEMNAME_TEXT);
			}
		}else if(ErrCodeExt.PARAM_LOSS_CITYID.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.PARAM_LOSS_CITYID  , ErrCodeExt.PARAM_LOSS_CITYID_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.PARAM_LOSS_CITYID  , ErrCodeExt.PARAM_LOSS_CITYID_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.PARAM_LOSS_CITYID  , ErrCodeExt.PARAM_LOSS_CITYID_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.PARAM_LOSS_CITYID  , ErrCodeExt.PARAM_LOSS_CITYID_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.PARAM_LOSS_CITYID  , ErrCodeExt.PARAM_LOSS_CITYID_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.PARAM_LOSS_CITYID  , ErrCodeExt.PARAM_LOSS_CITYID_TEXT);
			}
		}else if(ErrCodeExt.PARAM_LOSS_COUNTRIESID.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.PARAM_LOSS_COUNTRIESID  , ErrCodeExt.PARAM_LOSS_COUNTRIESID_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.PARAM_LOSS_COUNTRIESID  , ErrCodeExt.PARAM_LOSS_COUNTRIESID_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.PARAM_LOSS_COUNTRIESID  , ErrCodeExt.PARAM_LOSS_COUNTRIESID_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.PARAM_LOSS_COUNTRIESID  , ErrCodeExt.PARAM_LOSS_COUNTRIESID_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.PARAM_LOSS_COUNTRIESID  , ErrCodeExt.PARAM_LOSS_COUNTRIESID_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.PARAM_LOSS_COUNTRIESID  , ErrCodeExt.PARAM_LOSS_COUNTRIESID_TEXT);
			}
		}else if(ErrCodeExt.PARAM_LOSS_PROVINCE.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.PARAM_LOSS_PROVINCE  , ErrCodeExt.PARAM_LOSS_PROVINCE_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.PARAM_LOSS_PROVINCE  , ErrCodeExt.PARAM_LOSS_PROVINCE_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.PARAM_LOSS_PROVINCE  , ErrCodeExt.PARAM_LOSS_PROVINCE_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.PARAM_LOSS_PROVINCE  , ErrCodeExt.PARAM_LOSS_PROVINCE_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.PARAM_LOSS_PROVINCE  , ErrCodeExt.PARAM_LOSS_PROVINCE_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.PARAM_LOSS_PROVINCE  , ErrCodeExt.PARAM_LOSS_PROVINCE_TEXT);
			}
		}else if(ErrCodeExt.PARAM_LOSS_ZIPCODE.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.PARAM_LOSS_ZIPCODE  , ErrCodeExt.PARAM_LOSS_ZIPCODE_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.PARAM_LOSS_ZIPCODE  , ErrCodeExt.PARAM_LOSS_ZIPCODE_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.PARAM_LOSS_ZIPCODE  , ErrCodeExt.PARAM_LOSS_ZIPCODE_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.PARAM_LOSS_ZIPCODE  , ErrCodeExt.PARAM_LOSS_ZIPCODE_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.PARAM_LOSS_ZIPCODE  , ErrCodeExt.PARAM_LOSS_ZIPCODE_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.PARAM_LOSS_ZIPCODE  , ErrCodeExt.PARAM_LOSS_ZIPCODE_TEXT);
			}
		}else if(ErrCodeExt.PARAM_LOSS_STREETNAME.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.PARAM_LOSS_STREETNAME  , ErrCodeExt.PARAM_LOSS_STREETNAME_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.PARAM_LOSS_STREETNAME  , ErrCodeExt.PARAM_LOSS_STREETNAME_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.PARAM_LOSS_STREETNAME  , ErrCodeExt.PARAM_LOSS_STREETNAME_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.PARAM_LOSS_STREETNAME  , ErrCodeExt.PARAM_LOSS_STREETNAME_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.PARAM_LOSS_STREETNAME  , ErrCodeExt.PARAM_LOSS_STREETNAME_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.PARAM_LOSS_STREETNAME  , ErrCodeExt.PARAM_LOSS_STREETNAME_TEXT);
			}
		}else if(ErrCodeExt.USER_NOT_EXIST.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.USER_NOT_EXIST  , ErrCodeExt.USER_NOT_EXIST_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.USER_NOT_EXIST  , ErrCodeExt.USER_NOT_EXIST_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.USER_NOT_EXIST  , ErrCodeExt.USER_NOT_EXIST_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.USER_NOT_EXIST  , ErrCodeExt.USER_NOT_EXIST_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.USER_NOT_EXIST  , ErrCodeExt.USER_NOT_EXIST_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.USER_NOT_EXIST  , ErrCodeExt.USER_NOT_EXIST_TEXT);
			}
		}else if(ErrCodeExt.ACCOUNT_LOCK.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.ACCOUNT_LOCK  , ErrCodeExt.ACCOUNT_LOCK_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.ACCOUNT_LOCK  , ErrCodeExt.ACCOUNT_LOCK_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.ACCOUNT_LOCK  , ErrCodeExt.ACCOUNT_LOCK_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.ACCOUNT_LOCK  , ErrCodeExt.ACCOUNT_LOCK_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.ACCOUNT_LOCK  , ErrCodeExt.ACCOUNT_LOCK_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.ACCOUNT_LOCK  , ErrCodeExt.ACCOUNT_LOCK_TEXT);
			}
		}else if(ErrCodeExt.ACCOUNT_DELETE.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.ACCOUNT_DELETE  , ErrCodeExt.ACCOUNT_DELETE_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.ACCOUNT_DELETE  , ErrCodeExt.ACCOUNT_DELETE_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.ACCOUNT_DELETE  , ErrCodeExt.ACCOUNT_DELETE_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.ACCOUNT_DELETE  , ErrCodeExt.ACCOUNT_DELETE_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.ACCOUNT_DELETE  , ErrCodeExt.ACCOUNT_DELETE_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.ACCOUNT_DELETE  , ErrCodeExt.ACCOUNT_DELETE_TEXT);
			}
		}else if(ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME  , ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME  , ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME  , ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME  , ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME  , ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME  , ErrCodeExt.VERIFICATION_CODE_ERR_OVERTIME_TEXT);
			}
		}else if(ErrCodeExt.VERIFICATION_CODE_ERR.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.VERIFICATION_CODE_ERR  , ErrCodeExt.VERIFICATION_CODE_ERR_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.VERIFICATION_CODE_ERR  , ErrCodeExt.VERIFICATION_CODE_ERR_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.VERIFICATION_CODE_ERR  , ErrCodeExt.VERIFICATION_CODE_ERR_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.VERIFICATION_CODE_ERR  , ErrCodeExt.VERIFICATION_CODE_ERR_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.VERIFICATION_CODE_ERR  , ErrCodeExt.VERIFICATION_CODE_ERR_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.VERIFICATION_CODE_ERR  , ErrCodeExt.VERIFICATION_CODE_ERR_TEXT);
			}
		}else if(ErrCodeExt.PASSWD_ERROR.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.PASSWD_ERROR  , ErrCodeExt.PASSWD_ERROR_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar
				callResult.setErr(ErrCodeExt.PASSWD_ERROR  , ErrCodeExt.PASSWD_ERROR_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja
				callResult.setErr(ErrCodeExt.PASSWD_ERROR  , ErrCodeExt.PASSWD_ERROR_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR
				callResult.setErr(ErrCodeExt.PASSWD_ERROR  , ErrCodeExt.PASSWD_ERROR_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES
				callResult.setErr(ErrCodeExt.PASSWD_ERROR  , ErrCodeExt.PASSWD_ERROR_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.PASSWD_ERROR  , ErrCodeExt.PASSWD_ERROR_TEXT);
			}
		}else if(ErrCodeExt.OPEN_COMPONENT_CODE.equals(errCode)){
			if("en".equals(language)){
				callResult.setErr(ErrCodeExt.OPEN_COMPONENT_CODE  , ErrCodeExt.OPEN_COMPONENT_TEXT_EN);
			}else if("ar".equals(language)){//阿拉伯语  ar  Arabic
				callResult.setErr(ErrCodeExt.OPEN_COMPONENT_CODE  , ErrCodeExt.OPEN_COMPONENT_CODE_TEXT_AR);
			}else if("ja".equals(language)){//日文  ja  Japanese
				callResult.setErr(ErrCodeExt.OPEN_COMPONENT_CODE  , ErrCodeExt.OPEN_COMPONENT_CODE_TEXT_JA);
			}else if("fr".equals(language)){//法语  FR   French
				callResult.setErr(ErrCodeExt.OPEN_COMPONENT_CODE  , ErrCodeExt.OPEN_COMPONENT_CODE_TEXT_FR);
			}else if("es".equals(language)){//西班牙语 ES  Spanish
				callResult.setErr(ErrCodeExt.OPEN_COMPONENT_CODE  , ErrCodeExt.OPEN_COMPONENT_CODE_TEXT_ES);
			}else{
				callResult.setErr(ErrCodeExt.OPEN_COMPONENT_CODE  , ErrCodeExt.OPEN_COMPONENT_TEXT);
			}
		}


		//		getCallResult(callResult,model.getLanguage(),ErrCodeExt.SYSTEM_ERR);
		return callResult;
	}

}
